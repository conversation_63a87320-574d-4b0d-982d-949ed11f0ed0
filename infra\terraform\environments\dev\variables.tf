# Development Environment Variables

variable "project_id" {
  description = "GCP Project ID"
  type        = string
  
  validation {
    condition     = can(regex("^[a-z][a-z0-9-]{4,28}[a-z0-9]$", var.project_id))
    error_message = "Project ID must be 6-30 characters, start with a letter, and contain only lowercase letters, numbers, and hyphens."
  }
}

variable "project_name" {
  description = "Name of the project (used for resource naming)"
  type        = string
  default     = "platform"
  
  validation {
    condition     = can(regex("^[a-z][a-z0-9-]*[a-z0-9]$", var.project_name))
    error_message = "Project name must start with a letter, contain only lowercase letters, numbers, and hyphens."
  }
}

variable "region" {
  description = "GCP region for resources"
  type        = string
  default     = "australia-southeast1"
  
  validation {
    condition = contains([
      "australia-southeast1", "australia-southeast2",
      "us-central1", "us-east1", "us-west1", "us-west2",
      "europe-west1", "europe-west2", "europe-west3",
      "asia-east1", "asia-southeast1", "asia-northeast1"
    ], var.region)
    error_message = "Region must be a valid GCP region."
  }
}

variable "zone" {
  description = "GCP zone for resources"
  type        = string
  default     = "australia-southeast1-a"
  
  validation {
    condition     = can(regex("^[a-z]+-[a-z]+[0-9]+-[a-z]$", var.zone))
    error_message = "Zone must be a valid GCP zone format (e.g., australia-southeast1-a)."
  }
}

# GitHub configuration for CI/CD
variable "github_repo_owner" {
  description = "GitHub repository owner (organization or username)"
  type        = string
  default     = ""
}

variable "github_repo_name" {
  description = "GitHub repository name"
  type        = string
  default     = ""
}

# SSL/TLS configuration
variable "ssl_domains" {
  description = "List of domains for SSL certificate"
  type        = list(string)
  default     = []
}

# Development-specific overrides
variable "enable_debug_mode" {
  description = "Enable debug mode features for development"
  type        = bool
  default     = true
}

variable "enable_external_access" {
  description = "Enable external IP access to instances (for dev debugging)"
  type        = bool
  default     = true
}

variable "developer_ip_ranges" {
  description = "IP ranges for developer access (SSH, etc.)"
  type        = list(string)
  default     = ["0.0.0.0/0"]  # Allow from anywhere in dev
  
  validation {
    condition = alltrue([
      for cidr in var.developer_ip_ranges : can(cidrhost(cidr, 0))
    ])
    error_message = "All developer IP ranges must be valid IPv4 CIDR blocks."
  }
}

# Resource sizing for dev environment
variable "min_instance_count" {
  description = "Minimum number of instances"
  type        = number
  default     = 1
  
  validation {
    condition     = var.min_instance_count >= 1 && var.min_instance_count <= 10
    error_message = "Minimum instance count must be between 1 and 10."
  }
}

variable "max_instance_count" {
  description = "Maximum number of instances"
  type        = number
  default     = 3
  
  validation {
    condition     = var.max_instance_count >= 1 && var.max_instance_count <= 10
    error_message = "Maximum instance count must be between 1 and 10."
  }
}

# Development feature flags
variable "enable_migration_automation" {
  description = "Enable automated database migrations"
  type        = bool
  default     = true
}

variable "enable_monitoring" {
  description = "Enable monitoring and alerting"
  type        = bool
  default     = false  # Keep monitoring simple in dev
}

variable "enable_backup_automation" {
  description = "Enable automated backups"
  type        = bool
  default     = false  # Minimal backups in dev
}

# Cost optimization for dev
variable "schedule_shutdown" {
  description = "Schedule to shut down resources during non-working hours"
  type        = bool
  default     = false  # Manual control in dev
}

variable "auto_scaling_enabled" {
  description = "Enable auto-scaling for compute resources"
  type        = bool
  default     = false  # Keep scaling simple in dev
}