# Cloud Storage Module Variables

variable "project_name" {
  description = "Name of the project (used for resource naming)"
  type        = string
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
  default     = "dev"
  
  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "Environment must be one of: dev, staging, prod."
  }
}

variable "bucket_location" {
  description = "Location for the Cloud Storage bucket"
  type        = string
  default     = "australia-southeast1"
  
  validation {
    condition = contains([
      "US", "EU", "ASIA", 
      "australia-southeast1", "australia-southeast2",
      "us-central1", "us-east1", "us-west1", "us-west2",
      "europe-west1", "europe-west2", "europe-west3",
      "asia-east1", "asia-southeast1", "asia-northeast1"
    ], var.bucket_location)
    error_message = "Bucket location must be a valid GCS location."
  }
}

variable "storage_class" {
  description = "Storage class for the bucket"
  type        = string
  default     = "STANDARD"
  
  validation {
    condition     = contains(["STANDARD", "<PERSON>ARLIN<PERSON>", "COLDLIN<PERSON>", "ARCHIVE"], var.storage_class)
    error_message = "Storage class must be one of: STANDARD, NEARLINE, COLDLINE, ARCHIVE."
  }
}

variable "enable_versioning" {
  description = "Whether to enable object versioning"
  type        = bool
  default     = false
}

variable "enable_public_access" {
  description = "Whether to enable public read access to the bucket"
  type        = bool
  default     = true
}

# Website configuration
variable "main_page_suffix" {
  description = "Main page suffix for website hosting"
  type        = string
  default     = "index.html"
}

variable "not_found_page" {
  description = "404 page for website hosting"
  type        = string
  default     = "404.html"
}

# CORS configuration
variable "cors_origins" {
  description = "CORS origins for the bucket"
  type        = list(string)
  default     = ["*"]
}

# Lifecycle management
variable "lifecycle_rules" {
  description = "Lifecycle rules for the bucket"
  type = list(object({
    action                = string
    age_days              = number
    target_storage_class  = string
    matches_storage_class = list(string)
  }))
  default = [
    {
      action                = "SetStorageClass"
      age_days              = 30
      target_storage_class  = "NEARLINE"
      matches_storage_class = ["STANDARD"]
    },
    {
      action                = "SetStorageClass"
      age_days              = 90
      target_storage_class  = "COLDLINE"
      matches_storage_class = ["NEARLINE"]
    }
  ]
}

# Access logs configuration
variable "enable_access_logs" {
  description = "Whether to enable access logging"
  type        = bool
  default     = false
}

variable "access_log_bucket" {
  description = "Bucket for access logs (if empty, creates new bucket)"
  type        = string
  default     = ""
}

# IAM configuration
variable "enable_cloud_build_access" {
  description = "Whether to grant Cloud Build write access to the bucket"
  type        = bool
  default     = true
}

variable "additional_readers" {
  description = "Additional service accounts or users with read access"
  type        = map(list(string))
  default     = {}
}

variable "additional_writers" {
  description = "Additional service accounts or users with write access"
  type        = map(list(string))
  default     = {}
}

# Automated deployment configuration
variable "enable_automated_deployment" {
  description = "Whether to enable automated deployment via Cloud Build"
  type        = bool
  default     = false
}

variable "github_repo_owner" {
  description = "GitHub repository owner for automated deployment"
  type        = string
  default     = ""
}

variable "github_repo_name" {
  description = "GitHub repository name for automated deployment"
  type        = string
  default     = ""
}

variable "deploy_branch" {
  description = "Git branch to deploy from"
  type        = string
  default     = "main"
}

variable "cloudbuild_file_path" {
  description = "Path to Cloud Build configuration file for deployment"
  type        = string
  default     = "platform/web/cloudbuild.yaml"
}

# Default files
variable "upload_default_files" {
  description = "Whether to upload default index.html and 404.html files"
  type        = bool
  default     = true
}