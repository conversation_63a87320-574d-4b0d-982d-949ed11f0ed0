/* tslint:disable */
/* eslint-disable */
/**
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface OAuthSignInRequest
 */
export interface OAuthSignInRequest {
    /**
     * 
     * @type {string}
     * @memberof OAuthSignInRequest
     */
    provider: OAuthSignInRequestProviderEnum;
    /**
     * 
     * @type {string}
     * @memberof OAuthSignInRequest
     */
    redirectTo?: string;
}

/**
* @export
* @enum {string}
*/
export enum OAuthSignInRequestProviderEnum {
    Google = 'google'
}


/**
 * Check if a given object implements the OAuthSignInRequest interface.
 */
export function instanceOfOAuthSignInRequest(value: object): value is OAuthSignInRequest {
    if (!('provider' in value) || value['provider'] === undefined) return false;
    return true;
}

export function OAuthSignInRequestFromJSON(json: any): OAuthSignInRequest {
    return OAuthSignInRequestFromJSONTyped(json, false);
}

export function OAuthSignInRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): OAuthSignInRequest {
    if (json == null) {
        return json;
    }
    return {
        
        'provider': json['provider'],
        'redirectTo': json['redirectTo'] == null ? undefined : json['redirectTo'],
    };
}

  export function OAuthSignInRequestToJSON(json: any): OAuthSignInRequest {
      return OAuthSignInRequestToJSONTyped(json, false);
  }

  export function OAuthSignInRequestToJSONTyped(value?: OAuthSignInRequest | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'provider': value['provider'],
        'redirectTo': value['redirectTo'],
    };
}

