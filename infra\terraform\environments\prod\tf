#!/bin/bash
# Terraform wrapper script for cleaner output

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to run terraform with filtered output
run_terraform() {
    local cmd="$1"
    shift
    
    echo -e "${GREEN}Running: terraform $cmd $@${NC}"
    
    case "$cmd" in
        plan|apply)
            # For plan and apply, show progress but filter noise
            terraform "$cmd" "$@" 2>&1 | while IFS= read -r line; do
                # Skip warning lines about undeclared variables
                if [[ "$line" =~ "Warning: Value for undeclared variable" ]] || \
                   [[ "$line" =~ "The root module does not declare a variable" ]] || \
                   [[ "$line" =~ "To silence these warnings" ]]; then
                    continue
                fi
                
                # Skip empty lines
                if [[ -z "$line" ]]; then
                    continue
                fi
                
                # Highlight important lines
                if [[ "$line" =~ "Error:" ]]; then
                    echo -e "${RED}$line${NC}"
                elif [[ "$line" =~ "Plan:" ]] || [[ "$line" =~ "Apply complete!" ]]; then
                    echo -e "${GREEN}$line${NC}"
                elif [[ "$line" =~ "Creating..." ]] || [[ "$line" =~ "Modifying..." ]] || [[ "$line" =~ "Destroying..." ]]; then
                    echo -e "${YELLOW}$line${NC}"
                else
                    echo "$line"
                fi
            done | tail -n 100  # Keep last 100 lines in buffer
            ;;
        *)
            # For other commands, run normally
            terraform "$cmd" "$@"
            ;;
    esac
}

# Main execution
if [ $# -eq 0 ]; then
    echo "Usage: ./tf <command> [args...]"
    echo "Example: ./tf plan"
    echo "Example: ./tf apply -auto-approve"
    exit 1
fi

run_terraform "$@"