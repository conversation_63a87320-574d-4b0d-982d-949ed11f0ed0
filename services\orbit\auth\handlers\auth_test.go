package handlers

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"strings"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"

	"github.com/TwoDotAi/mono/services/orbit/auth/middleware"
)

func TestMain(m *testing.M) {
	gin.SetMode(gin.TestMode)
	os.Setenv("JWT_SECRET", "test-secret")
	os.Setenv("ENVIRONMENT", "test")
	code := m.Run()
	os.Unsetenv("JWT_SECRET")
	os.Unsetenv("ENVIRONMENT")
	os.Exit(code)
}

func TestGenerateJWTToken(t *testing.T) {
	userID := "user123"
	email := "<EMAIL>"

	token, err := generateJWTToken(userID, email)
	if err != nil {
		t.Errorf("generateJWTToken() error = %v", err)
		return
	}

	if token == "" {
		t.Error("generateJWTToken() returned empty token")
	}

	// Verify the token can be parsed
	claims := &middleware.Claims{}
	cfg := os.Getenv("JWT_SECRET")
	if cfg == "" {
		cfg = "test-secret"
	}
	
	parsedToken, err := jwt.ParseWithClaims(token, claims, func(token *jwt.Token) (interface{}, error) {
		return []byte(cfg), nil
	})

	if err != nil {
		t.Errorf("Failed to parse generated token: %v", err)
		return
	}

	if !parsedToken.Valid {
		t.Error("Generated token is not valid")
	}

	if claims.UserID != userID {
		t.Errorf("Token UserID = %v, want %v", claims.UserID, userID)
	}

	if claims.Email != email {
		t.Errorf("Token Email = %v, want %v", claims.Email, email)
	}
}

func TestSignInOAuth(t *testing.T) {
	tests := []struct {
		name           string
		bodyJSON       string
		expectedStatus int
		checkResponse  func(*testing.T, *httptest.ResponseRecorder)
	}{
		{
			name:           "google oauth request",
			bodyJSON:       `{"provider":"google","redirect_to":"http://localhost:3000/callback"}`,
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, w *httptest.ResponseRecorder) {
				var response map[string]string
				json.Unmarshal(w.Body.Bytes(), &response)
				if url, ok := response["url"]; !ok || url == "" {
					t.Error("Expected URL in response")
				}
			},
		},
		{
			name:           "unsupported provider",
			bodyJSON:       `{"provider":"facebook"}`,
			expectedStatus: http.StatusBadRequest,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			router := gin.New()
			router.POST("/oauth/signin", SignInOAuth)

			req, _ := http.NewRequest("POST", "/oauth/signin", strings.NewReader(tt.bodyJSON))
			req.Header.Set("Content-Type", "application/json")

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			if w.Code != tt.expectedStatus {
				t.Errorf("SignInOAuth() status = %v, want %v", w.Code, tt.expectedStatus)
			}

			if tt.checkResponse != nil {
				tt.checkResponse(t, w)
			}
		})
	}
}

func TestSignOut(t *testing.T) {
	router := gin.New()
	router.POST("/signout", SignOut)

	req, _ := http.NewRequest("POST", "/signout", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	if w.Code != http.StatusOK {
		t.Errorf("SignOut() status = %v, want %v", w.Code, http.StatusOK)
	}

	var response map[string]string
	json.Unmarshal(w.Body.Bytes(), &response)
	if response["message"] != "Successfully signed out" {
		t.Errorf("SignOut() unexpected message: %v", response["message"])
	}
}


func TestGetCurrentUserNoAuth(t *testing.T) {
	router := gin.New()
	router.GET("/user", GetCurrentUser)

	req, _ := http.NewRequest("GET", "/user", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	if w.Code != http.StatusUnauthorized {
		t.Errorf("GetCurrentUser() without auth status = %v, want %v", w.Code, http.StatusUnauthorized)
	}
}

func TestValidateToken(t *testing.T) {
	validToken := createTestToken("user123", "<EMAIL>", false)
	expiredToken := createTestToken("user123", "<EMAIL>", true)

	tests := []struct {
		name           string
		bodyJSON       string
		expectedStatus int
		checkResponse  func(*testing.T, *httptest.ResponseRecorder)
	}{
		{
			name:           "valid token",
			bodyJSON:       `{"token":"` + validToken + `"}`,
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, w *httptest.ResponseRecorder) {
				body := w.Body.String()
				if !strings.Contains(body, `"valid":true`) {
					t.Error("Expected token to be valid")
				}
				if !strings.Contains(body, `"user_id":"user123"`) {
					t.Error("Expected UserID user123 in response")
				}
				if !strings.Contains(body, `"email":"<EMAIL>"`) {
					t.Error("<NAME_EMAIL> in response")
				}
			},
		},
		{
			name:           "expired token",
			bodyJSON:       `{"token":"` + expiredToken + `"}`,
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, w *httptest.ResponseRecorder) {
				body := w.Body.String()
				if !strings.Contains(body, `"valid":false`) {
					t.Error("Expected token to be invalid")
				}
			},
		},
		{
			name:           "invalid token",
			bodyJSON:       `{"token":"invalid.jwt.token"}`,
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, w *httptest.ResponseRecorder) {
				body := w.Body.String()
				if !strings.Contains(body, `"valid":false`) {
					t.Error("Expected token to be invalid")
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			router := gin.New()
			router.POST("/validate", ValidateToken)

			req, _ := http.NewRequest("POST", "/validate", strings.NewReader(tt.bodyJSON))
			req.Header.Set("Content-Type", "application/json")

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			if w.Code != tt.expectedStatus {
				t.Errorf("ValidateToken() status = %v, want %v", w.Code, tt.expectedStatus)
			}

			if tt.checkResponse != nil {
				tt.checkResponse(t, w)
			}
		})
	}
}

func TestGetSession(t *testing.T) {
	tests := []struct {
		name           string
		authHeader     string
		expectedStatus int
	}{
		{
			name:           "no authorization header",
			authHeader:     "",
			expectedStatus: http.StatusUnauthorized,
		},
		{
			name:           "with authorization header",
			authHeader:     "Bearer some-token",
			expectedStatus: http.StatusUnauthorized, // Current implementation always returns unauthorized
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			router := gin.New()
			router.GET("/session", GetSession)

			req, _ := http.NewRequest("GET", "/session", nil)
			if tt.authHeader != "" {
				req.Header.Set("Authorization", tt.authHeader)
			}

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			if w.Code != tt.expectedStatus {
				t.Errorf("GetSession() status = %v, want %v", w.Code, tt.expectedStatus)
			}
		})
	}
}

func createTestToken(userID, email string, expired bool) string {
	claims := middleware.Claims{
		UserID: userID,
		Email:  email,
		RegisteredClaims: jwt.RegisteredClaims{
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
		},
	}

	if expired {
		claims.ExpiresAt = jwt.NewNumericDate(time.Now().Add(-1 * time.Hour))
	} else {
		claims.ExpiresAt = jwt.NewNumericDate(time.Now().Add(24 * time.Hour))
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, _ := token.SignedString([]byte("test-secret"))
	return tokenString
}