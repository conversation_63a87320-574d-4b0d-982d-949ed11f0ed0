#!/bin/bash
# Quick setup for STAGING environment
# Usage: source setup.sh (must be sourced to set environment variables)

echo "🎯 Setting up STAGING environment..."

# Source the main environment setup script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"

if [[ -f "$PROJECT_ROOT/terraform/ansible/setup-env.sh" ]]; then
    source "$PROJECT_ROOT/terraform/ansible/setup-env.sh"
else
    echo "❌ Could not find setup-env.sh script"
    return 1
fi

# Staging-specific overrides
export ENVIRONMENT="staging"
export GCP_PROJECT_ID="${GCP_PROJECT_ID:-crm-platform-staging}"
export GCP_REGION="us-central1"
export GCP_ZONE="us-central1-b"

# Staging-specific settings
export LOG_LEVEL="info"
export ENABLE_DEBUG="false"
export ENABLE_SWAGGER="false"
export CORS_ALLOWED_ORIGINS="https://staging.example.com"

echo ""
echo "🚀 STAGING environment ready!"
echo "💡 Quick commands:"
echo "   terraform init"
echo "   terraform plan"
echo "   terraform apply"
echo "   ansible-test"
echo ""