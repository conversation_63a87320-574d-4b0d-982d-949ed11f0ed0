<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${project_name} - ${environment}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
            line-height: 1.6;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .status {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .info {
            background: #f0f7ff;
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .code {
            background: #f5f5f5;
            border-radius: 4px;
            padding: 2px 6px;
            font-family: 'Monaco', 'Courier New', monospace;
            font-size: 0.9em;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .card h3 {
            margin: 0 0 10px 0;
            color: #2196f3;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>${project_name}</h1>
        <p>Infrastructure successfully deployed to <strong>${environment}</strong> environment</p>
    </div>

    <div class="status">
        <h2>✅ Cloud Storage Bucket Active</h2>
        <p>Your static web hosting bucket is configured and ready to serve your React application.</p>
    </div>

    <div class="info">
        <h2>📋 Next Steps</h2>
        <ol>
            <li>Build your React application with <span class="code">npm run build</span></li>
            <li>Upload the built files to this Cloud Storage bucket</li>
            <li>Configure your load balancer to serve files from this bucket</li>
            <li>Set up your custom domain with SSL certificates</li>
        </ol>
    </div>

    <div class="grid">
        <div class="card">
            <h3>🚀 Deploy Commands</h3>
            <p>Use <span class="code">gsutil</span> to upload your built React app:</p>
            <p><span class="code">gsutil -m rsync -r -d ./dist gs://bucket-name</span></p>
        </div>

        <div class="card">
            <h3>🔧 Configuration</h3>
            <p>Your bucket is configured with:</p>
            <ul>
                <li>CORS enabled for web access</li>
                <li>Public read access (if enabled)</li>
                <li>Lifecycle management</li>
            </ul>
        </div>

        <div class="card">
            <h3>📊 Monitoring</h3>
            <p>Monitor your static site with:</p>
            <ul>
                <li>Cloud Monitoring</li>
                <li>Access logs (if enabled)</li>
                <li>Load balancer metrics</li>
            </ul>
        </div>

        <div class="card">
            <h3>🔒 Security</h3>
            <p>Security features enabled:</p>
            <ul>
                <li>Uniform bucket-level access</li>
                <li>IAM-based permissions</li>
                <li>SSL/TLS via load balancer</li>
            </ul>
        </div>
    </div>

    <div class="info">
        <h2>🛠️ Platform Services</h2>
        <p>Your backend services are running on Compute Engine:</p>
        <ul>
            <li><strong>Gateway:</strong> API routing and load balancing</li>
            <li><strong>Auth Service:</strong> User authentication and sessions</li>
            <li><strong>CRM Backend:</strong> Core business logic and data management</li>
        </ul>
        <p>All services are connected to your Cloud SQL PostgreSQL database.</p>
    </div>

    <script>
        // Simple health check display
        document.addEventListener('DOMContentLoaded', function() {
            const timestamp = new Date().toLocaleString();
            const footer = document.createElement('div');
            footer.style.cssText = 'text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; font-size: 0.9em;';
            footer.innerHTML = `Page loaded at: $${timestamp}<br>Environment: <strong>${environment}</strong>`;
            document.body.appendChild(footer);
        });
    </script>
</body>
</html>