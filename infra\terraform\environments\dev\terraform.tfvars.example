# Development Environment Configuration Example
# Copy this file to terraform.tfvars and customize the values

# GCP Project Configuration
project_id   = "agent-dev-459718"
project_name = "platform"
region       = "australia-southeast1"
zone         = "australia-southeast1-a"

# GitHub Repository (for CI/CD automation)
github_repo_owner = "your-github-username"
github_repo_name  = "your-repo-name"

# Development Settings
enable_debug_mode      = true
enable_external_access = true

# Developer IP ranges (restrict to your office/home IPs in production)
developer_ip_ranges = [
  "0.0.0.0/0"  # Allow from anywhere - CHANGE THIS for security
  # "***********/24",    # Example: Your office IP range
  # "************/24",   # Example: Your home IP range
]

# Resource Scaling (keep minimal for dev)
min_instance_count = 1
max_instance_count = 2

# Feature Flags
enable_migration_automation = true
enable_monitoring          = false
enable_backup_automation   = false
schedule_shutdown          = false
auto_scaling_enabled       = false

# Development Notes:
# - This configuration is optimized for development use
# - Resources are sized to minimize costs
# - Security settings are relaxed for easier development
# - SSL is disabled (HTTP only)
# - External IPs are enabled for debugging
# - Database deletion protection is disabled
# - Backups are minimal