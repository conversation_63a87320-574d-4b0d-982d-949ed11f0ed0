package router

import (
	"github.com/gin-gonic/gin"
	"github.com/TwoDotAi/mono/services/orbit/gateway/config"
	"github.com/TwoDotAi/mono/services/orbit/gateway/handlers"
)

func SetupRoutes(r *gin.Engine, cfg *config.Config) {
	proxyHandler := handlers.NewProxyHandler(cfg)

	// API v1 routes
	v1 := r.Group("/api/v1")
	{
		// Authentication routes - handled by auth service
		auth := v1.Group("/auth")
		{
			auth.Any("/*path", proxyHandler.ProxyToService("auth"))
		}

		// User routes - currently handled by crm_backend
		users := v1.Group("/users")
		{
			users.Any("/*path", proxyHandler.ProxyToService("crm_backend"))
		}

		// Company routes - routed to crm_backend
		companies := v1.Group("/companies")
		{
			companies.Any("", proxyHandler.ProxyToService("crm_backend"))
			companies.Any("/*path", proxyHandler.ProxyToService("crm_backend"))
		}

		// Company statuses - routed to crm_backend
		v1.Any("/company-statuses", proxyHandler.ProxyToService("crm_backend"))

		// Contact routes - routed to crm_backend
		contacts := v1.Group("/contacts")
		{
			contacts.Any("", proxyHandler.ProxyToService("crm_backend"))
			contacts.Any("/*path", proxyHandler.ProxyToService("crm_backend"))
		}

		// Deal routes - routed to crm_backend
		deals := v1.Group("/deals")
		{
			deals.Any("", proxyHandler.ProxyToService("crm_backend"))
			deals.Any("/*path", proxyHandler.ProxyToService("crm_backend"))
		}

		// Deal stages - routed to crm_backend
		v1.Any("/deal-stages", proxyHandler.ProxyToService("crm_backend"))

		// Interaction routes - routed to crm_backend
		interactions := v1.Group("/interactions")
		{
			interactions.Any("", proxyHandler.ProxyToService("crm_backend"))
			interactions.Any("/*path", proxyHandler.ProxyToService("crm_backend"))
		}

		// Arli AI routes - routed to crm_backend
		arli := v1.Group("/arli")
		{
			arli.Any("", proxyHandler.ProxyToService("crm_backend"))
			arli.Any("/*path", proxyHandler.ProxyToService("crm_backend"))
		}
	}

}