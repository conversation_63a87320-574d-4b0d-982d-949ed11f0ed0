/**
 * Authentication Integration Tests
 * Tests real API calls to the backend authentication endpoints
 */

import { apiService } from '../../services/api.test';
import {
  User,
  LoginRequest,
  RegisterRequest,
  TokenResponse,
} from '@/types/api';

class AuthServiceTest {
  async login(credentials: LoginRequest): Promise<User> {
    const params = new URLSearchParams();
    params.append('username', credentials.username);
    params.append('password', credentials.password);

    const response = await apiService.post<TokenResponse>(
      '/auth/login',
      params
    );
    apiService.setTokens(response.data);

    // Get user data after login
    const userResponse = await apiService.get<User>('/auth/me');
    return userResponse.data;
  }

  async register(data: RegisterRequest): Promise<User> {
    const response = await apiService.post<User>('/auth/register', data);
    return response.data;
  }

  async getCurrentUser(): Promise<User> {
    const response = await apiService.get<User>('/auth/me');
    return response.data;
  }

  async updateProfile(data: Partial<User>): Promise<User> {
    const response = await apiService.put<User>('/auth/me', data);
    return response.data;
  }

  async changePassword(currentPassword: string, newPassword: string): Promise<void> {
    await apiService.post('/auth/change-password', {
      current_password: currentPassword,
      new_password: newPassword,
    });
  }

  logout() {
    apiService.logout();
  }

  isAuthenticated(): boolean {
    return !!apiService.getToken();
  }
}

const authService = new AuthServiceTest();

describe('Authentication Integration Tests', () => {
  let testUser: any;
  let createdUserToken: string | null = null;

  beforeEach(() => {
    // Clear any existing authentication
    authService.logout();
    testUser = (global as any).testUtils.generateTestUser();
  });

  afterEach(async () => {
    // Cleanup: logout any authenticated user
    if (createdUserToken) {
      authService.logout();
      createdUserToken = null;
    }
  });

  describe('User Registration', () => {
    test('should successfully register a new user', async () => {
      const registerData = {
        username: testUser.username,
        email: testUser.email,
        password: testUser.password,
        full_name: testUser.full_name
      };

      const user = await authService.register(registerData);

      expect(user).toBeDefined();
      expect(user.username).toBe(testUser.username);
      expect(user.email).toBe(testUser.email);
      expect(user.full_name).toBe(testUser.full_name);
      expect(user.id).toBeDefined();
      expect(user.created_at).toBeDefined();
    });

    test('should fail to register user with duplicate username', async () => {
      // Register first user
      await authService.register({
        username: testUser.username,
        email: testUser.email,
        password: testUser.password,
        full_name: testUser.full_name
      });

      // Try to register another user with same username
      const duplicateUser = (global as any).testUtils.generateTestUser();
      duplicateUser.username = testUser.username; // Same username

      await expect(authService.register({
        username: duplicateUser.username,
        email: duplicateUser.email,
        password: duplicateUser.password,
        full_name: duplicateUser.full_name
      })).rejects.toThrow();
    });

    test('should fail to register user with duplicate email', async () => {
      // Register first user
      await authService.register({
        username: testUser.username,
        email: testUser.email,
        password: testUser.password,
        full_name: testUser.full_name
      });

      // Try to register another user with same email
      const duplicateUser = (global as any).testUtils.generateTestUser();
      duplicateUser.email = testUser.email; // Same email

      await expect(authService.register({
        username: duplicateUser.username,
        email: duplicateUser.email,
        password: duplicateUser.password,
        full_name: duplicateUser.full_name
      })).rejects.toThrow();
    });

    test('should fail to register user with invalid email format', async () => {
      const invalidUser = {
        username: testUser.username,
        email: 'invalid-email-format',
        password: testUser.password,
        full_name: testUser.full_name
      };

      await expect(authService.register(invalidUser)).rejects.toThrow();
    });

    test('should fail to register user with weak password', async () => {
      const weakPasswordUser = {
        username: testUser.username,
        email: testUser.email,
        password: '123', // Too weak
        full_name: testUser.full_name
      };

      await expect(authService.register(weakPasswordUser)).rejects.toThrow();
    });
  });

  describe('User Login', () => {
    beforeEach(async () => {
      // Register a user first for login tests
      await authService.register({
        username: testUser.username,
        email: testUser.email,
        password: testUser.password,
        full_name: testUser.full_name
      });
    });

    test('should successfully login with valid credentials', async () => {
      const user = await authService.login({
        username: testUser.username,
        password: testUser.password
      });

      expect(user).toBeDefined();
      expect(user.username).toBe(testUser.username);
      expect(user.email).toBe(testUser.email);
      expect(authService.isAuthenticated()).toBe(true);
      expect(apiService.getToken()).toBeTruthy();

      createdUserToken = apiService.getToken();
    });

    test('should successfully login with email instead of username', async () => {
      const user = await authService.login({
        username: testUser.email, // Using email as username
        password: testUser.password
      });

      expect(user).toBeDefined();
      expect(user.email).toBe(testUser.email);
      expect(authService.isAuthenticated()).toBe(true);

      createdUserToken = apiService.getToken();
    });

    test('should fail to login with invalid username', async () => {
      await expect(authService.login({
        username: 'nonexistent_user',
        password: testUser.password
      })).rejects.toThrow();

      expect(authService.isAuthenticated()).toBe(false);
      expect(apiService.getToken()).toBeFalsy();
    });

    test('should fail to login with invalid password', async () => {
      await expect(authService.login({
        username: testUser.username,
        password: 'wrong_password'
      })).rejects.toThrow();

      expect(authService.isAuthenticated()).toBe(false);
      expect(apiService.getToken()).toBeFalsy();
    });

    test('should fail to login with empty credentials', async () => {
      await expect(authService.login({
        username: '',
        password: ''
      })).rejects.toThrow();

      expect(authService.isAuthenticated()).toBe(false);
    });
  });

  describe('User Profile Management', () => {
    beforeEach(async () => {
      // Register and login user
      await authService.register({
        username: testUser.username,
        email: testUser.email,
        password: testUser.password,
        full_name: testUser.full_name
      });

      await authService.login({
        username: testUser.username,
        password: testUser.password
      });

      createdUserToken = apiService.getToken();
    });

    test('should get current user profile', async () => {
      const user = await authService.getCurrentUser();

      expect(user).toBeDefined();
      expect(user.username).toBe(testUser.username);
      expect(user.email).toBe(testUser.email);
      expect(user.full_name).toBe(testUser.full_name);
    });

    test('should update user profile', async () => {
      const updateData = {
        full_name: 'Updated Full Name',
        bio: 'Updated bio information'
      };

      const updatedUser = await authService.updateProfile(updateData);

      expect(updatedUser.full_name).toBe(updateData.full_name);
      expect(updatedUser.bio).toBe(updateData.bio);
      expect(updatedUser.username).toBe(testUser.username); // Should remain unchanged
      expect(updatedUser.email).toBe(testUser.email); // Should remain unchanged
    });

    test('should change password successfully', async () => {
      const newPassword = 'NewSecurePassword123!';

      await expect(authService.changePassword(
        testUser.password,
        newPassword
      )).resolves.not.toThrow();

      // Verify can login with new password
      authService.logout();
      const user = await authService.login({
        username: testUser.username,
        password: newPassword
      });

      expect(user).toBeDefined();
      expect(authService.isAuthenticated()).toBe(true);

      createdUserToken = apiService.getToken();
    });

    test('should fail to change password with wrong current password', async () => {
      await expect(authService.changePassword(
        'wrong_current_password',
        'NewSecurePassword123!'
      )).rejects.toThrow();
    });

    test('should fail to change password to weak password', async () => {
      await expect(authService.changePassword(
        testUser.password,
        '123' // Too weak
      )).rejects.toThrow();
    });
  });

  describe('Authentication State Management', () => {
    test('should correctly identify unauthenticated state', () => {
      authService.logout();
      expect(authService.isAuthenticated()).toBe(false);
      expect(apiService.getToken()).toBeFalsy();
    });

    test('should correctly handle logout', async () => {
      // Register and login first
      await authService.register({
        username: testUser.username,
        email: testUser.email,
        password: testUser.password,
        full_name: testUser.full_name
      });

      await authService.login({
        username: testUser.username,
        password: testUser.password
      });

      expect(authService.isAuthenticated()).toBe(true);

      // Logout
      authService.logout();

      expect(authService.isAuthenticated()).toBe(false);
      expect(apiService.getToken()).toBeFalsy();
    });

    test('should fail to access protected endpoints without authentication', async () => {
      authService.logout();

      await expect(authService.getCurrentUser()).rejects.toThrow();
    });
  });

  describe('Token Management', () => {
    beforeEach(async () => {
      // Register and login user
      await authService.register({
        username: testUser.username,
        email: testUser.email,
        password: testUser.password,
        full_name: testUser.full_name
      });

      await authService.login({
        username: testUser.username,
        password: testUser.password
      });

      createdUserToken = apiService.getToken();
    });

    test('should have access and refresh tokens after login', () => {
      expect(apiService.getToken()).toBeTruthy();
      expect(apiService.getRefreshToken()).toBeTruthy();
    });

    test('should make authenticated requests with token', async () => {
      // This should succeed because we're authenticated
      const user = await authService.getCurrentUser();
      expect(user).toBeDefined();
    });
  });
});