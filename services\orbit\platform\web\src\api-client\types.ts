/**
 * TypeScript type definitions for CRM API
 */

// Base types
export interface BaseEntity {
  id: string;
  created_at: string;
  updated_at: string;
}

// Auth types
export interface User {
  id: string;
  email: string;
  email_confirmed_at?: string;
  created_at: string;
  updated_at: string;
}

export interface UserProfile extends BaseEntity {
  full_name?: string;
  avatar_url?: string;
  timezone?: string;
}

export interface AuthResponse {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  token_type: string;
  user: User;
}

// Company types
export interface Company extends BaseEntity {
  name: string;
  website?: string;
  phone?: string;
  address?: string;
  notes?: string;
  company_status_id?: string;
  is_deleted: boolean;
}

export interface CompanyStatus extends BaseEntity {
  name: string;
  pipeline_order: number;
}

export interface CompanyCreate {
  name: string;
  website?: string;
  phone?: string;
  address?: string;
  notes?: string;
  company_status_id?: string;
}

export interface CompanyUpdate {
  name?: string;
  website?: string;
  phone?: string;
  address?: string;
  notes?: string;
  company_status_id?: string;
}

// Contact types
export interface Contact extends BaseEntity {
  first_name: string;
  last_name: string;
  email?: string;
  phone?: string;
  job_title?: string;
  company_id?: string;
  created_by: string;
  is_deleted: boolean;
}

export interface ContactWithCompany extends Contact {
  company?: {
    id: string;
    name: string;
    is_deleted: boolean;
  };
}

export interface ContactCreate {
  first_name: string;
  last_name: string;
  email?: string;
  phone?: string;
  job_title?: string;
  company_id?: string;
}

export interface ContactUpdate {
  first_name?: string;
  last_name?: string;
  email?: string;
  phone?: string;
  job_title?: string;
  company_id?: string;
}

// Deal types
export interface Deal extends BaseEntity {
  title: string;
  description?: string;
  estimated_value?: number;
  company_id: string;
  deal_stage_id: string;
  expected_close_date?: string;
  created_by: string;
}

export interface DealStage extends BaseEntity {
  name: string;
  pipeline_order: number;
  is_closed_won: boolean;
  is_closed_lost: boolean;
}

export interface DealCreate {
  title: string;
  description?: string;
  estimated_value?: number;
  company_id: string;
  deal_stage_id: string;
  expected_close_date?: string;
}

export interface DealUpdate {
  title?: string;
  description?: string;
  estimated_value?: number;
  company_id?: string;
  deal_stage_id?: string;
  expected_close_date?: string;
}

// API Error types
export interface ApiError {
  code: string;
  message: string;
  details?: any;
}

export interface ValidationError extends ApiError {
  details: {
    field_errors: Record<string, string[]>;
  };
}
