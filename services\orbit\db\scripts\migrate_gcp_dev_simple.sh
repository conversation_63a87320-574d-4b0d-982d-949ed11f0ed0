#!/bin/bash
set -e

echo "Running database migrations for GCP dev environment via compute instance..."

# Get the workspace root - when running via <PERSON><PERSON>, we need to navigate to the workspace
if [ -n "$BUILD_WORKSPACE_DIRECTORY" ]; then
    cd "$BUILD_WORKSPACE_DIRECTORY"
elif [ -n "$TEST_WORKSPACE" ]; then
    cd "$TEST_WORKSPACE/.."
else
    # Find the workspace root by looking for WORKSPACE file
    CURRENT_DIR="$PWD"
    while [ "$CURRENT_DIR" != "/" ]; do
        if [ -f "$CURRENT_DIR/WORKSPACE" ]; then
            cd "$CURRENT_DIR"
            break
        fi
        CURRENT_DIR="$(dirname "$CURRENT_DIR")"
    done
fi

# Navigate to terraform dev environment to get current values
cd terraform/environments/dev

# Get connection details from terraform outputs
echo "Getting connection details from Terraform..."
INSTANCE_NAME=$(terraform output -raw instance_name)
DATABASE_NAME=$(terraform output -raw database_name)
DATABASE_USER=$(terraform output -raw database_user)
DATABASE_PRIVATE_IP=$(terraform output -raw database_private_ip)
ZONE=$(terraform output -json dev_access_info | jq -r '.zone')

echo "Connection details:"
echo "  Instance: $INSTANCE_NAME"
echo "  Database name: $DATABASE_NAME"
echo "  Database user: $DATABASE_USER"
echo "  Private IP: $DATABASE_PRIVATE_IP"
echo "  Zone: $ZONE"

# Get database password from Secret Manager locally
echo "Getting database password from Secret Manager..."
DATABASE_PASSWORD=$(gcloud secrets versions access latest --secret=platform-db-password)

if [ -z "$DATABASE_PASSWORD" ]; then
    echo "ERROR: Could not retrieve database password from Secret Manager"
    exit 1
fi

# Return to workspace root
cd ../../..

# Navigate to platform/db directory
cd platform/db

# Create inline migration script and run it directly
echo "Running migration on compute instance..."
gcloud compute ssh "$INSTANCE_NAME" --zone="$ZONE" --command="
    set -e
    
    echo 'Running database migration on compute instance...'
    
    # Create temporary directory for migration
    TEMP_DIR=\$(mktemp -d)
    cd \$TEMP_DIR
    
    # Create migration files
    mkdir -p migrations
    
    # Debug: Check current directory and permissions
    echo \"Current directory: \$(pwd)\"
    ls -la
    echo \"Creating migrations directory...\"
    mkdir -p migrations
    echo \"Directory created:\"
    ls -la migrations/
    
    # Create V001 migration
    cat > migrations/V001__initial_schema.sql << 'MIGRATION_EOF'
-- Initial database schema
-- V001__initial_schema.sql

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    full_name VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create index on email for faster lookups
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);

-- Create a trigger to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS \$\$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
\$\$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE
    ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
MIGRATION_EOF

    # Create V002 migration
    cat > migrations/V002__crm_schema.sql << 'MIGRATION_EOF'
-- CRM Database Schema Migration
-- V002__crm_schema.sql

-- Update users table to support authentication
ALTER TABLE users ADD COLUMN IF NOT EXISTS password_hash VARCHAR(255);
ALTER TABLE users ADD COLUMN IF NOT EXISTS email_confirmed_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS is_deleted BOOLEAN DEFAULT FALSE;

-- Create user profiles table
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    full_name VARCHAR(255),
    avatar_url TEXT,
    timezone VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create company statuses table (pipeline stages for companies)
CREATE TABLE IF NOT EXISTS company_statuses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    pipeline_order INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create companies table
CREATE TABLE IF NOT EXISTS companies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    website TEXT,
    phone VARCHAR(50),
    address TEXT,
    notes TEXT,
    company_status_id UUID REFERENCES company_statuses(id),
    is_deleted BOOLEAN DEFAULT FALSE,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create contacts table
CREATE TABLE IF NOT EXISTS contacts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(50),
    job_title VARCHAR(255),
    company_id UUID REFERENCES companies(id) ON DELETE SET NULL,
    created_by UUID NOT NULL REFERENCES users(id),
    is_deleted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create deal stages table (pipeline stages for deals)
CREATE TABLE IF NOT EXISTS deal_stages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    pipeline_order INTEGER NOT NULL,
    is_closed_won BOOLEAN DEFAULT FALSE,
    is_closed_lost BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create deals table
CREATE TABLE IF NOT EXISTS deals (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    estimated_value DECIMAL(15,2),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    deal_stage_id UUID NOT NULL REFERENCES deal_stages(id),
    expected_close_date DATE,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create interaction types table
CREATE TABLE IF NOT EXISTS interaction_types (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create interactions table (communication tracking)
CREATE TABLE IF NOT EXISTS interactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    subject VARCHAR(255),
    content TEXT,
    interaction_type_id UUID REFERENCES interaction_types(id),
    company_id UUID REFERENCES companies(id) ON DELETE CASCADE,
    contact_id UUID REFERENCES contacts(id) ON DELETE SET NULL,
    deal_id UUID REFERENCES deals(id) ON DELETE SET NULL,
    created_by UUID NOT NULL REFERENCES users(id),
    interaction_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create arli documents table (AI document processing)
CREATE TABLE IF NOT EXISTS arli_documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    content TEXT,
    metadata JSONB,
    source_url TEXT,
    document_type VARCHAR(100),
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create arli content blocks table
CREATE TABLE IF NOT EXISTS arli_content_blocks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID NOT NULL REFERENCES arli_documents(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    block_type VARCHAR(50) NOT NULL,
    metadata JSONB,
    order_index INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_companies_name ON companies(name);
CREATE INDEX IF NOT EXISTS idx_companies_status ON companies(company_status_id);
CREATE INDEX IF NOT EXISTS idx_companies_deleted ON companies(is_deleted);
CREATE INDEX IF NOT EXISTS idx_companies_created_by ON companies(created_by);

CREATE INDEX IF NOT EXISTS idx_contacts_name ON contacts(first_name, last_name);
CREATE INDEX IF NOT EXISTS idx_contacts_email ON contacts(email);
CREATE INDEX IF NOT EXISTS idx_contacts_company ON contacts(company_id);
CREATE INDEX IF NOT EXISTS idx_contacts_deleted ON contacts(is_deleted);
CREATE INDEX IF NOT EXISTS idx_contacts_created_by ON contacts(created_by);

CREATE INDEX IF NOT EXISTS idx_deals_title ON deals(title);
CREATE INDEX IF NOT EXISTS idx_deals_company ON deals(company_id);
CREATE INDEX IF NOT EXISTS idx_deals_stage ON deals(deal_stage_id);
CREATE INDEX IF NOT EXISTS idx_deals_created_by ON deals(created_by);
CREATE INDEX IF NOT EXISTS idx_deals_close_date ON deals(expected_close_date);

CREATE INDEX IF NOT EXISTS idx_interactions_company ON interactions(company_id);
CREATE INDEX IF NOT EXISTS idx_interactions_contact ON interactions(contact_id);
CREATE INDEX IF NOT EXISTS idx_interactions_deal ON interactions(deal_id);
CREATE INDEX IF NOT EXISTS idx_interactions_type ON interactions(interaction_type_id);
CREATE INDEX IF NOT EXISTS idx_interactions_date ON interactions(interaction_date);
CREATE INDEX IF NOT EXISTS idx_interactions_created_by ON interactions(created_by);

CREATE INDEX IF NOT EXISTS idx_arli_documents_type ON arli_documents(document_type);
CREATE INDEX IF NOT EXISTS idx_arli_documents_created_by ON arli_documents(created_by);
CREATE INDEX IF NOT EXISTS idx_arli_content_blocks_document ON arli_content_blocks(document_id);
CREATE INDEX IF NOT EXISTS idx_arli_content_blocks_type ON arli_content_blocks(block_type);
CREATE INDEX IF NOT EXISTS idx_arli_content_blocks_order ON arli_content_blocks(document_id, order_index);

-- Create triggers for updated_at columns
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE
    ON user_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_companies_updated_at BEFORE UPDATE
    ON companies FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_contacts_updated_at BEFORE UPDATE
    ON contacts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_deals_updated_at BEFORE UPDATE
    ON deals FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_interactions_updated_at BEFORE UPDATE
    ON interactions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_arli_documents_updated_at BEFORE UPDATE
    ON arli_documents FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_arli_content_blocks_updated_at BEFORE UPDATE
    ON arli_content_blocks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
MIGRATION_EOF

    # Check migration files were created
    echo \"Checking migration files...\"
    ls -la migrations/
    echo \"Migration files content:\"
    wc -l migrations/*.sql
    
    # Create flyway configuration
    cat > flyway.conf << 'FLYWAY_EOF'
# Flyway Configuration for GCP Cloud SQL
flyway.url=jdbc:postgresql://${DATABASE_PRIVATE_IP}:5432/${DATABASE_NAME}
flyway.user=${DATABASE_USER}
flyway.password=${DATABASE_PASSWORD}
flyway.schemas=public
flyway.locations=filesystem:/flyway/sql
flyway.baselineOnMigrate=true
flyway.baselineVersion=0
flyway.validateOnMigrate=true
flyway.cleanDisabled=true
flyway.connectRetries=5
flyway.connectRetriesInterval=10
FLYWAY_EOF

    # Run Flyway migration using Docker
    echo 'Running Flyway migrations...'
    echo \"Current directory: \$(pwd)\"
    echo \"Flyway config:\"
    cat flyway.conf
    echo \"Migration files:\"
    ls -la migrations/
    echo \"Starting Flyway...\"
    
    sudo docker run --rm \\
        --network=\"host\" \\
        -v \"\$(pwd)/migrations:/flyway/sql\" \\
        -v \"\$(pwd)/flyway.conf:/flyway/conf/flyway.conf\" \\
        flyway/flyway:10-alpine \\
        migrate
    
    echo 'Migration completed successfully!'
    
    # Clean up
    cd /
    rm -rf \$TEMP_DIR
"

echo ""
echo "GCP database migration completed successfully!"
echo ""
echo "Database connection details:"
echo "  Database: $DATABASE_NAME"
echo "  Private IP: $DATABASE_PRIVATE_IP"