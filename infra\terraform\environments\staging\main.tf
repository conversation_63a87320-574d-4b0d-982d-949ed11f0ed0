# Staging Environment Terraform Configuration
# This configuration deploys the platform to the staging environment

terraform {
  required_version = ">= 1.5"
  
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
    random = {
      source  = "hashicorp/random"
      version = "~> 3.1"
    }
  }

  backend "gcs" {
    bucket = "agent-dev-459718-terraform-state"  # GCS bucket for Terraform state
    prefix = "terraform/staging"
  }
}

# Configure the Google Cloud Provider
provider "google" {
  project = var.project_id
  region  = var.region
  zone    = var.zone
}

# Local values for staging environment
locals {
  environment = "staging"
  project_name = var.project_name
  
  # Staging-specific configurations (between dev and prod)
  machine_types = {
    compute = "e2-standard-2"
    database = "db-standard-1"
  }
  
  # Networking
  vpc_cidr = "10.2.0.0/16"
  public_subnet_cidr = "10.2.1.0/24"
  private_subnet_cidr = "10.2.2.0/24"
  
  # Database settings
  database_config = {
    postgres_version = "POSTGRES_15"
    disk_size_gb = 50
    max_disk_size_gb = 200
    backup_retention_days = 14
    availability_type = "ZONAL"  # Single zone for staging
    deletion_protection = true  # Protect staging data
  }
  
  # Security settings (moderate for staging)
  enable_ssl = true  # Enable SSL in staging
  enable_cloud_armor = true
  enable_public_access = true
  
  # SSL domains for staging
  ssl_domains = var.ssl_domains
  
  # Service ports
  service_ports = {
    auth_service = 8004
    crm_backend = 8003
    gateway = 8085
  }
}

# VPC Network
module "network" {
  source = "../../modules/network"
  
  project_name = local.project_name
  region = var.region
  
  public_subnet_cidr = local.public_subnet_cidr
  private_subnet_cidr = local.private_subnet_cidr
  
  # Moderate SSH restrictions for staging
  ssh_source_ranges = var.admin_ip_ranges
}

# Database
module "database" {
  source = "../../modules/database"
  
  project_name = local.project_name
  environment = local.environment
  region = var.region
  
  vpc_network = module.network.vpc_self_link
  
  postgres_version = local.database_config.postgres_version
  instance_tier = local.machine_types.database
  availability_type = local.database_config.availability_type
  disk_size_gb = local.database_config.disk_size_gb
  max_disk_size_gb = local.database_config.max_disk_size_gb
  backup_retention_days = local.database_config.backup_retention_days
  deletion_protection = local.database_config.deletion_protection
  
  database_name = "platform_staging_db"
  database_user = "platform_staging_user"
  
  # Enable migration automation for staging testing
  enable_migration_automation = true
  github_repo_owner = var.github_repo_owner
  github_repo_name = var.github_repo_name
  migration_branch = "main"
  
  # Test scheduled migrations in staging
  enable_scheduled_migrations = var.enable_scheduled_migrations
  migration_schedule = var.migration_schedule
  migration_timezone = var.migration_timezone
  
  # Enable notifications for testing
  enable_migration_notifications = var.enable_migration_notifications
  slack_webhook_url = var.slack_webhook_url
  notification_email = var.notification_email
}

# Artifact Registry
module "registry" {
  source = "../../modules/registry"
  
  project_name = local.project_name
  region = var.region
  
  # Moderate retention for staging
  cleanup_older_than_days = 30
  cleanup_untagged_older_than_days = 3
  immutable_tags = false  # Allow tag overwriting in staging
  
  enable_cloud_build_access = true
  enable_compute_access = true
  
  # Automated builds from develop and staging branches
  enable_automated_builds = true
  github_repo_owner = var.github_repo_owner
  github_repo_name = var.github_repo_name
  build_branch_pattern = "develop|staging/*"
  cloudbuild_file_path = "cloudbuild.staging.yaml"
}

# Cloud Storage for static files
module "storage" {
  source = "../../modules/storage"
  
  project_name = local.project_name
  environment = local.environment
  bucket_location = var.region
  storage_class = "STANDARD"
  
  enable_public_access = local.enable_public_access
  enable_versioning = true  # Enable versioning in staging
  
  # Staging lifecycle rules
  lifecycle_rules = [
    {
      action = "SetStorageClass"
      age_days = 15
      storage_classes = ["NEARLINE"]
    },
    {
      action = "Delete"
      age_days = 90
      storage_classes = ["NEARLINE"]
    }
  ]
  
  enable_automated_deployment = true
  github_repo_owner = var.github_repo_owner
  github_repo_name = var.github_repo_name
  deploy_branch = "develop"
  
  # SPA routing configuration - serve index.html for all routes
  not_found_page = "index.html"
  
  upload_default_files = false
}

# Compute Engine instance
module "compute" {
  source = "../../modules/compute"
  
  project_name = local.project_name
  environment = local.environment
  region = var.region
  zone = var.zone
  
  machine_type = local.machine_types.compute
  boot_disk_size_gb = 30
  docker_volumes_disk_size_gb = 50
  
  subnet_name = module.network.private_subnet_name
  assign_external_ip = false  # No external IP in staging
  create_static_ip = false
  
  # Registry configuration
  registry_url = module.registry.registry_hostname
  
  # Database configuration
  database_host = module.database.instance_private_ip
  database_name = module.database.database_name
  database_user = module.database.database_user
  database_password_secret = module.database.database_password_secret_name
  
  # Service ports
  auth_service_port = local.service_ports.auth_service
  crm_service_port = local.service_ports.crm_backend
  gateway_port = local.service_ports.gateway
}

# Load Balancer
module "loadbalancer" {
  source = "../../modules/loadbalancer"
  
  project_name = local.project_name
  environment = local.environment
  
  storage_bucket_name = module.storage.bucket_name
  api_backend_service = module.compute.backend_service_self_link
  
  # Enable SSL with managed certificates
  enable_ssl = local.enable_ssl
  use_managed_ssl = true
  ssl_domains = local.ssl_domains
  
  # Staging SSL policy
  create_ssl_policy = true
  ssl_policy_profile = "MODERN"
  ssl_min_tls_version = "TLS_1_2"
  
  # Staging CDN settings
  enable_cdn = true
  cdn_default_ttl = 1800   # 30 minutes
  cdn_max_ttl = 7200       # 2 hours
  cdn_client_ttl = 1800    # 30 minutes
  cdn_serve_while_stale = 3600
  
  # Enable moderate Cloud Armor security
  enable_cloud_armor = local.enable_cloud_armor
  rate_limit_requests_per_minute = var.rate_limit_requests_per_minute
  blocked_regions = var.blocked_regions
  blocked_ip_ranges = var.blocked_ip_ranges
}