/* tslint:disable */
/* eslint-disable */
/**
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface InteractionCreate
 */
export interface InteractionCreate {
    /**
     * 
     * @type {string}
     * @memberof InteractionCreate
     */
    companyId?: string;
    /**
     * 
     * @type {string}
     * @memberof InteractionCreate
     */
    contactId?: string;
    /**
     * 
     * @type {string}
     * @memberof InteractionCreate
     */
    interactionType: InteractionCreateInteractionTypeEnum;
    /**
     * 
     * @type {string}
     * @memberof InteractionCreate
     */
    notes: string;
    /**
     * 
     * @type {Date}
     * @memberof InteractionCreate
     */
    interactionDatetime: Date;
}

/**
* @export
* @enum {string}
*/
export enum InteractionCreateInteractionTypeEnum {
    Email = 'email',
    Phone = 'phone',
    Meeting = 'meeting',
    Demo = 'demo',
    Proposal = 'proposal',
    FollowUp = 'follow-up',
    Other = 'other'
}


/**
 * Check if a given object implements the InteractionCreate interface.
 */
export function instanceOfInteractionCreate(value: object): value is InteractionCreate {
    if (!('interactionType' in value) || value['interactionType'] === undefined) return false;
    if (!('notes' in value) || value['notes'] === undefined) return false;
    if (!('interactionDatetime' in value) || value['interactionDatetime'] === undefined) return false;
    return true;
}

export function InteractionCreateFromJSON(json: any): InteractionCreate {
    return InteractionCreateFromJSONTyped(json, false);
}

export function InteractionCreateFromJSONTyped(json: any, ignoreDiscriminator: boolean): InteractionCreate {
    if (json == null) {
        return json;
    }
    return {
        
        'companyId': json['company_id'] == null ? undefined : json['company_id'],
        'contactId': json['contact_id'] == null ? undefined : json['contact_id'],
        'interactionType': json['interaction_type'],
        'notes': json['notes'],
        'interactionDatetime': (new Date(json['interaction_datetime'])),
    };
}

  export function InteractionCreateToJSON(json: any): InteractionCreate {
      return InteractionCreateToJSONTyped(json, false);
  }

  export function InteractionCreateToJSONTyped(value?: InteractionCreate | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'company_id': value['companyId'],
        'contact_id': value['contactId'],
        'interaction_type': value['interactionType'],
        'notes': value['notes'],
        'interaction_datetime': ((value['interactionDatetime']).toISOString()),
    };
}

