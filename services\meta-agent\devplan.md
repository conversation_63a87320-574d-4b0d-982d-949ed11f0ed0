# AI Agent Platform - Bottom-Up Development Plan

## 🎯 Context & Philosophy

This development plan follows a **bottom-up approach** designed for **vibe coding** - where you can jump in at any level, maintain context across sessions, and build incrementally. Each component is self-contained yet designed to integrate seamlessly with others.

### 🧠 Mental Model
```
Foundation Layer → Core Services → Integration Layer → User Experience → Platform Services
```

Each layer builds on the previous, but components within a layer can be developed independently.

---

# 🏗️ Phase 1: Foundation Layer (Weeks 1-4)

## 1.1 Database & Storage Foundation

### 🎯 Context
Start here because **data persistence is the bedrock**. Every service will need to store and retrieve data. Building this first means all subsequent components have a solid foundation.

### 📦 What You're Building
A robust, scalable data layer that handles all platform data needs.

### 🛠️ Development Steps

#### Step 1.1.1: PostgreSQL Schema Design (2 days)
```sql
-- Core schema files to create:
├── schema/
│   ├── 00_extensions.sql     -- Enable required extensions
│   ├── 01_users.sql          -- User management tables
│   ├── 02_agents.sql         -- Agent definitions and metadata
│   ├── 03_executions.sql     -- Agent execution history
│   ├── 04_configurations.sql -- Agent and system configs
│   └── 05_audit.sql          -- Audit and compliance tables
```

**Vibe Coding Context:**
- 🎯 Goal: Design schemas that support agent lifecycle, user management, and audit trails
- 🔍 Focus: Think about how agents will store their state, configurations, and execution history
- 🧪 Test: Each table should support CRUD operations and have proper indexes

**Code Example:**
```sql
-- agents.sql
CREATE TABLE agents (
    agent_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    agent_type VARCHAR(100) NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    version VARCHAR(20) NOT NULL DEFAULT '1.0.0',
    configuration JSONB NOT NULL DEFAULT '{}',
    capabilities TEXT[] DEFAULT ARRAY[]::TEXT[],
    status agent_status_enum NOT NULL DEFAULT 'created',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES users(user_id),
    
    -- Indexes for performance
    INDEX idx_agents_type (agent_type),
    INDEX idx_agents_status (status),
    INDEX idx_agents_capabilities USING GIN (capabilities)
);
```

#### Step 1.1.2: Redis Configuration & Setup (1 day)
```yaml
# redis-config.yml
apiVersion: v1
kind: ConfigMap
metadata:
  name: redis-config
data:
  redis.conf: |
    # Agent state caching
    maxmemory 2gb
    maxmemory-policy allkeys-lru
    
    # Session management
    timeout 300
    tcp-keepalive 60
    
    # Performance
    save 900 1
    save 300 10
```

**Vibe Coding Context:**
- 🎯 Goal: Fast caching layer for agent state and sessions
- 🔍 Focus: Configure for both caching and pub/sub messaging
- 🧪 Test: Verify agent state can be cached and retrieved quickly

#### Step 1.1.3: ClickHouse Analytics Setup (1 day)
```sql
-- analytics_schema.sql
CREATE TABLE agent_metrics (
    timestamp DateTime64(3),
    agent_id String,
    metric_name String,
    metric_value Float64,
    labels Map(String, String),
    environment String DEFAULT 'production'
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (agent_id, metric_name, timestamp);
```

**Vibe Coding Context:**
- 🎯 Goal: High-performance analytics for agent metrics
- 🔍 Focus: Design for time-series data and fast aggregations
- 🧪 Test: Insert sample metrics and verify query performance

#### Step 1.1.4: Weaviate Vector Database (1 day)
```python
# vector_db_setup.py
import weaviate

def setup_agent_memory_schema():
    schema = {
        "class": "AgentMemory",
        "description": "Agent memory and experience storage",
        "properties": [
            {
                "name": "agent_id",
                "dataType": ["string"],
                "description": "Unique agent identifier"
            },
            {
                "name": "memory_content",
                "dataType": ["text"],
                "description": "The actual memory content"
            },
            {
                "name": "memory_type",
                "dataType": ["string"],
                "description": "Type of memory (experience, knowledge, etc.)"
            },
            {
                "name": "importance_score",
                "dataType": ["number"],
                "description": "Importance score for memory retention"
            }
        ],
        "vectorizer": "text2vec-openai"
    }
    return schema
```

**Vibe Coding Context:**
- 🎯 Goal: Semantic memory storage for agents
- 🔍 Focus: Design schema for agent memories and knowledge storage
- 🧪 Test: Store and retrieve similar memories using vector search

---

## 1.2 Message Queue & Event Streaming

### 🎯 Context
Agents need to communicate asynchronously. Kafka provides the backbone for all inter-agent communication and event-driven architecture.

### 📦 What You're Building
Reliable message streaming infrastructure for agent communication and system events.

### 🛠️ Development Steps

#### Step 1.2.1: Kafka Topic Design (1 day)
```yaml
# kafka-topics.yml
topics:
  - name: agent-communications
    partitions: 12
    replication: 3
    config:
      retention.ms: 604800000  # 7 days
      compression.type: snappy
      
  - name: agent-executions
    partitions: 6
    replication: 3
    config:
      retention.ms: 2592000000  # 30 days
      
  - name: system-events
    partitions: 3
    replication: 3
    config:
      retention.ms: 7776000000  # 90 days
```

**Vibe Coding Context:**
- 🎯 Goal: Design message topics for different communication patterns
- 🔍 Focus: Consider message volume, retention, and partitioning strategies
- 🧪 Test: Produce and consume messages across all topics

#### Step 1.2.2: Message Schema Definition (1 day)
```python
# message_schemas.py
from dataclasses import dataclass
from typing import Dict, Any, Optional
from datetime import datetime

@dataclass
class AgentMessage:
    message_id: str
    from_agent: str
    to_agent: str
    message_type: str
    payload: Dict[str, Any]
    timestamp: datetime
    correlation_id: Optional[str] = None
    priority: int = 5  # 1=highest, 10=lowest

@dataclass
class AgentExecutionEvent:
    execution_id: str
    agent_id: str
    event_type: str  # started, completed, failed
    data: Dict[str, Any]
    timestamp: datetime
```

**Vibe Coding Context:**
- 🎯 Goal: Standardized message formats for agent communication
- 🔍 Focus: Design messages that support tracing and debugging
- 🧪 Test: Serialize/deserialize messages and validate schemas

---

## 1.3 Configuration Management

### 🎯 Context
Before building services, establish how configuration will be managed. This enables consistent configuration across all components.

### 📦 What You're Building
Centralized configuration system that supports environment-specific settings and dynamic updates.

### 🛠️ Development Steps

#### Step 1.3.1: Configuration Schema (1 day)
```python
# config_models.py
from pydantic import BaseModel
from typing import Dict, List, Optional

class DatabaseConfig(BaseModel):
    host: str
    port: int = 5432
    database: str
    username: str
    password: str
    pool_size: int = 10
    
class RedisConfig(BaseModel):
    host: str
    port: int = 6379
    password: Optional[str] = None
    db: int = 0
    
class PlatformConfig(BaseModel):
    database: DatabaseConfig
    redis: RedisConfig
    kafka_brokers: List[str]
    environment: str = "development"
    log_level: str = "INFO"
    agent_execution_timeout: int = 300
    
    @classmethod
    def load_from_env(cls) -> "PlatformConfig":
        # Load configuration from environment variables
        pass
```

**Vibe Coding Context:**
- 🎯 Goal: Type-safe configuration management
- 🔍 Focus: Support different environments and validation
- 🧪 Test: Load configs from env vars and validate all fields

---

# 🔧 Phase 2: Core Services (Weeks 5-12)

## 2.1 Authentication & Authorization Service

### 🎯 Context
Security is foundational. Build this early so all subsequent services can integrate authentication and authorization from the start.

### 📦 What You're Building
Comprehensive auth service with JWT, RBAC, and audit capabilities.

### 🛠️ Development Steps

#### Step 2.1.1: User Management Core (3 days)
```python
# auth_service/models.py
from sqlalchemy import Column, String, Boolean, DateTime, JSON
from sqlalchemy.dialects.postgresql import UUID
import uuid

class User(Base):
    __tablename__ = "users"
    
    user_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    email = Column(String(255), unique=True, nullable=False)
    password_hash = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True)
    roles = Column(JSON, default=list)
    created_at = Column(DateTime, default=datetime.utcnow)
    last_login = Column(DateTime)
    
class Role(Base):
    __tablename__ = "roles"
    
    role_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(100), unique=True, nullable=False)
    permissions = Column(JSON, default=list)
    description = Column(String(500))
```

**Vibe Coding Context:**
- 🎯 Goal: Core user and role management
- 🔍 Focus: Design for extensibility and security
- 🧪 Test: Create users, assign roles, verify permissions

#### Step 2.1.2: JWT Token Management (2 days)
```python
# auth_service/jwt_manager.py
import jwt
from datetime import datetime, timedelta
from typing import Dict, Any

class JWTManager:
    def __init__(self, secret_key: str, algorithm: str = "HS256"):
        self.secret_key = secret_key
        self.algorithm = algorithm
        
    def create_access_token(self, user_id: str, roles: List[str]) -> str:
        payload = {
            "user_id": user_id,
            "roles": roles,
            "type": "access",
            "exp": datetime.utcnow() + timedelta(hours=1),
            "iat": datetime.utcnow()
        }
        return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
    
    def create_refresh_token(self, user_id: str) -> str:
        payload = {
            "user_id": user_id,
            "type": "refresh",
            "exp": datetime.utcnow() + timedelta(days=7),
            "iat": datetime.utcnow()
        }
        return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
    
    def verify_token(self, token: str) -> Dict[str, Any]:
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            return payload
        except jwt.InvalidTokenError:
            raise AuthenticationError("Invalid token")
```

**Vibe Coding Context:**
- 🎯 Goal: Secure token generation and validation
- 🔍 Focus: Handle token expiration and refresh flows
- 🧪 Test: Generate tokens, verify them, handle expiration

#### Step 2.1.3: RBAC Permission Engine (2 days)
```python
# auth_service/permissions.py
from typing import List, Dict, Any
from enum import Enum

class ResourceAction(Enum):
    CREATE = "create"
    READ = "read"
    UPDATE = "update"
    DELETE = "delete"
    EXECUTE = "execute"

class PermissionEngine:
    def __init__(self):
        self.permission_cache = {}
        
    def check_permission(
        self, 
        user_roles: List[str], 
        resource: str, 
        action: ResourceAction,
        context: Dict[str, Any] = None
    ) -> bool:
        """
        Check if user roles have permission for action on resource
        """
        for role in user_roles:
            if self._role_has_permission(role, resource, action, context):
                return True
        return False
    
    def _role_has_permission(
        self, 
        role: str, 
        resource: str, 
        action: ResourceAction,
        context: Dict[str, Any] = None
    ) -> bool:
        # Implementation for role-based permission checking
        # Support both static permissions and dynamic context evaluation
        pass
```

**Vibe Coding Context:**
- 🎯 Goal: Flexible permission system supporting RBAC and ABAC
- 🔍 Focus: Design for both simple and complex permission scenarios
- 🧪 Test: Various permission scenarios with different roles and contexts

---

## 2.2 Agent Runtime Engine

### 🎯 Context
This is the heart of the platform. All agents will run through this engine. Build it to be robust, scalable, and debuggable.

### 📦 What You're Building
The core engine that manages agent lifecycle, execution, and state.

### 🛠️ Development Steps

#### Step 2.2.1: Agent Definition & Registry (3 days)
```python
# agent_runtime/models.py
from dataclasses import dataclass
from typing import Dict, List, Any, Optional
from enum import Enum

class AgentStatus(Enum):
    CREATED = "created"
    DEPLOYING = "deploying"
    RUNNING = "running"
    PAUSED = "paused"
    ERROR = "error"
    TERMINATED = "terminated"

@dataclass
class AgentCapability:
    name: str
    version: str
    description: str
    parameters: Dict[str, Any]

@dataclass
class AgentDefinition:
    agent_id: str
    name: str
    agent_type: str
    version: str
    capabilities: List[AgentCapability]
    configuration: Dict[str, Any]
    resources: Dict[str, Any]  # CPU, memory requirements
    dependencies: List[str]    # Other agents this depends on

class AgentRegistry:
    def __init__(self, db_session):
        self.db = db_session
        self._agent_cache = {}
    
    async def register_agent(self, definition: AgentDefinition) -> str:
        """Register a new agent definition"""
        # Validate definition
        # Store in database
        # Update cache
        # Notify discovery service
        pass
    
    async def get_agent(self, agent_id: str) -> Optional[AgentDefinition]:
        """Retrieve agent definition"""
        pass
    
    async def find_agents_by_capability(self, capability: str) -> List[AgentDefinition]:
        """Find agents that provide a specific capability"""
        pass
```

**Vibe Coding Context:**
- 🎯 Goal: Central registry for all agent definitions and capabilities
- 🔍 Focus: Design for fast lookups and capability matching
- 🧪 Test: Register agents, find by capabilities, handle updates

#### Step 2.2.2: Agent Execution Engine (4 days)
```python
# agent_runtime/executor.py
import asyncio
from typing import Dict, Any
import logging

class AgentExecutor:
    def __init__(self, agent_id: str, config: Dict[str, Any]):
        self.agent_id = agent_id
        self.config = config
        self.state = AgentState()
        self.logger = logging.getLogger(f"agent.{agent_id}")
        
    async def execute_task(self, task: AgentTask) -> AgentResult:
        """Execute a single task"""
        execution_id = self._generate_execution_id()
        
        try:
            # Log task start
            self.logger.info(f"Starting task {task.task_id}", extra={
                "execution_id": execution_id,
                "task_type": task.task_type,
                "agent_id": self.agent_id
            })
            
            # Prepare execution context
            context = await self._prepare_context(task)
            
            # Execute task logic
            result = await self._execute_task_logic(task, context)
            
            # Update agent state
            await self._update_state(task, result)
            
            # Log success
            self.logger.info(f"Task completed", extra={
                "execution_id": execution_id,
                "duration": result.duration,
                "success": True
            })
            
            return result
            
        except Exception as e:
            # Log error
            self.logger.error(f"Task failed: {str(e)}", extra={
                "execution_id": execution_id,
                "error": str(e),
                "success": False
            })
            
            # Handle error recovery
            await self._handle_error(task, e)
            raise
    
    async def _execute_task_logic(self, task: AgentTask, context: Dict[str, Any]) -> AgentResult:
        """Core task execution logic - to be implemented by specific agent types"""
        raise NotImplementedError("Subclasses must implement task execution logic")
```

**Vibe Coding Context:**
- 🎯 Goal: Robust execution engine with error handling and logging
- 🔍 Focus: Design for observability and debugging
- 🧪 Test: Execute tasks, handle errors, verify state management

#### Step 2.2.3: Agent State Management (2 days)
```python
# agent_runtime/state_manager.py
import asyncio
import json
from typing import Dict, Any, Optional

class AgentStateManager:
    def __init__(self, redis_client, postgres_client):
        self.redis = redis_client
        self.postgres = postgres_client
        
    async def save_state(self, agent_id: str, state: Dict[str, Any]) -> None:
        """Save agent state to both cache and persistent storage"""
        # Save to Redis for fast access
        await self.redis.setex(
            f"agent:state:{agent_id}",
            3600,  # 1 hour TTL
            json.dumps(state)
        )
        
        # Save to PostgreSQL for persistence
        await self.postgres.execute(
            """
            INSERT INTO agent_states (agent_id, state_data, updated_at)
            VALUES ($1, $2, NOW())
            ON CONFLICT (agent_id) 
            DO UPDATE SET state_data = $2, updated_at = NOW()
            """,
            agent_id, json.dumps(state)
        )
    
    async def load_state(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """Load agent state, trying cache first"""
        # Try Redis first
        cached_state = await self.redis.get(f"agent:state:{agent_id}")
        if cached_state:
            return json.loads(cached_state)
        
        # Fall back to PostgreSQL
        row = await self.postgres.fetchrow(
            "SELECT state_data FROM agent_states WHERE agent_id = $1",
            agent_id
        )
        if row:
            state = json.loads(row['state_data'])
            # Refresh cache
            await self.redis.setex(
                f"agent:state:{agent_id}",
                3600,
                json.dumps(state)
            )
            return state
        
        return None
```

**Vibe Coding Context:**
- 🎯 Goal: Fast and reliable state persistence
- 🔍 Focus: Cache-first strategy with persistent backup
- 🧪 Test: Save/load state, verify cache behavior, test failover

---

## 2.3 Basic Agent Implementation

### 🎯 Context
Create a simple agent implementation to validate the runtime engine and provide a foundation for more complex agents.

### 📦 What You're Building
A basic "Hello World" agent that demonstrates the execution framework.

### 🛠️ Development Steps

#### Step 2.3.1: Base Agent Class (2 days)
```python
# agents/base_agent.py
from abc import ABC, abstractmethod
from typing import Dict, Any, List
import asyncio

class BaseAgent(ABC):
    def __init__(self, agent_id: str, config: Dict[str, Any]):
        self.agent_id = agent_id
        self.config = config
        self.capabilities = self._define_capabilities()
        self.memory = AgentMemory(agent_id)
        
    @abstractmethod
    def _define_capabilities(self) -> List[AgentCapability]:
        """Define what this agent can do"""
        pass
    
    @abstractmethod
    async def execute_task(self, task: AgentTask) -> AgentResult:
        """Execute a task assigned to this agent"""
        pass
    
    async def get_capability_info(self, capability_name: str) -> Dict[str, Any]:
        """Get information about a specific capability"""
        for cap in self.capabilities:
            if cap.name == capability_name:
                return {
                    "name": cap.name,
                    "version": cap.version,
                    "description": cap.description,
                    "parameters": cap.parameters
                }
        return None
    
    async def health_check(self) -> Dict[str, Any]:
        """Return agent health status"""
        return {
            "agent_id": self.agent_id,
            "status": "healthy",
            "capabilities": [cap.name for cap in self.capabilities],
            "memory_usage": await self.memory.get_usage_stats(),
            "last_activity": self.get_last_activity_time()
        }
```

**Vibe Coding Context:**
- 🎯 Goal: Abstract base class that all agents inherit from
- 🔍 Focus: Common functionality that every agent needs
- 🧪 Test: Subclass and implement a simple agent

#### Step 2.3.2: Echo Agent Implementation (1 day)
```python
# agents/echo_agent.py
class EchoAgent(BaseAgent):
    def _define_capabilities(self) -> List[AgentCapability]:
        return [
            AgentCapability(
                name="echo",
                version="1.0.0",
                description="Echoes back the input message",
                parameters={
                    "message": {"type": "string", "required": True}
                }
            )
        ]
    
    async def execute_task(self, task: AgentTask) -> AgentResult:
        if task.task_type == "echo":
            message = task.parameters.get("message", "")
            response = f"Echo from {self.agent_id}: {message}"
            
            # Store in memory for learning
            await self.memory.store_experience({
                "task_type": "echo",
                "input": message,
                "output": response,
                "timestamp": datetime.utcnow()
            })
            
            return AgentResult(
                task_id=task.task_id,
                success=True,
                output={"response": response},
                duration=0.001
            )
        else:
            raise ValueError(f"Unknown task type: {task.task_type}")
```

**Vibe Coding Context:**
- 🎯 Goal: Simple agent to validate the entire execution framework
- 🔍 Focus: Test task execution, memory storage, result formatting
- 🧪 Test: Send echo tasks, verify responses, check memory storage

---

# 🔗 Phase 3: Integration Layer (Weeks 13-20)

## 3.1 A2A Protocol Implementation

### 🎯 Context
Agents need to communicate with each other. This protocol enables that communication in a standardized way.

### 📦 What You're Building
A communication protocol that allows agents to discover, negotiate with, and collaborate with other agents.

### 🛠️ Development Steps

#### Step 3.1.1: Message Protocol Definition (2 days)
```python
# a2a_protocol/messages.py
from dataclasses import dataclass
from typing import Dict, Any, Optional, List
from enum import Enum

class MessageType(Enum):
    DISCOVERY_REQUEST = "discovery_request"
    DISCOVERY_RESPONSE = "discovery_response"
    TASK_REQUEST = "task_request"
    TASK_RESPONSE = "task_response"
    NEGOTIATION = "negotiation"
    HEARTBEAT = "heartbeat"
    ERROR = "error"

@dataclass
class A2AMessage:
    message_id: str
    message_type: MessageType
    from_agent: str
    to_agent: str
    payload: Dict[str, Any]
    correlation_id: Optional[str] = None
    timestamp: datetime = None
    priority: int = 5
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()

@dataclass
class DiscoveryRequest:
    requested_capability: str
    requirements: Dict[str, Any]
    max_results: int = 10

@dataclass
class TaskRequest:
    task_type: str
    parameters: Dict[str, Any]
    deadline: Optional[datetime] = None
    priority: int = 5
```

**Vibe Coding Context:**
- 🎯 Goal: Standardized message format for agent communication
- 🔍 Focus: Support different communication patterns
- 🧪 Test: Create messages, serialize/deserialize, validate formats

#### Step 3.1.2: Message Router (3 days)
```python
# a2a_protocol/router.py
import asyncio
from typing import Dict, Callable, List

class A2AMessageRouter:
    def __init__(self, kafka_producer, kafka_consumer):
        self.producer = kafka_producer
        self.consumer = kafka_consumer
        self.handlers: Dict[MessageType, Callable] = {}
        self.agent_registry = {}
        
    async def register_agent(self, agent_id: str, capabilities: List[str]) -> None:
        """Register an agent and its capabilities"""
        self.agent_registry[agent_id] = {
            "capabilities": capabilities,
            "last_seen": datetime.utcnow(),
            "status": "online"
        }
        
        # Subscribe to messages for this agent
        await self.consumer.subscribe([f"agent.{agent_id}"])
    
    async def send_message(self, message: A2AMessage) -> None:
        """Send a message to another agent"""
        # Validate recipient exists
        if message.to_agent not in self.agent_registry:
            raise ValueError(f"Unknown recipient agent: {message.to_agent}")
        
        # Route message to appropriate topic
        topic = f"agent.{message.to_agent}"
        
        await self.producer.send(
            topic,
            key=message.message_id,
            value=message.to_dict()
        )
    
    async def discover_agents(self, capability: str) -> List[str]:
        """Find agents that provide a specific capability"""
        matching_agents = []
        for agent_id, info in self.agent_registry.items():
            if capability in info["capabilities"]:
                matching_agents.append(agent_id)
        return matching_agents
    
    def register_handler(self, message_type: MessageType, handler: Callable):
        """Register a handler for a specific message type"""
        self.handlers[message_type] = handler
    
    async def start_listening(self):
        """Start listening for incoming messages"""
        async for message in self.consumer:
            try:
                a2a_message = A2AMessage.from_dict(message.value)
                handler = self.handlers.get(a2a_message.message_type)
                if handler:
                    await handler(a2a_message)
                else:
                    print(f"No handler for message type: {a2a_message.message_type}")
            except Exception as e:
                print(f"Error processing message: {e}")
```

**Vibe Coding Context:**
- 🎯 Goal: Reliable message routing between agents
- 🔍 Focus: Handle agent discovery, message delivery, error cases
- 🧪 Test: Register agents, send messages, verify delivery

#### Step 3.1.3: Agent Communication Client (2 days)
```python
# a2a_protocol/client.py
class A2AClient:
    def __init__(self, agent_id: str, router: A2AMessageRouter):
        self.agent_id = agent_id
        self.router = router
        self.pending_requests = {}
        
    async def discover_capability(self, capability: str) -> List[str]:
        """Discover agents that provide a capability"""
        return await self.router.discover_agents(capability)
    
    async def send_task_request(
        self, 
        target_agent: str, 
        task_type: str, 
        parameters: Dict[str, Any],
        timeout: int = 30
    ) -> Dict[str, Any]:
        """Send a task request and wait for response"""
        
        message_id = self._generate_message_id()
        request = A2AMessage(
            message_id=message_id,
            message_type=MessageType.TASK_REQUEST,
            from_agent=self.agent_id,
            to_agent=target_agent,
            payload={
                "task_type": task_type,
                "parameters": parameters
            }
        )
        
        # Create future for response
        response_future = asyncio.Future()
        self.pending_requests[message_id] = response_future
        
        # Send request
        await self.router.send_message(request)
        
        # Wait for response or timeout
        try:
            response = await asyncio.wait_for(response_future, timeout=timeout)
            return response
        except asyncio.TimeoutError:
            del self.pending_requests[message_id]
            raise TimeoutError(f"Task request to {target_agent} timed out")
    
    async def handle_task_response(self, message: A2AMessage):
        """Handle incoming task response"""
        correlation_id = message.correlation_id
        if correlation_id in self.pending_requests:
            future = self.pending_requests.pop(correlation_id)
            future.set_result(message.payload)
```

**Vibe Coding Context:**
- 🎯 Goal: Simple client interface for agent communication
- 🔍 Focus: Handle async request/response patterns
- 🧪 Test: Send requests, receive responses, handle timeouts

---

## 3.2 Multi-Agent Orchestrator

### 🎯 Context
When tasks are too complex for a single agent, the orchestrator decomposes them and coordinates multiple agents.

### 📦 What You're Building
An intelligent system that can break down complex tasks and coordinate multiple agents to complete them.

### 🛠️ Development Steps

#### Step 3.2.1: Task Decomposition Engine (4 days)
```python
# orchestration/task_decomposer.py
from typing import List, Dict, Any
from dataclasses import dataclass

@dataclass
class SubTask:
    task_id: str
    task_type: str
    required_capability: str
    parameters: Dict[str, Any]
    dependencies: List[str]  # Other subtask IDs this depends on
    priority: int = 5

@dataclass
class ExecutionPlan:
    plan_id: str
    original_task: str
    subtasks: List[SubTask]
    execution_order: List[List[str]]  # Execution phases
    estimated_duration: int
    required_agents: List[str]

class TaskDecomposer:
    def __init__(self, agent_registry, capability_knowledge):
        self.agent_registry = agent_registry
        self.capability_knowledge = capability_knowledge
        
    async def decompose_task(self, task_description: str, context: Dict[str, Any]) -> ExecutionPlan:
        """
        Analyze a complex task and break it into subtasks
        """
        # Use AI to understand the task
        task_analysis = await self._analyze_task_with_ai(task_description, context)
        
        # Identify required capabilities
        required_capabilities = self._extract_capabilities(task_analysis)
        
        # Create subtasks
        subtasks = []
        for capability in required_capabilities:
            subtask = SubTask(
                task_id=self._generate_task_id(),
                task_type=capability["type"],
                required_capability=capability["name"],
                parameters=capability["parameters"],
                dependencies=capability.get("dependencies", [])
            )
            subtasks.append(subtask)
        
        # Determine execution order
        execution_order = self._calculate_execution_order(subtasks)
        
        # Find available agents
        required_agents = await self._find_suitable_agents(required_capabilities)
        
        return ExecutionPlan(
            plan_id=self._generate_plan_id(),
            original_task=task_description,
            subtasks=subtasks,
            execution_order=execution_order,
            estimated_duration=self._estimate_duration(subtasks),
            required_agents=required_agents
        )
    
    async def _analyze_task_with_ai(self, task: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Use AI to understand task requirements"""
        # This would integrate with the Intelligence Layer
        # For now, return a mock analysis
        return {
            "task_type": "complex_analysis",
            "required_capabilities": ["data_analysis", "report_generation"],
            "estimated_complexity": "medium"
        }
```

**Vibe Coding Context:**
- 🎯 Goal: Intelligent task breakdown that considers dependencies
- 🔍 Focus: Create execution plans that can be monitored and adjusted
- 🧪 Test: Decompose various task types, verify execution order logic

#### Step 3.2.2: Agent Coordinator (3 days)
```python
# orchestration/coordinator.py
import asyncio
from typing import Dict, List, Any

class AgentCoordinator:
    def __init__(self, a2a_client: A2AClient, task_decomposer: TaskDecomposer):
        self.a2a_client = a2a_client
        self.task_decomposer = task_decomposer
        self.active_executions = {}
        
    async def execute_plan(self, plan: ExecutionPlan) -> Dict[str, Any]:
        """Execute a multi-agent plan"""
        execution_id = self._generate_execution_id()
        
        self.active_executions[execution_id] = {
            "plan": plan,
            "status": "running",
            "completed_subtasks": set(),
            "failed_subtasks": set(),
            "results": {}
        }
        
        try:
            # Execute phases in order
            for phase in plan.execution_order:
                await self._execute_phase(execution_id, phase)
            
            # Combine results
            final_result = await self._combine_results(execution_id)
            
            self.active_executions[execution_id]["status"] = "completed"
            return final_result
            
        except Exception as e:
            self.active_executions[execution_id]["status"] = "failed"
            self.active_executions[execution_id]["error"] = str(e)
            raise
    
    async def _execute_phase(self, execution_id: str, subtask_ids: List[str]) -> None:
        """Execute a phase of subtasks in parallel"""
        execution = self.active_executions[execution_id]
        plan = execution["plan"]
        
        # Get subtasks for this phase
        subtasks = [st for st in plan.subtasks if st.task_id in subtask_ids]
        
        # Execute all subtasks in parallel
        tasks = []
        for subtask in subtasks:
            # Find agent to execute this subtask
            agent = await self._select_agent_for_subtask(subtask)
            
            # Create execution task
            task = self._execute_subtask(execution_id, subtask, agent)
            tasks.append(task)
        
        # Wait for all subtasks to complete
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        for i, result in enumerate(results):
            subtask = subtasks[i]
            if isinstance(result, Exception):
                execution["failed_subtasks"].add(subtask.task_id)
                execution["results"][subtask.task_id] = {"error": str(result)}
            else:
                execution["completed_subtasks"].add(subtask.task_id)
                execution["results"][subtask.task_id] = result
    
    async def _execute_subtask(self, execution_id: str, subtask: SubTask, agent_id: str) -> Dict[str, Any]:
        """Execute a single subtask on a specific agent"""
        try:
            result = await self.a2a_client.send_task_request(
                target_agent=agent_id,
                task_type=subtask.task_type,
                parameters=subtask.parameters
            )
            return result
        except Exception as e:
            # Log error and potentially retry or reassign
            print(f"Subtask {subtask.task_id} failed on agent {agent_id}: {e}")
            raise
```

**Vibe Coding Context:**
- 🎯 Goal: Coordinate multiple agents to complete complex tasks
- 🔍 Focus: Handle parallel execution, failures, and result aggregation
- 🧪 Test: Execute multi-phase plans, verify parallel execution, test error handling

---

## 3.3 Intelligence Layer Foundation

### 🎯 Context
Agents need AI capabilities. Start with a simple LLM integration that can be extended later.

### 📦 What You're Building
Basic AI services that agents can use for text generation, analysis, and decision making.

### 🛠️ Development Steps

#### Step 3.3.1: LLM Gateway (3 days)
```python
# intelligence/llm_gateway.py
import asyncio
import aiohttp
from typing import Dict, Any, List, Optional
from abc import ABC, abstractmethod

class LLMProvider(ABC):
    @abstractmethod
    async def generate_text(self, prompt: str, **kwargs) -> str:
        pass
    
    @abstractmethod
    async def get_cost_estimate(self, prompt: str, **kwargs) -> float:
        pass

class OpenAIProvider(LLMProvider):
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.openai.com/v1"
        
    async def generate_text(self, prompt: str, model: str = "gpt-3.5-turbo", **kwargs) -> str:
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": model,
            "messages": [{"role": "user", "content": prompt}],
            "max_tokens": kwargs.get("max_tokens", 1000),
            "temperature": kwargs.get("temperature", 0.7)
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=data
            ) as response:
                result = await response.json()
                return result["choices"][0]["message"]["content"]
    
    async def get_cost_estimate(self, prompt: str, model: str = "gpt-3.5-turbo", **kwargs) -> float:
        # Rough cost estimation based on token count
        token_count = len(prompt.split()) * 1.3  # Rough approximation
        
        cost_per_1k_tokens = {
            "gpt-3.5-turbo": 0.002,
            "gpt-4": 0.03
        }
        
        return (token_count / 1000) * cost_per_1k_tokens.get(model, 0.002)

class LLMGateway:
    def __init__(self):
        self.providers: Dict[str, LLMProvider] = {}
        self.cost_tracker = CostTracker()
        
    def register_provider(self, name: str, provider: LLMProvider):
        self.providers[name] = provider
    
    async def generate_text(
        self, 
        prompt: str, 
        provider: str = "openai",
        model: str = "gpt-3.5-turbo",
        **kwargs
    ) -> Dict[str, Any]:
        """Generate text using specified provider"""
        if provider not in self.providers:
            raise ValueError(f"Unknown provider: {provider}")
        
        llm_provider = self.providers[provider]
        
        # Get cost estimate
        estimated_cost = await llm_provider.get_cost_estimate(prompt, model=model, **kwargs)
        
        # Generate text
        start_time = asyncio.get_event_loop().time()
        result = await llm_provider.generate_text(prompt, model=model, **kwargs)
        duration = asyncio.get_event_loop().time() - start_time
        
        # Track usage
        await self.cost_tracker.record_usage(
            provider=provider,
            model=model,
            cost=estimated_cost,
            duration=duration
        )
        
        return {
            "text": result,
            "cost": estimated_cost,
            "duration": duration,
            "provider": provider,
            "model": model
        }
```

**Vibe Coding Context:**
- 🎯 Goal: Abstracted LLM access with cost tracking
- 🔍 Focus: Support multiple providers and cost optimization
- 🧪 Test: Generate text with different providers, verify cost tracking

#### Step 3.3.2: Simple Reasoning Agent (2 days)
```python
# agents/reasoning_agent.py
class ReasoningAgent(BaseAgent):
    def __init__(self, agent_id: str, config: Dict[str, Any]):
        super().__init__(agent_id, config)
        self.llm_gateway = LLMGateway()
        
    def _define_capabilities(self) -> List[AgentCapability]:
        return [
            AgentCapability(
                name="analyze_text",
                version="1.0.0",
                description="Analyze text and provide insights",
                parameters={
                    "text": {"type": "string", "required": True},
                    "analysis_type": {"type": "string", "required": False}
                }
            ),
            AgentCapability(
                name="generate_response",
                version="1.0.0",
                description="Generate contextual responses",
                parameters={
                    "context": {"type": "string", "required": True},
                    "query": {"type": "string", "required": True}
                }
            )
        ]
    
    async def execute_task(self, task: AgentTask) -> AgentResult:
        if task.task_type == "analyze_text":
            return await self._analyze_text(task)
        elif task.task_type == "generate_response":
            return await self._generate_response(task)
        else:
            raise ValueError(f"Unknown task type: {task.task_type}")
    
    async def _analyze_text(self, task: AgentTask) -> AgentResult:
        text = task.parameters["text"]
        analysis_type = task.parameters.get("analysis_type", "general")
        
        prompt = f"""
        Analyze the following text for {analysis_type} insights:
        
        Text: {text}
        
        Provide a structured analysis including:
        1. Key themes
        2. Sentiment
        3. Important entities
        4. Summary
        """
        
        llm_result = await self.llm_gateway.generate_text(prompt)
        
        return AgentResult(
            task_id=task.task_id,
            success=True,
            output={
                "analysis": llm_result["text"],
                "cost": llm_result["cost"],
                "processing_time": llm_result["duration"]
            },
            duration=llm_result["duration"]
        )
```

**Vibe Coding Context:**
- 🎯 Goal: Practical agent that uses AI capabilities
- 🔍 Focus: Show how agents integrate with intelligence services
- 🧪 Test: Analyze different types of text, verify response quality

---

# 🎨 Phase 4: User Experience Layer (Weeks 21-28)

## 4.1 REST API Layer

### 🎯 Context
All platform functionality needs to be accessible via clean, well-documented APIs.

### 📦 What You're Building
Comprehensive REST API that exposes all platform capabilities.

### 🛠️ Development Steps

#### Step 4.1.1: API Foundation (2 days)
```python
# api/main.py
from fastapi import FastAPI, Depends, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
import uvicorn

app = FastAPI(
    title="AI Agent Platform API",
    description="Comprehensive API for managing and interacting with AI agents",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security
security = HTTPBearer()

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Validate JWT token and return user info"""
    try:
        payload = jwt_manager.verify_token(credentials.credentials)
        return payload
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials"
        )

# Health check
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow(),
        "version": "1.0.0"
    }

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
```

**Vibe Coding Context:**
- 🎯 Goal: Solid API foundation with security and documentation
- 🔍 Focus: Structure that can scale as you add endpoints
- 🧪 Test: API starts, health check works, authentication is enforced

#### Step 4.1.2: Agent Management Endpoints (3 days)
```python
# api/agents.py
from fastapi import APIRouter, Depends, HTTPException
from typing import List, Optional
from pydantic import BaseModel

router = APIRouter(prefix="/api/v1/agents", tags=["agents"])

class AgentCreateRequest(BaseModel):
    name: str
    agent_type: str
    description: Optional[str] = None
    configuration: Dict[str, Any] = {}
    capabilities: List[str] = []

class AgentResponse(BaseModel):
    agent_id: str
    name: str
    agent_type: str
    status: str
    capabilities: List[str]
    created_at: datetime
    updated_at: datetime

class TaskExecutionRequest(BaseModel):
    task_type: str
    parameters: Dict[str, Any]
    priority: int = 5

@router.post("/", response_model=AgentResponse)
async def create_agent(
    request: AgentCreateRequest,
    current_user = Depends(get_current_user),
    agent_service: AgentService = Depends(get_agent_service)
):
    """Create a new agent"""
    try:
        agent = await agent_service.create_agent(
            name=request.name,
            agent_type=request.agent_type,
            description=request.description,
            configuration=request.configuration,
            capabilities=request.capabilities,
            created_by=current_user["user_id"]
        )
        return AgentResponse.from_orm(agent)
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/", response_model=List[AgentResponse])
async def list_agents(
    agent_type: Optional[str] = None,
    status: Optional[str] = None,
    limit: int = 50,
    offset: int = 0,
    current_user = Depends(get_current_user),
    agent_service: AgentService = Depends(get_agent_service)
):
    """List agents with optional filtering"""
    agents = await agent_service.list_agents(
        agent_type=agent_type,
        status=status,
        limit=limit,
        offset=offset,
        user_id=current_user["user_id"]
    )
    return [AgentResponse.from_orm(agent) for agent in agents]

@router.get("/{agent_id}", response_model=AgentResponse)
async def get_agent(
    agent_id: str,
    current_user = Depends(get_current_user),
    agent_service: AgentService = Depends(get_agent_service)
):
    """Get agent details"""
    agent = await agent_service.get_agent(agent_id, current_user["user_id"])
    if not agent:
        raise HTTPException(status_code=404, detail="Agent not found")
    return AgentResponse.from_orm(agent)

@router.post("/{agent_id}/execute")
async def execute_task(
    agent_id: str,
    request: TaskExecutionRequest,
    current_user = Depends(get_current_user),
    agent_service: AgentService = Depends(get_agent_service)
):
    """Execute a task on an agent"""
    try:
        result = await agent_service.execute_task(
            agent_id=agent_id,
            task_type=request.task_type,
            parameters=request.parameters,
            priority=request.priority,
            user_id=current_user["user_id"]
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.delete("/{agent_id}")
async def delete_agent(
    agent_id: str,
    current_user = Depends(get_current_user),
    agent_service: AgentService = Depends(get_agent_service)
):
    """Delete an agent"""
    success = await agent_service.delete_agent(agent_id, current_user["user_id"])
    if not success:
        raise HTTPException(status_code=404, detail="Agent not found")
    return {"message": "Agent deleted successfully"}
```

**Vibe Coding Context:**
- 🎯 Goal: Complete CRUD operations for agent management
- 🔍 Focus: Clean request/response models and proper error handling
- 🧪 Test: Create agents, list them, execute tasks, verify permissions

#### Step 4.1.3: Orchestration Endpoints (2 days)
```python
# api/orchestration.py
from fastapi import APIRouter, Depends
from pydantic import BaseModel

router = APIRouter(prefix="/api/v1/orchestration", tags=["orchestration"])

class OrchestrationRequest(BaseModel):
    task_description: str
    context: Dict[str, Any] = {}
    priority: int = 5
    deadline: Optional[datetime] = None

class OrchestrationResponse(BaseModel):
    execution_id: str
    status: str
    plan: Dict[str, Any]
    estimated_duration: int
    progress: Dict[str, Any]

@router.post("/execute", response_model=OrchestrationResponse)
async def execute_complex_task(
    request: OrchestrationRequest,
    current_user = Depends(get_current_user),
    orchestrator: AgentCoordinator = Depends(get_orchestrator)
):
    """Execute a complex task requiring multiple agents"""
    
    # Decompose task
    decomposer = TaskDecomposer(agent_registry, capability_knowledge)
    plan = await decomposer.decompose_task(
        request.task_description, 
        request.context
    )
    
    # Start execution
    execution_id = await orchestrator.start_execution(plan)
    
    return OrchestrationResponse(
        execution_id=execution_id,
        status="running",
        plan=plan.to_dict(),
        estimated_duration=plan.estimated_duration,
        progress={"completed": 0, "total": len(plan.subtasks)}
    )

@router.get("/execution/{execution_id}", response_model=OrchestrationResponse)
async def get_execution_status(
    execution_id: str,
    current_user = Depends(get_current_user),
    orchestrator: AgentCoordinator = Depends(get_orchestrator)
):
    """Get status of an orchestration execution"""
    
    execution = await orchestrator.get_execution_status(execution_id)
    if not execution:
        raise HTTPException(status_code=404, detail="Execution not found")
    
    return OrchestrationResponse(
        execution_id=execution_id,
        status=execution["status"],
        plan=execution["plan"],
        estimated_duration=execution["estimated_duration"],
        progress=execution["progress"]
    )
```

**Vibe Coding Context:**
- 🎯 Goal: Expose orchestration capabilities via API
- 🔍 Focus: Async operations with status tracking
- 🧪 Test: Submit complex tasks, check status, verify execution

---

## 4.2 Basic Web Interface

### 🎯 Context
Provide a simple web interface to validate the APIs and give users a way to interact with the platform.

### 📦 What You're Building
Basic React application for agent management and monitoring.

### 🛠️ Development Steps

#### Step 4.2.1: React Foundation (2 days)
```typescript
// frontend/src/App.tsx
import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AuthProvider } from './contexts/AuthContext';
import { Layout } from './components/Layout';
import { AgentList } from './pages/AgentList';
import { AgentDetail } from './pages/AgentDetail';
import { Login } from './pages/Login';

const queryClient = new QueryClient();

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <Router>
          <Layout>
            <Routes>
              <Route path="/login" element={<Login />} />
              <Route path="/agents" element={<AgentList />} />
              <Route path="/agents/:agentId" element={<AgentDetail />} />
              <Route path="/" element={<AgentList />} />
            </Routes>
          </Layout>
        </Router>
      </AuthProvider>
    </QueryClientProvider>
  );
}

export default App;
```

**Vibe Coding Context:**
- 🎯 Goal: Modern React app with routing and state management
- 🔍 Focus: Clean architecture that can scale
- 🧪 Test: App loads, routing works, no console errors

#### Step 4.2.2: Agent Management Interface (3 days)
```typescript
// frontend/src/pages/AgentList.tsx
import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { agentService } from '../services/agentService';

interface Agent {
  agent_id: string;
  name: string;
  agent_type: string;
  status: string;
  capabilities: string[];
  created_at: string;
}

export const AgentList: React.FC = () => {
  const [filterType, setFilterType] = useState<string>('');
  const queryClient = useQueryClient();

  const { data: agents, isLoading, error } = useQuery({
    queryKey: ['agents', filterType],
    queryFn: () => agentService.listAgents({ agent_type: filterType || undefined })
  });

  const deleteAgentMutation = useMutation({
    mutationFn: agentService.deleteAgent,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['agents'] });
    }
  });

  const handleDeleteAgent = async (agentId: string) => {
    if (window.confirm('Are you sure you want to delete this agent?')) {
      await deleteAgentMutation.mutateAsync(agentId);
    }
  };

  if (isLoading) return <div className="loading">Loading agents...</div>;
  if (error) return <div className="error">Error loading agents</div>;

  return (
    <div className="agent-list">
      <div className="header">
        <h1>Agents</h1>
        <button 
          className="btn btn-primary"
          onClick={() => {/* Navigate to create agent */}}
        >
          Create Agent
        </button>
      </div>

      <div className="filters">
        <select 
          value={filterType} 
          onChange={(e) => setFilterType(e.target.value)}
        >
          <option value="">All Types</option>
          <option value="reasoning">Reasoning Agent</option>
          <option value="echo">Echo Agent</option>
        </select>
      </div>

      <div className="agent-grid">
        {agents?.map((agent: Agent) => (
          <div key={agent.agent_id} className="agent-card">
            <div className="card-header">
              <h3>{agent.name}</h3>
              <span className={`status status-${agent.status}`}>
                {agent.status}
              </span>
            </div>
            
            <div className="card-body">
              <p>Type: {agent.agent_type}</p>
              <p>Capabilities: {agent.capabilities.join(', ')}</p>
              <p>Created: {new Date(agent.created_at).toLocaleDateString()}</p>
            </div>
            
            <div className="card-actions">
              <button 
                className="btn btn-secondary"
                onClick={() => {/* Navigate to agent detail */}}
              >
                View Details
              </button>
              <button 
                className="btn btn-danger"
                onClick={() => handleDeleteAgent(agent.agent_id)}
                disabled={deleteAgentMutation.isPending}
              >
                Delete
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
```

**Vibe Coding Context:**
- 🎯 Goal: Functional agent management interface
- 🔍 Focus: Real data integration with proper loading/error states
- 🧪 Test: List agents, filter by type, delete agents

#### Step 4.2.3: Agent Services (1 day)
```typescript
// frontend/src/services/agentService.ts
import { apiClient } from './apiClient';

export interface CreateAgentRequest {
  name: string;
  agent_type: string;
  description?: string;
  configuration?: Record<string, any>;
  capabilities?: string[];
}

export interface Agent {
  agent_id: string;
  name: string;
  agent_type: string;
  status: string;
  capabilities: string[];
  created_at: string;
  updated_at: string;
}

export interface TaskExecutionRequest {
  task_type: string;
  parameters: Record<string, any>;
  priority?: number;
}

export const agentService = {
  async listAgents(filters?: { agent_type?: string }): Promise<Agent[]> {
    const params = new URLSearchParams();
    if (filters?.agent_type) {
      params.append('agent_type', filters.agent_type);
    }
    
    const response = await apiClient.get(`/agents?${params.toString()}`);
    return response.data;
  },

  async getAgent(agentId: string): Promise<Agent> {
    const response = await apiClient.get(`/agents/${agentId}`);
    return response.data;
  },

  async createAgent(request: CreateAgentRequest): Promise<Agent> {
    const response = await apiClient.post('/agents', request);
    return response.data;
  },

  async deleteAgent(agentId: string): Promise<void> {
    await apiClient.delete(`/agents/${agentId}`);
  },

  async executeTask(agentId: string, request: TaskExecutionRequest): Promise<any> {
    const response = await apiClient.post(`/agents/${agentId}/execute`, request);
    return response.data;
  }
};
```

**Vibe Coding Context:**
- 🎯 Goal: Clean service layer that wraps API calls
- 🔍 Focus: Type safety and error handling
- 🧪 Test: All service methods work with real API

---

# 🔧 Phase 5: Platform Services (Weeks 29-32)

## 5.1 Basic Monitoring & Logging

### 🎯 Context
Before going to production, you need observability. Start with basic monitoring that can be enhanced later.

### 📦 What You're Building
Essential monitoring and logging infrastructure.

### 🛠️ Development Steps

#### Step 5.1.1: Structured Logging (2 days)
```python
# monitoring/logger.py
import logging
import json
import sys
from datetime import datetime
from typing import Dict, Any, Optional
import asyncio
from contextvars import ContextVar

# Context variables for request tracing
request_id_var: ContextVar[Optional[str]] = ContextVar('request_id', default=None)
user_id_var: ContextVar[Optional[str]] = ContextVar('user_id', default=None)

class StructuredFormatter(logging.Formatter):
    def format(self, record):
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": record.levelname,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
            "request_id": request_id_var.get(),
            "user_id": user_id_var.get()
        }
        
        # Add extra fields if present
        if hasattr(record, 'extra'):
            log_entry.update(record.extra)
        
        # Add exception info if present
        if record.exc_info:
            log_entry["exception"] = self.formatException(record.exc_info)
        
        return json.dumps(log_entry)

def setup_logging(service_name: str, log_level: str = "INFO"):
    """Setup structured logging for a service"""
    
    # Create logger
    logger = logging.getLogger(service_name)
    logger.setLevel(getattr(logging, log_level.upper()))
    
    # Create handler
    handler = logging.StreamHandler(sys.stdout)
    handler.setFormatter(StructuredFormatter())
    
    # Add handler to logger
    logger.addHandler(handler)
    
    return logger

class AgentLogger:
    def __init__(self, agent_id: str):
        self.agent_id = agent_id
        self.logger = logging.getLogger(f"agent.{agent_id}")
    
    def log_execution_start(self, task_id: str, task_type: str):
        self.logger.info("Task execution started", extra={
            "agent_id": self.agent_id,
            "task_id": task_id,
            "task_type": task_type,
            "event_type": "execution_start"
        })
    
    def log_execution_complete(self, task_id: str, duration: float, success: bool):
        self.logger.info("Task execution completed", extra={
            "agent_id": self.agent_id,
            "task_id": task_id,
            "duration": duration,
            "success": success,
            "event_type": "execution_complete"
        })
    
    def log_error(self, task_id: str, error: str, error_type: str):
        self.logger.error("Task execution failed", extra={
            "agent_id": self.agent_id,
            "task_id": task_id,
            "error": error,
            "error_type": error_type,
            "event_type": "execution_error"
        })
```

**Vibe Coding Context:**
- 🎯 Goal: Consistent, searchable logging across all services
- 🔍 Focus: Structured JSON logs with correlation IDs
- 🧪 Test: Log different event types, verify JSON format, test correlation

#### Step 5.1.2: Basic Metrics Collection (2 days)
```python
# monitoring/metrics.py
import time
import asyncio
from typing import Dict, Any, Optional
from prometheus_client import Counter, Histogram, Gauge, CollectorRegistry, REGISTRY
import psutil

class PlatformMetrics:
    def __init__(self):
        # Agent execution metrics
        self.agent_executions = Counter(
            'agent_executions_total',
            'Total number of agent executions',
            ['agent_id', 'agent_type', 'status']
        )
        
        self.agent_execution_duration = Histogram(
            'agent_execution_duration_seconds',
            'Time spent executing agent tasks',
            ['agent_id', 'agent_type'],
            buckets=[0.1, 0.5, 1.0, 2.5, 5.0, 10.0, 30.0, 60.0]
        )
        
        self.active_agents = Gauge(
            'active_agents_total',
            'Number of currently active agents',
            ['agent_type']
        )
        
        # API metrics
        self.api_requests = Counter(
            'api_requests_total',
            'Total number of API requests',
            ['method', 'endpoint', 'status_code']
        )
        
        self.api_request_duration = Histogram(
            'api_request_duration_seconds',
            'Time spent processing API requests',
            ['method', 'endpoint'],
            buckets=[0.005, 0.01, 0.025, 0.05, 0.075, 0.1, 0.25, 0.5, 0.75, 1.0, 2.5, 5.0]
        )
        
        # System metrics
        self.system_cpu_usage = Gauge('system_cpu_usage_percent', 'System CPU usage percentage')
        self.system_memory_usage = Gauge('system_memory_usage_percent', 'System memory usage percentage')
        self.system_disk_usage = Gauge('system_disk_usage_percent', 'System disk usage percentage')
    
    def record_agent_execution(self, agent_id: str, agent_type: str, duration: float, success: bool):
        """Record agent execution metrics"""
        status = 'success' if success else 'failure'
        
        self.agent_executions.labels(
            agent_id=agent_id,
            agent_type=agent_type,
            status=status
        ).inc()
        
        self.agent_execution_duration.labels(
            agent_id=agent_id,
            agent_type=agent_type
        ).observe(duration)
    
    def record_api_request(self, method: str, endpoint: str, status_code: int, duration: float):
        """Record API request metrics"""
        self.api_requests.labels(
            method=method,
            endpoint=endpoint,
            status_code=str(status_code)
        ).inc()
        
        self.api_request_duration.labels(
            method=method,
            endpoint=endpoint
        ).observe(duration)
    
    def update_system_metrics(self):
        """Update system resource metrics"""
        cpu_percent = psutil.cpu_percent()
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        self.system_cpu_usage.set(cpu_percent)
        self.system_memory_usage.set(memory.percent)
        self.system_disk_usage.set((disk.used / disk.total) * 100)
    
    def update_active_agents(self, agent_counts: Dict[str, int]):
        """Update active agent counts"""
        for agent_type, count in agent_counts.items():
            self.active_agents.labels(agent_type=agent_type).set(count)

# Global metrics instance
platform_metrics = PlatformMetrics()

# Metrics middleware for FastAPI
from fastapi import Request, Response
import time

async def metrics_middleware(request: Request, call_next):
    start_time = time.time()
    
    response = await call_next(request)
    
    duration = time.time() - start_time
    
    platform_metrics.record_api_request(
        method=request.method,
        endpoint=request.url.path,
        status_code=response.status_code,
        duration=duration
    )
    
    return response
```

**Vibe Coding Context:**
- 🎯 Goal: Essential metrics for monitoring platform health
- 🔍 Focus: Metrics that help identify performance issues
- 🧪 Test: Verify metrics are collected, expose on /metrics endpoint

#### Step 5.1.3: Health Check System (1 day)
```python
# monitoring/health.py
import asyncio
from typing import Dict, Any, List
from enum import Enum
import aioredis
import asyncpg

class HealthStatus(Enum):
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"

class HealthCheck:
    def __init__(self, name: str, check_func, timeout: int = 10):
        self.name = name
        self.check_func = check_func
        self.timeout = timeout
    
    async def run(self) -> Dict[str, Any]:
        try:
            start_time = asyncio.get_event_loop().time()
            result = await asyncio.wait_for(self.check_func(), timeout=self.timeout)
            duration = asyncio.get_event_loop().time() - start_time
            
            return {
                "name": self.name,
                "status": HealthStatus.HEALTHY.value,
                "duration": duration,
                "details": result if isinstance(result, dict) else {"result": result}
            }
        except asyncio.TimeoutError:
            return {
                "name": self.name,
                "status": HealthStatus.UNHEALTHY.value,
                "error": "Health check timed out"
            }
        except Exception as e:
            return {
                "name": self.name,
                "status": HealthStatus.UNHEALTHY.value,
                "error": str(e)
            }

class HealthChecker:
    def __init__(self):
        self.health_checks: List[HealthCheck] = []
    
    def register_check(self, name: str, check_func, timeout: int = 10):
        """Register a health check"""
        self.health_checks.append(HealthCheck(name, check_func, timeout))
    
    async def check_all(self) -> Dict[str, Any]:
        """Run all health checks and return results"""
        results = await asyncio.gather(
            *[check.run() for check in self.health_checks],
            return_exceptions=True
        )
        
        # Process results
        check_results = []
        overall_status = HealthStatus.HEALTHY
        
        for result in results:
            if isinstance(result, Exception):
                check_results.append({
                    "name": "unknown",
                    "status": HealthStatus.UNHEALTHY.value,
                    "error": str(result)
                })
                overall_status = HealthStatus.UNHEALTHY
            else:
                check_results.append(result)
                if result["status"] == HealthStatus.UNHEALTHY.value:
                    overall_status = HealthStatus.UNHEALTHY
                elif result["status"] == HealthStatus.DEGRADED.value and overall_status == HealthStatus.HEALTHY:
                    overall_status = HealthStatus.DEGRADED
        
        return {
            "status": overall_status.value,
            "timestamp": asyncio.get_event_loop().time(),
            "checks": check_results
        }

# Initialize health checker
health_checker = HealthChecker()

# Database health checks
async def check_postgres():
    conn = await asyncpg.connect("postgresql://...")
    result = await conn.fetchval("SELECT 1")
    await conn.close()
    return {"connection": "ok", "query_result": result}

async def check_redis():
    redis = aioredis.from_url("redis://...")
    await redis.ping()
    await redis.close()
    return {"connection": "ok"}

# Register standard health checks
health_checker.register_check("postgres", check_postgres)
health_checker.register_check("redis", check_redis)

# API endpoint
from fastapi import APIRouter

health_router = APIRouter()

@health_router.get("/health")
async def health_endpoint():
    return await health_checker.check_all()

@health_router.get("/health/live")
async def liveness_probe():
    """Kubernetes liveness probe - basic service health"""
    return {"status": "alive"}

@health_router.get("/health/ready")
async def readiness_probe():
    """Kubernetes readiness probe - service ready to handle traffic"""
    health_result = await health_checker.check_all()
    if health_result["status"] in [HealthStatus.HEALTHY.value, HealthStatus.DEGRADED.value]:
        return {"status": "ready"}
    else:
        raise HTTPException(status_code=503, detail="Service not ready")
```

**Vibe Coding Context:**
- 🎯 Goal: Reliable health checking for all dependencies
- 🔍 Focus: Kubernetes-compatible health endpoints
- 🧪 Test: Health checks work, proper HTTP status codes returned

---

## 5.2 Configuration & Deployment

### 🎯 Context
Prepare the platform for deployment with proper configuration management and containerization.

### 📦 What You're Building
Production-ready deployment configuration and orchestration.

### 🛠️ Development Steps

#### Step 5.2.1: Docker Configuration (2 days)
```dockerfile
# Dockerfile.agent-runtime
FROM python:3.11-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# Create app user
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Set work directory
WORKDIR /app

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Change ownership to app user
RUN chown -R appuser:appuser /app
USER appuser

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Expose port
EXPOSE 8000

# Run application
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

```yaml
# docker-compose.yml
version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: ai_platform
      POSTGRES_USER: platform_user
      POSTGRES_PASSWORD: platform_pass
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./schema:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U platform_user -d ai_platform"]
      interval: 30s
      timeout: 10s
      retries: 5

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  kafka:
    image: confluentinc/cp-kafka:latest
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
    volumes:
      - kafka_data:/var/lib/kafka/data

  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    ports:
      - "2181:2181"
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    volumes:
      - zookeeper_data:/var/lib/zookeeper/data

  agent-runtime:
    build:
      context: .
      dockerfile: Dockerfile.agent-runtime
    depends_on:
      - postgres
      - redis
      - kafka
    ports:
      - "8000:8000"
    environment:
      DATABASE_URL: ******************************************************/ai_platform
      REDIS_URL: redis://redis:6379
      KAFKA_BROKERS: kafka:9092
      LOG_LEVEL: INFO
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  kafka_data:
  zookeeper_data:
```

**Vibe Coding Context:**
- 🎯 Goal: Containerized development environment
- 🔍 Focus: Production-like local development setup
- 🧪 Test: All services start, health checks pass, inter-service communication works

#### Step 5.2.2: Kubernetes Manifests (3 days)
```yaml
# k8s/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: ai-platform
  labels:
    name: ai-platform
---
# k8s/postgres.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgres
  namespace: ai-platform
spec:
  serviceName: postgres
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:15
        ports:
        - containerPort: 5432
        env:
        - name: POSTGRES_DB
          value: "ai_platform"
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: username
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: password
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - $(POSTGRES_USER)
            - -d
            - $(POSTGRES_DB)
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - $(POSTGRES_USER)
            - -d
            - $(POSTGRES_DB)
          initialDelaySeconds: 5
          periodSeconds: 5
  volumeClaimTemplates:
  - metadata:
      name: postgres-storage
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 10Gi
---
apiVersion: v1
kind: Service
metadata:
  name: postgres
  namespace: ai-platform
spec:
  selector:
    app: postgres
  ports:
  - port: 5432
    targetPort: 5432
  type: ClusterIP
```

```yaml
# k8s/agent-runtime.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: agent-runtime
  namespace: ai-platform
  labels:
    app: agent-runtime
spec:
  replicas: 3
  selector:
    matchLabels:
      app: agent-runtime
  template:
    metadata:
      labels:
        app: agent-runtime
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
    spec:
      containers:
      - name: agent-runtime
        image: ai-platform/agent-runtime:latest
        ports:
        - containerPort: 8000
          name: http
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-secret
              key: url
        - name: REDIS_URL
          valueFrom:
            configMapKeyRef:
              name: platform-config
              key: redis_url
        - name: KAFKA_BROKERS
          valueFrom:
            configMapKeyRef:
              name: platform-config
              key: kafka_brokers
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health/live
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: app-config
          mountPath: /app/config
          readOnly: true
      volumes:
      - name: app-config
        configMap:
          name: agent-runtime-config
---
apiVersion: v1
kind: Service
metadata:
  name: agent-runtime
  namespace: ai-platform
spec:
  selector:
    app: agent-runtime
  ports:
  - port: 80
    targetPort: 8000
    name: http
  type: ClusterIP
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: agent-runtime-hpa
  namespace: ai-platform
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: agent-runtime
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

**Vibe Coding Context:**
- 🎯 Goal: Production-ready Kubernetes deployment
- 🔍 Focus: Scalability, health checks, resource management
- 🧪 Test: Deploy to K8s cluster, verify scaling, test health probes

---

# 🎯 Context Switching & Vibe Coding Guidelines

## 📋 Session Context Template

When starting a coding session, use this template to quickly get back into context:

### Current Focus
```
📍 Where am I? 
   Phase: [1-5] | Component: [specific component]
   
🎯 What am I building?
   [Brief description of current component]
   
🔍 Why does this matter?
   [How this fits into the overall platform]
   
✅ What should I test?
   [Specific test scenarios to validate]
```

### Quick Start Commands
```bash
# Start development environment
docker-compose up -d

# Check service health
curl http://localhost:8000/health

# Run tests for current component
pytest tests/[current_component] -v

# View logs
docker-compose logs -f [service_name]
```

### Debug Checklist
```
□ All dependencies are running (DB, Redis, Kafka)
□ Environment variables are set correctly
□ Database migrations are up to date
□ No port conflicts
□ Health checks are passing
```

## 🔄 Context Preservation Strategies

### 1. **Component State Files**
Keep a `STATUS.md` file in each component directory:
```markdown
# Component Status

## Last Updated: [date]
## Current State: [in-progress/complete/testing]

### What Works:
- [ ] Feature 1
- [ ] Feature 2

### Current Issues:
- Issue description and potential solutions

### Next Steps:
1. Immediate next task
2. Pending integration points
3. Testing scenarios

### Integration Notes:
- Dependencies on other components
- API contracts that need to be maintained
```

### 2. **Session Notes Template**
```markdown
# Coding Session - [date]

## Accomplished:
- Completed items

## Current Blockers:
- What's preventing progress

## Next Session Goals:
- Specific, actionable items

## Code Changes:
- Files modified
- Key functions/classes added

## Testing Status:
- What was tested
- What needs testing
```

### 3. **Integration Checkpoints**
At each phase boundary, validate integration:
```python
# integration_test.py
async def test_phase_integration():
    """Test that all components in current phase work together"""
    
    # Test data flow
    assert database_connection_works()
    assert message_queue_accessible()
    
    # Test service communication
    assert services_can_communicate()
    
    # Test end-to-end scenarios
    assert basic_user_journey_works()
```

## 🚀 Quick Start Scripts

### Development Environment
```bash
#!/bin/bash
# scripts/dev-start.sh

echo "🚀 Starting AI Agent Platform Development Environment"

# Check prerequisites
command -v docker >/dev/null 2>&1 || { echo "Docker required"; exit 1; }
command -v docker-compose >/dev/null 2>&1 || { echo "Docker Compose required"; exit 1; }

# Start infrastructure
echo "📦 Starting infrastructure services..."
docker-compose up -d postgres redis kafka zookeeper

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
./scripts/wait-for-services.sh

# Run database migrations
echo "🗄️  Running database migrations..."
python scripts/migrate.py

# Start application services
echo "🏃 Starting application services..."
docker-compose up -d agent-runtime

echo "✅ Development environment ready!"
echo "🌐 API available at: http://localhost:8000"
echo "📊 Health check: http://localhost:8000/health"
```

### Component Testing
```bash
#!/bin/bash
# scripts/test-component.sh

COMPONENT=$1
if [ -z "$COMPONENT" ]; then
    echo "Usage: $0 <component_name>"
    echo "Available components: auth, agent-runtime, orchestration, intelligence"
    exit 1
fi

echo "🧪 Testing component: $COMPONENT"

# Run unit tests
pytest tests/unit/$COMPONENT -v

# Run integration tests if infrastructure is up
if docker-compose ps postgres | grep -q "Up"; then
    pytest tests/integration/$COMPONENT -v
else
    echo "⚠️  Infrastructure not running, skipping integration tests"
fi

# Run component-specific health checks
python scripts/health-check.py --component $COMPONENT
```

## 🔍 Debugging & Troubleshooting

### Common Issues & Solutions
```markdown
### "Can't connect to database"
1. Check if PostgreSQL container is running: `docker-compose ps postgres`
2. Verify connection string in environment variables
3. Check if database exists: `docker-compose exec postgres psql -U platform_user -d ai_platform -c "\l"`

### "Agent execution fails"
1. Check agent logs: `docker-compose logs agent-runtime`
2. Verify agent is registered: `curl http://localhost:8000/api/v1/agents`
3. Check message queue connectivity: `docker-compose logs kafka`

### "API returns 500 errors"
1. Check application logs for stack traces
2. Verify all environment variables are set
3. Test database connectivity in the application container
4. Check if required services (Redis, Kafka) are accessible
```

### Development Workflow
```mermaid
graph LR
    A[Start Session] --> B[Check Status]
    B --> C[Start Infra]
    C --> D[Code Feature]
    D --> E[Test Locally]
    E --> F{Tests Pass?}
    F -->|No| G[Debug & Fix]
    G --> D
    F -->|Yes| H[Update Status]
    H --> I[Commit Changes]
    I --> J[End Session]
```

This bottom-up development plan provides a comprehensive roadmap that maintains context across development sessions, enables independent component development, and builds toward a fully integrated AI Agent Platform. Each phase builds on the previous one while allowing for parallel development within phases.
