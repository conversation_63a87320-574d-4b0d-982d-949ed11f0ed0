"""
{{ agent_name | title }} Agent
{{ description }}

Generated on: {{ "now" | datetime }}
Agent Type: {{ agent_type | title }}
Capabilities: {{ capabilities | join(", ") }}
"""

{% for import_stmt in imports %}
{{ import_stmt }}
{% endfor %}

from pydantic import BaseModel, Field
from enum import Enum
import uuid

# A2A Protocol Support
from protocols.a2a_protocol import A2AProtocolHandler, A2AMessage, A2AMessageType
from orchestration.orchestrator import MultiAgentOrchestrator


class AgentStatus(str, Enum):
    """Agent execution status"""
    IDLE = "idle"
    RUNNING = "running"
    ERROR = "error"
    COMPLETED = "completed"


class TaskRequest(BaseModel):
    """Task request model"""
    task_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    task_type: str
    input_data: Dict[str, Any]
    priority: str = "medium"
    timeout: Optional[int] = None


class TaskResponse(BaseModel):
    """Task response model"""
    task_id: str
    status: AgentStatus
    result: Optional[Any] = None
    error: Optional[str] = None
    execution_time: Optional[float] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)


class {{ agent_class }}Agent:
    """
    {{ description }}
    
    Capabilities:
    {% for capability in capabilities %}
    - {{ capability | title | replace("_", " ") }}
    {% endfor %}
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.agent_id = str(uuid.uuid4())
        self.name = "{{ agent_name }}"
        self.agent_type = "{{ agent_type }}"
        self.config = config
        self.status = AgentStatus.IDLE
        self.logger = logging.getLogger(f"{self.__class__.__name__}")
        
        # Initialize capabilities
        self.capabilities = {{ capabilities | tojson }}
        
        # Initialize A2A Protocol Handler
        self.a2a_handler = A2AProtocolHandler()
        self.orchestrator = None
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger.info(f"Initialized {self.name} agent with ID: {self.agent_id}")
        self.logger.info(f"A2A Protocol: {'ENABLED' if self.a2a_handler.is_enabled() else 'DISABLED'}")
    
    async def initialize(self) -> bool:
        """Initialize the agent and its resources"""
        try:
            self.logger.info("Initializing agent resources...")
            
            # Initialize based on capabilities
            {% if 'api_integration' in capabilities %}
            # Setup API client
            self.api_client = httpx.AsyncClient(
                timeout=self.config.get('api_timeout', 30),
                headers={'User-Agent': f'{self.name}-agent/{self.agent_id}'}
            )
            {% endif %}
            
            {% if 'data_analysis' in capabilities %}
            # Setup data analysis tools
            self.analysis_config = self.config.get('analysis', {})
            {% endif %}
            
            {% if 'database' in capabilities %}
            # Setup database connection
            self.db_url = self.config.get('database_url')
            if self.db_url:
                from sqlalchemy import create_engine
                self.db_engine = create_engine(self.db_url)
            {% endif %}
            
            # Initialize A2A Protocol and register with orchestrator
            if self.a2a_handler.is_enabled():
                # Register agent with orchestrator
                self.orchestrator = MultiAgentOrchestrator()
                await self.orchestrator.register_agent(
                    agent_id=self.agent_id,
                    agent_type=self.agent_type,
                    capabilities=self.capabilities,
                    config=self.config
                )
                self.logger.info("Agent registered with orchestrator for A2A communication")
            
            # Custom initialization logic
            {% if custom_logic %}
            # Custom initialization
            {{ custom_logic | indent(12) }}
            {% endif %}
            
            self.logger.info("Agent initialization completed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Agent initialization failed: {e}")
            return False
    
    async def execute_task(self, task: TaskRequest) -> TaskResponse:
        """Execute a task and return the result"""
        start_time = datetime.now()
        self.status = AgentStatus.RUNNING
        
        try:
            self.logger.info(f"Executing task {task.task_id} of type {task.task_type}")
            
            # Route task based on type and capabilities
            result = None
            
            {% if 'conversation' in capabilities %}
            if task.task_type == "conversation":
                result = await self._handle_conversation(task.input_data)
            {% endif %}
            
            {% if 'data_analysis' in capabilities %}
            elif task.task_type == "analyze_data":
                result = await self._handle_data_analysis(task.input_data)
            {% endif %}
            
            {% if 'data_processing' in capabilities %}
            elif task.task_type == "process_data":
                result = await self._handle_data_processing(task.input_data)
            {% endif %}
            
            {% if 'system_monitoring' in capabilities %}
            elif task.task_type == "monitor_system":
                result = await self._handle_system_monitoring(task.input_data)
            {% endif %}
            
            {% if 'web_scraping' in capabilities %}
            elif task.task_type == "scrape_web":
                result = await self._handle_web_scraping(task.input_data)
            {% endif %}
            
            else:
                # Generic task handling
                result = await self._handle_generic_task(task)
            
            execution_time = (datetime.now() - start_time).total_seconds()
            self.status = AgentStatus.COMPLETED
            
            return TaskResponse(
                task_id=task.task_id,
                status=AgentStatus.COMPLETED,
                result=result,
                execution_time=execution_time,
                metadata={
                    "agent_id": self.agent_id,
                    "agent_name": self.name,
                    "completed_at": datetime.now().isoformat()
                }
            )
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            self.status = AgentStatus.ERROR
            self.logger.error(f"Task execution failed: {e}")
            
            return TaskResponse(
                task_id=task.task_id,
                status=AgentStatus.ERROR,
                error=str(e),
                execution_time=execution_time,
                metadata={
                    "agent_id": self.agent_id,
                    "agent_name": self.name,
                    "failed_at": datetime.now().isoformat()
                }
            )
    
    {% if 'conversation' in capabilities %}
    async def _handle_conversation(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle conversational tasks"""
        message = input_data.get("message", "")
        context = input_data.get("context", {})
        
        # Simple response generation (replace with actual AI integration)
        response = f"I understand you said: '{message}'. How can I help you further?"
        
        return {
            "response": response,
            "context": context,
            "agent_type": self.agent_type
        }
    {% endif %}
    
    {% if 'data_analysis' in capabilities %}
    async def _handle_data_analysis(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle data analysis tasks"""
        import pandas as pd
        import numpy as np
        
        data_source = input_data.get("data_source")
        analysis_type = input_data.get("analysis_type", "basic")
        
        # Mock data analysis
        if data_source:
            # In a real implementation, load and analyze actual data
            result = {
                "analysis_type": analysis_type,
                "data_points": 1000,
                "summary_statistics": {
                    "mean": 42.5,
                    "median": 41.2,
                    "std_dev": 12.3
                },
                "insights": [
                    "Data shows normal distribution",
                    "No significant outliers detected",
                    "Trend is stable over time period"
                ]
            }
        else:
            result = {"error": "No data source provided"}
        
        return result
    {% endif %}
    
    {% if 'data_processing' in capabilities %}
    async def _handle_data_processing(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle data processing tasks"""
        data = input_data.get("data", [])
        operation = input_data.get("operation", "transform")
        
        processed_data = []
        
        if operation == "transform":
            # Example transformation
            for item in data:
                if isinstance(item, dict):
                    processed_item = {k: str(v).upper() if isinstance(v, str) else v 
                                    for k, v in item.items()}
                    processed_data.append(processed_item)
                else:
                    processed_data.append(str(item).upper())
        
        return {
            "processed_data": processed_data,
            "operation": operation,
            "records_processed": len(processed_data)
        }
    {% endif %}
    
    {% if 'system_monitoring' in capabilities %}
    async def _handle_system_monitoring(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle system monitoring tasks"""
        import psutil
        
        metrics_requested = input_data.get("metrics", ["cpu", "memory", "disk"])
        results = {}
        
        if "cpu" in metrics_requested:
            results["cpu_usage"] = psutil.cpu_percent(interval=1)
        
        if "memory" in metrics_requested:
            memory = psutil.virtual_memory()
            results["memory_usage"] = {
                "percent": memory.percent,
                "available": memory.available,
                "total": memory.total
            }
        
        if "disk" in metrics_requested:
            disk = psutil.disk_usage('/')
            results["disk_usage"] = {
                "percent": (disk.used / disk.total) * 100,
                "free": disk.free,
                "total": disk.total
            }
        
        return {
            "system_metrics": results,
            "timestamp": datetime.now().isoformat(),
            "agent_id": self.agent_id
        }
    {% endif %}
    
    async def _handle_generic_task(self, task: TaskRequest) -> Dict[str, Any]:
        """Handle generic tasks that don't match specific capabilities"""
        return {
            "message": f"Task {task.task_type} completed by {self.name}",
            "input_received": task.input_data,
            "agent_capabilities": self.capabilities,
            "task_id": task.task_id
        }
    
    async def handle_a2a_message(self, message: A2AMessage) -> A2AMessage:
        """Handle Agent-to-Agent (A2A) protocol messages"""
        try:
            self.logger.info(f"Received A2A message: {message.message_type} from {message.sender_id}")
            
            response_content = {}
            
            if message.message_type == A2AMessageType.TASK_REQUEST:
                # Convert A2A message to internal task request
                task = TaskRequest(
                    task_type=message.content.get('task_type', 'generic'),
                    input_data=message.content,
                    priority=message.priority or 'medium'
                )
                
                # Execute the task
                task_response = await self.execute_task(task)
                response_content = {
                    "task_result": task_response.result,
                    "execution_time": task_response.execution_time,
                    "status": task_response.status.value
                }
                
            elif message.message_type == A2AMessageType.CAPABILITY_QUERY:
                response_content = {
                    "agent_id": self.agent_id,
                    "agent_name": self.name,
                    "agent_type": self.agent_type,
                    "capabilities": self.capabilities,
                    "status": self.status.value
                }
                
            elif message.message_type == A2AMessageType.STATUS_UPDATE:
                response_content = await self.get_status()
                
            elif message.message_type == A2AMessageType.HEARTBEAT:
                response_content = {
                    "agent_id": self.agent_id,
                    "timestamp": datetime.now().isoformat(),
                    "status": "alive",
                    "uptime": datetime.now().isoformat()
                }
                
            else:
                response_content = {
                    "error": f"Unsupported message type: {message.message_type}",
                    "supported_types": ["TASK_REQUEST", "CAPABILITY_QUERY", "STATUS_UPDATE", "HEARTBEAT"]
                }
            
            # Create A2A response message
            response = A2AMessage(
                message_type=A2AMessageType.TASK_RESPONSE,
                sender_id=self.agent_id,
                recipient_id=message.sender_id,
                content=response_content,
                correlation_id=message.correlation_id,
                priority=message.priority
            )
            
            self.logger.info(f"Sending A2A response to {message.sender_id}")
            return response
            
        except Exception as e:
            self.logger.error(f"Error handling A2A message: {e}")
            
            # Return error response
            return A2AMessage(
                message_type=A2AMessageType.ERROR,
                sender_id=self.agent_id,
                recipient_id=message.sender_id,
                content={"error": str(e), "message_id": message.message_id},
                correlation_id=message.correlation_id,
                priority=message.priority
            )
    
    async def send_a2a_message(self, recipient_id: str, message_type: A2AMessageType, content: Dict[str, Any]) -> A2AMessage:
        """Send an A2A message to another agent"""
        if not self.a2a_handler.is_enabled():
            raise Exception("A2A Protocol is not enabled")
        
        message = A2AMessage(
            message_type=message_type,
            sender_id=self.agent_id,
            recipient_id=recipient_id,
            content=content,
            priority='normal'
        )
        
        self.logger.info(f"Sending A2A message: {message_type} to {recipient_id}")
        return await self.a2a_handler.send_message(message)
    
    async def get_status(self) -> Dict[str, Any]:
        """Get current agent status"""
        return {
            "agent_id": self.agent_id,
            "name": self.name,
            "type": self.agent_type,
            "status": self.status.value,
            "capabilities": self.capabilities,
            "uptime": datetime.now().isoformat()
        }
    
    async def shutdown(self) -> bool:
        """Shutdown the agent gracefully"""
        try:
            self.logger.info("Shutting down agent...")
            
            {% if 'api_integration' in capabilities %}
            # Close API client
            if hasattr(self, 'api_client'):
                await self.api_client.aclose()
            {% endif %}
            
            {% if 'database' in capabilities %}
            # Close database connections
            if hasattr(self, 'db_engine'):
                self.db_engine.dispose()
            {% endif %}
            
            self.status = AgentStatus.IDLE
            self.logger.info("Agent shutdown completed")
            return True
            
        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")
            return False


# Factory function for creating agent instances
def create_agent(config: Dict[str, Any]) -> {{ agent_class }}Agent:
    """Create and initialize a new agent instance"""
    return {{ agent_class }}Agent(config)


# Main execution for standalone running
async def main():
    """Main function for running the agent standalone"""
    # Default configuration
    config = {
        {% for key, value in configuration.items() %}
        "{{ key }}": {{ value | tojson }},
        {% endfor %}
    }
    
    # Create and initialize agent
    agent = create_agent(config)
    
    if await agent.initialize():
        print(f"Agent {agent.name} initialized successfully")
        
        # Example task execution
        test_task = TaskRequest(
            task_type="test",
            input_data={"message": "Hello, agent!"}
        )
        
        result = await agent.execute_task(test_task)
        print(f"Task result: {result}")
        
        # Shutdown
        await agent.shutdown()
    else:
        print("Failed to initialize agent")


if __name__ == "__main__":
    asyncio.run(main())