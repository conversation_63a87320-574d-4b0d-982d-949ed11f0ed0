#!/bin/bash
# Generic infrastructure deployment script
# Deploys Terraform infrastructure for any environment

# Source common utilities
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/../common/utils.sh"
source "$SCRIPT_DIR/../common/environment.sh"

# Script configuration
SCRIPT_NAME="deploy_infrastructure.sh"
SCRIPT_DESCRIPTION="Deploy Terraform infrastructure for specified environment"

# Required tools
REQUIRED_TOOLS=("terraform" "gcloud" "jq")

# Main deployment function
deploy_infrastructure() {
    local env="$1"
    
    print_step "Step 1: Environment setup and validation"
    if ! setup_environment "$env"; then
        return 1
    fi
    
    print_environment_summary "$env"
    production_safety_check
    
    print_step "Step 2: Terraform initialization"
    cd "$TERRAFORM_DIR"
    
    # Initialize Terraform
    print_info "Initializing Terraform..."
    if ! execute terraform init -upgrade; then
        print_error "Terraform initialization failed"
        return 1
    fi
    
    print_success "Terraform initialized"
    
    print_step "Step 3: Terraform plan"
    print_info "Creating Terraform plan..."
    
    local plan_file="terraform-plan-$(date +%Y%m%d-%H%M%S).tfplan"
    if ! execute terraform plan -out="$plan_file" -var-file="terraform.tfvars"; then
        print_error "Terraform plan failed"
        return 1
    fi
    
    print_success "Terraform plan created: $plan_file"
    
    # Show plan summary
    print_info "Plan summary:"
    terraform show -no-color "$plan_file" | grep -E "^(Plan:|No changes)" || true
    
    print_step "Step 4: Terraform apply"
    if [[ "$DRY_RUN" == "true" ]]; then
        print_info "[DRY RUN] Would apply Terraform plan: $plan_file"
        return 0
    fi
    
    # Confirm apply (unless force is enabled)
    if [[ "$FORCE" != "true" ]]; then
        echo ""
        if ! confirm "Apply this Terraform plan?" "n"; then
            print_info "Terraform apply cancelled"
            return 0
        fi
    fi
    
    print_info "Applying Terraform plan..."
    if ! execute terraform apply "$plan_file"; then
        print_error "Terraform apply failed"
        return 1
    fi
    
    print_success "Terraform apply completed"
    
    print_step "Step 5: Verification"
    print_info "Verifying infrastructure deployment..."
    
    # Check that key resources exist
    if ! terraform output project_id > /dev/null 2>&1; then
        print_error "Infrastructure verification failed - no project_id output"
        return 1
    fi
    
    if ! terraform output instance_name > /dev/null 2>&1; then
        print_error "Infrastructure verification failed - no instance_name output"
        return 1
    fi
    
    if ! terraform output registry_url > /dev/null 2>&1; then
        print_error "Infrastructure verification failed - no registry_url output"
        return 1
    fi
    
    # Display key outputs
    print_info "Key infrastructure outputs:"
    echo "  🏗️  Project ID: $(terraform output -raw project_id)"
    echo "  🖥️  Instance Name: $(terraform output -raw instance_name)"
    echo "  📦 Registry URL: $(terraform output -raw registry_url)"
    echo "  💾 Database IP: $(terraform output -raw database_private_ip 2>/dev/null || echo 'N/A')"
    echo "  🌐 Load Balancer IP: $(terraform output -raw load_balancer_ip 2>/dev/null || echo 'N/A')"
    echo "  📄 Web Bucket: $(terraform output -raw web_bucket_name 2>/dev/null || echo 'N/A')"
    
    print_success "Infrastructure deployment completed successfully!"
    
    # Clean up plan file
    rm -f "$plan_file"
    
    return 0
}

# Main script execution
main() {
    setup_error_handling
    
    # Parse command line arguments
    parse_args "$SCRIPT_NAME" "$@"
    
    # Print banner
    print_banner "$SCRIPT_NAME" "$SCRIPT_DESCRIPTION"
    
    # Check required tools
    if ! check_required_tools "${REQUIRED_TOOLS[@]}"; then
        exit 1
    fi
    
    # Change to workspace directory
    if ! change_to_workspace; then
        exit 1
    fi
    
    # Validate environment
    if ! validate_environment "$ENVIRONMENT"; then
        exit 1
    fi
    
    # Deploy infrastructure
    if deploy_infrastructure "$ENVIRONMENT"; then
        print_success "Infrastructure deployment completed successfully! 🎉"
        exit 0
    else
        print_error "Infrastructure deployment failed!"
        exit 1
    fi
}

# Show usage if no arguments provided
if [[ $# -eq 0 ]]; then
    print_usage "$SCRIPT_NAME"
    exit 1
fi

# Execute main function with all arguments
main "$@"