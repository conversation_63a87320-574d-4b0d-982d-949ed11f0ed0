"""
AI Agent Platform - Pydantic Schemas for API
"""

from datetime import datetime
from typing import List, Optional, Dict, Any, Literal
from uuid import UUID, uuid4
from pydantic import BaseModel, Field, validator

from database.models import AgentStatus, TaskStatus, OrchestrationPattern


# Base schemas
class BaseSchema(BaseModel):
    """Base schema with common configuration"""
    
    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            UUID: lambda v: str(v)
        }


# User schemas
class UserBase(BaseSchema):
    username: str = Field(..., min_length=3, max_length=255)
    email: str = Field(..., pattern=r"^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$")
    full_name: Optional[str] = Field(None, max_length=255)


class UserCreate(UserBase):
    password: str = Field(..., min_length=8)


class UserResponse(UserBase):
    id: UUID
    is_active: bool
    is_superuser: bool
    created_at: datetime
    last_login: Optional[datetime]


class UserUpdate(BaseSchema):
    full_name: Optional[str] = Field(None, max_length=255)
    email: Optional[str] = Field(None, pattern=r"^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$")


# Authentication schemas
class Token(BaseSchema):
    access_token: str
    refresh_token: str
    token_type: str


class TokenData(BaseSchema):
    username: Optional[str] = None


class PasswordChangeRequest(BaseSchema):
    current_password: str = Field(..., min_length=8)
    new_password: str = Field(..., min_length=8)


# Supporting schemas for comprehensive agent configuration
class ResourceLimits(BaseSchema):
    """Resource limits for agent deployment"""
    cpu_limit: Optional[str] = Field(None, description="CPU limit (e.g., '500m', '1')")
    memory_limit: Optional[str] = Field(None, description="Memory limit (e.g., '512Mi', '1Gi')")
    disk_limit: Optional[str] = Field(None, description="Disk limit")

class DeploymentConfig(BaseSchema):
    """Deployment configuration for agents"""
    port: Optional[int] = Field(None, ge=1, le=65535, description="Port number")
    environment: Optional[Dict[str, str]] = Field(None, description="Environment variables")
    resources: Optional[ResourceLimits] = Field(None, description="Resource limits")
    auto_scale: Optional[bool] = Field(False, description="Enable auto-scaling")
    replicas: Optional[int] = Field(1, ge=1, description="Number of replicas")

class AdvancedOptions(BaseSchema):
    """Advanced configuration options"""
    use_ai_workflow: Optional[bool] = Field(False, description="Use AI-powered workflow")
    custom_templates: Optional[List[str]] = Field(default_factory=list, description="Custom templates")
    integration_endpoints: Optional[List[str]] = Field(default_factory=list, description="Integration endpoints")
    testing_enabled: Optional[bool] = Field(True, description="Enable testing")
    documentation_level: Optional[Literal["minimal", "standard", "comprehensive"]] = Field(
        "standard", description="Documentation level"
    )

# Agent schemas
class AgentBase(BaseSchema):
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    type: Literal["assistant", "analyst", "specialist", "coordinator", "processor", "monitor", "fullstack", "api_service", "background_worker", "data_processor", "integration"] = Field(
        "assistant", description="Agent type"
    )
    config: Dict[str, Any] = Field(default_factory=dict)
    capabilities: List[str] = Field(default_factory=list)


class AgentCreate(AgentBase):
    """Comprehensive agent creation schema that accepts all GenerationRequest fields"""

    # Core AI-powered generation fields
    requirements: Optional[str] = Field(None, description="Natural language requirements for AI generation")
    framework: Optional[Literal["fastapi", "flask", "express", "custom"]] = Field(
        None, description="Framework to use for the agent"
    )
    language: Optional[Literal["python", "javascript", "typescript"]] = Field(
        None, description="Programming language for the agent"
    )

    # Deployment configuration
    deployment: Optional[DeploymentConfig] = Field(None, description="Deployment configuration")

    # Advanced options
    advanced_options: Optional[AdvancedOptions] = Field(None, description="Advanced configuration options")

    @validator('config', pre=True, always=True)
    def merge_comprehensive_config(cls, v, values):
        """Merge all comprehensive fields into the config for backward compatibility"""
        if v is None:
            v = {}

        # Merge requirements if provided
        if 'requirements' in values and values['requirements']:
            v['requirements'] = values['requirements']

        # Merge framework if provided
        if 'framework' in values and values['framework']:
            v['framework'] = values['framework']

        # Merge language if provided
        if 'language' in values and values['language']:
            v['language'] = values['language']

        # Merge deployment config if provided
        if 'deployment' in values and values['deployment']:
            v['deployment'] = values['deployment'].dict() if hasattr(values['deployment'], 'dict') else values['deployment']

        # Merge advanced options if provided
        if 'advanced_options' in values and values['advanced_options']:
            v['advanced_options'] = values['advanced_options'].dict() if hasattr(values['advanced_options'], 'dict') else values['advanced_options']

        return v


class AgentUpdate(BaseSchema):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    config: Optional[Dict[str, Any]] = None
    capabilities: Optional[List[str]] = None
    constraints: Optional[Dict[str, Any]] = None
    requirements: Optional[str] = Field(None, description="User requirements and prompts for the agent")
    user_prompt: Optional[str] = Field(None, description="Original user prompt for the agent")


class AgentResponse(AgentBase):
    id: UUID
    status: AgentStatus
    version: str
    owner_id: UUID
    created_at: datetime
    updated_at: datetime
    last_heartbeat: Optional[datetime]
    cpu_usage: Optional[float]
    memory_usage: Optional[int]
    
    @property
    def requirements(self) -> Optional[str]:
        """Extract requirements from config"""
        return self.config.get('requirements') if self.config else None
    
    @property
    def user_prompt(self) -> Optional[str]:
        """Extract user_prompt from config"""
        return self.config.get('user_prompt') if self.config else None


class AgentListResponse(BaseSchema):
    agents: List[AgentResponse]
    total: int
    limit: int
    offset: int


class AgentStartResponse(BaseSchema):
    agent_id: UUID
    success: bool
    message: str


# Task schemas
class TaskBase(BaseSchema):
    title: str = Field(..., min_length=1, max_length=500)
    description: Optional[str] = Field(None, max_length=2000)
    type: str = Field(..., max_length=100)
    priority: int = Field(5, ge=1, le=10)
    input_data: Optional[Dict[str, Any]] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)
    deadline: Optional[datetime] = None


class TaskCreate(TaskBase):
    assigned_agent_id: Optional[UUID] = None


class TaskUpdate(BaseSchema):
    title: Optional[str] = Field(None, min_length=1, max_length=500)
    description: Optional[str] = Field(None, max_length=2000)
    status: Optional[TaskStatus] = None
    priority: Optional[int] = Field(None, ge=1, le=10)
    output_data: Optional[Dict[str, Any]] = None
    progress_percentage: Optional[int] = Field(None, ge=0, le=100)
    assigned_agent_id: Optional[UUID] = None


class TaskResponse(TaskBase):
    id: UUID
    status: TaskStatus
    progress_percentage: int
    steps_completed: int
    total_steps: Optional[int]
    assigned_agent_id: Optional[UUID]
    orchestration_id: Optional[UUID]
    parent_task_id: Optional[UUID]
    created_at: datetime
    updated_at: datetime
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    output_data: Optional[Dict[str, Any]]


class TaskListResponse(BaseSchema):
    tasks: List[TaskResponse]
    total: int
    limit: int
    offset: int


# Orchestration schemas
class OrchestrationBase(BaseSchema):
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    pattern: OrchestrationPattern = OrchestrationPattern.SEQUENTIAL
    config: Dict[str, Any] = Field(default_factory=dict)
    execution_plan: Dict[str, Any] = Field(default_factory=dict)


class OrchestrationCreate(OrchestrationBase):
    agent_ids: List[UUID] = Field(..., min_items=1)


class OrchestrationUpdate(BaseSchema):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    config: Optional[Dict[str, Any]] = None
    execution_plan: Optional[Dict[str, Any]] = None


class OrchestrationMemberResponse(BaseSchema):
    agent_id: UUID
    role: str
    join_order: int


class OrchestrationResponse(OrchestrationBase):
    id: UUID
    status: str
    progress_percentage: int
    owner_id: UUID
    created_at: datetime
    updated_at: datetime
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    members: List[OrchestrationMemberResponse]


class OrchestrationListResponse(BaseSchema):
    orchestrations: List[OrchestrationResponse]
    total: int
    limit: int
    offset: int


class OrchestrationStartResponse(BaseSchema):
    orchestration_id: UUID
    success: bool
    message: str


class OrchestrationProgressResponse(BaseSchema):
    orchestration_id: str
    status: str
    pattern: str
    progress_percentage: float
    total_steps: int
    completed_steps: int
    start_time: Optional[str]
    end_time: Optional[str]
    duration_seconds: Optional[float]
    agent_count: int
    results: Dict[str, Any]


class AddAgentRequest(BaseSchema):
    agent_id: UUID
    role: str = Field("worker", max_length=100)


# AI Model schemas
class AIModelBase(BaseSchema):
    name: str = Field(..., min_length=1, max_length=255)
    provider: str = Field(..., max_length=100)
    model_id: str = Field(..., max_length=255)
    version: Optional[str] = Field(None, max_length=50)
    config: Dict[str, Any] = Field(default_factory=dict)
    capabilities: List[str] = Field(default_factory=list)
    max_tokens: Optional[int] = Field(None, gt=0)
    input_cost_per_token: Optional[float] = Field(None, ge=0)
    output_cost_per_token: Optional[float] = Field(None, ge=0)


class AIModelCreate(AIModelBase):
    pass


class AIModelUpdate(BaseSchema):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    config: Optional[Dict[str, Any]] = None
    capabilities: Optional[List[str]] = None
    is_active: Optional[bool] = None
    is_default: Optional[bool] = None


class AIModelResponse(AIModelBase):
    id: UUID
    is_active: bool
    is_default: bool
    created_at: datetime
    updated_at: datetime


# Agent Generation schemas
class AgentGenerationBase(BaseSchema):
    specification: Dict[str, Any] = Field(..., min_items=1)
    target_framework: str = Field(..., max_length=100)
    deployment_target: str = Field(..., max_length=100)


class AgentGenerationCreate(AgentGenerationBase):
    pass


class AgentGenerationResponse(AgentGenerationBase):
    id: UUID
    status: str
    generated_files: Optional[Dict[str, Any]]
    build_logs: Optional[str]
    test_results: Optional[Dict[str, Any]]
    requester_id: UUID
    created_at: datetime
    updated_at: datetime


# Communication schemas
class CommunicationBase(BaseSchema):
    protocol: str = Field(..., max_length=50)
    message_type: str = Field(..., max_length=100)
    content: Dict[str, Any] = Field(..., min_items=1)
    response_required: bool = False


class CommunicationCreate(CommunicationBase):
    receiver_agent_id: Optional[UUID] = None


class CommunicationResponse(CommunicationBase):
    id: UUID
    sender_agent_id: UUID
    receiver_agent_id: Optional[UUID]
    status: str
    response_received: bool
    created_at: datetime


# Error schemas
class ErrorResponse(BaseSchema):
    error: str
    detail: Optional[str] = None
    code: Optional[str] = None


# Health check schemas
class HealthCheckResponse(BaseSchema):
    status: str
    version: str
    environment: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)


# Runtime schemas
class RuntimeInfoResponse(BaseSchema):
    agent_id: str
    status: str
    process_id: Optional[int]
    start_time: Optional[str]
    last_heartbeat: Optional[str]
    cpu_usage: float
    memory_usage: int
    current_task: Optional[Dict[str, Any]]
    queue_size: int
    task_history_count: int
    capabilities: List[str]
    constraints: Dict[str, Any]


class TaskExecutionRequest(BaseSchema):
    task_id: Optional[UUID] = None
    task_type: str = Field(..., max_length=100)
    input_data: Dict[str, Any] = Field(default_factory=dict)
    metadata: Optional[Dict[str, Any]] = None


class TaskExecutionResponse(BaseSchema):
    task_id: Optional[str]
    agent_id: UUID
    status: str
    result: Dict[str, Any]
    message: str


class ResourceUsageResponse(BaseSchema):
    total_cpu_usage: float
    total_memory_mb: int
    average_cpu_per_agent: float
    average_memory_per_agent: float


class TaskStatisticsResponse(BaseSchema):
    queued_tasks: int
    completed_tasks: int


class SystemStatsResponse(BaseSchema):
    total_agents: Optional[int] = None
    active_agents: Optional[int] = None
    cpu_usage: Optional[float] = None
    memory_usage: Optional[float] = None
    disk_usage: Optional[float] = None
    uptime: Optional[str] = None
    success_rate: Optional[float] = None
    resource_usage: Optional[ResourceUsageResponse] = None
    task_statistics: Optional[TaskStatisticsResponse] = None

# Alias for OpenAPI generation
SystemStats = SystemStatsResponse

class SystemHealthResponse(BaseSchema):
    """System health check response"""
    status: Literal["healthy", "degraded", "unhealthy"]
    version: str
    uptime: str
    cpu_usage: Optional[float] = None
    memory_usage: Optional[float] = None
    memory_mb: Optional[int] = None
    components: Dict[str, Dict[str, Any]] = Field(default_factory=dict)

# Alias for OpenAPI generation
SystemHealth = SystemHealthResponse

class MigrationProjectResponse(BaseSchema):
    """Migration project status and details"""
    id: UUID
    plan_id: UUID
    status: Literal["pending", "in_progress", "completed", "failed", "cancelled"]
    progress_percentage: float = Field(ge=0, le=100)
    current_phase: Optional[str] = None
    estimated_completion: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    error_message: Optional[str] = None

# Alias for OpenAPI generation
MigrationProject = MigrationProjectResponse


# A2A (Agent-to-Agent) Protocol Schemas
class A2AMessage(BaseSchema):
    """Agent-to-Agent communication message"""
    id: UUID = Field(default_factory=uuid4)
    sender_id: UUID
    receiver_id: UUID
    message_type: Literal["request", "response", "notification", "heartbeat"]
    action: str = Field(..., description="Action to perform or response type")
    payload: Dict[str, Any] = Field(default_factory=dict)
    correlation_id: Optional[UUID] = None  # For request-response correlation
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    ttl_seconds: Optional[int] = Field(default=300, description="Time to live in seconds")

class A2ACapability(BaseSchema):
    """Agent capability for A2A communication"""
    name: str
    description: str
    input_schema: Dict[str, Any] = Field(default_factory=dict)
    output_schema: Dict[str, Any] = Field(default_factory=dict)
    requires_auth: bool = False

class A2AAgentInfo(BaseSchema):
    """Agent information for A2A discovery"""
    agent_id: UUID
    name: str
    type: str
    status: str
    endpoint_url: str
    capabilities: List[A2ACapability] = Field(default_factory=list)
    last_heartbeat: datetime
    metadata: Dict[str, Any] = Field(default_factory=dict)

class A2ADiscoveryRequest(BaseSchema):
    """Request to discover other agents"""
    requesting_agent_id: UUID
    capability_filter: Optional[str] = None
    agent_type_filter: Optional[str] = None

class A2ADiscoveryResponse(BaseSchema):
    """Response with discovered agents"""
    agents: List[A2AAgentInfo]
    total_count: int

class A2AMessageRequest(BaseSchema):
    """Request to send message to another agent"""
    receiver_id: UUID
    message_type: Literal["request", "notification"]
    action: str
    payload: Dict[str, Any] = Field(default_factory=dict)
    correlation_id: Optional[UUID] = None
    ttl_seconds: Optional[int] = 300

class A2AMessageResponse(BaseSchema):
    """Response from A2A message"""
    message_id: UUID
    status: Literal["sent", "delivered", "failed", "timeout"]
    response_payload: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None


# Metrics schemas
class AgentMetrics(BaseSchema):
    total_agents: int
    running_agents: int
    stopped_agents: int
    error_agents: int


class SystemMetrics(BaseSchema):
    uptime_seconds: float
    cpu_usage_percent: float
    memory_usage_mb: int
    disk_usage_percent: float


class MetricsResponse(BaseSchema):
    agents: AgentMetrics
    system: SystemMetrics
    timestamp: datetime = Field(default_factory=datetime.utcnow)