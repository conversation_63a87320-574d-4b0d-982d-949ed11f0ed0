/**
 * AI Agent Platform - Create Task Page
 * Create new tasks for agent execution
 */

'use client';

import React from 'react';
import { AppLayout } from '@/components/Layout/AppLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowLeft, Plus, Zap } from 'lucide-react';
import Link from 'next/link';

export default function CreateTaskPage() {
  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/tasks">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Tasks
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Create New Task</h1>
            <p className="text-muted-foreground">
              Create a new task for your agents to execute
            </p>
          </div>
        </div>

        {/* Coming Soon */}
        <Card>
          <CardContent className="py-12">
            <div className="flex flex-col items-center space-y-4 text-center">
              <div className="p-4 bg-muted rounded-full">
                <Zap className="h-12 w-12 text-muted-foreground" />
              </div>
              <div>
                <h3 className="text-xl font-semibold">Task Creation Coming Soon</h3>
                <p className="text-muted-foreground max-w-md">
                  The task creation interface is currently under development. 
                  Soon you'll be able to create custom tasks with advanced configuration options.
                </p>
              </div>
              <div className="flex gap-4">
                <Button variant="outline" asChild>
                  <Link href="/tasks">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    View Existing Tasks
                  </Link>
                </Button>
                <Button asChild>
                  <Link href="/generator">
                    <Plus className="h-4 w-4 mr-2" />
                    Create Agent Instead
                  </Link>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Agent Generator</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <p className="text-sm text-muted-foreground">
                Create a custom AI agent with our visual generator
              </p>
              <Button asChild className="w-full">
                <Link href="/generator">
                  <Plus className="h-4 w-4 mr-2" />
                  Create Agent
                </Link>
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Existing Tasks</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <p className="text-sm text-muted-foreground">
                View and manage your existing tasks
              </p>
              <Button variant="outline" asChild className="w-full">
                <Link href="/tasks">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  View Tasks
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </AppLayout>
  );
}