server {
    listen 80;
    server_name _;
    
    # Proxy API requests to gateway (preserves full path)
    location /api/ {
        proxy_pass http://platform-gateway:8085;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # Hide backend CORS headers to prevent duplicates
        proxy_hide_header Access-Control-Allow-Origin;
        proxy_hide_header Access-Control-Allow-Credentials;
        proxy_hide_header Access-Control-Allow-Methods;
        proxy_hide_header Access-Control-Allow-Headers;
        proxy_hide_header Access-Control-Max-Age;
        
        # CORS headers with dynamic origin support
        # Allow production domain and localhost for development
        set $cors_origin "";
        if ($http_origin ~ "^https://internal\.dev\.twodot\.ai$") {
            set $cors_origin "https://internal.dev.twodot.ai";
        }
        if ($http_origin ~ "^http://localhost:8080$") {
            set $cors_origin "http://localhost:8080";
        }
        if ($http_origin ~ "^http://localhost:3000$") {
            set $cors_origin "http://localhost:3000";
        }
        
        # Apply CORS headers if origin is allowed
        add_header 'Access-Control-Allow-Origin' '$cors_origin' always;
        add_header 'Access-Control-Allow-Credentials' 'true' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS, PATCH' always;
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,Accept,Origin' always;
        add_header 'Access-Control-Max-Age' '86400' always;
        
        # Handle preflight requests
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '$cors_origin' always;
            add_header 'Access-Control-Allow-Credentials' 'true' always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS, PATCH' always;
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,Accept,Origin' always;
            add_header 'Access-Control-Max-Age' '86400' always;
            add_header 'Content-Type' 'text/plain; charset=utf-8';
            add_header 'Content-Length' 0;
            return 204;
        }
    }
    
    # Health check endpoint
    location /health {
        proxy_pass http://platform-gateway:8085/health;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Add health check headers
        add_header 'Cache-Control' 'no-cache, no-store, must-revalidate';
        add_header 'Pragma' 'no-cache';
        add_header 'Expires' '0';
    }
    
    # Default response for other paths
    location / {
        return 200 'Platform services are running';
        add_header Content-Type text/plain;
        add_header 'Cache-Control' 'no-cache, no-store, must-revalidate';
        add_header 'Pragma' 'no-cache';
        add_header 'Expires' '0';
    }
}