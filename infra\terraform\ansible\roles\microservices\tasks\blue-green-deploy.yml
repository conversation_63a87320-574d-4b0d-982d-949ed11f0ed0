---
- name: Deploy services to {{ target_color }} environment
  community.docker.docker_compose_v2:
    project_src: "{{ microservices_root }}"
    project_name: "microservices-{{ target_color }}"
    state: present
    remove_orphans: yes
    services: "{{ services.keys() | list }}"
  environment:
    DEPLOYMENT_COLOR: "{{ target_color }}"
  register: deployment_result
  tags: ['blue-green', 'deploy']

- name: Wait for {{ target_color }} services to be healthy
  uri:
    url: "http://localhost:{{ item.value.port }}-{{ target_color }}{{ item.value.health_check.path }}"
    method: GET
    status_code: 200
  retries: 30
  delay: 10
  loop: "{{ services | dict2items }}"
  when: item.value.health_check is defined
  tags: ['blue-green', 'health-check']

- name: Update deployment color label
  copy:
    content: "{{ target_color }}"
    dest: "/var/lib/microservices/deployment-color"
    mode: '0644'
  tags: ['blue-green', 'metadata']