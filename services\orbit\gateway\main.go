package main

import (
	"log"
	"os"

	"github.com/gin-gonic/gin"
	"github.com/TwoDotAi/mono/services/orbit/gateway/config"
	"github.com/TwoDotAi/mono/services/orbit/gateway/handlers"
	"github.com/TwoDotAi/mono/services/orbit/gateway/middleware"
	"github.com/TwoDotAi/mono/services/orbit/gateway/router"
	"github.com/TwoDotAi/mono/shared/go/logging"
)

func main() {
	// Load configuration
	cfg := config.Load()

	// Initialize Gin router without default middleware
	r := gin.New()

	// Apply custom middleware (replaces gin.Default() middleware)
	r.Use(gin.Recovery()) // Keep recovery middleware
	r.Use(middleware.CORS())
	r.Use(logging.GinLogger("GATEWAY")) // Shared colorful logger
	r.Use(logging.ErrorHandler("GATEWAY")) // Shared error handler

	// Health check endpoint (support both GET and HEAD)
	r.GET("/health", handlers.HealthCheck)
	r.HEAD("/health", handlers.HealthCheck)

	// Initialize service routes
	router.SetupRoutes(r, cfg)

	// Start server
	port := os.Getenv("PORT")
	if port == "" {
		port = "8085"
	}

	logging.StartupMessage("GATEWAY", port)
	if err := r.Run("0.0.0.0:" + port); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}