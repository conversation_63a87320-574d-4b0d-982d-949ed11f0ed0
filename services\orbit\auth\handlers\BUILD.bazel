load("@rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "handlers",
    srcs = ["auth.go"],
    importpath = "github.com/TwoDotAi/mono/services/orbit/auth/handlers",
    visibility = ["//visibility:public"],
    deps = [
        "//shared/go/logging",
        "//services/orbit/auth/config",
        "//services/orbit/auth/database",
        "//services/orbit/auth/middleware",
        "//generated/orbit:openapi_server",
        "@com_github_gin_gonic_gin//:gin",
        "@com_github_golang_jwt_jwt_v5//:jwt",
        "@com_github_google_uuid//:uuid",
        "@com_github_oapi_codegen_runtime//types",
        "@org_golang_x_crypto//bcrypt",
        "@org_uber_go_zap//:zap",
    ],
)

go_test(
    name = "handlers_test",
    srcs = ["auth_test.go"],
    embed = [":handlers"],
    deps = [
        "//services/orbit/auth/middleware",
        "@com_github_gin_gonic_gin//:gin",
        "@com_github_golang_jwt_jwt_v5//:jwt",
    ],
)
