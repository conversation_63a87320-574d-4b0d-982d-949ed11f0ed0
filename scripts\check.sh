#!/bin/bash

# check.sh - Run platform validation checks
# This script runs various bazel commands to validate the platform build
# Only displays output if there are errors

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to run a command silently and only show output on error
run_check() {
    local description="$1"
    local command="$2"
    
    echo -n "Checking $description... "
    
    # Capture both stdout and stderr
    if output=$(eval "$command" 2>&1); then
        echo -e "${GREEN}✓${NC}"
    else
        echo -e "${RED}✗${NC}"
        echo -e "${RED}Error in $description:${NC}"
        echo "$output"
        echo
        exit 1
    fi
}

echo "🔍 Running platform validation checks..."
echo

# Build checks
run_check "platform build" "bazel build //platform:platform"
run_check "gateway build" "bazel build //platform/gateway:gateway"
run_check "auth service build" "bazel build //platform/auth:auth_service"
run_check "CRM backend build" "bazel build //platform/crm_backend:crm_backend"
run_check "web app build" "bazel build //platform/web:build"

# API client generation
run_check "API client generation" "bazel build //platform/api-clients:all_clients"

# Database checks
run_check "database migrations" "bazel build //platform/db:migrations"
run_check "database start script" "bazel build //platform/db:start"
run_check "database stop script" "bazel build //platform/db:stop"
run_check "database test data script" "bazel build //platform/db:test_data"
run_check "database clean script" "bazel build //platform/db:clean"

# Service run checks (validate they can start)
# Note: These are basic build tests since timeout isn't available on macOS
run_check "web dev server build" "bazel build //platform/web:dev"
run_check "auth service build" "bazel build //platform/auth:auth_service"
run_check "gateway service build" "bazel build //platform/gateway:gateway"
run_check "CRM backend service build" "bazel build //platform/crm_backend:crm_backend"

# Test checks (disabled until tests are properly configured)
# if bazel query "//platform/..." --output=label 2>/dev/null | grep -q "_test$"; then
#     run_check "platform tests" "bazel test //platform/..."
# fi

# Docker image builds (if configured)
if bazel query "//platform/..." --output=label 2>/dev/null | grep -q "_image$"; then
    run_check "Docker images" "bazel build //platform/...image"
fi

echo
echo -e "${GREEN}✅ All platform checks passed!${NC}"
echo
echo "🚀 Platform is ready for:"
echo "  • bazel run //platform:all    (start all services)"
echo "  • docker-compose up -d        (start with Docker)"
echo "  • bazel run //platform/db:test_data    (load test data)"