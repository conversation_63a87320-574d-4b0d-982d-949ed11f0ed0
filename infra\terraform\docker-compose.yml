version: '3.8'

services:
  # Database (not included in VM deployment - uses Cloud SQL)
  
  # Nginx reverse proxy
  nginx:
    image: nginx:alpine
    container_name: platform-nginx
    ports:
      - "80:80"
    environment:
      - NGINX_HOST=_
      - NGINX_PORT=80
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf:ro
      - /usr/share/nginx/html:/usr/share/nginx/html
    depends_on:
      - gateway
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - platform-network

  # Gateway Service
  gateway:
    image: ${REGISTRY_URL}/gateway:latest
    container_name: platform-gateway
    ports:
      - "8085:8085"
    environment:
      - PORT=8085
      - GIN_MODE=release
      - AUTH_SERVICE_URL=http://platform-auth:8004
      - CRM_BACKEND_URL=http://platform-crm-backend:8003
    depends_on:
      - auth
      - crm-backend
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8085/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - platform-network

  # Auth Service
  auth:
    image: ${REGISTRY_URL}/auth:latest
    container_name: platform-auth
    ports:
      - "8004:8004"
    environment:
      - PORT=8004
      - GIN_MODE=release
      - DATABASE_URL=${DATABASE_URL}
      - JWT_SECRET=${JWT_SECRET:-dev-jwt-secret-change-in-production}
      - GOOGLE_OAUTH_CLIENT_ID=${GOOGLE_OAUTH_CLIENT_ID}
      - GOOGLE_OAUTH_CLIENT_SECRET=${GOOGLE_OAUTH_CLIENT_SECRET}
      - GOOGLE_OAUTH_REDIRECT_URL=${GOOGLE_OAUTH_REDIRECT_URL}
      - OAUTH_STATE_SECRET=${OAUTH_STATE_SECRET}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8004/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - platform-network

  # CRM Backend Service
  crm-backend:
    image: ${REGISTRY_URL}/crm-backend:latest
    container_name: platform-crm-backend
    ports:
      - "8003:8003"
    environment:
      - PORT=8003
      - GIN_MODE=release
      - DATABASE_URL=${DATABASE_URL}
      - AUTH_SERVICE_URL=http://platform-auth:8004
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8003/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - platform-network

networks:
  platform-network:
    driver: bridge

volumes:
  # No persistent volumes needed for stateless services
  # Database is managed by Cloud SQL