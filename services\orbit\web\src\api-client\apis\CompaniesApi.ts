/* tslint:disable */
/* eslint-disable */
/**
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  Company,
  CompanyCreate,
  CompanyDetails,
  CompanyStatus,
  CompanyStatusUpdateRequest,
  CompanyUpdate,
} from '../models/index';
import {
    CompanyFromJSON,
    CompanyToJSON,
    CompanyCreateFromJSON,
    CompanyCreateToJSON,
    CompanyDetailsFromJSON,
    CompanyDetailsToJSON,
    CompanyStatusFromJSON,
    CompanyStatusToJSON,
    CompanyStatusUpdateRequestFromJSON,
    CompanyStatusUpdateRequestToJSON,
    CompanyUpdateFromJSON,
    CompanyUpdateToJSON,
} from '../models/index';

export interface CompaniesGetRequest {
    statusId?: string;
    includeDeleted?: boolean;
}

export interface CompaniesIdDeleteRequest {
    id: string;
}

export interface CompaniesIdGetRequest {
    id: string;
}

export interface CompaniesIdPutRequest {
    id: string;
    companyUpdate: CompanyUpdate;
}

export interface CompaniesIdStatusPutRequest {
    id: string;
    companyStatusUpdateRequest: CompanyStatusUpdateRequest;
}

export interface CompaniesPostRequest {
    companyCreate: CompanyCreate;
}

/**
 * 
 */
export class CompaniesApi extends runtime.BaseAPI {

    /**
     * Retrieve a list of all active companies
     * List companies
     */
    async companiesGetRaw(requestParameters: CompaniesGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Array<Company>>> {
        const queryParameters: any = {};

        if (requestParameters['statusId'] != null) {
            queryParameters['status_id'] = requestParameters['statusId'];
        }

        if (requestParameters['includeDeleted'] != null) {
            queryParameters['include_deleted'] = requestParameters['includeDeleted'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("bearerAuth", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/companies`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => jsonValue.map(CompanyFromJSON));
    }

    /**
     * Retrieve a list of all active companies
     * List companies
     */
    async companiesGet(requestParameters: CompaniesGetRequest = {}, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Array<Company>> {
        const response = await this.companiesGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Soft delete a company (sets is_deleted to true)
     * Delete company
     */
    async companiesIdDeleteRaw(requestParameters: CompaniesIdDeleteRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<void>> {
        if (requestParameters['id'] == null) {
            throw new runtime.RequiredError(
                'id',
                'Required parameter "id" was null or undefined when calling companiesIdDelete().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("bearerAuth", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/companies/{id}`.replace(`{${"id"}}`, encodeURIComponent(String(requestParameters['id']))),
            method: 'DELETE',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.VoidApiResponse(response);
    }

    /**
     * Soft delete a company (sets is_deleted to true)
     * Delete company
     */
    async companiesIdDelete(requestParameters: CompaniesIdDeleteRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void> {
        await this.companiesIdDeleteRaw(requestParameters, initOverrides);
    }

    /**
     * Retrieve a specific company by ID
     * Get company
     */
    async companiesIdGetRaw(requestParameters: CompaniesIdGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<CompanyDetails>> {
        if (requestParameters['id'] == null) {
            throw new runtime.RequiredError(
                'id',
                'Required parameter "id" was null or undefined when calling companiesIdGet().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("bearerAuth", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/companies/{id}`.replace(`{${"id"}}`, encodeURIComponent(String(requestParameters['id']))),
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => CompanyDetailsFromJSON(jsonValue));
    }

    /**
     * Retrieve a specific company by ID
     * Get company
     */
    async companiesIdGet(requestParameters: CompaniesIdGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<CompanyDetails> {
        const response = await this.companiesIdGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Update an existing company
     * Update company
     */
    async companiesIdPutRaw(requestParameters: CompaniesIdPutRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Company>> {
        if (requestParameters['id'] == null) {
            throw new runtime.RequiredError(
                'id',
                'Required parameter "id" was null or undefined when calling companiesIdPut().'
            );
        }

        if (requestParameters['companyUpdate'] == null) {
            throw new runtime.RequiredError(
                'companyUpdate',
                'Required parameter "companyUpdate" was null or undefined when calling companiesIdPut().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("bearerAuth", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/companies/{id}`.replace(`{${"id"}}`, encodeURIComponent(String(requestParameters['id']))),
            method: 'PUT',
            headers: headerParameters,
            query: queryParameters,
            body: CompanyUpdateToJSON(requestParameters['companyUpdate']),
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => CompanyFromJSON(jsonValue));
    }

    /**
     * Update an existing company
     * Update company
     */
    async companiesIdPut(requestParameters: CompaniesIdPutRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Company> {
        const response = await this.companiesIdPutRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Update the status of a company
     * Update company status
     */
    async companiesIdStatusPutRaw(requestParameters: CompaniesIdStatusPutRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<void>> {
        if (requestParameters['id'] == null) {
            throw new runtime.RequiredError(
                'id',
                'Required parameter "id" was null or undefined when calling companiesIdStatusPut().'
            );
        }

        if (requestParameters['companyStatusUpdateRequest'] == null) {
            throw new runtime.RequiredError(
                'companyStatusUpdateRequest',
                'Required parameter "companyStatusUpdateRequest" was null or undefined when calling companiesIdStatusPut().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("bearerAuth", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/companies/{id}/status`.replace(`{${"id"}}`, encodeURIComponent(String(requestParameters['id']))),
            method: 'PUT',
            headers: headerParameters,
            query: queryParameters,
            body: CompanyStatusUpdateRequestToJSON(requestParameters['companyStatusUpdateRequest']),
        }, initOverrides);

        return new runtime.VoidApiResponse(response);
    }

    /**
     * Update the status of a company
     * Update company status
     */
    async companiesIdStatusPut(requestParameters: CompaniesIdStatusPutRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void> {
        await this.companiesIdStatusPutRaw(requestParameters, initOverrides);
    }

    /**
     * Create a new company
     * Create company
     */
    async companiesPostRaw(requestParameters: CompaniesPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Company>> {
        if (requestParameters['companyCreate'] == null) {
            throw new runtime.RequiredError(
                'companyCreate',
                'Required parameter "companyCreate" was null or undefined when calling companiesPost().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("bearerAuth", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/companies`,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: CompanyCreateToJSON(requestParameters['companyCreate']),
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => CompanyFromJSON(jsonValue));
    }

    /**
     * Create a new company
     * Create company
     */
    async companiesPost(requestParameters: CompaniesPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Company> {
        const response = await this.companiesPostRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Retrieve all company statuses ordered by pipeline order
     * List company statuses
     */
    async companyStatusesGetRaw(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Array<CompanyStatus>>> {
        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("bearerAuth", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/company-statuses`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => jsonValue.map(CompanyStatusFromJSON));
    }

    /**
     * Retrieve all company statuses ordered by pipeline order
     * List company statuses
     */
    async companyStatusesGet(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Array<CompanyStatus>> {
        const response = await this.companyStatusesGetRaw(initOverrides);
        return await response.value();
    }

}
