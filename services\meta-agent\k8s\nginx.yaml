apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx
  namespace: ai-agent-platform
  labels:
    app: nginx
    component: proxy
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: nginx
  template:
    metadata:
      labels:
        app: nginx
        component: proxy
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 101
        fsGroup: 101
      containers:
      - name: nginx
        image: nginx:1.24-alpine
        ports:
        - name: http
          containerPort: 80
          protocol: TCP
        - name: https
          containerPort: 443
          protocol: TCP
        volumeMounts:
        - name: nginx-config
          mountPath: /etc/nginx/nginx.conf
          subPath: nginx.conf
          readOnly: true
        - name: cache
          mountPath: /var/cache/nginx
        - name: run
          mountPath: /var/run
        - name: logs
          mountPath: /var/log/nginx
        # Uncomment when SSL certificates are available
        # - name: tls-certs
        #   mountPath: /etc/ssl/certs/
        #   readOnly: true
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 3
          failureThreshold: 3
        lifecycle:
          preStop:
            exec:
              command:
              - /bin/sh
              - -c
              - sleep 15 && nginx -s quit
      volumes:
      - name: nginx-config
        configMap:
          name: nginx-config
          items:
          - key: nginx.conf
            path: nginx.conf
      - name: cache
        emptyDir: {}
      - name: run
        emptyDir: {}
      - name: logs
        emptyDir: {}
      # Uncomment when SSL certificates are available
      # - name: tls-certs
      #   secret:
      #     secretName: tls-secret
      nodeSelector:
        kubernetes.io/arch: amd64

---
apiVersion: v1
kind: Service
metadata:
  name: nginx
  namespace: ai-agent-platform
  labels:
    app: nginx
    component: proxy
spec:
  type: LoadBalancer
  ports:
  - name: http
    port: 80
    targetPort: 80
    protocol: TCP
  - name: https
    port: 443
    targetPort: 443
    protocol: TCP
  selector:
    app: nginx
  sessionAffinity: None
  externalTrafficPolicy: Local

---
# Ingress for additional routing control (optional)
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ai-agent-platform-ingress
  namespace: ai-agent-platform
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    # Enable cert-manager for automatic SSL certificates
    # cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  ingressClassName: nginx
  # Uncomment when SSL certificates are configured
  # tls:
  # - hosts:
  #   - your-domain.com
  #   - www.your-domain.com
  #   secretName: tls-secret
  rules:
  - host: your-domain.com
    http:
      paths:
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: ai-agent-backend
            port:
              number: 8000
      - path: /
        pathType: Prefix
        backend:
          service:
            name: ai-agent-frontend
            port:
              number: 3000
  - host: www.your-domain.com
    http:
      paths:
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: ai-agent-backend
            port:
              number: 8000
      - path: /
        pathType: Prefix
        backend:
          service:
            name: ai-agent-frontend
            port:
              number: 3000

---
# Pod Disruption Budget
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: nginx-pdb
  namespace: ai-agent-platform
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: nginx

---
# Network Policy for Nginx
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: nginx-netpol
  namespace: ai-agent-platform
spec:
  podSelector:
    matchLabels:
      app: nginx
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from: []  # Allow from anywhere (internet traffic)
    ports:
    - protocol: TCP
      port: 80
    - protocol: TCP
      port: 443
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: ai-agent-backend
    ports:
    - protocol: TCP
      port: 8000
  - to:
    - podSelector:
        matchLabels:
          app: ai-agent-frontend
    ports:
    - protocol: TCP
      port: 3000
  - to: []  # Allow DNS resolution
    ports:
    - protocol: UDP
      port: 53

---
# ConfigMap for Nginx health check endpoint
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-health-config
  namespace: ai-agent-platform
data:
  health.conf: |
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }