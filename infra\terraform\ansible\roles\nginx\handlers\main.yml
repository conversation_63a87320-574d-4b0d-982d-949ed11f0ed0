---
- name: reload nginx container
  docker_container:
    name: nginx
    state: started
    restart: no
    command: nginx -s reload
  tags: ['nginx']

- name: restart nginx container
  community.docker.docker_compose_v2:
    project_src: "{{ microservices_root }}"
    files:
      - nginx-compose.yml
    restarted: yes
  tags: ['nginx']

- name: test nginx config
  docker_container:
    name: nginx-test
    image: nginx:alpine
    command: nginx -t
    volumes:
      - "{{ microservices_root }}/nginx/nginx.conf:/etc/nginx/nginx.conf:ro"
      - "{{ microservices_root }}/nginx/upstream.conf:/etc/nginx/conf.d/upstream.conf:ro"
    state: started
    detach: no
    cleanup: yes
  tags: ['nginx', 'test']