# 🚀 Microservices Infrastructure Automation

**Complete automation for deploying microservices to Google Cloud Platform using Terraform + Ansible on Container-Optimized OS.**

## ✨ What's Included

- 🎯 **Fully Automated Deployments** - Single command deploys everything
- 🤖 **Container-Optimized OS** - Secure, minimal Google-managed OS  
- 🔄 **Zero-Downtime Deployments** - Blue-green deployment strategy
- 🌍 **Environment Auto-Detection** - Smart configuration based on directory
- 🐳 **Automated Docker Builds** - Images built and pushed automatically
- 🛡️ **Security by Default** - IAM, secrets, networking best practices

## 🏗️ Architecture

```
Load Balancer → Container-Optimized OS → Docker Containers → Cloud SQL
     ↓              ↓                      ↓
   SSL/TLS     Ansible-Managed        Gateway/Auth/CRM
```

### Infrastructure Components

| Component | Technology | Purpose |
|-----------|------------|---------|
| **Compute** | Container-Optimized OS | Secure, minimal OS optimized for containers |
| **Orchestration** | Ansible + Docker | Service deployment and configuration |
| **Database** | Cloud SQL PostgreSQL | Managed database with private networking |
| **Networking** | VPC + Load Balancer | Secure networking with SSL termination |
| **Registry** | Artifact Registry | Private Docker image storage |
| **Secrets** | Secret Manager | Secure credential management |

### Service Stack

| Service | Port | Purpose |
|---------|------|---------|
| **Gateway** | 80, 8085 | API gateway and load balancing |
| **Auth Service** | 8004 | Authentication and OAuth |
| **CRM Backend** | 8003 | Core business logic |

## 📁 Directory Structure

```
terraform/
├── environments/          # Environment-specific configurations
│   ├── dev/              # 👈 Development environment
│   ├── staging/          # Staging environment  
│   └── prod/             # Production environment
├── ansible/              # 🤖 Service automation
│   ├── playbooks/        # Deployment scripts
│   ├── roles/           # Service configuration
│   ├── inventory/       # Dynamic host discovery
│   └── setup-env.sh     # Environment auto-setup
├── modules/              # Reusable infrastructure components
│   ├── compute/         # Container-optimized VM instances
│   ├── network/         # VPC and security groups
│   ├── database/        # Cloud SQL with private networking
│   ├── registry/        # Docker image registry
│   └── loadbalancer/    # Load balancing and SSL
└── scripts/             # Build and deployment helpers
```

## 🚀 Deployment Flow: Code to Production

### Prerequisites

1. **Install Tools**:
   ```bash
   # Install required tools (if not already installed)
   brew install terraform ansible gcloud
   ```

2. **GCP Authentication**:
   ```bash
   # Authenticate with GCP
   gcloud auth login
   gcloud auth application-default login
   ```

### 🔄 Complete Deployment Workflow

**Code Changes → Dev Environment Deployment**

```bash
# 1. Make code changes to your services
# Edit files in platform/gateway/, platform/auth/, platform/crm_backend/, etc.

# 2. Build, push, and deploy in one command
bazel run //terraform:build_and_push_backend_dev
bazel run //terraform:deploy_to_instance_dev

# 3. Deploy frontend updates (if needed)
bazel run //terraform:deploy_web_dev
```

### 🏗️ Initial Environment Setup (One-time)

If this is your first deployment to a new environment:

```bash
# Navigate to dev environment
cd terraform/environments/dev

# Auto-configure environment (detects project, region, etc.)
source setup.sh

# Deploy infrastructure + services
tf-apply
```

**What happens during initial setup:**

1. 🏗️ **Infrastructure Creation** (~5-8 minutes)
   - Creates VPC, subnets, firewall rules
   - Sets up Cloud SQL PostgreSQL database
   - Creates Container-Optimized OS VM instance
   - Configures load balancer with SSL

2. 🤖 **Service Deployment** (~3-5 minutes)
   - Configures Docker authentication
   - Pulls images from Artifact Registry
   - Deploys all microservices
   - Sets up health monitoring

3. ✅ **Service Verification**
   - Tests service connectivity
   - Verifies load balancer routing
   - Confirms health endpoints

**Total initial setup time: ~10-15 minutes**

### 🔄 Daily Development Workflow

For ongoing development after initial setup:

```bash
# Option A: Update backend services only
bazel run //terraform:build_and_push_backend_dev    # Build & push new images
bazel run //terraform:deploy_to_instance_dev        # Deploy to running infrastructure

# Option B: Update frontend only  
bazel run //terraform:deploy_web_dev      # Build & deploy React app

# Option C: Update everything
bazel run //terraform:build_and_push_backend_dev && \
bazel run //terraform:deploy_to_instance_dev && \
bazel run //terraform:deploy_web_dev
```

### 📊 Verify Deployment

```bash
# Get load balancer IP
cd terraform/environments/dev && terraform output load_balancer_ip

# Test backend services
curl http://<load-balancer-ip>/health

# Test frontend (via load balancer domain)
curl https://internal.dev.twodot.ai

# Check service status on compute instance
gcloud compute ssh platform-platform-dev --zone=australia-southeast1-a \
  --command="sudo docker ps"
```

## 🏭 Production Environment Ready

The production environment is now fully configured and ready for deployment:

### Production Features

| Feature | Setting | Description |
|---------|---------|-------------|
| **Instance Size** | e2-standard-2 | Production-grade compute |
| **Database** | db-n1-standard-1 | High-performance database |
| **High Availability** | ✅ Regional | Database with failover |
| **SSL Certificates** | ✅ Managed | Google-managed SSL |
| **Deletion Protection** | ✅ Enabled | Protects production data |
| **Migration Automation** | ❌ Disabled | Requires manual approval |
| **Backup Automation** | ✅ Enabled | Automated daily backups |

### Deploy to Production

```bash
# Navigate to production environment
cd terraform/environments/prod

# Copy and configure production settings
cp terraform.tfvars.example terraform.tfvars
# Edit terraform.tfvars with your production values

# Initialize and deploy
terraform init
terraform plan
terraform apply
```

### Production Security

- **Restricted Access**: Admin IP ranges must be configured
- **SSL/TLS Required**: Valid domains must be provided
- **Database Protection**: Deletion protection enabled
- **Manual Migrations**: Database changes require manual approval
- **Audit Logging**: Enabled for compliance

### Required Configuration

Before deploying production, ensure you have:

1. **Valid SSL domains** configured in `ssl_domains`
2. **Admin IP ranges** specified (never use `0.0.0.0/0`)
3. **GCS state bucket** created for Terraform state
4. **Production project** with appropriate permissions

## 🆕 Creating New Environments

### Quick Copy Method

```bash
# Copy dev environment as template
cp -r terraform/environments/dev terraform/environments/staging

# Update configuration
cd terraform/environments/staging
```

Edit `terraform.tfvars`:

```hcl
# Staging Environment Configuration
project_id   = "your-project-staging"
project_name = "platform"
region       = "us-central1"
zone         = "us-central1-a"

# SSL domains for staging
ssl_domains = ["staging.example.com", "api.staging.example.com"]

# Staging-specific settings
enable_monitoring = true
enable_backup_automation = true
min_instance_count = 1
max_instance_count = 3
```

### Deploy New Environment

```bash
# Set up environment
source setup.sh

# Initialize Terraform
terraform init

# Deploy infrastructure + services
tf-apply
```

## 🔄 Advanced Operations

### Health Monitoring

```bash
# Check service status on compute instance
gcloud compute ssh platform-platform-dev --zone=australia-southeast1-a \
  --command="sudo docker ps"

# View service logs
gcloud compute ssh platform-platform-dev --zone=australia-southeast1-a \
  --command="sudo docker logs platform-gateway"

# Test API endpoints
curl https://internal.dev.twodot.ai/api/v1/health
```

### Rollback (If Needed)

```bash
# Deploy previous image versions
bazel run //terraform:build_and_push_backend_dev    # Use git checkout to previous version first
bazel run //terraform:deploy_to_instance_dev
```

## 🎯 Available Bazel Commands

### Backend Services

```bash
bazel run //terraform:build_and_push_backend_dev    # Build & push backend images
bazel run //terraform:deploy_to_instance_dev        # Deploy to compute instance
```

### Frontend Application

```bash
bazel run //terraform:deploy_web_dev      # Build & deploy React app
```

### Infrastructure Management (from terraform/environments/dev)

```bash
tf-plan                     # Plan infrastructure changes
tf-apply                    # Deploy infrastructure + services  
tf-destroy                  # Destroy environment
```

### Testing & Validation

```bash
bazel run //terraform:test_prod_config    # Test production environment configuration
```

## 🔧 Advanced Configuration

### Environment Variables

Customize deployments in `terraform/ansible/vars/{environment}.yml`:

```yaml
# terraform/ansible/vars/dev.yml
services:
  gateway:
    image: "australia-southeast1-docker.pkg.dev/project-id/platform-docker/gateway:latest"
    environment:
      LOG_LEVEL: "debug"
      CORS_ALLOWED_ORIGINS: "http://localhost:8080,http://localhost:3000"

  auth:
    image: "australia-southeast1-docker.pkg.dev/project-id/platform-docker/auth:latest"
    environment:
      LOG_LEVEL: "debug"
      JWT_EXPIRY: "24h"

  crm_backend:
    image: "australia-southeast1-docker.pkg.dev/project-id/platform-docker/crm-backend:latest"
    environment:
      LOG_LEVEL: "info"
      ENABLE_SWAGGER: "true"
```

### Service Versions

Control specific versions in `terraform.tfvars`:

```hcl
service_versions = {
  gateway = "v1.2.3"
  auth    = "v1.2.3"  
  backend = "v1.2.3"
}
```

### Blue-Green Deployments

```bash
# Deploy to specific color
ansible-playbook -i inventory/terraform-dev.yml playbooks/deploy-services-cos.yml \
  --extra-vars "deployment_color=green"

# Switch traffic
ansible-playbook -i inventory/terraform-dev.yml playbooks/switch-traffic.yml
```

## 🛡️ Security Features

### Built-in Security

- **Container-Optimized OS**: Minimal attack surface, automatic security updates
- **Private Networking**: Services communicate via private IPs only
- **IAM Roles**: Least privilege access with service accounts
- **Secret Management**: Credentials stored in GCP Secret Manager
- **Firewall Rules**: Only necessary ports exposed
- **SSL/TLS**: Automatic certificate management

### Network Security

```
Internet → Load Balancer → Private Subnet → Container-Optimized OS → Docker Network
         (SSL Termination)   (Firewall)        (Minimal OS)      (Isolated Services)
```

## 📊 Monitoring & Health Checks

### Automated Health Monitoring

- **Service Health Endpoints**: `/health` checks for all services
- **Load Balancer Health Checks**: Automatic failover
- **Container Restart Policy**: Auto-restart on failure
- **Deployment Verification**: Health checks during deployment

### Logging

- **Container Logs**: JSON structured logging
- **Ansible Logs**: Deployment automation logs in `terraform/ansible/logs/`
- **Cloud Logging**: Centralized log aggregation
- **Health Check Logs**: Load balancer monitoring

## ⚠️ Common Gotchas

### Load Balancer Backend Service Configuration

**Issue**: 502 Bad Gateway errors when accessing API endpoints through the load balancer.

**Root Cause**: The backend service in the compute module was configured to target port 80 (expecting nginx as a reverse proxy), but the microservices run directly on their assigned ports (8085 for gateway, 8004 for auth, 8003 for CRM backend).

**Symptoms**:
- Frontend receives 502 Bad Gateway errors for API calls like `/api/v1/auth/signin`
- Load balancer health checks fail
- Direct API calls to compute instance work fine
- Services show as healthy in Docker containers

**Fix Applied**:
```hcl
# In terraform/modules/compute/main.tf

# BEFORE (Incorrect):
resource "google_compute_health_check" "gateway_health_check" {
  http_health_check {
    port         = 80              # ❌ Wrong - no service on port 80
    request_path = "/health"
  }
}

resource "google_compute_backend_service" "platform_backend" {
  port_name = "http"               # ❌ Wrong - targets port 80
}

# AFTER (Fixed):
resource "google_compute_health_check" "gateway_health_check" {
  http_health_check {
    port         = var.gateway_port  # ✅ Correct - targets 8085
    request_path = "/health"
  }
}

resource "google_compute_backend_service" "platform_backend" {
  port_name = "gateway"            # ✅ Correct - uses gateway named port
}
```

**Prevention**: Always verify that health checks and backend services target the actual service ports, not assumed proxy ports.

---

## 🆘 Troubleshooting

### Common Issues

#### 1. Docker Authentication Errors

```bash
# Re-build and push images
bazel run //terraform:build_and_push_backend_dev

# Check service account permissions
gcloud projects get-iam-policy <project-id>
```

#### 2. Service Startup Issues

```bash
# Check container logs
ansible all -i inventory/terraform-dev.yml -m command -a "docker logs auth"

# Restart services
ansible-deploy

# Check network connectivity
ansible all -i inventory/terraform-dev.yml -m command -a "docker network ls"
```

#### 3. Environment Setup Issues

```bash
# Reset environment detection
cd terraform/environments/dev
source setup.sh

# Verify environment variables
echo "Project: $GCP_PROJECT_ID"
echo "Region: $GCP_REGION" 
echo "SSH Key: $ANSIBLE_SSH_KEY"
```

#### 4. Database Connection Issues

```bash
# Check Cloud SQL status
gcloud sql instances list

# Verify private IP connectivity
ansible all -i inventory/terraform-dev.yml -m command -a "nslookup <database-ip>"

# Check service environment variables
ansible all -i inventory/terraform-dev.yml -m command -a "docker exec auth env | grep DATABASE"
```

### Debug Mode

Enable verbose output:

```bash
# Verbose Ansible output
export ANSIBLE_VERBOSITY=2
ansible-deploy

# Terraform debug mode
export TF_LOG=DEBUG
tf-apply
```

### Integration Tests

Run comprehensive tests:

```bash
# Full integration test suite
ansible-integration-test

# Test specific components
ansible-playbook -i inventory/terraform-dev.yml playbooks/deploy-services-cos.yml --check --diff
```

## 🎉 Success Metrics

After deployment, you should have:

✅ **Secure Infrastructure**: Container-Optimized OS with minimal attack surface  
✅ **Automated Deployments**: Single-command infrastructure + service deployment  
✅ **Zero-Downtime Updates**: Blue-green deployment with health checks  
✅ **Monitoring**: Built-in health checks and logging  
✅ **Scalability**: Easy environment replication  
✅ **Security**: Private networking, IAM, and secret management  
✅ **Reliability**: Auto-restart, rollback capabilities  

**Your microservices platform is production-ready!** 🚀

## 📚 Additional Resources

- **Platform Documentation**: `platform/README.md` - How to build and test services locally
- **API Documentation**: `platform/rest-api/openapi.yaml` - API specifications
- **Ansible Playbooks**: `terraform/ansible/playbooks/` - Deployment automation details
- **Service Architecture**: `platform/*/README.md` - Individual service documentation

---

**Questions?** Run `ansible-integration-test` to diagnose issues, or check the troubleshooting section above.