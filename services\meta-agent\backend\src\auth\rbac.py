"""
Role-Based Access Control (RBAC) Service
Advanced permission management system
"""

from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Set
from enum import Enum
from sqlalchemy.orm import Session, joinedload
from fastapi import HTTPException, status
import structlog

from config.settings import settings
from database.models import User, Role, Permission, UserRole, RolePermission
from database.connection import get_db

logger = structlog.get_logger(__name__)


class ResourceType(str, Enum):
    """Resource types for permissions"""
    AGENTS = "agents"
    ORCHESTRATIONS = "orchestrations"
    TASKS = "tasks"
    USERS = "users"
    ROLES = "roles"
    PERMISSIONS = "permissions"
    SYSTEM = "system"
    AI_MODELS = "ai_models"
    GENERATIONS = "generations"
    MFA = "mfa"


class ActionType(str, Enum):
    """Action types for permissions"""
    CREATE = "create"
    READ = "read"
    UPDATE = "update"
    DELETE = "delete"
    EXECUTE = "execute"
    MANAGE = "manage"
    ADMIN = "admin"


class SystemRoles:
    """System-defined roles"""
    SUPERUSER = "superuser"
    ADMIN = "admin"
    USER = "user"
    AGENT_MANAGER = "agent_manager"
    ORCHESTRATOR = "orchestrator"
    VIEWER = "viewer"
    GUEST = "guest"


class SystemPermissions:
    """System-defined permissions"""
    # Agent permissions
    AGENTS_CREATE = "agents:create"
    AGENTS_READ = "agents:read"
    AGENTS_UPDATE = "agents:update"
    AGENTS_DELETE = "agents:delete"
    AGENTS_EXECUTE = "agents:execute"
    AGENTS_MANAGE = "agents:manage"
    
    # Orchestration permissions
    ORCHESTRATIONS_CREATE = "orchestrations:create"
    ORCHESTRATIONS_READ = "orchestrations:read"
    ORCHESTRATIONS_UPDATE = "orchestrations:update"
    ORCHESTRATIONS_DELETE = "orchestrations:delete"
    ORCHESTRATIONS_EXECUTE = "orchestrations:execute"
    ORCHESTRATIONS_MANAGE = "orchestrations:manage"
    
    # Task permissions
    TASKS_CREATE = "tasks:create"
    TASKS_READ = "tasks:read"
    TASKS_UPDATE = "tasks:update"
    TASKS_DELETE = "tasks:delete"
    TASKS_EXECUTE = "tasks:execute"
    TASKS_MANAGE = "tasks:manage"
    
    # User permissions
    USERS_CREATE = "users:create"
    USERS_READ = "users:read"
    USERS_UPDATE = "users:update"
    USERS_DELETE = "users:delete"
    USERS_MANAGE = "users:manage"
    
    # Role permissions
    ROLES_CREATE = "roles:create"
    ROLES_READ = "roles:read"
    ROLES_UPDATE = "roles:update"
    ROLES_DELETE = "roles:delete"
    ROLES_MANAGE = "roles:manage"
    
    # System permissions
    SYSTEM_ADMIN = "system:admin"
    SYSTEM_MONITOR = "system:monitor"
    SYSTEM_CONFIG = "system:config"
    
    # AI Model permissions
    AI_MODELS_CREATE = "ai_models:create"
    AI_MODELS_READ = "ai_models:read"
    AI_MODELS_UPDATE = "ai_models:update"
    AI_MODELS_DELETE = "ai_models:delete"
    AI_MODELS_MANAGE = "ai_models:manage"
    
    # Generation permissions
    GENERATIONS_CREATE = "generations:create"
    GENERATIONS_READ = "generations:read"
    GENERATIONS_UPDATE = "generations:update"
    GENERATIONS_DELETE = "generations:delete"
    GENERATIONS_MANAGE = "generations:manage"


class RBACService:
    """Role-Based Access Control service"""
    
    def __init__(self):
        self._permission_cache: Dict[str, Set[str]] = {}
        self._cache_ttl = timedelta(minutes=15)
        self._cache_timestamps: Dict[str, datetime] = {}
        logger.info("RBAC service initialized")
    
    async def initialize_system_data(self, db: Session):
        """Initialize system roles and permissions"""
        try:
            # Create system permissions
            await self._create_system_permissions(db)
            
            # Create system roles
            await self._create_system_roles(db)
            
            logger.info("System RBAC data initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize system RBAC data: {e}")
            raise
    
    async def _create_system_permissions(self, db: Session):
        """Create system-defined permissions"""
        permissions_data = [
            # Agent permissions
            (SystemPermissions.AGENTS_CREATE, "Create Agents", "Create new agents", ResourceType.AGENTS, ActionType.CREATE),
            (SystemPermissions.AGENTS_READ, "View Agents", "View agent information", ResourceType.AGENTS, ActionType.READ),
            (SystemPermissions.AGENTS_UPDATE, "Update Agents", "Modify agent configuration", ResourceType.AGENTS, ActionType.UPDATE),
            (SystemPermissions.AGENTS_DELETE, "Delete Agents", "Remove agents", ResourceType.AGENTS, ActionType.DELETE),
            (SystemPermissions.AGENTS_EXECUTE, "Execute Agents", "Run and control agents", ResourceType.AGENTS, ActionType.EXECUTE),
            (SystemPermissions.AGENTS_MANAGE, "Manage Agents", "Full agent management", ResourceType.AGENTS, ActionType.MANAGE),
            
            # Orchestration permissions
            (SystemPermissions.ORCHESTRATIONS_CREATE, "Create Orchestrations", "Create orchestrations", ResourceType.ORCHESTRATIONS, ActionType.CREATE),
            (SystemPermissions.ORCHESTRATIONS_READ, "View Orchestrations", "View orchestrations", ResourceType.ORCHESTRATIONS, ActionType.READ),
            (SystemPermissions.ORCHESTRATIONS_UPDATE, "Update Orchestrations", "Modify orchestrations", ResourceType.ORCHESTRATIONS, ActionType.UPDATE),
            (SystemPermissions.ORCHESTRATIONS_DELETE, "Delete Orchestrations", "Remove orchestrations", ResourceType.ORCHESTRATIONS, ActionType.DELETE),
            (SystemPermissions.ORCHESTRATIONS_EXECUTE, "Execute Orchestrations", "Run orchestrations", ResourceType.ORCHESTRATIONS, ActionType.EXECUTE),
            (SystemPermissions.ORCHESTRATIONS_MANAGE, "Manage Orchestrations", "Full orchestration management", ResourceType.ORCHESTRATIONS, ActionType.MANAGE),
            
            # Task permissions
            (SystemPermissions.TASKS_CREATE, "Create Tasks", "Create new tasks", ResourceType.TASKS, ActionType.CREATE),
            (SystemPermissions.TASKS_READ, "View Tasks", "View task information", ResourceType.TASKS, ActionType.READ),
            (SystemPermissions.TASKS_UPDATE, "Update Tasks", "Modify tasks", ResourceType.TASKS, ActionType.UPDATE),
            (SystemPermissions.TASKS_DELETE, "Delete Tasks", "Remove tasks", ResourceType.TASKS, ActionType.DELETE),
            (SystemPermissions.TASKS_EXECUTE, "Execute Tasks", "Run and control tasks", ResourceType.TASKS, ActionType.EXECUTE),
            (SystemPermissions.TASKS_MANAGE, "Manage Tasks", "Full task management", ResourceType.TASKS, ActionType.MANAGE),
            
            # User permissions
            (SystemPermissions.USERS_CREATE, "Create Users", "Create new users", ResourceType.USERS, ActionType.CREATE),
            (SystemPermissions.USERS_READ, "View Users", "View user information", ResourceType.USERS, ActionType.READ),
            (SystemPermissions.USERS_UPDATE, "Update Users", "Modify user profiles", ResourceType.USERS, ActionType.UPDATE),
            (SystemPermissions.USERS_DELETE, "Delete Users", "Remove users", ResourceType.USERS, ActionType.DELETE),
            (SystemPermissions.USERS_MANAGE, "Manage Users", "Full user management", ResourceType.USERS, ActionType.MANAGE),
            
            # Role permissions
            (SystemPermissions.ROLES_CREATE, "Create Roles", "Create new roles", ResourceType.ROLES, ActionType.CREATE),
            (SystemPermissions.ROLES_READ, "View Roles", "View role information", ResourceType.ROLES, ActionType.READ),
            (SystemPermissions.ROLES_UPDATE, "Update Roles", "Modify roles", ResourceType.ROLES, ActionType.UPDATE),
            (SystemPermissions.ROLES_DELETE, "Delete Roles", "Remove roles", ResourceType.ROLES, ActionType.DELETE),
            (SystemPermissions.ROLES_MANAGE, "Manage Roles", "Full role management", ResourceType.ROLES, ActionType.MANAGE),
            
            # System permissions
            (SystemPermissions.SYSTEM_ADMIN, "System Administrator", "Full system administration", ResourceType.SYSTEM, ActionType.ADMIN),
            (SystemPermissions.SYSTEM_MONITOR, "System Monitor", "Monitor system health", ResourceType.SYSTEM, ActionType.READ),
            (SystemPermissions.SYSTEM_CONFIG, "System Configuration", "Configure system settings", ResourceType.SYSTEM, ActionType.UPDATE),
            
            # AI Model permissions
            (SystemPermissions.AI_MODELS_CREATE, "Create AI Models", "Create AI model configurations", ResourceType.AI_MODELS, ActionType.CREATE),
            (SystemPermissions.AI_MODELS_READ, "View AI Models", "View AI model information", ResourceType.AI_MODELS, ActionType.READ),
            (SystemPermissions.AI_MODELS_UPDATE, "Update AI Models", "Modify AI models", ResourceType.AI_MODELS, ActionType.UPDATE),
            (SystemPermissions.AI_MODELS_DELETE, "Delete AI Models", "Remove AI models", ResourceType.AI_MODELS, ActionType.DELETE),
            (SystemPermissions.AI_MODELS_MANAGE, "Manage AI Models", "Full AI model management", ResourceType.AI_MODELS, ActionType.MANAGE),
            
            # Generation permissions
            (SystemPermissions.GENERATIONS_CREATE, "Create Generations", "Create agent generations", ResourceType.GENERATIONS, ActionType.CREATE),
            (SystemPermissions.GENERATIONS_READ, "View Generations", "View generation information", ResourceType.GENERATIONS, ActionType.READ),
            (SystemPermissions.GENERATIONS_UPDATE, "Update Generations", "Modify generations", ResourceType.GENERATIONS, ActionType.UPDATE),
            (SystemPermissions.GENERATIONS_DELETE, "Delete Generations", "Remove generations", ResourceType.GENERATIONS, ActionType.DELETE),
            (SystemPermissions.GENERATIONS_MANAGE, "Manage Generations", "Full generation management", ResourceType.GENERATIONS, ActionType.MANAGE),
        ]
        
        for name, display_name, description, resource, action in permissions_data:
            existing = db.query(Permission).filter(Permission.name == name).first()
            if not existing:
                permission = Permission(
                    name=name,
                    display_name=display_name,
                    description=description,
                    resource=resource.value,
                    action=action.value,
                    is_system=True,
                )
                db.add(permission)
        
        db.commit()
    
    async def _create_system_roles(self, db: Session):
        """Create system-defined roles"""
        roles_data = [
            (SystemRoles.SUPERUSER, "Super Administrator", "Full system access", [
                SystemPermissions.SYSTEM_ADMIN,
                SystemPermissions.USERS_MANAGE,
                SystemPermissions.ROLES_MANAGE,
                SystemPermissions.AGENTS_MANAGE,
                SystemPermissions.ORCHESTRATIONS_MANAGE,
                SystemPermissions.TASKS_MANAGE,
                SystemPermissions.AI_MODELS_MANAGE,
                SystemPermissions.GENERATIONS_MANAGE,
            ]),
            
            (SystemRoles.ADMIN, "Administrator", "Administrative access", [
                SystemPermissions.SYSTEM_MONITOR,
                SystemPermissions.USERS_CREATE,
                SystemPermissions.USERS_READ,
                SystemPermissions.USERS_UPDATE,
                SystemPermissions.ROLES_READ,
                SystemPermissions.AGENTS_MANAGE,
                SystemPermissions.ORCHESTRATIONS_MANAGE,
                SystemPermissions.TASKS_MANAGE,
                SystemPermissions.AI_MODELS_MANAGE,
                SystemPermissions.GENERATIONS_MANAGE,
            ]),
            
            (SystemRoles.AGENT_MANAGER, "Agent Manager", "Agent management access", [
                SystemPermissions.AGENTS_CREATE,
                SystemPermissions.AGENTS_READ,
                SystemPermissions.AGENTS_UPDATE,
                SystemPermissions.AGENTS_DELETE,
                SystemPermissions.AGENTS_EXECUTE,
                SystemPermissions.TASKS_CREATE,
                SystemPermissions.TASKS_READ,
                SystemPermissions.TASKS_UPDATE,
                SystemPermissions.TASKS_EXECUTE,
                SystemPermissions.AI_MODELS_READ,
                SystemPermissions.GENERATIONS_CREATE,
                SystemPermissions.GENERATIONS_READ,
            ]),
            
            (SystemRoles.ORCHESTRATOR, "Orchestrator", "Orchestration management access", [
                SystemPermissions.ORCHESTRATIONS_CREATE,
                SystemPermissions.ORCHESTRATIONS_READ,
                SystemPermissions.ORCHESTRATIONS_UPDATE,
                SystemPermissions.ORCHESTRATIONS_DELETE,
                SystemPermissions.ORCHESTRATIONS_EXECUTE,
                SystemPermissions.AGENTS_READ,
                SystemPermissions.AGENTS_EXECUTE,
                SystemPermissions.TASKS_CREATE,
                SystemPermissions.TASKS_READ,
                SystemPermissions.TASKS_UPDATE,
                SystemPermissions.TASKS_EXECUTE,
            ]),
            
            (SystemRoles.USER, "Regular User", "Standard user access", [
                SystemPermissions.AGENTS_CREATE,
                SystemPermissions.AGENTS_READ,
                SystemPermissions.AGENTS_UPDATE,
                SystemPermissions.AGENTS_EXECUTE,
                SystemPermissions.ORCHESTRATIONS_CREATE,
                SystemPermissions.ORCHESTRATIONS_READ,
                SystemPermissions.ORCHESTRATIONS_EXECUTE,
                SystemPermissions.TASKS_CREATE,
                SystemPermissions.TASKS_READ,
                SystemPermissions.TASKS_UPDATE,
                SystemPermissions.TASKS_EXECUTE,
                SystemPermissions.AI_MODELS_READ,
                SystemPermissions.GENERATIONS_CREATE,
                SystemPermissions.GENERATIONS_READ,
            ]),
            
            (SystemRoles.VIEWER, "Viewer", "Read-only access", [
                SystemPermissions.AGENTS_READ,
                SystemPermissions.ORCHESTRATIONS_READ,
                SystemPermissions.TASKS_READ,
                SystemPermissions.AI_MODELS_READ,
                SystemPermissions.GENERATIONS_READ,
            ]),
            
            (SystemRoles.GUEST, "Guest", "Limited access", [
                SystemPermissions.AGENTS_READ,
                SystemPermissions.TASKS_READ,
            ]),
        ]
        
        for role_name, display_name, description, permission_names in roles_data:
            existing_role = db.query(Role).filter(Role.name == role_name).first()
            if not existing_role:
                role = Role(
                    name=role_name,
                    display_name=display_name,
                    description=description,
                    is_system=True,
                    is_active=True,
                )
                db.add(role)
                db.flush()  # Ensure role has an ID
                
                # Add permissions to role
                for permission_name in permission_names:
                    permission = db.query(Permission).filter(Permission.name == permission_name).first()
                    if permission:
                        role_permission = RolePermission(
                            role_id=role.id,
                            permission_id=permission.id,
                        )
                        db.add(role_permission)
        
        db.commit()
    
    async def check_permission(self, user: User, permission: str, db: Session) -> bool:
        """Check if user has specific permission"""
        try:
            # Superuser has all permissions
            if user.is_superuser:
                return True
            
            # Get user permissions (with caching)
            user_permissions = await self._get_user_permissions(user, db)
            
            # Check exact permission match
            if permission in user_permissions:
                return True
            
            # Check wildcard permissions (e.g., agents:manage includes agents:create)
            resource, action = permission.split(":", 1)
            manage_permission = f"{resource}:manage"
            if manage_permission in user_permissions:
                return True
            
            # Check admin permissions
            if f"{resource}:admin" in user_permissions:
                return True
            
            # Check system admin permission
            if SystemPermissions.SYSTEM_ADMIN in user_permissions:
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking permission {permission} for user {user.id}: {e}")
            return False
    
    async def check_resource_access(self, user: User, resource: str, action: str, db: Session) -> bool:
        """Check if user can perform action on resource"""
        permission = f"{resource}:{action}"
        return await self.check_permission(user, permission, db)
    
    async def get_user_permissions(self, user: User, db: Session) -> List[str]:
        """Get all permissions for user"""
        return list(await self._get_user_permissions(user, db))
    
    async def _get_user_permissions(self, user: User, db: Session) -> Set[str]:
        """Get user permissions with caching"""
        cache_key = f"user_permissions_{user.id}"
        
        # Check cache
        if cache_key in self._permission_cache:
            if (cache_key in self._cache_timestamps and 
                datetime.utcnow() - self._cache_timestamps[cache_key] < self._cache_ttl):
                return self._permission_cache[cache_key]
        
        # Load permissions from database
        permissions = set()
        
        # Get user roles with permissions
        user_roles = db.query(UserRole).filter(
            UserRole.user_id == user.id,
            UserRole.is_active == True,
        ).options(
            joinedload(UserRole.role).joinedload(Role.permissions).joinedload(RolePermission.permission)
        ).all()
        
        for user_role in user_roles:
            # Check if role is still active and not expired
            if (user_role.role.is_active and 
                (not user_role.expires_at or user_role.expires_at > datetime.utcnow())):
                
                for role_permission in user_role.role.permissions:
                    permissions.add(role_permission.permission.name)
        
        # Cache permissions
        self._permission_cache[cache_key] = permissions
        self._cache_timestamps[cache_key] = datetime.utcnow()
        
        return permissions
    
    async def assign_role_to_user(self, user: User, role_name: str, granted_by: Optional[User], 
                                  expires_at: Optional[datetime], db: Session) -> bool:
        """Assign role to user"""
        try:
            # Get role
            role = db.query(Role).filter(
                Role.name == role_name,
                Role.is_active == True
            ).first()
            
            if not role:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Role '{role_name}' not found"
                )
            
            # Check if user already has this role
            existing = db.query(UserRole).filter(
                UserRole.user_id == user.id,
                UserRole.role_id == role.id,
                UserRole.is_active == True
            ).first()
            
            if existing:
                # Update existing role assignment
                existing.expires_at = expires_at
                existing.granted_by = granted_by.id if granted_by else None
                existing.granted_at = datetime.utcnow()
            else:
                # Create new role assignment
                user_role = UserRole(
                    user_id=user.id,
                    role_id=role.id,
                    granted_by=granted_by.id if granted_by else None,
                    expires_at=expires_at,
                    is_active=True,
                )
                db.add(user_role)
            
            db.commit()
            
            # Clear user's permission cache
            self._clear_user_cache(user.id)
            
            logger.info(f"Role '{role_name}' assigned to user {user.id}")
            return True
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to assign role '{role_name}' to user {user.id}: {e}")
            return False
    
    async def remove_role_from_user(self, user: User, role_name: str, db: Session) -> bool:
        """Remove role from user"""
        try:
            # Get role
            role = db.query(Role).filter(Role.name == role_name).first()
            if not role:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Role '{role_name}' not found"
                )
            
            # Find and deactivate user role
            user_role = db.query(UserRole).filter(
                UserRole.user_id == user.id,
                UserRole.role_id == role.id,
                UserRole.is_active == True
            ).first()
            
            if user_role:
                user_role.is_active = False
                db.commit()
                
                # Clear user's permission cache
                self._clear_user_cache(user.id)
                
                logger.info(f"Role '{role_name}' removed from user {user.id}")
                return True
            
            return False
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to remove role '{role_name}' from user {user.id}: {e}")
            return False
    
    async def get_user_roles(self, user: User, db: Session) -> List[Dict[str, Any]]:
        """Get user's active roles"""
        user_roles = db.query(UserRole).filter(
            UserRole.user_id == user.id,
            UserRole.is_active == True,
        ).options(
            joinedload(UserRole.role)
        ).all()
        
        active_roles = []
        for user_role in user_roles:
            if (user_role.role.is_active and 
                (not user_role.expires_at or user_role.expires_at > datetime.utcnow())):
                
                active_roles.append({
                    "id": str(user_role.role.id),
                    "name": user_role.role.name,
                    "display_name": user_role.role.display_name,
                    "description": user_role.role.description,
                    "granted_at": user_role.granted_at.isoformat(),
                    "expires_at": user_role.expires_at.isoformat() if user_role.expires_at else None,
                    "is_system": user_role.role.is_system,
                })
        
        return active_roles
    
    async def create_role(self, name: str, display_name: str, description: str, 
                         permission_names: List[str], created_by: User, db: Session) -> Dict[str, Any]:
        """Create new role"""
        try:
            # Check if role already exists
            existing = db.query(Role).filter(Role.name == name).first()
            if existing:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Role '{name}' already exists"
                )
            
            # Create role
            role = Role(
                name=name,
                display_name=display_name,
                description=description,
                is_system=False,
                is_active=True,
                created_by=str(created_by.id),
            )
            db.add(role)
            db.flush()
            
            # Add permissions to role
            for permission_name in permission_names:
                permission = db.query(Permission).filter(Permission.name == permission_name).first()
                if permission:
                    role_permission = RolePermission(
                        role_id=role.id,
                        permission_id=permission.id,
                    )
                    db.add(role_permission)
            
            db.commit()
            db.refresh(role)
            
            logger.info(f"Role '{name}' created by user {created_by.id}")
            
            return {
                "id": str(role.id),
                "name": role.name,
                "display_name": role.display_name,
                "description": role.description,
                "is_system": role.is_system,
                "is_active": role.is_active,
                "created_at": role.created_at.isoformat(),
            }
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to create role '{name}': {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create role"
            )
    
    async def get_all_roles(self, db: Session) -> List[Dict[str, Any]]:
        """Get all roles"""
        roles = db.query(Role).filter(Role.is_active == True).all()
        
        return [
            {
                "id": str(role.id),
                "name": role.name,
                "display_name": role.display_name,
                "description": role.description,
                "is_system": role.is_system,
                "is_active": role.is_active,
                "created_at": role.created_at.isoformat(),
            }
            for role in roles
        ]
    
    async def get_all_permissions(self, db: Session) -> List[Dict[str, Any]]:
        """Get all permissions"""
        permissions = db.query(Permission).all()
        
        return [
            {
                "id": str(permission.id),
                "name": permission.name,
                "display_name": permission.display_name,
                "description": permission.description,
                "resource": permission.resource,
                "action": permission.action,
                "is_system": permission.is_system,
            }
            for permission in permissions
        ]
    
    def _clear_user_cache(self, user_id: str):
        """Clear user's permission cache"""
        cache_key = f"user_permissions_{user_id}"
        if cache_key in self._permission_cache:
            del self._permission_cache[cache_key]
        if cache_key in self._cache_timestamps:
            del self._cache_timestamps[cache_key]
    
    def clear_all_cache(self):
        """Clear all permission caches"""
        self._permission_cache.clear()
        self._cache_timestamps.clear()


# Global RBAC service instance
rbac_service = RBACService()