load("@rules_go//go:def.bzl", "go_library")

package(default_visibility = ["//visibility:public"])

# All orbit generated API clients
filegroup(
    name = "all_clients",
    srcs = [
        ":go_server",
        ":go_client",
        ":typescript_client",
    ],
)

# Generated Go server code
filegroup(
    name = "go_server",
    srcs = glob(
        ["go/server/*.go"],
        allow_empty = True,
    ),
)

# Generated Go client code
filegroup(
    name = "go_client",
    srcs = glob(
        ["go/client/*.go"],
        allow_empty = True,
    ),
)

# Generated TypeScript client code
filegroup(
    name = "typescript_client",
    srcs = glob(
        ["typescript/**/*.ts"],
        allow_empty = True,
    ),
)

# Go library for generated server code
go_library(
    name = "openapi_server",
    srcs = glob(
        ["go/server/*.go"],
        allow_empty = True,
    ),
    importpath = "github.com/TwoDotAi/mono/generated/orbit/go/server",
    deps = [
        "@com_github_gin_gonic_gin//:gin",
        "@com_github_oapi_codegen_runtime//:runtime",
        "@com_github_oapi_codegen_runtime//types",
    ],
)

# Go library for generated client code
go_library(
    name = "openapi_client_go",
    srcs = glob(
        ["go/client/*.go"],
        allow_empty = True,
    ),
    importpath = "github.com/TwoDotAi/mono/generated/orbit/go/client",
    deps = [
        "@com_github_oapi_codegen_runtime//:runtime",
        "@com_github_oapi_codegen_runtime//types",
    ],
)