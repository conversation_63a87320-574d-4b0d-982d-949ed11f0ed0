# Database Module for GCP Cloud SQL PostgreSQL
# Creates Cloud SQL instance, database, user, and handles migrations

terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
    random = {
      source  = "hashicorp/random"
      version = "~> 3.1"
    }
  }
}

# Generate random password for database user
resource "random_password" "db_password" {
  length  = 32
  special = true
}

# Store database password in Secret Manager
resource "google_secret_manager_secret" "db_password" {
  secret_id = "${var.project_name}-db-password"
  
  replication {
    auto {}
  }
  
  depends_on = [google_project_service.secretmanager]
}

resource "google_secret_manager_secret_version" "db_password" {
  secret      = google_secret_manager_secret.db_password.id
  secret_data = random_password.db_password.result
}

# Enable required APIs
resource "google_project_service" "sqladmin" {
  service = "sqladmin.googleapis.com"
  
  disable_dependent_services = true
}

resource "google_project_service" "secretmanager" {
  service = "secretmanager.googleapis.com"
  
  disable_dependent_services = true
}

# Cloud SQL PostgreSQL instance
resource "google_sql_database_instance" "postgres" {
  name             = "${var.project_name}-postgres-${var.environment}"
  database_version = var.postgres_version
  region           = var.region
  
  deletion_protection = var.deletion_protection
  
  settings {
    tier                        = var.instance_tier
    availability_type           = var.availability_type
    disk_size                   = var.disk_size_gb
    disk_type                   = "PD_SSD"
    disk_autoresize            = true
    disk_autoresize_limit      = var.max_disk_size_gb

    backup_configuration {
      enabled                        = true
      start_time                     = "02:00"  # 2 AM UTC
      point_in_time_recovery_enabled = true
      transaction_log_retention_days = 7
      backup_retention_settings {
        retained_backups = var.backup_retention_days
        retention_unit   = "COUNT"
      }
    }

    ip_configuration {
      ipv4_enabled                                  = false  # No public IP
      private_network                               = var.vpc_network
      enable_private_path_for_google_cloud_services = true
      
      # Only allow authorized networks if specified
      dynamic "authorized_networks" {
        for_each = var.authorized_networks
        content {
          name  = authorized_networks.value.name
          value = authorized_networks.value.cidr
        }
      }
    }

    database_flags {
      name  = "log_checkpoints"
      value = "on"
    }

    database_flags {
      name  = "log_connections"
      value = "on"
    }

    database_flags {
      name  = "log_disconnections"
      value = "on"
    }

    database_flags {
      name  = "log_lock_waits"
      value = "on"
    }

    database_flags {
      name  = "log_min_duration_statement"
      value = "1000"  # Log queries taking more than 1 second
    }

    maintenance_window {
      day          = 7  # Sunday
      hour         = 3  # 3 AM UTC
      update_track = "stable"
    }

    insights_config {
      query_insights_enabled  = true
      query_string_length     = 1024
      record_application_tags = true
      record_client_address   = true
    }
  }

  depends_on = [
    google_project_service.sqladmin,
    google_service_networking_connection.private_vpc_connection
  ]
}

# Main application database
resource "google_sql_database" "app_database" {
  name     = var.database_name
  instance = google_sql_database_instance.postgres.name
  
  # Use UTF8 encoding
  charset   = "UTF8"
  collation = "en_US.UTF8"
}

# Database user for application
resource "google_sql_user" "app_user" {
  name     = var.database_user
  instance = google_sql_database_instance.postgres.name
  password = random_password.db_password.result
  
  # Don't allow deletion of user
  deletion_policy = "ABANDON"
}

# Private IP range for VPC peering (required for private Cloud SQL)
resource "google_compute_global_address" "private_ip_range" {
  name          = "${var.project_name}-private-ip-range"
  purpose       = "VPC_PEERING"
  address_type  = "INTERNAL"
  prefix_length = 16
  network       = var.vpc_network
}

# VPC peering connection for private Cloud SQL
resource "google_service_networking_connection" "private_vpc_connection" {
  network                 = var.vpc_network
  service                 = "servicenetworking.googleapis.com"
  reserved_peering_ranges = [google_compute_global_address.private_ip_range.name]
  
  depends_on = [google_project_service.servicenetworking]
}

# Enable Service Networking API
resource "google_project_service" "servicenetworking" {
  service = "servicenetworking.googleapis.com"
  
  disable_dependent_services = true
}

# Cloud Build trigger for database migrations
resource "google_cloudbuild_trigger" "db_migration" {
  count = var.enable_migration_automation ? 1 : 0
  
  name        = "${var.project_name}-db-migration"
  description = "Automated database migrations for ${var.project_name}"

  github {
    owner = var.github_repo_owner
    name  = var.github_repo_name
    push {
      branch = var.migration_branch
    }
  }

  filename = var.migration_cloudbuild_file

  substitutions = {
    _DATABASE_NAME = var.database_name
    _DATABASE_USER = var.database_user
    _INSTANCE_NAME = google_sql_database_instance.postgres.name
    _SECRET_NAME   = google_secret_manager_secret.db_password.secret_id
    _INSTANCE_CONNECTION_NAME = google_sql_database_instance.postgres.connection_name
  }

  depends_on = [google_project_service.cloudbuild]
}

# Enable Cloud Build API
resource "google_project_service" "cloudbuild" {
  service = "cloudbuild.googleapis.com"
  
  disable_dependent_services = true
}