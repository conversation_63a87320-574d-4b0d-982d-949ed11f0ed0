#!/bin/bash
set -e

echo "Fixing Flyway state (clearing schema history and re-running migrations)..."

# Get the workspace root - when running via <PERSON>zel, we need to navigate to the workspace
if [ -n "$BUILD_WORKSPACE_DIRECTORY" ]; then
    cd "$BUILD_WORKSPACE_DIRECTORY"
elif [ -n "$TEST_WORKSPACE" ]; then
    cd "$TEST_WORKSPACE/.."
else
    # Find the workspace root by looking for WORKSPACE file
    CURRENT_DIR="$PWD"
    while [ "$CURRENT_DIR" != "/" ]; do
        if [ -f "$CURRENT_DIR/WORKSPACE" ]; then
            cd "$CURRENT_DIR"
            break
        fi
        CURRENT_DIR="$(dirname "$CURRENT_DIR")"
    done
fi

# Now navigate to the platform/db directory
cd platform/db

# Check if postgres container is running
if ! docker ps | grep -q platform_postgres; then
    echo "ERROR: PostgreSQL container is not running!"
    echo "Please run 'bazel run //platform/db:start' first."
    exit 1
fi

# Get the network name - try different approaches to find it
NETWORK=$(docker inspect platform_postgres -f '{{range $key, $value := .NetworkSettings.Networks}}{{$key}}{{end}}' 2>/dev/null | head -n1)

if [ -z "$NETWORK" ]; then
    # Try using the default network name from docker-compose
    NETWORK="platform_db_default"
    # Check if the network exists
    if ! docker network ls | grep -q "$NETWORK"; then
        # Fall back to host networking
        NETWORK="host"
    fi
fi

echo "Using network: $NETWORK"

# Clean the Flyway schema history
echo "Cleaning Flyway schema history..."

if [ "$NETWORK" = "host" ]; then
    # Use host networking
    docker run --rm \
        --network="host" \
        -v "$(pwd)/migrations:/flyway/sql" \
        -v "$(pwd)/flyway.conf:/flyway/conf/flyway.conf" \
        -e FLYWAY_URL="**************************************" \
        -e FLYWAY_USER="postgres" \
        -e FLYWAY_PASSWORD="postgres" \
        -e FLYWAY_LOCATIONS="filesystem:/flyway/sql" \
        -e FLYWAY_BASELINE_ON_MIGRATE="true" \
        -e FLYWAY_CLEAN_DISABLED="false" \
        flyway/flyway:10-alpine \
        clean
else
    # Use container networking
    docker run --rm \
        --network="$NETWORK" \
        -v "$(pwd)/migrations:/flyway/sql" \
        -v "$(pwd)/flyway.conf:/flyway/conf/flyway.conf" \
        -e FLYWAY_URL="**********************************************" \
        -e FLYWAY_USER="postgres" \
        -e FLYWAY_PASSWORD="postgres" \
        -e FLYWAY_LOCATIONS="filesystem:/flyway/sql" \
        -e FLYWAY_BASELINE_ON_MIGRATE="true" \
        -e FLYWAY_CLEAN_DISABLED="false" \
        flyway/flyway:10-alpine \
        clean
fi

echo "Re-running migrations..."

# Run the migration script
if [ "$NETWORK" = "host" ]; then
    # Use host networking
    docker run --rm \
        --network="host" \
        -v "$(pwd)/migrations:/flyway/sql" \
        -v "$(pwd)/flyway.conf:/flyway/conf/flyway.conf" \
        -e FLYWAY_URL="**************************************" \
        -e FLYWAY_USER="postgres" \
        -e FLYWAY_PASSWORD="postgres" \
        -e FLYWAY_LOCATIONS="filesystem:/flyway/sql" \
        -e FLYWAY_BASELINE_ON_MIGRATE="true" \
        flyway/flyway:10-alpine \
        migrate
else
    # Use container networking
    docker run --rm \
        --network="$NETWORK" \
        -v "$(pwd)/migrations:/flyway/sql" \
        -v "$(pwd)/flyway.conf:/flyway/conf/flyway.conf" \
        -e FLYWAY_URL="**********************************************" \
        -e FLYWAY_USER="postgres" \
        -e FLYWAY_PASSWORD="postgres" \
        -e FLYWAY_LOCATIONS="filesystem:/flyway/sql" \
        -e FLYWAY_BASELINE_ON_MIGRATE="true" \
        flyway/flyway:10-alpine \
        migrate
fi

echo "Flyway state fixed successfully!"