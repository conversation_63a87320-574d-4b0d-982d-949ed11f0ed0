# Additional variables for database migration automation

variable "enable_scheduled_migrations" {
  description = "Whether to enable scheduled automated migrations"
  type        = bool
  default     = false
}

variable "migration_schedule" {
  description = "Cron schedule for automated migrations (e.g., '0 2 * * 1' for Mondays at 2 AM)"
  type        = string
  default     = "0 2 * * 1"  # Every Monday at 2 AM
  
  validation {
    condition     = can(regex("^[0-9*,/-]+ [0-9*,/-]+ [0-9*,/-]+ [0-9*,/-]+ [0-9*,/-]+$", var.migration_schedule))
    error_message = "Migration schedule must be a valid cron expression."
  }
}

variable "migration_timezone" {
  description = "Timezone for scheduled migrations"
  type        = string
  default     = "UTC"
}

variable "enable_migration_notifications" {
  description = "Whether to enable migration notifications via Slack/email"
  type        = bool
  default     = false
}

variable "slack_webhook_url" {
  description = "Slack webhook URL for migration notifications"
  type        = string
  default     = ""
  sensitive   = true
}

variable "notification_email" {
  description = "Email address for migration notifications"
  type        = string
  default     = ""
  
  validation {
    condition = var.notification_email == "" || can(regex("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", var.notification_email))
    error_message = "Notification email must be a valid email address or empty."
  }
}

variable "migration_timeout_minutes" {
  description = "Timeout for migration jobs in minutes"
  type        = number
  default     = 20
  
  validation {
    condition     = var.migration_timeout_minutes >= 5 && var.migration_timeout_minutes <= 120
    error_message = "Migration timeout must be between 5 and 120 minutes."
  }
}

variable "enable_migration_rollback" {
  description = "Whether to enable automatic rollback on migration failure"
  type        = bool
  default     = false
}

variable "rollback_backup_retention_days" {
  description = "Number of days to retain rollback backups"
  type        = number
  default     = 7
  
  validation {
    condition     = var.rollback_backup_retention_days >= 1 && var.rollback_backup_retention_days <= 30
    error_message = "Rollback backup retention must be between 1 and 30 days."
  }
}