---
# Terraform-generated Ansible inventory for prod environment
all:
  hosts:
    platform-platform-prod:
      ansible_host: ************
      ansible_user: ansible
      ansible_ssh_private_key_file: "{{ lookup('env', 'ANSIBLE_SSH_KEY') }}"
      ansible_ssh_common_args: '-o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null'
      
      # GCP metadata
      gcp_project_id: twodot-agent-prod
      gcp_zone: australia-southeast1-a
      gcp_instance_name: platform-platform-prod
      environment: prod
      
      # Network information
      internal_ip: ********
      external_ip: ************
      
  children:
    microservices:
      hosts:
        platform-platform-prod:
    prod:
      hosts:
        platform-platform-prod: