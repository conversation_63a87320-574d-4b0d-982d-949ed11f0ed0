#!/bin/bash
set -euo pipefail

# Startup script for Compute Engine instance running Docker Compose

# Update system
apt-get update
apt-get install -y ca-certificates curl gnupg lsb-release jq

# Install Docker
curl -fsSL https://download.docker.com/linux/debian/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/debian $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null
apt-get update
apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin

# Install gcloud CLI if not present
if ! command -v gcloud &> /dev/null; then
    curl https://dl.google.com/dl/cloudsdk/channels/rapid/downloads/google-cloud-cli-latest-linux-x86_64.tar.gz | tar -xz -C /opt
    /opt/google-cloud-sdk/install.sh --quiet
    ln -s /opt/google-cloud-sdk/bin/gcloud /usr/local/bin/gcloud
fi

# Configure Docker to use gcloud as credential helper
gcloud auth configure-docker ${registry_url} --quiet

# Start and enable Docker
systemctl start docker
systemctl enable docker

# Create application directory
mkdir -p /opt/platform
cd /opt/platform

# Mount additional disk for Docker volumes
mkfs.ext4 -F /dev/disk/by-id/google-docker-volumes
mkdir -p /var/lib/docker-volumes
mount /dev/disk/by-id/google-docker-volumes /var/lib/docker-volumes
echo '/dev/disk/by-id/google-docker-volumes /var/lib/docker-volumes ext4 defaults 0 2' >> /etc/fstab

# Get secrets from Secret Manager using metadata server
get_secret() {
    local secret_name=$1
    local project_id="${project_id}"
    local access_token=$(curl -s -H 'Metadata-Flavor: Google' http://metadata.google.internal/computeMetadata/v1/instance/service-accounts/default/token | grep -o '"access_token":"[^"]*' | cut -d'"' -f4)
    local secret_data=$(curl -s -H "Authorization: Bearer $access_token" "https://secretmanager.googleapis.com/v1/projects/$project_id/secrets/$secret_name/versions/latest:access" | grep -o '"data":"[^"]*' | cut -d'"' -f4)
    echo "$secret_data" | base64 -d
}

DB_PASSWORD=$(get_secret "${db_password_secret}")
GOOGLE_OAUTH_CLIENT_ID=$(get_secret "${google_oauth_client_id_secret}")
GOOGLE_OAUTH_CLIENT_SECRET=$(get_secret "${google_oauth_client_secret_secret}")
OAUTH_STATE_SECRET=$(get_secret "${oauth_state_secret}")

# Create environment file for Docker Compose
cat > /opt/platform/.env << EOF
# Database Configuration
DATABASE_HOST=${db_host}
DATABASE_NAME=${db_name}
DATABASE_USER=${db_user}
DATABASE_PASSWORD=$DB_PASSWORD
DATABASE_URL=postgresql://${db_user}:$DB_PASSWORD@${db_host}:5432/${db_name}

# OAuth Configuration
GOOGLE_OAUTH_CLIENT_ID=$GOOGLE_OAUTH_CLIENT_ID
GOOGLE_OAUTH_CLIENT_SECRET=$GOOGLE_OAUTH_CLIENT_SECRET
GOOGLE_OAUTH_REDIRECT_URL=https://internal.dev.twodot.ai/auth/callback/google
OAUTH_STATE_SECRET=$OAUTH_STATE_SECRET

# Service Ports
AUTH_SERVICE_PORT=${auth_service_port}
CRM_SERVICE_PORT=${crm_service_port}
GATEWAY_PORT=${gateway_port}

# Registry Configuration
REGISTRY_URL=${registry_url}

# GCP Configuration
PROJECT_ID=${project_id}
EOF

# Create docker-compose.yml for VM deployment
cat > /opt/platform/docker-compose.yml << 'EOF'
version: '3.8'

services:
  # Nginx reverse proxy
  nginx:
    image: nginx:alpine
    container_name: platform-nginx
    ports:
      - "80:80"
    environment:
      - NGINX_HOST=_
      - NGINX_PORT=80
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf:ro
    depends_on:
      - gateway
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - platform-network

  # Gateway Service
  gateway:
    image: ${registry_url}/gateway:latest
    container_name: platform-gateway
    ports:
      - "${gateway_port}:${gateway_port}"
    environment:
      - PORT=${gateway_port}
      - GIN_MODE=release
      - AUTH_SERVICE_URL=http://platform-auth:${auth_service_port}
      - CRM_BACKEND_URL=http://platform-crm-backend:${crm_service_port}
    depends_on:
      - auth
      - crm-backend
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:${gateway_port}/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - platform-network
    logging:
      driver: gcplogs
      options:
        gcp-project: "${project_id}"

  # Auth Service
  auth:
    image: ${registry_url}/auth:latest
    container_name: platform-auth
    ports:
      - "${auth_service_port}:${auth_service_port}"
    environment:
      - PORT=${auth_service_port}
      - GIN_MODE=release
      - DATABASE_URL=$${DATABASE_URL}
      - JWT_SECRET=$${JWT_SECRET:-dev-jwt-secret-change-in-production}
      - GOOGLE_OAUTH_CLIENT_ID=$${GOOGLE_OAUTH_CLIENT_ID}
      - GOOGLE_OAUTH_CLIENT_SECRET=$${GOOGLE_OAUTH_CLIENT_SECRET}
      - GOOGLE_OAUTH_REDIRECT_URL=$${GOOGLE_OAUTH_REDIRECT_URL}
      - OAUTH_STATE_SECRET=$${OAUTH_STATE_SECRET}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:${auth_service_port}/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - platform-network
    logging:
      driver: gcplogs
      options:
        gcp-project: "${project_id}"

  # CRM Backend Service
  crm-backend:
    image: ${registry_url}/crm-backend:latest
    container_name: platform-crm-backend
    ports:
      - "${crm_service_port}:${crm_service_port}"
    environment:
      - PORT=${crm_service_port}
      - GIN_MODE=release
      - DATABASE_URL=$${DATABASE_URL}
      - AUTH_SERVICE_URL=http://platform-auth:${auth_service_port}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:${crm_service_port}/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - platform-network
    logging:
      driver: gcplogs
      options:
        gcp-project: "${project_id}"

networks:
  platform-network:
    driver: bridge
EOF

# Create nginx.conf for reverse proxy
cat > /opt/platform/nginx.conf << 'EOF'
server {
    listen 80;
    server_name _;
    
    # Proxy API requests to gateway (preserves full path)
    location /api/ {
        proxy_pass http://platform-gateway:${gateway_port};
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # CORS headers with dynamic origin support
        # Allow production domain and localhost for development
        set $cors_origin "";
        if ($http_origin ~ "^https://internal\.dev\.twodot\.ai$") {
            set $cors_origin "https://internal.dev.twodot.ai";
        }
        if ($http_origin ~ "^http://localhost:8080$") {
            set $cors_origin "http://localhost:8080";
        }
        if ($http_origin ~ "^http://localhost:3000$") {
            set $cors_origin "http://localhost:3000";
        }
        
        # Apply CORS headers if origin is allowed
        add_header 'Access-Control-Allow-Origin' '$cors_origin' always;
        add_header 'Access-Control-Allow-Credentials' 'true' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS, PATCH' always;
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,Accept,Origin' always;
        add_header 'Access-Control-Max-Age' '86400' always;
        
        # Handle preflight requests
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '$cors_origin' always;
            add_header 'Access-Control-Allow-Credentials' 'true' always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS, PATCH' always;
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,Accept,Origin' always;
            add_header 'Access-Control-Max-Age' '86400' always;
            add_header 'Content-Type' 'text/plain; charset=utf-8';
            add_header 'Content-Length' 0;
            return 204;
        }
    }
    
    # Health check endpoint
    location /health {
        proxy_pass http://platform-gateway:${gateway_port}/health;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Add health check headers
        add_header 'Cache-Control' 'no-cache, no-store, must-revalidate';
        add_header 'Pragma' 'no-cache';
        add_header 'Expires' '0';
    }
    
    # Default response for other paths
    location / {
        return 200 'Platform services are running';
        add_header Content-Type text/plain;
        add_header 'Cache-Control' 'no-cache, no-store, must-revalidate';
        add_header 'Pragma' 'no-cache';
        add_header 'Expires' '0';
    }
}
EOF

# Create systemd service for Docker Compose
cat > /etc/systemd/system/platform-services.service << EOF
[Unit]
Description=Platform Docker Compose Services
Requires=docker.service
After=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=/opt/platform
ExecStart=/usr/bin/docker compose up -d
ExecStop=/usr/bin/docker compose down
TimeoutStartSec=0

[Install]
WantedBy=multi-user.target
EOF

# Run database migrations automatically before starting services
echo "=== Running Database Migrations ==="
echo "Creating temporary directory for migration..."
TEMP_DIR=$(mktemp -d)
cd $TEMP_DIR

# Create migration files
mkdir -p migrations

echo "Creating V001 migration..."
cat > migrations/V001__initial_schema.sql << 'MIGRATION_EOF'
-- Initial database schema
-- V001__initial_schema.sql

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    full_name VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create index on email for faster lookups
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);

-- Create a trigger to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE
    ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
MIGRATION_EOF

echo "Creating V002 migration..."
cat > migrations/V002__crm_schema.sql << 'MIGRATION_EOF'
-- CRM Database Schema Migration
-- V002__crm_schema.sql

-- Update users table to support authentication
ALTER TABLE users ADD COLUMN IF NOT EXISTS password_hash VARCHAR(255);
ALTER TABLE users ADD COLUMN IF NOT EXISTS email_confirmed_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS is_deleted BOOLEAN DEFAULT FALSE;

-- Create user profiles table
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    full_name VARCHAR(255),
    avatar_url TEXT,
    timezone VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create company statuses table (pipeline stages for companies)
CREATE TABLE IF NOT EXISTS company_statuses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    pipeline_order INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create companies table
CREATE TABLE IF NOT EXISTS companies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    website TEXT,
    phone VARCHAR(50),
    address TEXT,
    notes TEXT,
    company_status_id UUID REFERENCES company_statuses(id),
    is_deleted BOOLEAN DEFAULT FALSE,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create contacts table
CREATE TABLE IF NOT EXISTS contacts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(50),
    job_title VARCHAR(255),
    company_id UUID REFERENCES companies(id) ON DELETE SET NULL,
    created_by UUID NOT NULL REFERENCES users(id),
    is_deleted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create deal stages table (pipeline stages for deals)
CREATE TABLE IF NOT EXISTS deal_stages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    pipeline_order INTEGER NOT NULL,
    is_closed_won BOOLEAN DEFAULT FALSE,
    is_closed_lost BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create deals table
CREATE TABLE IF NOT EXISTS deals (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    estimated_value DECIMAL(15,2),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    deal_stage_id UUID NOT NULL REFERENCES deal_stages(id),
    expected_close_date DATE,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create interaction types table
CREATE TABLE IF NOT EXISTS interaction_types (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create interactions table (communication tracking)
CREATE TABLE IF NOT EXISTS interactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    subject VARCHAR(255),
    content TEXT,
    interaction_type_id UUID REFERENCES interaction_types(id),
    company_id UUID REFERENCES companies(id) ON DELETE CASCADE,
    contact_id UUID REFERENCES contacts(id) ON DELETE SET NULL,
    deal_id UUID REFERENCES deals(id) ON DELETE SET NULL,
    created_by UUID NOT NULL REFERENCES users(id),
    interaction_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create arli documents table (AI document processing)
CREATE TABLE IF NOT EXISTS arli_documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    content TEXT,
    metadata JSONB,
    source_url TEXT,
    document_type VARCHAR(100),
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create arli content blocks table
CREATE TABLE IF NOT EXISTS arli_content_blocks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID NOT NULL REFERENCES arli_documents(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    block_type VARCHAR(50) NOT NULL,
    metadata JSONB,
    order_index INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_companies_name ON companies(name);
CREATE INDEX IF NOT EXISTS idx_companies_status ON companies(company_status_id);
CREATE INDEX IF NOT EXISTS idx_companies_deleted ON companies(is_deleted);
CREATE INDEX IF NOT EXISTS idx_companies_created_by ON companies(created_by);

CREATE INDEX IF NOT EXISTS idx_contacts_name ON contacts(first_name, last_name);
CREATE INDEX IF NOT EXISTS idx_contacts_email ON contacts(email);
CREATE INDEX IF NOT EXISTS idx_contacts_company ON contacts(company_id);
CREATE INDEX IF NOT EXISTS idx_contacts_deleted ON contacts(is_deleted);
CREATE INDEX IF NOT EXISTS idx_contacts_created_by ON contacts(created_by);

CREATE INDEX IF NOT EXISTS idx_deals_title ON deals(title);
CREATE INDEX IF NOT EXISTS idx_deals_company ON deals(company_id);
CREATE INDEX IF NOT EXISTS idx_deals_stage ON deals(deal_stage_id);
CREATE INDEX IF NOT EXISTS idx_deals_created_by ON deals(created_by);
CREATE INDEX IF NOT EXISTS idx_deals_close_date ON deals(expected_close_date);

CREATE INDEX IF NOT EXISTS idx_interactions_company ON interactions(company_id);
CREATE INDEX IF NOT EXISTS idx_interactions_contact ON interactions(contact_id);
CREATE INDEX IF NOT EXISTS idx_interactions_deal ON interactions(deal_id);
CREATE INDEX IF NOT EXISTS idx_interactions_type ON interactions(interaction_type_id);
CREATE INDEX IF NOT EXISTS idx_interactions_date ON interactions(interaction_date);
CREATE INDEX IF NOT EXISTS idx_interactions_created_by ON interactions(created_by);

CREATE INDEX IF NOT EXISTS idx_arli_documents_type ON arli_documents(document_type);
CREATE INDEX IF NOT EXISTS idx_arli_documents_created_by ON arli_documents(created_by);
CREATE INDEX IF NOT EXISTS idx_arli_content_blocks_document ON arli_content_blocks(document_id);
CREATE INDEX IF NOT EXISTS idx_arli_content_blocks_type ON arli_content_blocks(block_type);
CREATE INDEX IF NOT EXISTS idx_arli_content_blocks_order ON arli_content_blocks(document_id, order_index);

-- Create triggers for updated_at columns
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE
    ON user_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_companies_updated_at BEFORE UPDATE
    ON companies FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_contacts_updated_at BEFORE UPDATE
    ON contacts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_deals_updated_at BEFORE UPDATE
    ON deals FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_interactions_updated_at BEFORE UPDATE
    ON interactions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_arli_documents_updated_at BEFORE UPDATE
    ON arli_documents FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_arli_content_blocks_updated_at BEFORE UPDATE
    ON arli_content_blocks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
MIGRATION_EOF

# Create flyway configuration  
echo "Creating Flyway configuration..."
cat > flyway.conf << 'FLYWAY_EOF'
# Flyway Configuration for GCP Cloud SQL
flyway.url=jdbc:postgresql://${db_host}:5432/${db_name}
flyway.user=${db_user}
flyway.password=${DB_PASSWORD}
flyway.schemas=public
flyway.locations=filesystem:/flyway/sql
flyway.baselineOnMigrate=true
flyway.baselineVersion=0
flyway.validateOnMigrate=true
flyway.cleanDisabled=true
flyway.connectRetries=5
flyway.connectRetriesInterval=10
FLYWAY_EOF

# Run Flyway migration using Docker
echo "Running Flyway migrations..."
docker run --rm \
    --network="host" \
    -v "$(pwd)/migrations:/flyway/sql" \
    -v "$(pwd)/flyway.conf:/flyway/conf/flyway.conf" \
    flyway/flyway:10-alpine \
    migrate

echo "Migration completed successfully!"

# Clean up migration files
cd /opt/platform
rm -rf $TEMP_DIR

# Enable and start the platform services
systemctl daemon-reload
systemctl enable platform-services
systemctl start platform-services

# Create log rotation for Docker logs
cat > /etc/logrotate.d/docker-platform << EOF
/var/lib/docker-volumes/*/logs/*.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 0644 root root
}
EOF

# Create health check script
cat > /opt/platform/health-check.sh << 'EOF'
#!/bin/bash
set -euo pipefail

# Health check script for platform services

services=("auth-service" "crm-backend" "gateway")
failed_services=()

for service in "$${services[@]}"; do
    if ! docker compose ps "$service" | grep -q "running"; then
        failed_services+=("$service")
    fi
done

if [ $${#failed_services[@]} -eq 0 ]; then
    echo "All services are healthy"
    exit 0
else
    echo "Failed services: $${failed_services[*]}"
    exit 1
fi
EOF

chmod +x /opt/platform/health-check.sh

# Set up cron job for health checks
echo "*/5 * * * * root /opt/platform/health-check.sh >> /var/log/platform-health.log 2>&1" >> /etc/crontab

# Create update script for pulling latest images
cat > /opt/platform/update-services.sh << 'EOF'
#!/bin/bash
set -euo pipefail

# Update script to pull latest images and restart services

cd /opt/platform

echo "Pulling latest images..."
docker compose pull

echo "Restarting services..."
docker compose up -d

echo "Cleaning up old images..."
docker image prune -f

echo "Update completed successfully"
EOF

chmod +x /opt/platform/update-services.sh

echo "Platform services startup completed successfully"