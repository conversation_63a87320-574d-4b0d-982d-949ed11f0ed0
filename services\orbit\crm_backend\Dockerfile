FROM golang:1.24-alpine AS builder

WORKDIR /app

# Copy go mod files from root
COPY go.mod go.sum ./

# Copy shared dependencies first
COPY shared/go/ ./shared/go/

# Copy service dependencies
COPY services/orbit/rest-api/ ./services/orbit/rest-api/

RUN go mod download

# Copy source code
COPY services/orbit/crm_backend/ ./services/orbit/crm_backend/

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -o crm_backend ./services/orbit/crm_backend

FROM alpine:latest

RUN apk --no-cache add ca-certificates wget
WORKDIR /root/

# Copy the binary from builder
COPY --from=builder /app/crm_backend .

# Copy database migrations (if running migrations from the app)
COPY services/orbit/db/migrations ./db/migrations/

EXPOSE 8003

CMD ["./crm_backend"]