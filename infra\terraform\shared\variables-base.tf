# Shared Variable Definitions
# This file contains common variable definitions that should be consistent across all environments

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
  
  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "Environment must be one of: dev, staging, prod."
  }
}

variable "project_id" {
  description = "GCP Project ID"
  type        = string
  
  validation {
    condition     = can(regex("^[a-z][a-z0-9-]{4,28}[a-z0-9]$", var.project_id))
    error_message = "Project ID must be 6-30 characters, start with a letter, and contain only lowercase letters, numbers, and hyphens."
  }
}

variable "project_name" {
  description = "Name of the project (used for resource naming)"
  type        = string
  default     = "platform"
  
  validation {
    condition     = can(regex("^[a-z][a-z0-9-]*[a-z0-9]$", var.project_name))
    error_message = "Project name must start with a letter, contain only lowercase letters, numbers, and hyphens."
  }
}

variable "region" {
  description = "GCP region for resources"
  type        = string
  default     = "australia-southeast1"
  
  validation {
    condition = contains([
      "australia-southeast1", "australia-southeast2",
      "us-central1", "us-east1", "us-west1", "us-west2", 
      "us-east4", "us-west3", "us-west4",
      "europe-west1", "europe-west2", "europe-west3", "europe-west4",
      "europe-north1", "europe-central2",
      "asia-east1", "asia-southeast1", "asia-northeast1", "asia-south1",
      "northamerica-northeast1", "southamerica-east1"
    ], var.region)
    error_message = "Region must be a valid GCP region."
  }
}

variable "zone" {
  description = "GCP zone for resources"
  type        = string
  default     = "australia-southeast1-a"
  
  validation {
    condition     = can(regex("^[a-z]+-[a-z]+[0-9]+-[a-z]$", var.zone))
    error_message = "Zone must be a valid GCP zone format (e.g., australia-southeast1-a)."
  }
}

# GitHub configuration for CI/CD
variable "github_repo_owner" {
  description = "GitHub repository owner (organization or username)"
  type        = string
  default     = ""
  
  validation {
    condition     = var.github_repo_owner == "" || can(regex("^[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?$", var.github_repo_owner))
    error_message = "GitHub repo owner must be a valid username or organization name."
  }
}

variable "github_repo_name" {
  description = "GitHub repository name"
  type        = string
  default     = ""
  
  validation {
    condition     = var.github_repo_name == "" || can(regex("^[a-zA-Z0-9._-]+$", var.github_repo_name))
    error_message = "GitHub repo name must contain only alphanumeric characters, dots, underscores, and hyphens."
  }
}

# SSL/TLS configuration
variable "ssl_domains" {
  description = "List of domains for SSL certificate"
  type        = list(string)
  default     = []
  
  validation {
    condition = alltrue([
      for domain in var.ssl_domains : can(regex("^[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?(\\.[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?)*$", domain))
    ])
    error_message = "All SSL domains must be valid domain names."
  }
}

# Environment-specific optional overrides
variable "custom_machine_types" {
  description = "Custom machine types override for environment"
  type = object({
    compute  = optional(string)
    database = optional(string)
  })
  default = {}
}

variable "custom_network_config" {
  description = "Custom network configuration override"
  type = object({
    vpc_cidr            = optional(string)
    public_subnet_cidr  = optional(string) 
    private_subnet_cidr = optional(string)
    ssh_source_ranges   = optional(list(string))
  })
  default = {}
}

variable "custom_security_config" {
  description = "Custom security configuration override"
  type = object({
    enable_ssl           = optional(bool)
    enable_cloud_armor   = optional(bool)
    enable_public_access = optional(bool)
    assign_external_ip   = optional(bool)
    create_static_ip     = optional(bool)
    enable_http_redirect = optional(bool)
    enable_cdn          = optional(bool)
  })
  default = {}
}

variable "custom_feature_flags" {
  description = "Custom feature flags override"
  type = object({
    enable_migration_automation = optional(bool)
    enable_monitoring          = optional(bool)
    enable_backup_automation   = optional(bool)
    schedule_shutdown         = optional(bool)
    auto_scaling_enabled      = optional(bool)
    enable_debug_mode         = optional(bool)
    enable_external_access    = optional(bool)
  })
  default = {}
}

variable "custom_scaling_config" {
  description = "Custom scaling configuration override"
  type = object({
    min_instance_count = optional(number)
    max_instance_count = optional(number)
  })
  default = {}
  
  validation {
    condition = (
      var.custom_scaling_config.min_instance_count == null || 
      (var.custom_scaling_config.min_instance_count >= 1 && var.custom_scaling_config.min_instance_count <= 20)
    )
    error_message = "Minimum instance count must be between 1 and 20."
  }
  
  validation {
    condition = (
      var.custom_scaling_config.max_instance_count == null || 
      (var.custom_scaling_config.max_instance_count >= 1 && var.custom_scaling_config.max_instance_count <= 50)
    )
    error_message = "Maximum instance count must be between 1 and 50."
  }
}

# Development IP ranges (for SSH access, etc.)
variable "admin_ip_ranges" {
  description = "IP ranges allowed for administrative access (SSH, etc.)"
  type        = list(string)
  default     = ["0.0.0.0/0"]  # Can be overridden per environment
  
  validation {
    condition = alltrue([
      for cidr in var.admin_ip_ranges : can(cidrhost(cidr, 0))
    ])
    error_message = "All admin IP ranges must be valid IPv4 CIDR blocks."
  }
}

# Additional production-specific variables (will be null/ignored in dev)
variable "rate_limit_requests_per_minute" {
  description = "Rate limit requests per minute (0 to disable)"
  type        = number
  default     = null
  
  validation {
    condition     = var.rate_limit_requests_per_minute == null || (var.rate_limit_requests_per_minute >= 0 && var.rate_limit_requests_per_minute <= 10000)
    error_message = "Rate limit must be between 0 and 10000 requests per minute."
  }
}

variable "blocked_regions" {
  description = "List of region codes to block"
  type        = list(string)
  default     = []
}

variable "blocked_ip_ranges" {
  description = "List of IP ranges to block"
  type        = list(string)
  default     = []
  
  validation {
    condition = alltrue([
      for cidr in var.blocked_ip_ranges : can(cidrhost(cidr, 0))
    ])
    error_message = "All blocked IP ranges must be valid IPv4 CIDR blocks."
  }
}

variable "notification_email" {
  description = "Email address for notifications"
  type        = string
  default     = ""
  
  validation {
    condition     = var.notification_email == "" || can(regex("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", var.notification_email))
    error_message = "Notification email must be a valid email address."
  }
}

variable "alert_email_addresses" {
  description = "List of email addresses for alerts"
  type        = list(string)
  default     = []
  
  validation {
    condition = alltrue([
      for email in var.alert_email_addresses : can(regex("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", email))
    ])
    error_message = "All alert email addresses must be valid email addresses."
  }
}

variable "data_retention_days" {
  description = "Number of days to retain logs and data"
  type        = number
  default     = null
  
  validation {
    condition     = var.data_retention_days == null || (var.data_retention_days >= 1 && var.data_retention_days <= 3650)
    error_message = "Data retention days must be between 1 and 3650 (10 years)."
  }
}

# Optional Slack integration
variable "slack_webhook_url" {
  description = "Slack webhook URL for notifications"
  type        = string
  default     = ""
  sensitive   = true
}