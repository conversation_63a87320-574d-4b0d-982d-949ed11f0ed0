import { h, FunctionalComponent } from 'preact';
import { useState, useEffect } from 'preact/hooks';
import { api } from '@/services/api';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend } from 'recharts';

interface Response {
  id: number;
  customer: string;
  type: string;
  content: string;
}

interface ResponseTrackerProps {
  /** Initial responses to display */
  initialResponses?: Response[];
}

const ResponseTracker: FunctionalComponent<ResponseTrackerProps> = ({ initialResponses = [] }) => {
  const [responses, setResponses] = useState<Response[]>(initialResponses);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<string>('');
  const [search, setSearch] = useState<string>('');

  useEffect(() => {
    const fetchResponses = async () => {
      setLoading(true);
      try {
        // Replace with actual API call if needed
        // const data = await api.getResponses();
        // setResponses(data);
      } catch (err) {
        setError('Failed to fetch responses.');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    // Uncomment if API call is needed
    // fetchResponses();
  }, []);

  const filteredResponses = responses.filter((response) =>
    response.type.toLowerCase().includes(filter.toLowerCase()) &&
    response.customer.toLowerCase().includes(search.toLowerCase())
  );

  const responseTypes = [...new Set(responses.map((response) => response.type))];

  return (
    <div class="p-4">
      <div class="flex justify-between mb-4">
        <input
          type="text"
          placeholder="Search by customer"
          class="border p-2 rounded"
          value={search}
          onInput={(e) => setSearch(e.currentTarget.value)}
        />
        <select
          class="border p-2 rounded"
          value={filter}
          onChange={(e) => setFilter(e.currentTarget.value)}
        >
          <option value="">All Types</option>
          {responseTypes.map((type) => (
            <option key={type} value={type}>{type}</option>
          ))}
        </select>
      </div>

      {loading && <div>Loading...</div>}
      {error && <div class="text-red-500">{error}</div>}

      {filteredResponses.length === 0 && !loading && !error && <div>No responses found.</div>}

      {filteredResponses.length > 0 && (
        <BarChart width={600} height={300} data={filteredResponses}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="customer" />
          <YAxis />
          <Tooltip />
          <Legend />
          <Bar dataKey="id" fill="#8884d8" />
        </BarChart>
      )}
    </div>
  );
};

export default ResponseTracker;
