/* tslint:disable */
/* eslint-disable */
/**
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { User } from './User';
import {
    UserFromJSON,
    UserFromJSONTyped,
    UserToJSON,
    UserToJSONTyped,
} from './User';

/**
 * 
 * @export
 * @interface Session
 */
export interface Session {
    /**
     * 
     * @type {string}
     * @memberof Session
     */
    accessToken?: string;
    /**
     * 
     * @type {string}
     * @memberof Session
     */
    refreshToken?: string;
    /**
     * 
     * @type {number}
     * @memberof Session
     */
    expiresIn?: number;
    /**
     * 
     * @type {string}
     * @memberof Session
     */
    tokenType?: string;
    /**
     * 
     * @type {User}
     * @memberof Session
     */
    user?: User;
}

/**
 * Check if a given object implements the Session interface.
 */
export function instanceOfSession(value: object): value is Session {
    return true;
}

export function SessionFromJSON(json: any): Session {
    return SessionFromJSONTyped(json, false);
}

export function SessionFromJSONTyped(json: any, ignoreDiscriminator: boolean): Session {
    if (json == null) {
        return json;
    }
    return {
        
        'accessToken': json['access_token'] == null ? undefined : json['access_token'],
        'refreshToken': json['refresh_token'] == null ? undefined : json['refresh_token'],
        'expiresIn': json['expires_in'] == null ? undefined : json['expires_in'],
        'tokenType': json['token_type'] == null ? undefined : json['token_type'],
        'user': json['user'] == null ? undefined : UserFromJSON(json['user']),
    };
}

  export function SessionToJSON(json: any): Session {
      return SessionToJSONTyped(json, false);
  }

  export function SessionToJSONTyped(value?: Session | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'access_token': value['accessToken'],
        'refresh_token': value['refreshToken'],
        'expires_in': value['expiresIn'],
        'token_type': value['tokenType'],
        'user': UserToJSON(value['user']),
    };
}

