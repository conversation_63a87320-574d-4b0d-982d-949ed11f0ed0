---
- name: Create monitoring directories
  file:
    path: "{{ item }}"
    state: directory
    mode: '0755'
  loop:
    - /var/lib/microservices/monitoring
    - /var/lib/microservices/monitoring/alerts
    - /var/log/microservices
  tags: ['monitoring', 'directories']

- name: Deploy monitoring script
  template:
    src: health-monitor.sh.j2
    dest: /usr/local/bin/microservices-health-monitor
    mode: '0755'
  tags: ['monitoring', 'scripts']

- name: Set up monitoring cron job
  cron:
    name: "Microservices health monitoring"
    minute: "*/5"
    job: "/usr/local/bin/microservices-health-monitor > /var/log/microservices/health-monitor.log 2>&1"
  tags: ['monitoring', 'cron']

- name: Configure service metrics collection
  template:
    src: metrics-collector.py.j2
    dest: /usr/local/bin/metrics-collector.py
    mode: '0755'
  when: enable_monitoring | default(true)
  tags: ['monitoring', 'metrics']

- name: Set up log aggregation
  template:
    src: log-aggregator.conf.j2
    dest: /etc/rsyslog.d/50-microservices.conf
    mode: '0644'
  notify: restart rsyslog
  tags: ['monitoring', 'logging']

- name: Create alert rules
  template:
    src: "alerts/{{ item }}.yml.j2"
    dest: "/var/lib/microservices/monitoring/alerts/{{ item }}.yml"
    mode: '0644'
  loop:
    - service-down
    - high-memory
    - high-cpu
    - deployment-failed
  tags: ['monitoring', 'alerts']

- name: Deploy monitoring dashboard
  template:
    src: monitoring-dashboard.json.j2
    dest: /var/lib/microservices/monitoring/dashboard.json
    mode: '0644'
  tags: ['monitoring', 'dashboard']