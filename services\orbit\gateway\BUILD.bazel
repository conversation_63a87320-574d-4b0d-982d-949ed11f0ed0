load("@gazelle//:def.bzl", "gazelle")
load("@rules_go//go:def.bzl", "go_binary", "go_library")
load("@rules_oci//oci:defs.bzl", "oci_image")
load("@rules_pkg//pkg:tar.bzl", "pkg_tar")

# Main library
go_library(
    name = "gateway_lib",
    srcs = ["main.go"],
    importpath = "github.com/TwoDotAi/mono/services/orbit/gateway",
    visibility = ["//visibility:public"],
    deps = [
        "//shared/go/logging",
        "//services/orbit/gateway/config",
        "//services/orbit/gateway/handlers",
        "//services/orbit/gateway/middleware",
        "//services/orbit/gateway/router",
        "@com_github_gin_gonic_gin//:gin",
    ],
)

go_binary(
    name = "gateway",
    embed = [":gateway_lib"],
    visibility = ["//visibility:public"],
)

# Package binary for container
pkg_tar(
    name = "gateway_binary_tar",
    srcs = [":gateway"],
    package_dir = "/",
)

# Test for the gateway - commented out until test files are created
# go_test(
#     name = "gateway_test",
#     srcs = [
#         "handlers/health_test.go",
#         "handlers/proxy_test.go",
#         "router/routes_test.go",
#     ],
#     embed = [":gateway_lib"],
#     deps = [
#         "@com_github_stretchr_testify//assert",
#         "@com_github_stretchr_testify//require",
#     ],
#     tags = ["unit"],
# )

# Integration test for the gateway - commented out until test file is created
# go_test(
#     name = "gateway_integration_test",
#     srcs = ["integration_test.go"],
#     embed = [":gateway_lib"],
#     deps = [
#         "@com_github_stretchr_testify//assert",
#         "@com_github_stretchr_testify//require",
#     ],
#     tags = ["integration"],
# )

# OCI image for gateway
oci_image(
    name = "gateway_image",
    base = "@distroless_base",
    entrypoint = ["/gateway"],
    env = {
        "PORT": "8085",
        "GIN_MODE": "release",
    },
    exposed_ports = ["8085"],
    tars = [":gateway_binary_tar"],
    visibility = ["//visibility:public"],
)

# Gazelle for dependency management
gazelle(name = "gazelle")
