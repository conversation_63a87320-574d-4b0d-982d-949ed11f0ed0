#!/usr/bin/env python3
"""
Direct backend runner for AI Agent Platform
"""
import os
import sys
import subprocess

# Set working directory
backend_dir = '/Users/<USER>/workspaces/git/ai-agent/backend'
os.chdir(backend_dir)

# Try to use the virtual environment python
venv_python = os.path.join(backend_dir, 'venv', 'bin', 'python')

# Check if venv exists
if os.path.exists(venv_python):
    print(f"Using virtual environment: {venv_python}")
    python_exec = venv_python
else:
    print("Using system python")
    python_exec = sys.executable

# Execute the uvicorn command
cmd = [
    python_exec, '-m', 'uvicorn', 
    'src.main:app',
    '--host', '0.0.0.0',
    '--port', '8000',
    '--reload',
    '--log-level', 'info'
]

print(f"Executing: {' '.join(cmd)}")
print(f"Working directory: {os.getcwd()}")

# Run the command
try:
    result = subprocess.run(cmd, cwd=backend_dir, check=False)
    print(f"Command completed with exit code: {result.returncode}")
except KeyboardInterrupt:
    print("\nServer stopped by user")
except Exception as e:
    print(f"Error running server: {e}")