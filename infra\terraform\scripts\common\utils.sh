#!/bin/bash
# Common utility functions for deployment scripts

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Helper functions for formatted output
print_error() {
    echo -e "${RED}❌ Error: $1${NC}" >&2
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_step() {
    echo -e "${CYAN}🚀 $1${NC}"
}

print_header() {
    echo ""
    echo -e "${PURPLE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    echo ""
}

# Logging functions
log_info() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] INFO: $1" | tee -a "${LOG_FILE:-/tmp/deployment.log}"
}

log_error() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1" | tee -a "${LOG_FILE:-/tmp/deployment.log}" >&2
}

log_warning() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] WARNING: $1" | tee -a "${LOG_FILE:-/tmp/deployment.log}"
}

# Check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check if a file exists and is readable
file_exists() {
    [[ -f "$1" && -r "$1" ]]
}

# Check if a directory exists
dir_exists() {
    [[ -d "$1" ]]
}

# Get the absolute path of a file or directory
get_absolute_path() {
    local path="$1"
    if [[ "$path" = /* ]]; then
        echo "$path"
    else
        echo "$PWD/$path"
    fi
}

# Find the workspace root by looking for WORKSPACE file
find_workspace_root() {
    local current_dir="$PWD"
    while [[ "$current_dir" != "/" ]]; do
        if [[ -f "$current_dir/WORKSPACE" ]]; then
            echo "$current_dir"
            return 0
        fi
        current_dir="$(dirname "$current_dir")"
    done
    
    print_error "Could not find WORKSPACE file. Are you in the correct directory?"
    return 1
}

# Change to workspace directory
change_to_workspace() {
    if [[ -n "${BUILD_WORKSPACE_DIRECTORY:-}" ]]; then
        cd "$BUILD_WORKSPACE_DIRECTORY"
    elif [[ -n "${TEST_WORKSPACE:-}" ]]; then
        cd "$TEST_WORKSPACE"
    else
        local workspace_root
        workspace_root=$(find_workspace_root)
        if [[ $? -eq 0 ]]; then
            cd "$workspace_root"
        else
            return 1
        fi
    fi
}

# URL encode a string (for database passwords with special characters)
url_encode() {
    local string="${1}"
    python3 -c "import urllib.parse; print(urllib.parse.quote('$string'))"
}

# Check if a port is open
check_port() {
    local host="$1"
    local port="$2"
    local timeout="${3:-5}"
    
    timeout "$timeout" bash -c "echo >/dev/tcp/$host/$port" 2>/dev/null
}

# Wait for a service to be ready
wait_for_service() {
    local host="$1"
    local port="$2"
    local timeout="${3:-60}"
    local check_interval="${4:-5}"
    
    local elapsed=0
    while [[ $elapsed -lt $timeout ]]; do
        if check_port "$host" "$port" 2; then
            print_success "Service at $host:$port is ready"
            return 0
        fi
        
        print_info "Waiting for service at $host:$port... (${elapsed}s/${timeout}s)"
        sleep "$check_interval"
        elapsed=$((elapsed + check_interval))
    done
    
    print_error "Service at $host:$port did not become ready within ${timeout}s"
    return 1
}

# Retry a command with exponential backoff
retry_command() {
    local max_attempts="$1"
    local delay="$2"
    local command=("${@:3}")
    
    local attempt=1
    while [[ $attempt -le $max_attempts ]]; do
        if "${command[@]}"; then
            return 0
        fi
        
        if [[ $attempt -eq $max_attempts ]]; then
            print_error "Command failed after $max_attempts attempts: ${command[*]}"
            return 1
        fi
        
        print_warning "Attempt $attempt failed. Retrying in ${delay}s..."
        sleep "$delay"
        delay=$((delay * 2))  # Exponential backoff
        attempt=$((attempt + 1))
    done
}

# Check if running in CI environment
is_ci() {
    [[ "${CI:-false}" == "true" ]] || [[ -n "${GITHUB_ACTIONS:-}" ]] || [[ -n "${BUILDKITE:-}" ]]
}

# Prompt for user confirmation (skip in CI)
confirm() {
    local message="$1"
    local default="${2:-n}"
    
    if is_ci; then
        print_info "Running in CI mode, assuming '$default' for: $message"
        [[ "$default" == "y" ]] || [[ "$default" == "Y" ]]
        return
    fi
    
    local prompt="$message (y/N): "
    if [[ "$default" == "y" ]] || [[ "$default" == "Y" ]]; then
        prompt="$message (Y/n): "
    fi
    
    read -p "$prompt" -r response
    response=${response:-$default}
    
    case "$response" in
        [yY]|[yY][eE][sS]) return 0 ;;
        *) return 1 ;;
    esac
}

# Clean up function (trap on EXIT)
cleanup() {
    local exit_code=$?
    if [[ $exit_code -ne 0 ]]; then
        print_error "Script failed with exit code $exit_code"
    fi
    
    # Clean up temporary files
    if [[ -n "${TEMP_DIR:-}" && -d "$TEMP_DIR" ]]; then
        rm -rf "$TEMP_DIR"
    fi
    
    exit $exit_code
}

# Set up error handling
setup_error_handling() {
    set -euo pipefail
    trap cleanup EXIT
    
    # Create temporary directory
    TEMP_DIR=$(mktemp -d)
    export TEMP_DIR
}

# Create a temporary file
create_temp_file() {
    local prefix="${1:-tmp}"
    mktemp "${TEMP_DIR}/${prefix}.XXXXXX"
}

# Check if we have required tools
check_required_tools() {
    local tools=("$@")
    local missing_tools=()
    
    for tool in "${tools[@]}"; do
        if ! command_exists "$tool"; then
            missing_tools+=("$tool")
        fi
    done
    
    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        print_error "Missing required tools: ${missing_tools[*]}"
        print_info "Please install the missing tools and try again"
        return 1
    fi
    
    return 0
}

# Parse command line arguments
parse_args() {
    local script_name="$1"
    shift
    
    # Default values
    ENVIRONMENT=""
    VERBOSE=false
    DRY_RUN=false
    FORCE=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--environment)
                ENVIRONMENT="$2"
                shift 2
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -n|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -f|--force)
                FORCE=true
                shift
                ;;
            -h|--help)
                print_usage "$script_name"
                exit 0
                ;;
            *)
                if [[ -z "$ENVIRONMENT" ]]; then
                    ENVIRONMENT="$1"
                else
                    print_error "Unknown option: $1"
                    print_usage "$script_name"
                    exit 1
                fi
                shift
                ;;
        esac
    done
    
    # Export variables for use in other scripts
    export ENVIRONMENT VERBOSE DRY_RUN FORCE
}

# Print usage information
print_usage() {
    local script_name="$1"
    echo "Usage: $script_name [OPTIONS] ENVIRONMENT"
    echo ""
    echo "Arguments:"
    echo "  ENVIRONMENT    Target environment (dev, staging, prod)"
    echo ""
    echo "Options:"
    echo "  -e, --environment ENV  Target environment (alternative to positional arg)"
    echo "  -v, --verbose         Enable verbose output"
    echo "  -n, --dry-run         Show what would be done without executing"
    echo "  -f, --force          Force execution without confirmation"
    echo "  -h, --help           Show this help message"
}

# Execute a command with optional dry-run support
execute() {
    local cmd=("$@")
    
    if [[ "$VERBOSE" == "true" ]]; then
        print_info "Executing: ${cmd[*]}"
    fi
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_info "[DRY RUN] Would execute: ${cmd[*]}"
        return 0
    fi
    
    "${cmd[@]}"
}

# Print script banner
print_banner() {
    local script_name="$1"
    local description="$2"
    
    print_header "🔧 $script_name"
    print_info "$description"
    print_info "Environment: $ENVIRONMENT"
    if [[ "$DRY_RUN" == "true" ]]; then
        print_warning "DRY RUN MODE - No changes will be made"
    fi
    echo ""
}