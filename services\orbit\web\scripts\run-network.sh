#!/bin/bash

echo "🚀 Setting up CRM for network access..."

# Set network IP configuration
./set-network-ip.sh

echo ""
echo "📋 Next steps:"
echo "1. In one terminal, start the backend:"
echo "   cd /Users/<USER>/Projects/mono && bazel run //crm_backend:crm_backend"
echo ""
echo "2. In another terminal, start the frontend:"
echo "   cd /Users/<USER>/Projects/mono/services/orbit/web && npm run dev"
echo ""
echo "3. Access from other devices on your network:"
echo "   Frontend: http://$(./set-network-ip.sh | grep 'Backend will be accessible' | cut -d' ' -f6 | sed 's/8003/8080/'):8080"
echo ""
echo "🔗 The frontend will automatically connect to the backend on the network IP!"