#!/bin/bash
set -e

# Logging setup
exec > >(tee /var/log/startup-script.log)
exec 2>&1
echo "=== Platform Services Startup Script - $(date) ==="

# Configure Docker authentication for COS
echo "Configuring Docker authentication for COS..."

# Get access token for Docker authentication  
echo "Getting access token for Docker..."
ACCESS_TOKEN=$(curl -s -H "Metadata-Flavor: Google" \
  "http://metadata.google.internal/computeMetadata/v1/instance/service-accounts/default/token" | \
  python3 -c "import sys, json; print(json.load(sys.stdin)['access_token'])" 2>/dev/null || echo "")

if [ -n "$ACCESS_TOKEN" ]; then
  echo "$ACCESS_TOKEN" | docker login -u oauth2accesstoken --password-stdin australia-southeast1-docker.pkg.dev
  echo "Docker authentication successful"
else
  echo "ERROR: Could not get access token for Docker authentication"
  exit 1
fi

# Create platform services directory in writable location for COS
mkdir -p /var/lib/platform
cd /var/lib/platform

# Get database password from Secret Manager
echo "Retrieving database password from Secret Manager..."

# Use REST API since gcloud is not available on Container-Optimized OS
SECRET_RESPONSE=$(curl -s -H "Authorization: Bearer $ACCESS_TOKEN" \
  "https://secretmanager.googleapis.com/v1/projects/twodot-agent-prod/secrets/platform-db-password/versions/latest:access" 2>/dev/null || echo "")

if [ -n "$SECRET_RESPONSE" ]; then
  DB_PASSWORD=$(echo "$SECRET_RESPONSE" | python3 -c "import sys, json, base64; data = json.load(sys.stdin); print(base64.b64decode(data['payload']['data']).decode())" 2>/dev/null || echo "placeholder")
else
  DB_PASSWORD="placeholder"
fi

if [ -z "$DB_PASSWORD" ] || [ "$DB_PASSWORD" = "placeholder" ]; then
  echo "ERROR: Could not retrieve database password from Secret Manager"
  echo "Using placeholder password - services will fail to start"
  DB_PASSWORD="placeholder"
else
  echo "Database password retrieved successfully: ${#DB_PASSWORD} characters"
fi

# Create Docker network
docker network create platform-network || true

# Deploy services
echo "=== Deploying Platform Services ==="

# Pull and run Auth service
echo "Deploying Auth service..."
docker pull australia-southeast1-docker.pkg.dev/twodot-agent-prod/platform-docker/auth:latest
docker stop auth || true
docker rm auth || true
docker run -d --name auth \
  --network platform-network \
  -p 8004:8004 \
  -e PORT=8004 \
  -e DATABASE_URL=************************************************************/platform_prod_db \
  -e LOG_LEVEL=info \
  --restart unless-stopped \
  australia-southeast1-docker.pkg.dev/twodot-agent-prod/platform-docker/auth:latest

# Pull and run CRM Backend service
echo "Deploying CRM Backend service..."
docker pull australia-southeast1-docker.pkg.dev/twodot-agent-prod/platform-docker/crm-backend:latest
docker stop crm-backend || true
docker rm crm-backend || true
docker run -d --name crm-backend \
  --network platform-network \
  -p 8003:8003 \
  -e PORT=8003 \
  -e DATABASE_URL=************************************************************/platform_prod_db \
  -e LOG_LEVEL=info \
  --restart unless-stopped \
  australia-southeast1-docker.pkg.dev/twodot-agent-prod/platform-docker/crm-backend:latest

# Pull and run Gateway service
echo "Deploying Gateway service..."
docker pull australia-southeast1-docker.pkg.dev/twodot-agent-prod/platform-docker/gateway:latest
docker stop gateway || true
docker rm gateway || true
docker run -d --name gateway \
  --network platform-network \
  -p 8085:8085 \
  -e PORT=8085 \
  -e AUTH_SERVICE_URL=http://auth:8004 \
  -e CRM_BACKEND_URL=http://crm-backend:8003 \
  -e LOG_LEVEL=info \
  --restart unless-stopped \
  australia-southeast1-docker.pkg.dev/twodot-agent-prod/platform-docker/gateway:latest

# Wait for services to start
sleep 30

# Verify services are running
echo "=== Service Status ==="
docker ps --filter network=platform-network

echo "=== Startup script completed successfully - $(date) ==="