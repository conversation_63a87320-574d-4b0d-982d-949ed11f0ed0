# Generic Environment Outputs
# This file contains outputs that are consistent across all environments

# Environment information
output "environment" {
  description = "Environment name"
  value       = var.environment
}

output "project_id" {
  description = "GCP Project ID"
  value       = var.project_id
}

output "region" {
  description = "GCP region"
  value       = var.region
}

output "zone" {
  description = "GCP zone"
  value       = var.zone
}

# Network outputs
output "vpc_network_name" {
  description = "Name of the VPC network"
  value       = module.network.vpc_name
}

output "vpc_network_self_link" {
  description = "Self link of the VPC network"
  value       = module.network.vpc_self_link
}

output "public_subnet_name" {
  description = "Name of the public subnet"
  value       = module.network.public_subnet_name
}

output "private_subnet_name" {
  description = "Name of the private subnet"
  value       = module.network.private_subnet_name
}

# Database outputs (consistent naming across environments)
output "database_private_ip" {
  description = "Private IP address of the database"
  value       = module.database.instance_private_ip
  sensitive   = true
}

output "database_name" {
  description = "Name of the application database"
  value       = module.database.database_name
}

output "database_user" {
  description = "Database username"
  value       = module.database.database_user
}

output "database_password_secret_name" {
  description = "Name of the secret containing the database password"
  value       = module.database.database_password_secret_name
}

output "database_connection_name" {
  description = "Database connection name for Cloud SQL"
  value       = module.database.instance_connection_name
}

# Storage outputs
output "web_bucket_name" {
  description = "Name of the web storage bucket"
  value       = module.storage.bucket_name
}

output "web_bucket_url" {
  description = "URL of the web storage bucket"
  value       = module.storage.bucket_url
}

# Registry outputs
output "registry_hostname" {
  description = "Hostname of the Artifact Registry"
  value       = module.registry.registry_hostname
}

output "registry_url" {
  description = "URL of the Artifact Registry"
  value       = module.registry.registry_url
}

# Compute outputs
output "instance_name" {
  description = "Name of the compute instance"
  value       = module.compute.instance_name
}

output "instance_internal_ip" {
  description = "Internal IP address of the compute instance"
  value       = module.compute.instance_internal_ip
  sensitive   = true
}

output "instance_external_ip" {
  description = "External IP address of the compute instance"
  value       = module.compute.instance_external_ip
  sensitive   = true
}

output "backend_service_self_link" {
  description = "Self link of the backend service"
  value       = module.compute.backend_service_self_link
}

# Load balancer outputs
output "load_balancer_ip" {
  description = "IP address of the load balancer"
  value       = module.loadbalancer.load_balancer_ip
}

output "ssl_domains" {
  description = "SSL domains configured for the load balancer"
  value       = var.ssl_domains
}

# OAuth secrets outputs
output "oauth_secrets" {
  description = "OAuth secrets information"
  value = {
    client_id_secret_name     = module.secrets.oauth_secrets.client_id
    client_secret_secret_name = module.secrets.oauth_secrets.client_secret
    state_secret_name         = module.secrets.oauth_secrets.state_secret
  }
  sensitive = true
}

# Environment-specific access information
output "dev_access_info" {
  description = "Development environment access information"
  value = var.environment == "dev" ? {
    project_id = var.project_id
    region     = var.region
    zone       = var.zone
    instance_name = module.compute.instance_name
  } : null
}

output "staging_access_info" {
  description = "Staging environment access information"
  value = var.environment == "staging" ? {
    project_id = var.project_id
    region     = var.region
    zone       = var.zone
    instance_name = module.compute.instance_name
  } : null
}

output "prod_access_info" {
  description = "Production environment access information"
  value = var.environment == "prod" ? {
    project_id = var.project_id
    region     = var.region
    zone       = var.zone
    instance_name = module.compute.instance_name
  } : null
}

# Deployment URLs
output "frontend_url" {
  description = "Frontend application URL"
  value = var.environment == "prod" ? "https://twodot.ai" : "https://${var.environment}.twodot.ai"
}

output "api_url" {
  description = "API base URL"
  value = var.environment == "prod" ? "https://api.twodot.ai" : "https://api.${var.environment}.twodot.ai"
}

# Configuration summary
output "environment_config" {
  description = "Environment configuration summary"
  value = {
    environment    = var.environment
    machine_types  = local.machine_types
    network_config = {
      vpc_cidr            = local.vpc_cidr
      public_subnet_cidr  = local.public_subnet_cidr
      private_subnet_cidr = local.private_subnet_cidr
    }
    security_config = local.security_config
    feature_flags   = local.feature_flags
    database_config = {
      name               = local.database_name
      user               = local.database_user
      postgres_version   = local.env_config.database_config.postgres_version
      availability_type  = local.env_config.database_config.availability_type
      deletion_protection = local.env_config.database_config.deletion_protection
    }
  }
}