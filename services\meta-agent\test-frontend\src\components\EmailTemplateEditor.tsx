import { h, FunctionalComponent } from 'preact';
import { useState, useEffect } from 'preact/hooks';
import { api } from '@/services/api';

interface EmailTemplate {
  subject: string;
  body: string;
}

type EmailTemplateFormData = EmailTemplate;

interface FormErrors {
  subject?: string;
  body?: string;
}

/**
 * @description Email template editor component.
 * @param {object} props - Component props.
 * @param {EmailTemplate} [props.initialTemplate] - Initial template data for editing.
 * @param {(template: EmailTemplate) => void} props.onSave - Callback function when the template is saved.
 */
const EmailTemplateEditor: FunctionalComponent<{
  initialTemplate?: EmailTemplate;
  onSave: (template: EmailTemplate) => void;
}> = ({ initialTemplate, onSave }) => {
  const [formData, setFormData] = useState<EmailTemplateFormData>(
    initialTemplate || { subject: '', body: '' }
  );
  const [errors, setErrors] = useState<FormErrors>({});
  const [loading, setLoading] = useState(false);
  const [preview, setPreview] = useState(false);

  useEffect(() => {
    if (initialTemplate) {
      setFormData(initialTemplate);
    }
  }, [initialTemplate]);

  const validateForm = (data: EmailTemplateFormData): boolean => {
    const newErrors: FormErrors = {};
    
    if (!data.subject.trim()) {
      newErrors.subject = 'Subject is required';
    }
    if (!data.body.trim()) {
      newErrors.body = 'Body is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };


  const handleSubmit = async (e: Event) => {
    e.preventDefault();
    if (!validateForm(formData)) return;

    const data = formData;
    setLoading(true);
    try {
      // Placeholder for API call
      // const response = await api.saveEmailTemplate(data);
      onSave(data);
    } catch (error) {
      console.error('Error saving template:', error);
      // Handle error, e.g., display error message
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} class="p-4">
      <div class="mb-4">
        <label for="subject" class="block text-sm font-medium text-gray-700">Subject</label>
        <input 
          type="text" 
          id="subject" 
          value={formData.subject}
          onInput={(e) => setFormData(prev => ({ ...prev, subject: (e.target as HTMLInputElement).value }))}
          class="mt-1 p-2 border rounded w-full" 
        />
        {errors.subject && <p class="text-red-500 text-xs mt-1">{errors.subject.message}</p>}
      </div>
      <div class="mb-4">
        <label for="body" class="block text-sm font-medium text-gray-700">Body</label>
        <textarea 
          id="body" 
          value={formData.body}
          onInput={(e) => setFormData(prev => ({ ...prev, body: (e.target as HTMLTextAreaElement).value }))}
          class="mt-1 p-2 border rounded w-full h-48" 
        />
        {errors.body && <p class="text-red-500 text-xs mt-1">{errors.body.message}</p>}
      </div>
      {/* Placeholder for merge field insertion */}
      <div class="mb-4">
        <button type="button" onClick={() => setPreview(!preview)} class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
          {preview ? 'Edit' : 'Preview'}
        </button>
      </div>
      {preview && (
        <div class="border rounded p-4">
          {/* Placeholder for preview functionality */}
          <p>Subject: {/* Display subject */}</p>
          <p>{/* Display body */}</p>
        </div>
      )}
      <button type="submit" disabled={loading} class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
        {loading ? 'Saving...' : 'Save'}
      </button>
    </form>
  );
};

export default EmailTemplateEditor;
