#!/bin/bash
# Terraform deployment wrapper with automatic migration
set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_error() {
    echo -e "${RED}❌ Error: $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_step() {
    echo -e "${BLUE}🚀 $1${NC}"
}

# Get the workspace root
if [ -n "$BUILD_WORKSPACE_DIRECTORY" ]; then
    cd "$BUILD_WORKSPACE_DIRECTORY"
elif [ -n "$TEST_WORKSPACE" ]; then
    cd "$TEST_WORKSPACE"
else
    # Find the workspace root by looking for WORKSPACE file
    CURRENT_DIR="$PWD"
    while [ "$CURRENT_DIR" != "/" ]; do
        if [ -f "$CURRENT_DIR/WORKSPACE" ]; then
            cd "$CURRENT_DIR"
            break
        fi
        CURRENT_DIR="$(dirname "$CURRENT_DIR")"
    done
fi

echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "🚀 TERRAFORM AUTO-DEPLOYMENT"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""
print_info "Starting Terraform deployment with automatic migration detection..."
echo ""

# Parse arguments
ENVIRONMENT="${1:-dev}"
DEPLOY_WEB="${2:-true}"
FORCE_MIGRATION="${3:-false}"

# Export environment variables for the terraform script
export DEPLOY_WEB="$DEPLOY_WEB"
export FORCE_MIGRATION="$FORCE_MIGRATION"

print_info "Configuration:"
echo "  • Environment: $ENVIRONMENT"
echo "  • Deploy Web: $DEPLOY_WEB"
echo "  • Force Migration: $FORCE_MIGRATION"
echo ""

# Run terraform with auto-migration
print_step "Running Terraform with auto-migration"
echo ""

if bazel run //terraform:terraform_with_auto_migration -- "$ENVIRONMENT"; then
    print_success "Terraform auto-deployment completed successfully"
else
    print_error "Terraform auto-deployment failed"
    exit 1
fi

echo ""
print_success "Auto-deployment completed! 🎉"