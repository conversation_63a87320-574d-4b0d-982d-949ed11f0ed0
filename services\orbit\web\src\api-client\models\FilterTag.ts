/* tslint:disable */
/* eslint-disable */
/**
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface FilterTag
 */
export interface FilterTag {
    /**
     * 
     * @type {string}
     * @memberof FilterTag
     */
    id?: string;
    /**
     * 
     * @type {string}
     * @memberof FilterTag
     */
    name?: string;
    /**
     * 
     * @type {string}
     * @memberof FilterTag
     */
    color?: string;
    /**
     * 
     * @type {Date}
     * @memberof FilterTag
     */
    createdAt?: Date;
}

/**
 * Check if a given object implements the FilterTag interface.
 */
export function instanceOfFilterTag(value: object): value is FilterTag {
    return true;
}

export function FilterTagFromJSON(json: any): FilterTag {
    return FilterTagFromJSONTyped(json, false);
}

export function FilterTagFromJSONTyped(json: any, ignoreDiscriminator: boolean): FilterTag {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'] == null ? undefined : json['id'],
        'name': json['name'] == null ? undefined : json['name'],
        'color': json['color'] == null ? undefined : json['color'],
        'createdAt': json['created_at'] == null ? undefined : (new Date(json['created_at'])),
    };
}

  export function FilterTagToJSON(json: any): FilterTag {
      return FilterTagToJSONTyped(json, false);
  }

  export function FilterTagToJSONTyped(value?: FilterTag | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'name': value['name'],
        'color': value['color'],
        'created_at': value['createdAt'] == null ? undefined : ((value['createdAt']).toISOString()),
    };
}

