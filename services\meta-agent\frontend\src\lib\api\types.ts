/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export interface paths {
    "/api/v1/auth/register": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Register
         * @description Register a new user
         */
        post: operations["register_api_v1_auth_register_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/auth/login": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Login
         * @description Login with username/email and password
         */
        post: operations["login_api_v1_auth_login_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/auth/refresh": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Refresh Token
         * @description Refresh access token using refresh token
         */
        post: operations["refresh_token_api_v1_auth_refresh_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/auth/me": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Current User Info
         * @description Get current user information
         */
        get: operations["get_current_user_info_api_v1_auth_me_get"];
        /**
         * Update Current User
         * @description Update current user information
         */
        put: operations["update_current_user_api_v1_auth_me_put"];
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/auth/change-password": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Change Password
         * @description Change user password
         */
        post: operations["change_password_api_v1_auth_change_password_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/auth/create-admin": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Create Admin User
         * @description Create admin user (superuser only)
         */
        post: operations["create_admin_user_api_v1_auth_create_admin_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/auth/oauth/providers": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Oauth Providers
         * @description Get available OAuth providers
         */
        get: operations["get_oauth_providers_api_v1_auth_oauth_providers_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/auth/oauth/init": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Init Oauth Flow
         * @description Initialize OAuth authentication flow
         */
        post: operations["init_oauth_flow_api_v1_auth_oauth_init_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/auth/oauth/callback": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Oauth Callback
         * @description Handle OAuth callback and complete authentication
         */
        post: operations["oauth_callback_api_v1_auth_oauth_callback_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/auth/login/mfa": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Login With Mfa
         * @description Login with MFA support
         */
        post: operations["login_with_mfa_api_v1_auth_login_mfa_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/auth/mfa/setup/totp": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Setup Totp Mfa
         * @description Setup TOTP (authenticator app) MFA device
         */
        post: operations["setup_totp_mfa_api_v1_auth_mfa_setup_totp_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/auth/mfa/setup/sms": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Setup Sms Mfa
         * @description Setup SMS MFA device
         */
        post: operations["setup_sms_mfa_api_v1_auth_mfa_setup_sms_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/auth/mfa/verify/setup": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Verify Mfa Setup
         * @description Verify and complete MFA device setup
         */
        post: operations["verify_mfa_setup_api_v1_auth_mfa_verify_setup_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/auth/mfa/devices": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Mfa Devices
         * @description Get user's MFA devices
         */
        get: operations["get_mfa_devices_api_v1_auth_mfa_devices_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/auth/mfa/devices/{device_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post?: never;
        /**
         * Remove Mfa Device
         * @description Remove MFA device
         */
        delete: operations["remove_mfa_device_api_v1_auth_mfa_devices__device_id__delete"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/auth/mfa/send-sms": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Send Sms Code
         * @description Send SMS verification code
         */
        post: operations["send_sms_code_api_v1_auth_mfa_send_sms_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/auth/permissions": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get User Permissions
         * @description Get current user's permissions and roles
         */
        get: operations["get_user_permissions_api_v1_auth_permissions_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/auth/roles": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get All Roles
         * @description Get all available roles (admin only)
         */
        get: operations["get_all_roles_api_v1_auth_roles_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/auth/permissions/all": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get All Permissions
         * @description Get all available permissions (admin only)
         */
        get: operations["get_all_permissions_api_v1_auth_permissions_all_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/auth/roles/create": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Create Role
         * @description Create new role (admin only)
         */
        post: operations["create_role_api_v1_auth_roles_create_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/auth/roles/assign": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Assign Role
         * @description Assign role to user (admin only)
         */
        post: operations["assign_role_api_v1_auth_roles_assign_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/auth/roles/remove": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post?: never;
        /**
         * Remove Role
         * @description Remove role from user (admin only)
         */
        delete: operations["remove_role_api_v1_auth_roles_remove_delete"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/auth/security/login-attempts": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Login Attempts
         * @description Get user's recent login attempts
         */
        get: operations["get_login_attempts_api_v1_auth_security_login_attempts_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/agents/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * List Agents
         * @description List agents with optional filters
         */
        get: operations["list_agents_api_v1_agents__get"];
        put?: never;
        /**
         * Create Agent
         * @description Create a new AI agent with full-stack code generation and deployment
         */
        post: operations["create_agent_api_v1_agents__post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/agents/{agent_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Agent
         * @description Get agent by ID
         */
        get: operations["get_agent_api_v1_agents__agent_id__get"];
        /**
         * Update Agent
         * @description Update an existing agent
         */
        put: operations["update_agent_api_v1_agents__agent_id__put"];
        post?: never;
        /**
         * Delete Agent
         * @description Delete an agent
         */
        delete: operations["delete_agent_api_v1_agents__agent_id__delete"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/agents/{agent_id}/start": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Start Agent
         * @description Start an agent
         */
        post: operations["start_agent_api_v1_agents__agent_id__start_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/agents/{agent_id}/stop": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Stop Agent
         * @description Stop an agent
         */
        post: operations["stop_agent_api_v1_agents__agent_id__stop_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/agents/{agent_id}/deployment": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Agent Deployment
         * @description Get agent deployment status and information
         */
        get: operations["get_agent_deployment_api_v1_agents__agent_id__deployment_get"];
        put?: never;
        post?: never;
        /**
         * Stop Agent Deployment
         * @description Stop and remove agent deployment
         */
        delete: operations["stop_agent_deployment_api_v1_agents__agent_id__deployment_delete"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/agents/{agent_id}/deploy": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Deploy Agent Endpoint
         * @description Deploy an existing agent (if not already deployed)
         */
        post: operations["deploy_agent_endpoint_api_v1_agents__agent_id__deploy_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/agents/{agent_id}/heartbeat": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Update Heartbeat
         * @description Update agent heartbeat
         */
        post: operations["update_heartbeat_api_v1_agents__agent_id__heartbeat_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tasks/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * List Tasks
         * @description List tasks with optional filters
         */
        get: operations["list_tasks_api_v1_tasks__get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/tasks/{task_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Task
         * @description Get task by ID
         */
        get: operations["get_task_api_v1_tasks__task_id__get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/orchestrations/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * List Orchestrations
         * @description List orchestrations for current user
         */
        get: operations["list_orchestrations_api_v1_orchestrations__get"];
        put?: never;
        /**
         * Create Orchestration
         * @description Create a new orchestration
         */
        post: operations["create_orchestration_api_v1_orchestrations__post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/orchestrations/{orchestration_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Orchestration
         * @description Get orchestration by ID
         */
        get: operations["get_orchestration_api_v1_orchestrations__orchestration_id__get"];
        /**
         * Update Orchestration
         * @description Update an orchestration
         */
        put: operations["update_orchestration_api_v1_orchestrations__orchestration_id__put"];
        post?: never;
        /**
         * Delete Orchestration
         * @description Delete an orchestration
         */
        delete: operations["delete_orchestration_api_v1_orchestrations__orchestration_id__delete"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/orchestrations/{orchestration_id}/start": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Start Orchestration
         * @description Start an orchestration
         */
        post: operations["start_orchestration_api_v1_orchestrations__orchestration_id__start_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/orchestrations/{orchestration_id}/stop": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Stop Orchestration
         * @description Stop an orchestration
         */
        post: operations["stop_orchestration_api_v1_orchestrations__orchestration_id__stop_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/orchestrations/{orchestration_id}/pause": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Pause Orchestration
         * @description Pause an orchestration
         */
        post: operations["pause_orchestration_api_v1_orchestrations__orchestration_id__pause_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/orchestrations/{orchestration_id}/resume": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Resume Orchestration
         * @description Resume an orchestration
         */
        post: operations["resume_orchestration_api_v1_orchestrations__orchestration_id__resume_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/orchestrations/{orchestration_id}/progress": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Orchestration Progress
         * @description Get orchestration progress
         */
        get: operations["get_orchestration_progress_api_v1_orchestrations__orchestration_id__progress_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/orchestrations/{orchestration_id}/agents": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Add Agent To Orchestration
         * @description Add an agent to orchestration
         */
        post: operations["add_agent_to_orchestration_api_v1_orchestrations__orchestration_id__agents_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/orchestrations/{orchestration_id}/agents/{agent_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post?: never;
        /**
         * Remove Agent From Orchestration
         * @description Remove an agent from orchestration
         */
        delete: operations["remove_agent_from_orchestration_api_v1_orchestrations__orchestration_id__agents__agent_id__delete"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/runtime/agents": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * List Active Agents
         * @description List all active agent runtimes
         */
        get: operations["list_active_agents_api_v1_runtime_agents_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/runtime/agents/{agent_id}/info": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Agent Runtime Info
         * @description Get runtime information for a specific agent
         */
        get: operations["get_agent_runtime_info_api_v1_runtime_agents__agent_id__info_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/runtime/agents/{agent_id}/pause": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Pause Agent Runtime
         * @description Pause an agent runtime
         */
        post: operations["pause_agent_runtime_api_v1_runtime_agents__agent_id__pause_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/runtime/agents/{agent_id}/resume": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Resume Agent Runtime
         * @description Resume an agent runtime
         */
        post: operations["resume_agent_runtime_api_v1_runtime_agents__agent_id__resume_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/runtime/agents/{agent_id}/execute-task": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Execute Task On Agent
         * @description Execute a task on a specific agent
         */
        post: operations["execute_task_on_agent_api_v1_runtime_agents__agent_id__execute_task_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/runtime/agents/{agent_id}/queue-task": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Queue Task For Agent
         * @description Add a task to an agent's execution queue
         */
        post: operations["queue_task_for_agent_api_v1_runtime_agents__agent_id__queue_task_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/runtime/system/stats": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get System Stats
         * @description Get system statistics
         */
        get: operations["get_system_stats_api_v1_runtime_system_stats_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/runtime/system/health": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get System Health
         * @description Get system health status
         */
        get: operations["get_system_health_api_v1_runtime_system_health_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/runtime/system/shutdown": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Shutdown All Agents
         * @description Shutdown all agent runtimes (admin only)
         */
        post: operations["shutdown_all_agents_api_v1_runtime_system_shutdown_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/runtime/messaging/status": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Messaging Status
         * @description Get message queue service status
         */
        get: operations["get_messaging_status_api_v1_runtime_messaging_status_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/runtime/messaging/connect": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Connect Message Queue
         * @description Connect to message queue service
         */
        post: operations["connect_message_queue_api_v1_runtime_messaging_connect_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/api/v1/ai/chat": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Chat Completion
         * @description Generate chat completion using AI providers
         */
        post: operations["chat_completion_api_v1_api_v1_ai_chat_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/api/v1/ai/completion": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Text Completion
         * @description Generate text completion using AI providers
         */
        post: operations["text_completion_api_v1_api_v1_ai_completion_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/api/v1/ai/code": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Generate Code
         * @description Generate code using AI providers
         */
        post: operations["generate_code_api_v1_api_v1_ai_code_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/api/v1/ai/embeddings": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Generate Embeddings
         * @description Generate text embeddings using AI providers
         */
        post: operations["generate_embeddings_api_v1_api_v1_ai_embeddings_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/api/v1/ai/process": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Process Ai Request
         * @description Process generic AI request
         */
        post: operations["process_ai_request_api_v1_api_v1_ai_process_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/api/v1/ai/models": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Available Models
         * @description Get all available AI models by provider
         */
        get: operations["get_available_models_api_v1_api_v1_ai_models_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/api/v1/ai/providers": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Providers
         * @description Get list of configured AI providers
         */
        get: operations["get_providers_api_v1_api_v1_ai_providers_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/api/v1/ai/health": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Health Check
         * @description Check health status of all AI providers
         */
        get: operations["health_check_api_v1_api_v1_ai_health_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/api/v1/ai/capabilities": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Capabilities
         * @description Get AI capabilities by task type
         */
        get: operations["get_capabilities_api_v1_api_v1_ai_capabilities_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/api/v1/ai/batch": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Batch Process
         * @description Process multiple AI requests in batch
         */
        post: operations["batch_process_api_v1_api_v1_ai_batch_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/generation/templates": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Available Templates
         * @description Get all available agent templates
         */
        get: operations["get_available_templates_api_v1_generation_templates_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/generation/templates/{agent_type}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Templates By Type
         * @description Get templates filtered by agent type
         */
        get: operations["get_templates_by_type_api_v1_generation_templates__agent_type__get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/generation/templates/language/{language}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Templates By Language
         * @description Get templates filtered by programming language
         */
        get: operations["get_templates_by_language_api_v1_generation_templates_language__language__get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/generation/validate": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Validate Generation Request
         * @description Validate an agent generation request
         */
        post: operations["validate_generation_request_api_v1_generation_validate_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/generation/generate": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Generate Agent
         * @description Generate a new agent
         */
        post: operations["generate_agent_api_v1_generation_generate_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/generation/status/{generation_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Generation Status
         * @description Get the status of an agent generation
         */
        get: operations["get_generation_status_api_v1_generation_status__generation_id__get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/generation/download/{generation_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Download Generated Agent
         * @description Download the generated agent code as a ZIP file
         */
        get: operations["download_generated_agent_api_v1_generation_download__generation_id__get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/generation/cleanup/{generation_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post?: never;
        /**
         * Cleanup Generation
         * @description Clean up generation files and status
         */
        delete: operations["cleanup_generation_api_v1_generation_cleanup__generation_id__delete"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/generation/capabilities": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Available Capabilities
         * @description Get list of all available capabilities
         */
        get: operations["get_available_capabilities_api_v1_generation_capabilities_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/generation/build-deploy/{generation_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Build Deploy Agent
         * @description Build, deploy and test A2A communication for a generated agent
         */
        post: operations["build_deploy_agent_api_v1_generation_build_deploy__generation_id__post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/generation/build-status/{generation_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Build Status
         * @description Get build and deployment status
         */
        get: operations["get_build_status_api_v1_generation_build_status__generation_id__get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/generation/examples": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Generation Examples
         * @description Get example agent generation configurations
         */
        get: operations["get_generation_examples_api_v1_generation_examples_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/vector/knowledge/store": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Store Agent Knowledge
         * @description Store knowledge for an agent
         */
        post: operations["store_agent_knowledge_api_v1_vector_knowledge_store_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/vector/knowledge/search": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Search Agent Knowledge
         * @description Search agent knowledge base
         */
        post: operations["search_agent_knowledge_api_v1_vector_knowledge_search_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/vector/conversation/store": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Store Conversation Context
         * @description Store conversation context for an agent
         */
        post: operations["store_conversation_context_api_v1_vector_conversation_store_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/vector/conversation/context": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Get Conversation Context
         * @description Get relevant conversation context for current query
         */
        post: operations["get_conversation_context_api_v1_vector_conversation_context_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/vector/code/store": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Store Code Snippet
         * @description Store code snippet for agent reference
         */
        post: operations["store_code_snippet_api_v1_vector_code_store_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/vector/code/search": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Search Code Snippets
         * @description Search code snippets
         */
        post: operations["search_code_snippets_api_v1_vector_code_search_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/vector/data": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post?: never;
        /**
         * Delete Collection Data
         * @description Delete data from collection based on filter conditions
         */
        delete: operations["delete_collection_data_api_v1_vector_data_delete"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/vector/collections/{collection_name}/info": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Collection Info
         * @description Get information about a collection
         */
        get: operations["get_collection_info_api_v1_vector_collections__collection_name__info_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/vector/health": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Vector Db Health Check
         * @description Check vector database health
         */
        get: operations["vector_db_health_check_api_v1_vector_health_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/a2a/register": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Register Agent
         * @description Register an agent with the A2A protocol
         */
        post: operations["register_agent_api_v1_a2a_register_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/a2a/unregister/{agent_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post?: never;
        /**
         * Unregister Agent
         * @description Unregister an agent from the A2A protocol
         */
        delete: operations["unregister_agent_api_v1_a2a_unregister__agent_id__delete"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/a2a/send/{sender_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Send Message
         * @description Send a message from one agent to another
         */
        post: operations["send_message_api_v1_a2a_send__sender_id__post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/a2a/broadcast/{sender_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Broadcast Message
         * @description Broadcast a message to multiple agents
         */
        post: operations["broadcast_message_api_v1_a2a_broadcast__sender_id__post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/a2a/conversation/establish/{initiator_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Establish Conversation
         * @description Establish a conversation between multiple agents
         */
        post: operations["establish_conversation_api_v1_a2a_conversation_establish__initiator_id__post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/a2a/conversation/{conversation_id}/history": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Conversation History
         * @description Get conversation history
         */
        get: operations["get_conversation_history_api_v1_a2a_conversation__conversation_id__history_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/a2a/capabilities/query": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Query Capabilities
         * @description Query agent capabilities
         */
        post: operations["query_capabilities_api_v1_a2a_capabilities_query_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/a2a/messages/search": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Search Messages
         * @description Search through A2A messages
         */
        post: operations["search_messages_api_v1_a2a_messages_search_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/a2a/agents/active": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Active Agents
         * @description Get list of active agents
         */
        get: operations["get_active_agents_api_v1_a2a_agents_active_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/a2a/agents/{agent_id}/status": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Agent Status
         * @description Get status of a specific agent
         */
        get: operations["get_agent_status_api_v1_a2a_agents__agent_id__status_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/google-adk/generate/text": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Generate Text
         * @description Generate text using Google's generative AI models
         */
        post: operations["generate_text_api_v1_google_adk_generate_text_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/google-adk/generate/code": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Generate Code
         * @description Generate code using Google's code generation models
         */
        post: operations["generate_code_api_v1_google_adk_generate_code_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/google-adk/analyze/image": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Analyze Image Base64
         * @description Analyze image using Google's vision models (base64 input)
         */
        post: operations["analyze_image_base64_api_v1_google_adk_analyze_image_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/google-adk/analyze/image/upload": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Analyze Image Upload
         * @description Analyze uploaded image using Google's vision models
         */
        post: operations["analyze_image_upload_api_v1_google_adk_analyze_image_upload_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/google-adk/embeddings": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Generate Embeddings
         * @description Generate embeddings using Google's embedding models
         */
        post: operations["generate_embeddings_api_v1_google_adk_embeddings_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/google-adk/chat": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Chat
         * @description Chat with Google's conversational AI models
         */
        post: operations["chat_api_v1_google_adk_chat_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/google-adk/agent/create": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Create Agent With Adk
         * @description Create an AI agent configuration optimized for Google ADK
         */
        post: operations["create_agent_with_adk_api_v1_google_adk_agent_create_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/google-adk/agent/enhance-knowledge": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Enhance Agent Knowledge
         * @description Enhance agent with knowledge using Google's AI capabilities
         */
        post: operations["enhance_agent_knowledge_api_v1_google_adk_agent_enhance_knowledge_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/google-adk/agent/analyze-performance": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Analyze Agent Performance
         * @description Analyze agent performance using Google's AI
         */
        post: operations["analyze_agent_performance_api_v1_google_adk_agent_analyze_performance_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/google-adk/models/{model}/info": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Model Info
         * @description Get information about a specific Google AI model
         */
        get: operations["get_model_info_api_v1_google_adk_models__model__info_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/google-adk/health": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Health Check
         * @description Check Google ADK service health
         */
        get: operations["health_check_api_v1_google_adk_health_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/deployment/deploy": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Deploy Agent
         * @description Deploy an agent with specified configuration.
         */
        post: operations["deploy_agent_api_v1_deployment_deploy_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/deployment/status/{deployment_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Deployment Status
         * @description Get detailed status of a specific deployment.
         */
        get: operations["get_deployment_status_api_v1_deployment_status__deployment_id__get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/deployment/list": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * List Deployments
         * @description List deployments with optional filtering.
         */
        get: operations["list_deployments_api_v1_deployment_list_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/deployment/rollback/{deployment_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Rollback Deployment
         * @description Rollback a deployment to the previous version.
         */
        post: operations["rollback_deployment_api_v1_deployment_rollback__deployment_id__post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/deployment/terminate/{deployment_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post?: never;
        /**
         * Terminate Deployment
         * @description Terminate a deployment.
         */
        delete: operations["terminate_deployment_api_v1_deployment_terminate__deployment_id__delete"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/deployment/scale/{deployment_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Scale Deployment
         * @description Scale a deployment to the specified number of replicas.
         */
        post: operations["scale_deployment_api_v1_deployment_scale__deployment_id__post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/deployment/logs/{deployment_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Deployment Logs
         * @description Get logs for a specific deployment.
         */
        get: operations["get_deployment_logs_api_v1_deployment_logs__deployment_id__get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/deployment/metrics/{deployment_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Deployment Metrics
         * @description Get metrics for a specific deployment.
         */
        get: operations["get_deployment_metrics_api_v1_deployment_metrics__deployment_id__get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/deployment/strategies": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Deployment Strategies
         * @description Get available deployment strategies and their descriptions.
         */
        get: operations["get_deployment_strategies_api_v1_deployment_strategies_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/deployment/health": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Deployment Service Health
         * @description Check health of the deployment service.
         */
        get: operations["deployment_service_health_api_v1_deployment_health_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/evolution/metrics": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Evolution Metrics
         * @description Get platform evolution metrics and statistics.
         */
        get: operations["get_evolution_metrics_api_v1_evolution_metrics_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/evolution/initiatives": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Improvement Initiatives
         * @description Get list of improvement initiatives with optional filtering.
         */
        get: operations["get_improvement_initiatives_api_v1_evolution_initiatives_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/evolution/patterns": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Usage Patterns
         * @description Get identified usage patterns and trends.
         */
        get: operations["get_usage_patterns_api_v1_evolution_patterns_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/evolution/insights": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Platform Insights
         * @description Get AI-generated platform insights and recommendations.
         */
        get: operations["get_platform_insights_api_v1_evolution_insights_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/evolution/initiatives/{initiative_id}/approve": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Approve Initiative
         * @description Approve an improvement initiative for implementation.
         */
        post: operations["approve_initiative_api_v1_evolution_initiatives__initiative_id__approve_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/evolution/initiatives/{initiative_id}/reject": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Reject Initiative
         * @description Reject an improvement initiative with reason.
         */
        post: operations["reject_initiative_api_v1_evolution_initiatives__initiative_id__reject_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/evolution/scan": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Trigger Evolution Scan
         * @description Manually trigger a platform evolution analysis scan.
         */
        post: operations["trigger_evolution_scan_api_v1_evolution_scan_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/evolution/scan/{scan_id}/status": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Scan Status
         * @description Get status of an evolution scan.
         */
        get: operations["get_scan_status_api_v1_evolution_scan__scan_id__status_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/evolution/history": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Evolution History
         * @description Get platform evolution history and changes over time.
         */
        get: operations["get_evolution_history_api_v1_evolution_history_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/evolution/config": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Evolution Config
         * @description Get platform evolution system configuration.
         */
        get: operations["get_evolution_config_api_v1_evolution_config_get"];
        put?: never;
        /**
         * Update Evolution Config
         * @description Update platform evolution system configuration.
         */
        post: operations["update_evolution_config_api_v1_evolution_config_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/evolution/health": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Evolution Service Health
         * @description Check health of the evolution service.
         */
        get: operations["evolution_service_health_api_v1_evolution_health_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/workflows/create": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Create Workflow
         * @description Create a new workflow.
         */
        post: operations["create_workflow_api_v1_workflows_create_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/workflows/list": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * List Workflows
         * @description List workflows with optional filtering.
         */
        get: operations["list_workflows_api_v1_workflows_list_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/workflows/{workflow_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Workflow
         * @description Get a specific workflow definition.
         */
        get: operations["get_workflow_api_v1_workflows__workflow_id__get"];
        /**
         * Update Workflow
         * @description Update an existing workflow.
         */
        put: operations["update_workflow_api_v1_workflows__workflow_id__put"];
        post?: never;
        /**
         * Delete Workflow
         * @description Delete a workflow.
         */
        delete: operations["delete_workflow_api_v1_workflows__workflow_id__delete"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/workflows/{workflow_id}/execute": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Execute Workflow
         * @description Execute a workflow.
         */
        post: operations["execute_workflow_api_v1_workflows__workflow_id__execute_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/workflows/{workflow_id}/executions": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Workflow Executions
         * @description Get execution history for a workflow.
         */
        get: operations["get_workflow_executions_api_v1_workflows__workflow_id__executions_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/workflows/executions/{execution_id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Execution Status
         * @description Get detailed status of a workflow execution.
         */
        get: operations["get_execution_status_api_v1_workflows_executions__execution_id__get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/workflows/executions/{execution_id}/nodes": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Execution Node Details
         * @description Get detailed execution status for each node in the workflow.
         */
        get: operations["get_execution_node_details_api_v1_workflows_executions__execution_id__nodes_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/workflows/executions/{execution_id}/cancel": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Cancel Execution
         * @description Cancel a running workflow execution.
         */
        post: operations["cancel_execution_api_v1_workflows_executions__execution_id__cancel_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/workflows/node-types": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Available Node Types
         * @description Get all available node types and their configurations.
         */
        get: operations["get_available_node_types_api_v1_workflows_node_types_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/workflows/{workflow_id}/activate": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Activate Workflow
         * @description Activate a workflow for automatic execution.
         */
        post: operations["activate_workflow_api_v1_workflows__workflow_id__activate_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/workflows/{workflow_id}/deactivate": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Deactivate Workflow
         * @description Deactivate a workflow.
         */
        post: operations["deactivate_workflow_api_v1_workflows__workflow_id__deactivate_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/workflows/executions/{execution_id}/stream": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Stream Execution Logs
         * @description Stream real-time execution logs.
         */
        get: operations["stream_execution_logs_api_v1_workflows_executions__execution_id__stream_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/workflows/health": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Workflow Service Health
         * @description Check health of the workflow service.
         */
        get: operations["workflow_service_health_api_v1_workflows_health_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/ai/providers": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Available Providers
         * @description Get list of available AI providers
         */
        get: operations["get_available_providers_api_v1_ai_providers_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/ai/parse-requirements": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Parse Requirements
         * @description Parse natural language requirements into agent configuration
         */
        post: operations["parse_requirements_api_v1_ai_parse_requirements_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/ai/analyze-code": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Analyze Code
         * @description Analyze code for quality, security, and performance
         */
        post: operations["analyze_code_api_v1_ai_analyze_code_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/ai/generate-documentation": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Generate Documentation
         * @description Generate documentation for code
         */
        post: operations["generate_documentation_api_v1_ai_generate_documentation_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/ai/generate": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Generate Ai Content
         * @description Generate AI content using specified capability
         */
        post: operations["generate_ai_content_api_v1_ai_generate_post"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/ai/capabilities": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Ai Capabilities
         * @description Get available AI capabilities
         */
        get: operations["get_ai_capabilities_api_v1_ai_capabilities_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/v1/ai/health": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Ai Service Health
         * @description Check AI service health and provider status
         */
        get: operations["ai_service_health_api_v1_ai_health_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Root
         * @description Root endpoint
         */
        get: operations["root__get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/health": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Health Check
         * @description Health check endpoint
         */
        get: operations["health_check_health_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/metrics": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Metrics
         * @description Prometheus metrics endpoint
         */
        get: operations["metrics_metrics_get"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
}
export type webhooks = Record<string, never>;
export interface components {
    schemas: {
        /**
         * A2AResponse
         * @description Response structure for A2A communications
         */
        A2AResponse: {
            /** Success */
            success: boolean;
            /** Message Id */
            message_id: string;
            /** Data */
            data?: Record<string, never> | null;
            /** Error */
            error?: string | null;
            /**
             * Timestamp
             * Format: date-time
             */
            timestamp?: string;
        };
        /** AIGenerationRequest */
        AIGenerationRequest: {
            /**
             * Prompt
             * @description AI prompt
             */
            prompt: string;
            /**
             * Capability
             * @description AI capability to use
             */
            capability: string;
            /**
             * Max Tokens
             * @description Maximum tokens to generate
             * @default 4000
             */
            max_tokens: number;
            /**
             * Temperature
             * @description Temperature for generation
             * @default 0.7
             */
            temperature: number;
            /**
             * System Message
             * @description System message
             */
            system_message?: string | null;
            /**
             * Preferred Provider
             * @description Preferred AI provider
             */
            preferred_provider?: string | null;
        };
        /** AIGenerationResponse */
        AIGenerationResponse: {
            /** Content */
            content: string;
            /** Provider */
            provider: string;
            /** Model */
            model: string;
            /** Usage */
            usage: Record<string, never>;
            /** Metadata */
            metadata: Record<string, never>;
        };
        /**
         * AIProvider
         * @description Supported AI providers
         * @enum {string}
         */
        AIProvider: "openai" | "anthropic" | "google" | "azure" | "huggingface" | "local";
        /**
         * AIRequestSchema
         * @description AI request schema
         */
        AIRequestSchema: {
            task_type: components["schemas"]["TaskType"];
            provider?: components["schemas"]["AIProvider"] | null;
            /** Model */
            model: string;
            /** Input Data */
            input_data: Record<string, never>;
            /** Parameters */
            parameters?: Record<string, never> | null;
            /**
             * Timeout
             * @default 300
             */
            timeout: number | null;
        };
        /**
         * AIResponseSchema
         * @description AI response schema
         */
        AIResponseSchema: {
            /** Task Id */
            task_id: string;
            /** Provider */
            provider: string;
            /** Model */
            model: string;
            /** Result */
            result: unknown;
            /** Metadata */
            metadata: Record<string, never>;
            /** Usage */
            usage?: Record<string, never> | null;
            /** Execution Time */
            execution_time: number;
            /** Success */
            success: boolean;
            /** Error */
            error?: string | null;
        };
        /** ActiveAgentsResponse */
        ActiveAgentsResponse: {
            /** Agents */
            agents: components["schemas"]["AgentStatusResponse"][];
            /** Total Active */
            total_active: number;
            /** Total Inactive */
            total_inactive: number;
        };
        /** AddAgentRequest */
        AddAgentRequest: {
            /**
             * Agent Id
             * Format: uuid
             */
            agent_id: string;
            /**
             * Role
             * @default worker
             */
            role: string;
        };
        /**
         * AdvancedOptions
         * @description Advanced configuration options
         */
        AdvancedOptions: {
            /**
             * Use Ai Workflow
             * @description Use AI-powered workflow
             * @default false
             */
            use_ai_workflow: boolean | null;
            /**
             * Custom Templates
             * @description Custom templates
             */
            custom_templates?: string[] | null;
            /**
             * Integration Endpoints
             * @description Integration endpoints
             */
            integration_endpoints?: string[] | null;
            /**
             * Testing Enabled
             * @description Enable testing
             * @default true
             */
            testing_enabled: boolean | null;
            /**
             * Documentation Level
             * @description Documentation level
             * @default standard
             */
            documentation_level: ("minimal" | "standard" | "comprehensive") | null;
        };
        /** AgentADKRequest */
        AgentADKRequest: {
            /**
             * Agent Type
             * @description Type of agent
             */
            agent_type: string;
            /**
             * Capabilities
             * @description Agent capabilities
             */
            capabilities: string[];
            /**
             * Model Preferences
             * @description Model preferences
             */
            model_preferences?: {
                [key: string]: string;
            };
            /**
             * Metadata
             * @description Additional metadata
             */
            metadata?: Record<string, never> | null;
        };
        /** AgentADKResponse */
        AgentADKResponse: {
            /** Success */
            success: boolean;
            /** Agent Config */
            agent_config: Record<string, never>;
            /** Recommended Models */
            recommended_models: {
                [key: string]: string;
            };
            /** Features */
            features: {
                [key: string]: boolean;
            };
        };
        /**
         * AgentCapability
         * @description Agent capability definition
         */
        AgentCapability: {
            /** Name */
            name: string;
            /** Description */
            description: string;
            /** Version */
            version: string;
            /** Parameters */
            parameters?: Record<string, never>;
            /** Constraints */
            constraints?: Record<string, never>;
        };
        /**
         * AgentCreate
         * @description Comprehensive agent creation schema that accepts all GenerationRequest fields
         */
        AgentCreate: {
            /** Name */
            name: string;
            /** Description */
            description?: string | null;
            /**
             * Type
             * @description Agent type
             * @default assistant
             * @enum {string}
             */
            type: "assistant" | "analyst" | "specialist" | "coordinator" | "processor" | "monitor" | "fullstack" | "api_service" | "background_worker" | "data_processor" | "integration";
            /** Config */
            config?: Record<string, never>;
            /** Capabilities */
            capabilities?: string[];
            /**
             * Requirements
             * @description Natural language requirements for AI generation
             */
            requirements?: string | null;
            /**
             * Framework
             * @description Framework to use for the agent
             */
            framework?: ("fastapi" | "flask" | "express" | "custom") | null;
            /**
             * Language
             * @description Programming language for the agent
             */
            language?: ("python" | "javascript" | "typescript") | null;
            /** @description Deployment configuration */
            deployment?: components["schemas"]["DeploymentConfig"] | null;
            /** @description Advanced configuration options */
            advanced_options?: components["schemas"]["AdvancedOptions"] | null;
        };
        /** AgentListResponse */
        AgentListResponse: {
            /** Agents */
            agents: components["schemas"]["AgentResponse"][];
            /** Total */
            total: number;
            /** Limit */
            limit: number;
            /** Offset */
            offset: number;
        };
        /** AgentResponse */
        AgentResponse: {
            /** Name */
            name: string;
            /** Description */
            description?: string | null;
            /**
             * Type
             * @description Agent type
             * @default assistant
             * @enum {string}
             */
            type: "assistant" | "analyst" | "specialist" | "coordinator" | "processor" | "monitor" | "fullstack" | "api_service" | "background_worker" | "data_processor" | "integration";
            /** Config */
            config?: Record<string, never>;
            /** Capabilities */
            capabilities?: string[];
            /**
             * Id
             * Format: uuid
             */
            id: string;
            status: components["schemas"]["AgentStatus"];
            /** Version */
            version: string;
            /**
             * Owner Id
             * Format: uuid
             */
            owner_id: string;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
            /**
             * Updated At
             * Format: date-time
             */
            updated_at: string;
            /** Last Heartbeat */
            last_heartbeat: string | null;
            /** Cpu Usage */
            cpu_usage: number | null;
            /** Memory Usage */
            memory_usage: number | null;
        };
        /** AgentStartResponse */
        AgentStartResponse: {
            /**
             * Agent Id
             * Format: uuid
             */
            agent_id: string;
            /** Success */
            success: boolean;
            /** Message */
            message: string;
        };
        /**
         * AgentStatus
         * @description Agent status enumeration
         * @enum {string}
         */
        AgentStatus: "created" | "starting" | "running" | "paused" | "stopped" | "error" | "terminated";
        /** AgentStatusResponse */
        AgentStatusResponse: {
            /** Agent Id */
            agent_id: string;
            /** Status */
            status: string;
            /** Agent Type */
            agent_type: string;
            /** Capabilities */
            capabilities: components["schemas"]["AgentCapability"][];
            /** Last Heartbeat */
            last_heartbeat: string;
            /** Registered At */
            registered_at: string;
            /** Metadata */
            metadata: Record<string, never>;
        };
        /**
         * AgentType
         * @description Supported agent types for generation
         * @enum {string}
         */
        AgentType: "assistant" | "analyst" | "specialist" | "coordinator" | "processor" | "monitor" | "fullstack";
        /** AgentUpdate */
        AgentUpdate: {
            /** Name */
            name?: string | null;
            /** Description */
            description?: string | null;
            /** Config */
            config?: Record<string, never> | null;
            /** Capabilities */
            capabilities?: string[] | null;
            /** Constraints */
            constraints?: Record<string, never> | null;
        };
        /** Body_analyze_image_upload_api_v1_google_adk_analyze_image_upload_post */
        Body_analyze_image_upload_api_v1_google_adk_analyze_image_upload_post: {
            /**
             * File
             * Format: binary
             */
            file: string;
        };
        /** Body_login_api_v1_auth_login_post */
        Body_login_api_v1_auth_login_post: {
            /** Grant Type */
            grant_type?: string | null;
            /** Username */
            username: string;
            /** Password */
            password: string;
            /**
             * Scope
             * @default
             */
            scope: string;
            /** Client Id */
            client_id?: string | null;
            /** Client Secret */
            client_secret?: string | null;
        };
        /** Body_refresh_token_api_v1_auth_refresh_post */
        Body_refresh_token_api_v1_auth_refresh_post: {
            /** Refresh Token */
            refresh_token: string;
        };
        /** BroadcastMessageRequest */
        BroadcastMessageRequest: {
            /**
             * Subject
             * @description Broadcast subject
             */
            subject: string;
            /**
             * Payload
             * @description Message payload
             */
            payload: Record<string, never>;
            /**
             * Filter Agent Types
             * @description Filter by agent types
             */
            filter_agent_types?: string[] | null;
            /**
             * Filter Capabilities
             * @description Filter by capabilities
             */
            filter_capabilities?: string[] | null;
            /** @default medium */
            priority: components["schemas"]["MessagePriority"];
        };
        /** CodeAnalysisRequest */
        CodeAnalysisRequest: {
            /**
             * Code
             * @description Code to analyze
             */
            code: string;
            /**
             * Language
             * @description Programming language
             */
            language: string;
            /**
             * Analysis Type
             * @description Type of analysis to perform
             * @default full
             */
            analysis_type: string;
        };
        /** CodeAnalysisResponse */
        CodeAnalysisResponse: {
            /** Quality Score */
            quality_score: number;
            /** Issues */
            issues: string[];
            /** Suggestions */
            suggestions: string[];
            /** Complexity */
            complexity: string;
            /** Maintainability */
            maintainability: string;
            /** Security Concerns */
            security_concerns: string[];
            /** Performance Notes */
            performance_notes: string[];
        };
        /** CodeGenerationResponse */
        CodeGenerationResponse: {
            /** Success */
            success: boolean;
            /** Code */
            code: string;
            /** Language */
            language: string;
            /** Model */
            model: string;
            /** Metadata */
            metadata?: Record<string, never>;
        };
        /** CodeResult */
        CodeResult: {
            /** Id */
            id: string;
            /** Score */
            score: number;
            /** Title */
            title: string;
            /** Code */
            code: string;
            /** Language */
            language: string;
            /** Description */
            description: string;
            /** Tags */
            tags: string[];
            /** Created At */
            created_at: string;
            /** Metadata */
            metadata: Record<string, never>;
        };
        /** CollectionInfoResponse */
        CollectionInfoResponse: {
            /** Success */
            success: boolean;
            /** Info */
            info: Record<string, never>;
        };
        /**
         * CompletionRequest
         * @description Text completion request
         */
        CompletionRequest: {
            /** Prompt */
            prompt: string;
            /**
             * Model
             * @default gpt-3.5-turbo
             */
            model: string | null;
            provider?: components["schemas"]["AIProvider"] | null;
            /**
             * Temperature
             * @default 0.7
             */
            temperature: number | null;
            /**
             * Max Tokens
             * @default 1000
             */
            max_tokens: number | null;
        };
        /** ContextResult */
        ContextResult: {
            /** Id */
            id: string;
            /** Score */
            score: number;
            /** Message */
            message: string;
            /** Role */
            role: string;
            /** Conversation Id */
            conversation_id: string;
            /** Created At */
            created_at: string;
            /** Metadata */
            metadata: Record<string, never>;
        };
        /** ConversationHistoryResponse */
        ConversationHistoryResponse: {
            /** Success */
            success: boolean;
            /** Conversation Id */
            conversation_id: string;
            /** Messages */
            messages: components["schemas"]["ConversationMessage"][];
            /** Total Messages */
            total_messages: number;
        };
        /** ConversationMessage */
        ConversationMessage: {
            /** Id */
            id: string;
            type: components["schemas"]["MessageType"];
            /** Sender Id */
            sender_id: string;
            /** Receiver Id */
            receiver_id: string | null;
            /** Subject */
            subject: string;
            /** Payload */
            payload: Record<string, never>;
            /** Timestamp */
            timestamp: string;
        };
        /** CreateRoleRequest */
        CreateRoleRequest: {
            /** Name */
            name: string;
            /** Display Name */
            display_name: string;
            /** Description */
            description?: string | null;
            /** Permissions */
            permissions: string[];
        };
        /** DeleteDataRequest */
        DeleteDataRequest: {
            /**
             * Collection Name
             * @description Collection name (without prefix)
             */
            collection_name: string;
            /**
             * Filter Conditions
             * @description Filter conditions for deletion
             */
            filter_conditions: Record<string, never>;
        };
        /** DeleteDataResponse */
        DeleteDataResponse: {
            /** Success */
            success: boolean;
            /** Message */
            message: string;
        };
        /**
         * DeploymentConfig
         * @description Deployment configuration for agents
         */
        DeploymentConfig: {
            /**
             * Port
             * @description Port number
             */
            port?: number | null;
            /**
             * Environment
             * @description Environment variables
             */
            environment?: {
                [key: string]: string;
            } | null;
            /** @description Resource limits */
            resources?: components["schemas"]["ResourceLimits"] | null;
            /**
             * Auto Scale
             * @description Enable auto-scaling
             * @default false
             */
            auto_scale: boolean | null;
            /**
             * Replicas
             * @description Number of replicas
             * @default 1
             */
            replicas: number | null;
        };
        /** DeploymentListResponse */
        DeploymentListResponse: {
            /** Deployments */
            deployments: components["schemas"]["DeploymentStatusResponse"][];
            /** Total */
            total: number;
            /** Active */
            active: number;
            /** Failed */
            failed: number;
        };
        /** DeploymentRequest */
        DeploymentRequest: {
            /**
             * Agent Definition
             * @description Agent definition to deploy
             */
            agent_definition: Record<string, never>;
            /**
             * @description Deployment strategy
             * @default rolling
             */
            strategy: components["schemas"]["DeploymentStrategy"];
            /**
             * Replicas
             * @description Number of replicas
             * @default 1
             */
            replicas: number;
            /**
             * Resources
             * @description Resource requirements
             */
            resources?: Record<string, never> | null;
            /**
             * Environment Variables
             * @description Environment variables
             */
            environment_variables?: {
                [key: string]: string;
            } | null;
            /**
             * Health Check Path
             * @description Health check endpoint path
             * @default /health
             */
            health_check_path: string;
            /**
             * Port Range Start
             * @description Port range start
             */
            port_range_start?: number | null;
            /**
             * Port Range End
             * @description Port range end
             */
            port_range_end?: number | null;
        };
        /** DeploymentResponse */
        DeploymentResponse: {
            /** Deployment Id */
            deployment_id: string;
            /** Status */
            status: string;
            /** Port */
            port: number | null;
            /** Endpoints */
            endpoints: string[];
            /** Created At */
            created_at: string;
        };
        /** DeploymentStatusResponse */
        DeploymentStatusResponse: {
            /** Deployment Id */
            deployment_id: string;
            /** Status */
            status: string;
            /** Stage */
            stage: string;
            /** Progress */
            progress: number;
            /** Logs */
            logs: string[];
            /** Health Status */
            health_status: string;
            /** Endpoints */
            endpoints: string[];
            /** Metrics */
            metrics: Record<string, never>;
            /** Updated At */
            updated_at: string;
        };
        /**
         * DeploymentStrategy
         * @description Deployment strategies.
         * @enum {string}
         */
        DeploymentStrategy: "blue_green" | "rolling" | "canary" | "recreate";
        /** DocumentationRequest */
        DocumentationRequest: {
            /**
             * Code
             * @description Code to document
             */
            code: string;
            /**
             * Language
             * @description Programming language
             */
            language: string;
            /**
             * Doc Type
             * @description Type of documentation
             * @default api
             */
            doc_type: string;
        };
        /** DocumentationResponse */
        DocumentationResponse: {
            /** Documentation */
            documentation: string;
            /**
             * Format
             * @default markdown
             */
            format: string;
        };
        /** EmbeddingResponse */
        EmbeddingResponse: {
            /** Success */
            success: boolean;
            /** Embeddings */
            embeddings: number[][];
            /** Model */
            model: string;
            /** Dimensions */
            dimensions: number;
            /** Metadata */
            metadata?: Record<string, never>;
        };
        /** EstablishConversationRequest */
        EstablishConversationRequest: {
            /**
             * Participant Ids
             * @description List of participant agent IDs
             */
            participant_ids: string[];
            /**
             * Context
             * @description Conversation context
             */
            context: Record<string, never>;
        };
        /** EstablishConversationResponse */
        EstablishConversationResponse: {
            /** Success */
            success: boolean;
            /** Conversation Id */
            conversation_id: string;
            /** Participants */
            participants: string[];
        };
        /** EvolutionMetrics */
        EvolutionMetrics: {
            /** Total Improvements */
            total_improvements: number;
            /** Active Initiatives */
            active_initiatives: number;
            /** Completed Initiatives */
            completed_initiatives: number;
            /** Performance Improvements */
            performance_improvements: number;
            /** Capability Enhancements */
            capability_enhancements: number;
            /** Usability Improvements */
            usability_improvements: number;
            /** Last Update */
            last_update: string;
        };
        /** GenerationRequestModel */
        GenerationRequestModel: {
            /**
             * Agent Name
             * @description Name of the agent
             */
            agent_name: string;
            /** @description Type of agent to generate */
            agent_type: components["schemas"]["AgentType"];
            /** @description Programming language for the agent */
            language: components["schemas"]["ProgrammingLanguage"];
            /**
             * Description
             * @description Description of the agent
             */
            description: string;
            /**
             * Capabilities
             * @description List of agent capabilities
             */
            capabilities: string[];
            /**
             * Configuration
             * @description Custom configuration
             */
            configuration?: Record<string, never>;
            /**
             * Custom Logic
             * @description Custom logic code
             */
            custom_logic?: string | null;
            /**
             * Deployment Config
             * @description Deployment configuration
             */
            deployment_config?: Record<string, never> | null;
        };
        /** GenerationResponse */
        GenerationResponse: {
            /** Generation Id */
            generation_id: string;
            /**
             * Status
             * @default pending
             */
            status: string;
            /**
             * Message
             * @default Agent generation started
             */
            message: string;
            /** Download Url */
            download_url?: string | null;
        };
        /** GenerationStatus */
        GenerationStatus: {
            /** Generation Id */
            generation_id: string;
            /** Status */
            status: string;
            /** Progress */
            progress: number;
            /** Message */
            message: string;
            /** Download Url */
            download_url?: string | null;
            /** Error */
            error?: string | null;
        };
        /** GetContextRequest */
        GetContextRequest: {
            /**
             * Agent Id
             * @description Agent ID
             */
            agent_id: string;
            /**
             * Query
             * @description Context query
             */
            query: string;
            /**
             * Limit
             * @description Maximum context items
             * @default 5
             */
            limit: number;
            /**
             * Score Threshold
             * @description Minimum similarity score
             * @default 0.6
             */
            score_threshold: number;
        };
        /** GetContextResponse */
        GetContextResponse: {
            /** Success */
            success: boolean;
            /** Context */
            context: components["schemas"]["ContextResult"][];
            /** Total Found */
            total_found: number;
        };
        /** HTTPValidationError */
        HTTPValidationError: {
            /** Detail */
            detail?: components["schemas"]["ValidationError"][];
        };
        /** HealthCheckResponse */
        HealthCheckResponse: {
            /** Status */
            status: string;
            /** Vertex Ai */
            vertex_ai: Record<string, never>;
            /** Generative Ai */
            generative_ai: Record<string, never>;
            /** Available Models */
            available_models: string[];
            /** Timestamp */
            timestamp: string;
        };
        /** HealthResponse */
        HealthResponse: {
            /** Success */
            success: boolean;
            /** Status */
            status: string;
            /** Details */
            details: Record<string, never>;
        };
        /** ImageAnalysisRequest */
        ImageAnalysisRequest: {
            /**
             * Prompt
             * @description Analysis prompt
             */
            prompt: string;
            /**
             * Image Base64
             * @description Base64 encoded image
             */
            image_base64?: string | null;
            /**
             * Model
             * @description Model to use
             * @default gemini-pro-vision
             */
            model: string;
            /**
             * Temperature
             * @description Temperature
             */
            temperature?: number | null;
        };
        /** ImageAnalysisResponse */
        ImageAnalysisResponse: {
            /** Success */
            success: boolean;
            /** Analysis */
            analysis: string;
            /** Model */
            model: string;
            /** Metadata */
            metadata?: Record<string, never>;
        };
        /** ImprovementInitiative */
        ImprovementInitiative: {
            /** Id */
            id: string;
            /** Title */
            title: string;
            /** Description */
            description: string;
            /** Category */
            category: string;
            /** Priority */
            priority: string;
            /** Status */
            status: string;
            /** Impact Score */
            impact_score: number;
            /** Effort Estimate */
            effort_estimate: number;
            /** Created At */
            created_at: string;
            /** Updated At */
            updated_at: string;
            /** Completion Date */
            completion_date?: string | null;
            /** Metrics */
            metrics: Record<string, never>;
        };
        /** KnowledgeEnhancementRequest */
        KnowledgeEnhancementRequest: {
            /**
             * Agent Id
             * @description Agent ID to enhance
             */
            agent_id: string;
            /**
             * Knowledge Sources
             * @description Knowledge sources
             */
            knowledge_sources: Record<string, never>[];
        };
        /** KnowledgeEnhancementResponse */
        KnowledgeEnhancementResponse: {
            /** Success */
            success: boolean;
            /** Sources Processed */
            sources_processed: number;
            /** Message */
            message: string;
        };
        /** KnowledgeResult */
        KnowledgeResult: {
            /** Id */
            id: string;
            /** Score */
            score: number;
            /** Content */
            content: string;
            /** Agent Id */
            agent_id: string;
            /** Category */
            category: string;
            /** Metadata */
            metadata: Record<string, never>;
            /** Created At */
            created_at: string;
        };
        /** LoginWithMFARequest */
        LoginWithMFARequest: {
            /**
             * Email
             * Format: email
             */
            email: string;
            /** Password */
            password: string;
            /** Mfa Token */
            mfa_token?: string | null;
        };
        /** MFASetupSMSRequest */
        MFASetupSMSRequest: {
            /** Device Name */
            device_name: string;
            /** Phone Number */
            phone_number: string;
        };
        /** MFASetupTOTPRequest */
        MFASetupTOTPRequest: {
            /** Device Name */
            device_name: string;
        };
        /** MFATokenRequest */
        MFATokenRequest: {
            /** Token */
            token: string;
        };
        /** MFAVerifyRequest */
        MFAVerifyRequest: {
            /** Device Id */
            device_id: string;
            /** Code */
            code: string;
        };
        /**
         * MessagePriority
         * @description Message priority levels
         * @enum {string}
         */
        MessagePriority: "low" | "medium" | "high" | "critical";
        /** MessageSearchResult */
        MessageSearchResult: {
            /** Id */
            id: string;
            /** Score */
            score: number;
            /** Content */
            content: string;
            /** Agent Id */
            agent_id: string;
            /** Metadata */
            metadata: Record<string, never>;
            /** Created At */
            created_at: string;
        };
        /**
         * MessageType
         * @description A2A message types
         * @enum {string}
         */
        MessageType: "request" | "response" | "broadcast" | "handshake" | "heartbeat" | "terminate" | "error";
        /** ModelInfoResponse */
        ModelInfoResponse: {
            /** Model */
            model: string;
            /** Name */
            name: string;
            /** Description */
            description: string;
            /** Capabilities */
            capabilities: string[];
            /** Metadata */
            metadata: Record<string, never>;
        };
        /** NodeExecutionResponse */
        NodeExecutionResponse: {
            /** Node Id */
            node_id: string;
            /** Node Name */
            node_name: string;
            /** Status */
            status: string;
            /** Started At */
            started_at: string | null;
            /** Completed At */
            completed_at: string | null;
            /** Duration */
            duration: number | null;
            /** Input Data */
            input_data: Record<string, never>;
            /** Output Data */
            output_data: Record<string, never>;
            /** Error */
            error: string | null;
        };
        /** OAuthCallbackRequest */
        OAuthCallbackRequest: {
            /** Provider */
            provider: string;
            /** Code */
            code: string;
            /** State */
            state: string;
        };
        /** OAuthInitRequest */
        OAuthInitRequest: {
            /**
             * Provider
             * @description OAuth provider (google, github, microsoft)
             */
            provider: string;
            /**
             * Redirect Url
             * @description URL to redirect after successful auth
             */
            redirect_url?: string | null;
        };
        /** OAuthInitResponse */
        OAuthInitResponse: {
            /** Authorization Url */
            authorization_url: string;
            /** State */
            state: string;
        };
        /** OrchestrationCreate */
        OrchestrationCreate: {
            /** Name */
            name: string;
            /** Description */
            description?: string | null;
            /** @default sequential */
            pattern: components["schemas"]["OrchestrationPattern"];
            /** Config */
            config?: Record<string, never>;
            /** Execution Plan */
            execution_plan?: Record<string, never>;
            /** Agent Ids */
            agent_ids: string[];
        };
        /** OrchestrationListResponse */
        OrchestrationListResponse: {
            /** Orchestrations */
            orchestrations: components["schemas"]["OrchestrationResponse"][];
            /** Total */
            total: number;
            /** Limit */
            limit: number;
            /** Offset */
            offset: number;
        };
        /** OrchestrationMemberResponse */
        OrchestrationMemberResponse: {
            /**
             * Agent Id
             * Format: uuid
             */
            agent_id: string;
            /** Role */
            role: string;
            /** Join Order */
            join_order: number;
        };
        /**
         * OrchestrationPattern
         * @description Orchestration pattern enumeration
         * @enum {string}
         */
        OrchestrationPattern: "sequential" | "parallel" | "hierarchical" | "peer_to_peer" | "event_driven";
        /** OrchestrationProgressResponse */
        OrchestrationProgressResponse: {
            /** Orchestration Id */
            orchestration_id: string;
            /** Status */
            status: string;
            /** Pattern */
            pattern: string;
            /** Progress Percentage */
            progress_percentage: number;
            /** Total Steps */
            total_steps: number;
            /** Completed Steps */
            completed_steps: number;
            /** Start Time */
            start_time: string | null;
            /** End Time */
            end_time: string | null;
            /** Duration Seconds */
            duration_seconds: number | null;
            /** Agent Count */
            agent_count: number;
            /** Results */
            results: Record<string, never>;
        };
        /** OrchestrationResponse */
        OrchestrationResponse: {
            /** Name */
            name: string;
            /** Description */
            description?: string | null;
            /** @default sequential */
            pattern: components["schemas"]["OrchestrationPattern"];
            /** Config */
            config?: Record<string, never>;
            /** Execution Plan */
            execution_plan?: Record<string, never>;
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** Status */
            status: string;
            /** Progress Percentage */
            progress_percentage: number;
            /**
             * Owner Id
             * Format: uuid
             */
            owner_id: string;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
            /**
             * Updated At
             * Format: date-time
             */
            updated_at: string;
            /** Started At */
            started_at: string | null;
            /** Completed At */
            completed_at: string | null;
            /** Members */
            members: components["schemas"]["OrchestrationMemberResponse"][];
        };
        /** OrchestrationStartResponse */
        OrchestrationStartResponse: {
            /**
             * Orchestration Id
             * Format: uuid
             */
            orchestration_id: string;
            /** Success */
            success: boolean;
            /** Message */
            message: string;
        };
        /** OrchestrationUpdate */
        OrchestrationUpdate: {
            /** Name */
            name?: string | null;
            /** Description */
            description?: string | null;
            /** Config */
            config?: Record<string, never> | null;
            /** Execution Plan */
            execution_plan?: Record<string, never> | null;
        };
        /** PasswordChangeRequest */
        PasswordChangeRequest: {
            /** Current Password */
            current_password: string;
            /** New Password */
            new_password: string;
        };
        /** PerformanceAnalysisRequest */
        PerformanceAnalysisRequest: {
            /**
             * Agent Id
             * @description Agent ID to analyze
             */
            agent_id: string;
            /**
             * Metrics
             * @description Performance metrics
             */
            metrics: Record<string, never>;
        };
        /** PerformanceAnalysisResponse */
        PerformanceAnalysisResponse: {
            /** Success */
            success: boolean;
            /** Analysis */
            analysis: string;
            /** Recommendations */
            recommendations: string[];
            /** Metrics Summary */
            metrics_summary: Record<string, never>;
            /** Timestamp */
            timestamp: string;
        };
        /** PlatformInsights */
        PlatformInsights: {
            /** Overall Health Score */
            overall_health_score: number;
            /** Performance Trends */
            performance_trends: {
                [key: string]: number;
            };
            /** Usage Patterns */
            usage_patterns: components["schemas"]["UsagePattern"][];
            /** Improvement Suggestions */
            improvement_suggestions: string[];
            /** Bottlenecks */
            bottlenecks: string[];
            /** Optimization Opportunities */
            optimization_opportunities: string[];
            /** Generated At */
            generated_at: string;
        };
        /**
         * ProgrammingLanguage
         * @description Supported programming languages
         * @enum {string}
         */
        ProgrammingLanguage: "python" | "typescript" | "javascript";
        /** QueryCapabilitiesRequest */
        QueryCapabilitiesRequest: {
            /**
             * Agent Id
             * @description Specific agent ID to query
             */
            agent_id?: string | null;
            /**
             * Capability Name
             * @description Specific capability to search for
             */
            capability_name?: string | null;
        };
        /** QueryCapabilitiesResponse */
        QueryCapabilitiesResponse: {
            /** Capabilities */
            capabilities: {
                [key: string]: components["schemas"]["AgentCapability"][];
            };
            /** Total Agents */
            total_agents: number;
        };
        /** RegisterAgentRequest */
        RegisterAgentRequest: {
            /**
             * Agent Id
             * @description Agent ID to register
             */
            agent_id: string;
            /**
             * Agent Type
             * @description Type of agent
             */
            agent_type: string;
            /**
             * Capabilities
             * @description Agent capabilities
             */
            capabilities: components["schemas"]["AgentCapability"][];
            /**
             * Metadata
             * @description Additional metadata
             */
            metadata?: Record<string, never> | null;
        };
        /** RegisterAgentResponse */
        RegisterAgentResponse: {
            /** Success */
            success: boolean;
            /** Message */
            message: string;
        };
        /** RequirementsParseRequest */
        RequirementsParseRequest: {
            /**
             * Requirements
             * @description Natural language requirements for agent
             */
            requirements: string;
            /**
             * Context
             * @description Additional context
             */
            context?: Record<string, never> | null;
        };
        /** RequirementsParseResponse */
        RequirementsParseResponse: {
            /** Name */
            name: string;
            /** Type */
            type: string;
            /** Language */
            language: string;
            /** Framework */
            framework: string;
            /** Capabilities */
            capabilities: string[];
            /** Deployment */
            deployment: Record<string, never>;
            /** Advanced Options */
            advanced_options: Record<string, never>;
            /** Reasoning */
            reasoning: string;
            /**
             * Confidence
             * @description Confidence in parsing accuracy
             * @default 0.8
             */
            confidence: number;
        };
        /**
         * ResourceLimits
         * @description Resource limits for agent deployment
         */
        ResourceLimits: {
            /**
             * Cpu Limit
             * @description CPU limit (e.g., '500m', '1')
             */
            cpu_limit?: string | null;
            /**
             * Memory Limit
             * @description Memory limit (e.g., '512Mi', '1Gi')
             */
            memory_limit?: string | null;
            /**
             * Disk Limit
             * @description Disk limit
             */
            disk_limit?: string | null;
        };
        /** ResourceUsageResponse */
        ResourceUsageResponse: {
            /** Total Cpu Usage */
            total_cpu_usage: number;
            /** Total Memory Mb */
            total_memory_mb: number;
            /** Average Cpu Per Agent */
            average_cpu_per_agent: number;
            /** Average Memory Per Agent */
            average_memory_per_agent: number;
        };
        /** RoleAssignmentRequest */
        RoleAssignmentRequest: {
            /** User Id */
            user_id: string;
            /** Role Name */
            role_name: string;
            /** Expires At */
            expires_at?: string | null;
        };
        /** RuntimeInfoResponse */
        RuntimeInfoResponse: {
            /** Agent Id */
            agent_id: string;
            /** Status */
            status: string;
            /** Process Id */
            process_id: number | null;
            /** Start Time */
            start_time: string | null;
            /** Last Heartbeat */
            last_heartbeat: string | null;
            /** Cpu Usage */
            cpu_usage: number;
            /** Memory Usage */
            memory_usage: number;
            /** Current Task */
            current_task: Record<string, never> | null;
            /** Queue Size */
            queue_size: number;
            /** Task History Count */
            task_history_count: number;
            /** Capabilities */
            capabilities: string[];
            /** Constraints */
            constraints: Record<string, never>;
        };
        /** SearchCodeRequest */
        SearchCodeRequest: {
            /**
             * Query
             * @description Search query
             */
            query: string;
            /**
             * Language
             * @description Filter by programming language
             */
            language?: string | null;
            /**
             * Tags
             * @description Filter by tags
             */
            tags?: string[] | null;
            /**
             * Limit
             * @description Maximum results to return
             * @default 10
             */
            limit: number;
            /**
             * Score Threshold
             * @description Minimum similarity score
             * @default 0.6
             */
            score_threshold: number;
        };
        /** SearchCodeResponse */
        SearchCodeResponse: {
            /** Success */
            success: boolean;
            /** Results */
            results: components["schemas"]["CodeResult"][];
            /** Total Found */
            total_found: number;
        };
        /** SearchKnowledgeRequest */
        SearchKnowledgeRequest: {
            /**
             * Query
             * @description Search query
             */
            query: string;
            /**
             * Agent Id
             * @description Filter by agent ID
             */
            agent_id?: string | null;
            /**
             * Category
             * @description Filter by category
             */
            category?: string | null;
            /**
             * Limit
             * @description Maximum results to return
             * @default 10
             */
            limit: number;
            /**
             * Score Threshold
             * @description Minimum similarity score
             * @default 0.7
             */
            score_threshold: number;
        };
        /** SearchKnowledgeResponse */
        SearchKnowledgeResponse: {
            /** Success */
            success: boolean;
            /** Results */
            results: components["schemas"]["KnowledgeResult"][];
            /** Total Found */
            total_found: number;
        };
        /** SearchMessagesRequest */
        SearchMessagesRequest: {
            /**
             * Query
             * @description Search query
             */
            query: string;
            /**
             * Agent Id
             * @description Filter by agent ID
             */
            agent_id?: string | null;
            /** @description Filter by message type */
            message_type?: components["schemas"]["MessageType"] | null;
            /**
             * Limit
             * @description Maximum results
             * @default 20
             */
            limit: number;
        };
        /** SearchMessagesResponse */
        SearchMessagesResponse: {
            /** Success */
            success: boolean;
            /** Results */
            results: components["schemas"]["MessageSearchResult"][];
            /** Total Found */
            total_found: number;
        };
        /** SendMessageRequest */
        SendMessageRequest: {
            /**
             * Receiver Id
             * @description Target agent ID (null for broadcast)
             */
            receiver_id?: string | null;
            /**
             * Receiver Type
             * @description Target agent type
             */
            receiver_type?: string | null;
            /**
             * Subject
             * @description Message subject
             */
            subject: string;
            /**
             * Payload
             * @description Message payload
             */
            payload: Record<string, never>;
            /** @default medium */
            priority: components["schemas"]["MessagePriority"];
            /** @default request */
            type: components["schemas"]["MessageType"];
            /**
             * Reply To
             * @description Message ID to reply to
             */
            reply_to?: string | null;
            /**
             * Correlation Id
             * @description Conversation correlation ID
             */
            correlation_id?: string | null;
            /**
             * Expires In Seconds
             * @description Message expiration time
             */
            expires_in_seconds?: number | null;
            /**
             * Wait For Response
             * @description Wait for response
             * @default false
             */
            wait_for_response: boolean;
            /**
             * Timeout
             * @description Response timeout in seconds
             * @default 30
             */
            timeout: number;
        };
        /** StoreCodeSnippetRequest */
        StoreCodeSnippetRequest: {
            /**
             * Title
             * @description Code snippet title
             */
            title: string;
            /**
             * Code
             * @description Code content
             */
            code: string;
            /**
             * Language
             * @description Programming language
             */
            language: string;
            /**
             * Description
             * @description Code description
             */
            description: string;
            /**
             * Tags
             * @description Tags for categorization
             */
            tags?: string[] | null;
            /**
             * Metadata
             * @description Additional metadata
             */
            metadata?: Record<string, never> | null;
        };
        /** StoreCodeSnippetResponse */
        StoreCodeSnippetResponse: {
            /** Success */
            success: boolean;
            /** Point Id */
            point_id: string;
            /** Message */
            message: string;
        };
        /** StoreConversationRequest */
        StoreConversationRequest: {
            /**
             * Agent Id
             * @description Agent ID
             */
            agent_id: string;
            /**
             * User Id
             * @description User ID
             */
            user_id: string;
            /**
             * Conversation Id
             * @description Conversation ID
             */
            conversation_id: string;
            /**
             * Message
             * @description Message content
             */
            message: string;
            /**
             * Role
             * @description Message role (user or assistant)
             */
            role: string;
            /**
             * Metadata
             * @description Additional metadata
             */
            metadata?: Record<string, never> | null;
        };
        /** StoreConversationResponse */
        StoreConversationResponse: {
            /** Success */
            success: boolean;
            /** Point Id */
            point_id: string;
            /** Message */
            message: string;
        };
        /** StoreKnowledgeRequest */
        StoreKnowledgeRequest: {
            /**
             * Agent Id
             * @description Agent ID to store knowledge for
             */
            agent_id: string;
            /**
             * Content
             * @description Knowledge content
             */
            content: string;
            /**
             * Category
             * @description Knowledge category
             * @default general
             */
            category: string;
            /**
             * Metadata
             * @description Additional metadata
             */
            metadata?: Record<string, never> | null;
        };
        /** StoreKnowledgeResponse */
        StoreKnowledgeResponse: {
            /** Success */
            success: boolean;
            /** Point Id */
            point_id: string;
            /** Message */
            message: string;
        };
        /**
         * SystemHealthResponse
         * @description System health check response
         */
        SystemHealthResponse: {
            /**
             * Status
             * @enum {string}
             */
            status: "healthy" | "degraded" | "unhealthy";
            /** Version */
            version: string;
            /** Uptime */
            uptime: string;
            /** Cpu Usage */
            cpu_usage?: number | null;
            /** Memory Usage */
            memory_usage?: number | null;
            /** Memory Mb */
            memory_mb?: number | null;
            /** Components */
            components?: {
                [key: string]: Record<string, never>;
            };
        };
        /** SystemStatsResponse */
        SystemStatsResponse: {
            /** Total Agents */
            total_agents?: number | null;
            /** Active Agents */
            active_agents?: number | null;
            /** Cpu Usage */
            cpu_usage?: number | null;
            /** Memory Usage */
            memory_usage?: number | null;
            /** Disk Usage */
            disk_usage?: number | null;
            /** Uptime */
            uptime?: string | null;
            /** Success Rate */
            success_rate?: number | null;
            resource_usage?: components["schemas"]["ResourceUsageResponse"] | null;
            task_statistics?: components["schemas"]["TaskStatisticsResponse"] | null;
        };
        /** TaskExecutionRequest */
        TaskExecutionRequest: {
            /** Task Id */
            task_id?: string | null;
            /** Task Type */
            task_type: string;
            /** Input Data */
            input_data?: Record<string, never>;
            /** Metadata */
            metadata?: Record<string, never> | null;
        };
        /** TaskExecutionResponse */
        TaskExecutionResponse: {
            /** Task Id */
            task_id: string | null;
            /**
             * Agent Id
             * Format: uuid
             */
            agent_id: string;
            /** Status */
            status: string;
            /** Result */
            result: Record<string, never>;
            /** Message */
            message: string;
        };
        /** TaskListResponse */
        TaskListResponse: {
            /** Tasks */
            tasks: components["schemas"]["TaskResponse"][];
            /** Total */
            total: number;
            /** Limit */
            limit: number;
            /** Offset */
            offset: number;
        };
        /** TaskResponse */
        TaskResponse: {
            /** Title */
            title: string;
            /** Description */
            description?: string | null;
            /** Type */
            type: string;
            /**
             * Priority
             * @default 5
             */
            priority: number;
            /** Input Data */
            input_data?: Record<string, never> | null;
            /** Metadata */
            metadata?: Record<string, never>;
            /** Deadline */
            deadline?: string | null;
            /**
             * Id
             * Format: uuid
             */
            id: string;
            status: components["schemas"]["TaskStatus"];
            /** Progress Percentage */
            progress_percentage: number;
            /** Steps Completed */
            steps_completed: number;
            /** Total Steps */
            total_steps: number | null;
            /** Assigned Agent Id */
            assigned_agent_id: string | null;
            /** Orchestration Id */
            orchestration_id: string | null;
            /** Parent Task Id */
            parent_task_id: string | null;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
            /**
             * Updated At
             * Format: date-time
             */
            updated_at: string;
            /** Started At */
            started_at: string | null;
            /** Completed At */
            completed_at: string | null;
            /** Output Data */
            output_data: Record<string, never> | null;
        };
        /** TaskStatisticsResponse */
        TaskStatisticsResponse: {
            /** Queued Tasks */
            queued_tasks: number;
            /** Completed Tasks */
            completed_tasks: number;
        };
        /**
         * TaskStatus
         * @description Task status enumeration
         * @enum {string}
         */
        TaskStatus: "pending" | "assigned" | "in_progress" | "completed" | "failed" | "cancelled";
        /**
         * TaskType
         * @description AI task types
         * @enum {string}
         */
        TaskType: "text_completion" | "chat" | "code_generation" | "summarization" | "translation" | "classification" | "extraction" | "embeddings" | "image_analysis" | "speech_to_text" | "text_to_speech";
        /** TemplateInfo */
        TemplateInfo: {
            /** Name */
            name: string;
            /** Description */
            description: string;
            /** Agent Type */
            agent_type: string;
            /** Language */
            language: string;
            /** Capabilities */
            capabilities: string[];
            /** Config Schema */
            config_schema: Record<string, never>;
            /** Examples */
            examples: Record<string, never>[];
        };
        /** TextGenerationRequest */
        TextGenerationRequest: {
            /**
             * Prompt
             * @description Generation prompt
             */
            prompt: string;
            /**
             * Model
             * @description Model to use
             * @default gemini-pro
             */
            model: string;
            /**
             * Temperature
             * @description Temperature
             */
            temperature?: number | null;
            /**
             * Max Output Tokens
             * @description Max tokens
             */
            max_output_tokens?: number | null;
            /**
             * Top P
             * @description Top P
             */
            top_p?: number | null;
            /**
             * Top K
             * @description Top K
             */
            top_k?: number | null;
            /**
             * Agent Context
             * @description Agent context
             */
            agent_context?: Record<string, never> | null;
        };
        /** TextGenerationResponse */
        TextGenerationResponse: {
            /** Success */
            success: boolean;
            /** Text */
            text: string;
            /** Model */
            model: string;
            /** Tokens Used */
            tokens_used?: number | null;
            /** Metadata */
            metadata?: Record<string, never>;
        };
        /** Token */
        Token: {
            /** Access Token */
            access_token: string;
            /** Refresh Token */
            refresh_token: string;
            /** Token Type */
            token_type: string;
        };
        /** UsagePattern */
        UsagePattern: {
            /** Pattern Id */
            pattern_id: string;
            /** Name */
            name: string;
            /** Description */
            description: string;
            /** Frequency */
            frequency: number;
            /** Trend */
            trend: string;
            /** Impact Areas */
            impact_areas: string[];
            /** Improvement Opportunities */
            improvement_opportunities: string[];
            /** Identified At */
            identified_at: string;
        };
        /** UserCreate */
        UserCreate: {
            /** Username */
            username: string;
            /** Email */
            email: string;
            /** Full Name */
            full_name?: string | null;
            /** Password */
            password: string;
        };
        /** UserPermissionsResponse */
        UserPermissionsResponse: {
            /** Permissions */
            permissions: string[];
            /** Roles */
            roles: Record<string, never>[];
        };
        /** UserResponse */
        UserResponse: {
            /** Username */
            username: string;
            /** Email */
            email: string;
            /** Full Name */
            full_name?: string | null;
            /**
             * Id
             * Format: uuid
             */
            id: string;
            /** Is Active */
            is_active: boolean;
            /** Is Superuser */
            is_superuser: boolean;
            /**
             * Created At
             * Format: date-time
             */
            created_at: string;
            /** Last Login */
            last_login: string | null;
        };
        /** UserUpdate */
        UserUpdate: {
            /** Full Name */
            full_name?: string | null;
            /** Email */
            email?: string | null;
        };
        /** ValidationError */
        ValidationError: {
            /** Location */
            loc: (string | number)[];
            /** Message */
            msg: string;
            /** Error Type */
            type: string;
        };
        /** ValidationResponse */
        ValidationResponse: {
            /** Valid */
            valid: boolean;
            /**
             * Errors
             * @default []
             */
            errors: string[];
            /**
             * Warnings
             * @default []
             */
            warnings: string[];
        };
        /** WorkflowConnection */
        WorkflowConnection: {
            /** Source */
            source: string;
            /** Target */
            target: string;
            /**
             * Source Output
             * @default main
             */
            source_output: string;
            /**
             * Target Input
             * @default main
             */
            target_input: string;
        };
        /** WorkflowDefinition */
        WorkflowDefinition: {
            /** Name */
            name: string;
            /** Description */
            description?: string | null;
            /** Nodes */
            nodes: components["schemas"]["WorkflowNode"][];
            /** Connections */
            connections: components["schemas"]["WorkflowConnection"][];
            /** Settings */
            settings?: Record<string, never>;
            /** Tags */
            tags?: string[];
        };
        /** WorkflowExecutionResponse */
        WorkflowExecutionResponse: {
            /** Execution Id */
            execution_id: string;
            /** Workflow Id */
            workflow_id: string;
            /** Status */
            status: string;
            /** Started At */
            started_at: string;
            /** Completed At */
            completed_at: string | null;
            /** Duration */
            duration: number | null;
            /** Trigger Data */
            trigger_data: Record<string, never>;
            /** Results */
            results: Record<string, never>;
            /** Error */
            error: string | null;
        };
        /** WorkflowNode */
        WorkflowNode: {
            /** Id */
            id: string;
            /** Type */
            type: string;
            /** Name */
            name: string;
            /** Position */
            position: {
                [key: string]: number;
            };
            /** Parameters */
            parameters?: Record<string, never>;
            /** Credentials */
            credentials?: Record<string, never> | null;
        };
        /** WorkflowResponse */
        WorkflowResponse: {
            /** Id */
            id: string;
            /** Name */
            name: string;
            /** Description */
            description: string | null;
            /** Status */
            status: string;
            /** Created At */
            created_at: string;
            /** Updated At */
            updated_at: string;
            /** Created By */
            created_by: string;
            /** Node Count */
            node_count: number;
            /** Execution Count */
            execution_count: number;
            /** Last Execution */
            last_execution: string | null;
        };
        /**
         * ChatMessage
         * @description Chat message schema
         */
        api__ai__ChatMessage: {
            /**
             * Role
             * @description Message role: system, user, or assistant
             */
            role: string;
            /**
             * Content
             * @description Message content
             */
            content: string;
        };
        /**
         * ChatRequest
         * @description Chat completion request
         */
        api__ai__ChatRequest: {
            /** Messages */
            messages: components["schemas"]["api__ai__ChatMessage"][];
            /**
             * Model
             * @default gpt-3.5-turbo
             */
            model: string | null;
            provider?: components["schemas"]["AIProvider"] | null;
            /**
             * Temperature
             * @default 0.7
             */
            temperature: number | null;
            /**
             * Max Tokens
             * @default 1000
             */
            max_tokens: number | null;
            /**
             * Top P
             * @default 1
             */
            top_p: number | null;
            /**
             * Frequency Penalty
             * @default 0
             */
            frequency_penalty: number | null;
            /**
             * Presence Penalty
             * @default 0
             */
            presence_penalty: number | null;
        };
        /**
         * CodeGenerationRequest
         * @description Code generation request
         */
        api__ai__CodeGenerationRequest: {
            /** Prompt */
            prompt: string;
            /**
             * Language
             * @default python
             */
            language: string | null;
            /**
             * Model
             * @default gpt-3.5-turbo
             */
            model: string | null;
            provider?: components["schemas"]["AIProvider"] | null;
            /**
             * Max Tokens
             * @default 2000
             */
            max_tokens: number | null;
        };
        /**
         * EmbeddingRequest
         * @description Embeddings request
         */
        api__ai__EmbeddingRequest: {
            /** Text */
            text: string;
            /**
             * Model
             * @default text-embedding-ada-002
             */
            model: string | null;
            /** @default openai */
            provider: components["schemas"]["AIProvider"] | null;
        };
        /** ChatMessage */
        api__google_adk__ChatMessage: {
            /**
             * Role
             * @description Message role (user/assistant)
             */
            role: string;
            /**
             * Content
             * @description Message content
             */
            content: string;
        };
        /** ChatRequest */
        api__google_adk__ChatRequest: {
            /**
             * Messages
             * @description Chat messages
             */
            messages: components["schemas"]["api__google_adk__ChatMessage"][];
            /**
             * Model
             * @description Model to use
             * @default gemini-pro
             */
            model: string;
            /**
             * Temperature
             * @description Temperature
             */
            temperature?: number | null;
            /**
             * Max Output Tokens
             * @description Max tokens
             */
            max_output_tokens?: number | null;
            /**
             * Stream
             * @description Stream response
             * @default false
             */
            stream: boolean;
        };
        /** CodeGenerationRequest */
        api__google_adk__CodeGenerationRequest: {
            /**
             * Prompt
             * @description Code generation prompt
             */
            prompt: string;
            /**
             * Language
             * @description Programming language
             * @default python
             */
            language: string;
            /**
             * Model
             * @description Model to use
             * @default code-bison
             */
            model: string;
            /**
             * Temperature
             * @description Temperature
             */
            temperature?: number | null;
            /**
             * Max Output Tokens
             * @description Max tokens
             */
            max_output_tokens?: number | null;
        };
        /** EmbeddingRequest */
        api__google_adk__EmbeddingRequest: {
            /**
             * Texts
             * @description Texts to embed
             */
            texts: string[];
            /**
             * Model
             * @description Embedding model
             * @default textembedding-gecko
             */
            model: string;
            /**
             * Task Type
             * @description Task type
             * @default RETRIEVAL_DOCUMENT
             */
            task_type: string;
        };
    };
    responses: never;
    parameters: never;
    requestBodies: never;
    headers: never;
    pathItems: never;
}
export type $defs = Record<string, never>;
export interface operations {
    register_api_v1_auth_register_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["UserCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UserResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    login_api_v1_auth_login_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/x-www-form-urlencoded": components["schemas"]["Body_login_api_v1_auth_login_post"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Token"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    refresh_token_api_v1_auth_refresh_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/x-www-form-urlencoded": components["schemas"]["Body_refresh_token_api_v1_auth_refresh_post"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["Token"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_current_user_info_api_v1_auth_me_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UserResponse"];
                };
            };
        };
    };
    update_current_user_api_v1_auth_me_put: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["UserUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UserResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    change_password_api_v1_auth_change_password_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["PasswordChangeRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    create_admin_user_api_v1_auth_create_admin_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["UserCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UserResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_oauth_providers_api_v1_auth_oauth_providers_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
        };
    };
    init_oauth_flow_api_v1_auth_oauth_init_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["OAuthInitRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["OAuthInitResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    oauth_callback_api_v1_auth_oauth_callback_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["OAuthCallbackRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    login_with_mfa_api_v1_auth_login_mfa_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["LoginWithMFARequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    setup_totp_mfa_api_v1_auth_mfa_setup_totp_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["MFASetupTOTPRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    setup_sms_mfa_api_v1_auth_mfa_setup_sms_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["MFASetupSMSRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    verify_mfa_setup_api_v1_auth_mfa_verify_setup_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["MFAVerifyRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_mfa_devices_api_v1_auth_mfa_devices_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
        };
    };
    remove_mfa_device_api_v1_auth_mfa_devices__device_id__delete: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                device_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    send_sms_code_api_v1_auth_mfa_send_sms_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["MFATokenRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_user_permissions_api_v1_auth_permissions_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UserPermissionsResponse"];
                };
            };
        };
    };
    get_all_roles_api_v1_auth_roles_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
        };
    };
    get_all_permissions_api_v1_auth_permissions_all_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
        };
    };
    create_role_api_v1_auth_roles_create_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["CreateRoleRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    assign_role_api_v1_auth_roles_assign_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["RoleAssignmentRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    remove_role_api_v1_auth_roles_remove_delete: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["RoleAssignmentRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_login_attempts_api_v1_auth_security_login_attempts_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
        };
    };
    list_agents_api_v1_agents__get: {
        parameters: {
            query?: {
                status?: components["schemas"]["AgentStatus"] | null;
                limit?: number;
                offset?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AgentListResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    create_agent_api_v1_agents__post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["AgentCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AgentResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_agent_api_v1_agents__agent_id__get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                agent_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AgentResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    update_agent_api_v1_agents__agent_id__put: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                agent_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["AgentUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AgentResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    delete_agent_api_v1_agents__agent_id__delete: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                agent_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    start_agent_api_v1_agents__agent_id__start_post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                agent_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AgentStartResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    stop_agent_api_v1_agents__agent_id__stop_post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                agent_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AgentStartResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_agent_deployment_api_v1_agents__agent_id__deployment_get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                agent_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    stop_agent_deployment_api_v1_agents__agent_id__deployment_delete: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                agent_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    deploy_agent_endpoint_api_v1_agents__agent_id__deploy_post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                agent_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    update_heartbeat_api_v1_agents__agent_id__heartbeat_post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                agent_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    list_tasks_api_v1_tasks__get: {
        parameters: {
            query?: {
                status?: components["schemas"]["TaskStatus"] | null;
                type?: string | null;
                agent_id?: string | null;
                size?: number;
                page?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TaskListResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_task_api_v1_tasks__task_id__get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                task_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TaskResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    list_orchestrations_api_v1_orchestrations__get: {
        parameters: {
            query?: {
                status?: string | null;
                pattern?: components["schemas"]["OrchestrationPattern"] | null;
                limit?: number;
                offset?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["OrchestrationListResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    create_orchestration_api_v1_orchestrations__post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["OrchestrationCreate"];
            };
        };
        responses: {
            /** @description Successful Response */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["OrchestrationResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_orchestration_api_v1_orchestrations__orchestration_id__get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                orchestration_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["OrchestrationResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    update_orchestration_api_v1_orchestrations__orchestration_id__put: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                orchestration_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["OrchestrationUpdate"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["OrchestrationResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    delete_orchestration_api_v1_orchestrations__orchestration_id__delete: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                orchestration_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    start_orchestration_api_v1_orchestrations__orchestration_id__start_post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                orchestration_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["OrchestrationStartResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    stop_orchestration_api_v1_orchestrations__orchestration_id__stop_post: {
        parameters: {
            query?: {
                graceful?: boolean;
            };
            header?: never;
            path: {
                orchestration_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["OrchestrationStartResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    pause_orchestration_api_v1_orchestrations__orchestration_id__pause_post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                orchestration_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    resume_orchestration_api_v1_orchestrations__orchestration_id__resume_post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                orchestration_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_orchestration_progress_api_v1_orchestrations__orchestration_id__progress_get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                orchestration_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["OrchestrationProgressResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    add_agent_to_orchestration_api_v1_orchestrations__orchestration_id__agents_post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                orchestration_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["AddAgentRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    remove_agent_from_orchestration_api_v1_orchestrations__orchestration_id__agents__agent_id__delete: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                orchestration_id: string;
                agent_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    list_active_agents_api_v1_runtime_agents_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["RuntimeInfoResponse"][];
                };
            };
        };
    };
    get_agent_runtime_info_api_v1_runtime_agents__agent_id__info_get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                agent_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["RuntimeInfoResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    pause_agent_runtime_api_v1_runtime_agents__agent_id__pause_post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                agent_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    resume_agent_runtime_api_v1_runtime_agents__agent_id__resume_post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                agent_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    execute_task_on_agent_api_v1_runtime_agents__agent_id__execute_task_post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                agent_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["TaskExecutionRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TaskExecutionResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    queue_task_for_agent_api_v1_runtime_agents__agent_id__queue_task_post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                agent_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["TaskExecutionRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_system_stats_api_v1_runtime_system_stats_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["SystemStatsResponse"];
                };
            };
        };
    };
    get_system_health_api_v1_runtime_system_health_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["SystemHealthResponse"];
                };
            };
        };
    };
    shutdown_all_agents_api_v1_runtime_system_shutdown_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
        };
    };
    get_messaging_status_api_v1_runtime_messaging_status_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
        };
    };
    connect_message_queue_api_v1_runtime_messaging_connect_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
        };
    };
    chat_completion_api_v1_api_v1_ai_chat_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["api__ai__ChatRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AIResponseSchema"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    text_completion_api_v1_api_v1_ai_completion_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["CompletionRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AIResponseSchema"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    generate_code_api_v1_api_v1_ai_code_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["api__ai__CodeGenerationRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AIResponseSchema"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    generate_embeddings_api_v1_api_v1_ai_embeddings_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["api__ai__EmbeddingRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AIResponseSchema"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    process_ai_request_api_v1_api_v1_ai_process_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["AIRequestSchema"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AIResponseSchema"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_available_models_api_v1_api_v1_ai_models_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        [key: string]: string[];
                    };
                };
            };
        };
    };
    get_providers_api_v1_api_v1_ai_providers_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": string[];
                };
            };
        };
    };
    health_check_api_v1_api_v1_ai_health_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        [key: string]: boolean;
                    };
                };
            };
        };
    };
    get_capabilities_api_v1_api_v1_ai_capabilities_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        [key: string]: string[];
                    };
                };
            };
        };
    };
    batch_process_api_v1_api_v1_ai_batch_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["AIRequestSchema"][];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_available_templates_api_v1_generation_templates_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TemplateInfo"][];
                };
            };
        };
    };
    get_templates_by_type_api_v1_generation_templates__agent_type__get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                agent_type: components["schemas"]["AgentType"];
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TemplateInfo"][];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_templates_by_language_api_v1_generation_templates_language__language__get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                language: components["schemas"]["ProgrammingLanguage"];
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TemplateInfo"][];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    validate_generation_request_api_v1_generation_validate_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["GenerationRequestModel"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    generate_agent_api_v1_generation_generate_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["GenerationRequestModel"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenerationResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_generation_status_api_v1_generation_status__generation_id__get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                generation_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenerationStatus"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    download_generated_agent_api_v1_generation_download__generation_id__get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                generation_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    cleanup_generation_api_v1_generation_cleanup__generation_id__delete: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                generation_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_available_capabilities_api_v1_generation_capabilities_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
        };
    };
    build_deploy_agent_api_v1_generation_build_deploy__generation_id__post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                generation_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_build_status_api_v1_generation_build_status__generation_id__get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                generation_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_generation_examples_api_v1_generation_examples_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
        };
    };
    store_agent_knowledge_api_v1_vector_knowledge_store_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["StoreKnowledgeRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["StoreKnowledgeResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    search_agent_knowledge_api_v1_vector_knowledge_search_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["SearchKnowledgeRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["SearchKnowledgeResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    store_conversation_context_api_v1_vector_conversation_store_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["StoreConversationRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["StoreConversationResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_conversation_context_api_v1_vector_conversation_context_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["GetContextRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GetContextResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    store_code_snippet_api_v1_vector_code_store_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["StoreCodeSnippetRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["StoreCodeSnippetResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    search_code_snippets_api_v1_vector_code_search_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["SearchCodeRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["SearchCodeResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    delete_collection_data_api_v1_vector_data_delete: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["DeleteDataRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DeleteDataResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_collection_info_api_v1_vector_collections__collection_name__info_get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                collection_name: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["CollectionInfoResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    vector_db_health_check_api_v1_vector_health_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HealthResponse"];
                };
            };
        };
    };
    register_agent_api_v1_a2a_register_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["RegisterAgentRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["RegisterAgentResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    unregister_agent_api_v1_a2a_unregister__agent_id__delete: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                agent_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["RegisterAgentResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    send_message_api_v1_a2a_send__sender_id__post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                sender_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["SendMessageRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["A2AResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    broadcast_message_api_v1_a2a_broadcast__sender_id__post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                sender_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["BroadcastMessageRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["A2AResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    establish_conversation_api_v1_a2a_conversation_establish__initiator_id__post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                initiator_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["EstablishConversationRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["EstablishConversationResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_conversation_history_api_v1_a2a_conversation__conversation_id__history_get: {
        parameters: {
            query?: {
                limit?: number;
            };
            header?: never;
            path: {
                conversation_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ConversationHistoryResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    query_capabilities_api_v1_a2a_capabilities_query_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["QueryCapabilitiesRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["QueryCapabilitiesResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    search_messages_api_v1_a2a_messages_search_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["SearchMessagesRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["SearchMessagesResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_active_agents_api_v1_a2a_agents_active_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ActiveAgentsResponse"];
                };
            };
        };
    };
    get_agent_status_api_v1_a2a_agents__agent_id__status_get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                agent_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AgentStatusResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    generate_text_api_v1_google_adk_generate_text_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["TextGenerationRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TextGenerationResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    generate_code_api_v1_google_adk_generate_code_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["api__google_adk__CodeGenerationRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["CodeGenerationResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    analyze_image_base64_api_v1_google_adk_analyze_image_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["ImageAnalysisRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ImageAnalysisResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    analyze_image_upload_api_v1_google_adk_analyze_image_upload_post: {
        parameters: {
            query: {
                prompt: string;
                model?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "multipart/form-data": components["schemas"]["Body_analyze_image_upload_api_v1_google_adk_analyze_image_upload_post"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ImageAnalysisResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    generate_embeddings_api_v1_google_adk_embeddings_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["api__google_adk__EmbeddingRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["EmbeddingResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    chat_api_v1_google_adk_chat_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["api__google_adk__ChatRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    create_agent_with_adk_api_v1_google_adk_agent_create_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["AgentADKRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AgentADKResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    enhance_agent_knowledge_api_v1_google_adk_agent_enhance_knowledge_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["KnowledgeEnhancementRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["KnowledgeEnhancementResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    analyze_agent_performance_api_v1_google_adk_agent_analyze_performance_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["PerformanceAnalysisRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["PerformanceAnalysisResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_model_info_api_v1_google_adk_models__model__info_get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                model: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ModelInfoResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    health_check_api_v1_google_adk_health_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HealthCheckResponse"];
                };
            };
        };
    };
    deploy_agent_api_v1_deployment_deploy_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["DeploymentRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DeploymentResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_deployment_status_api_v1_deployment_status__deployment_id__get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                deployment_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DeploymentStatusResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    list_deployments_api_v1_deployment_list_get: {
        parameters: {
            query?: {
                status?: string | null;
                limit?: number;
                offset?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DeploymentListResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    rollback_deployment_api_v1_deployment_rollback__deployment_id__post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                deployment_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    terminate_deployment_api_v1_deployment_terminate__deployment_id__delete: {
        parameters: {
            query?: {
                force?: boolean;
            };
            header?: never;
            path: {
                deployment_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    scale_deployment_api_v1_deployment_scale__deployment_id__post: {
        parameters: {
            query: {
                replicas: number;
            };
            header?: never;
            path: {
                deployment_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_deployment_logs_api_v1_deployment_logs__deployment_id__get: {
        parameters: {
            query?: {
                lines?: number;
                follow?: boolean;
            };
            header?: never;
            path: {
                deployment_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_deployment_metrics_api_v1_deployment_metrics__deployment_id__get: {
        parameters: {
            query?: {
                time_range?: string;
            };
            header?: never;
            path: {
                deployment_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_deployment_strategies_api_v1_deployment_strategies_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
        };
    };
    deployment_service_health_api_v1_deployment_health_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
        };
    };
    get_evolution_metrics_api_v1_evolution_metrics_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["EvolutionMetrics"];
                };
            };
        };
    };
    get_improvement_initiatives_api_v1_evolution_initiatives_get: {
        parameters: {
            query?: {
                status?: string | null;
                category?: string | null;
                limit?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ImprovementInitiative"][];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_usage_patterns_api_v1_evolution_patterns_get: {
        parameters: {
            query?: {
                time_range?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UsagePattern"][];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_platform_insights_api_v1_evolution_insights_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["PlatformInsights"];
                };
            };
        };
    };
    approve_initiative_api_v1_evolution_initiatives__initiative_id__approve_post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                initiative_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    reject_initiative_api_v1_evolution_initiatives__initiative_id__reject_post: {
        parameters: {
            query: {
                reason: string;
            };
            header?: never;
            path: {
                initiative_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    trigger_evolution_scan_api_v1_evolution_scan_post: {
        parameters: {
            query?: {
                force_full_scan?: boolean;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_scan_status_api_v1_evolution_scan__scan_id__status_get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                scan_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_evolution_history_api_v1_evolution_history_get: {
        parameters: {
            query?: {
                limit?: number;
                days?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_evolution_config_api_v1_evolution_config_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
        };
    };
    update_evolution_config_api_v1_evolution_config_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": Record<string, never>;
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    evolution_service_health_api_v1_evolution_health_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
        };
    };
    create_workflow_api_v1_workflows_create_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["WorkflowDefinition"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["WorkflowResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    list_workflows_api_v1_workflows_list_get: {
        parameters: {
            query?: {
                status?: string | null;
                tag?: string | null;
                limit?: number;
                offset?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["WorkflowResponse"][];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_workflow_api_v1_workflows__workflow_id__get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                workflow_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["WorkflowDefinition"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    update_workflow_api_v1_workflows__workflow_id__put: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                workflow_id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["WorkflowDefinition"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["WorkflowResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    delete_workflow_api_v1_workflows__workflow_id__delete: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                workflow_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    execute_workflow_api_v1_workflows__workflow_id__execute_post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                workflow_id: string;
            };
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": Record<string, never>;
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["WorkflowExecutionResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_workflow_executions_api_v1_workflows__workflow_id__executions_get: {
        parameters: {
            query?: {
                limit?: number;
                offset?: number;
            };
            header?: never;
            path: {
                workflow_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["WorkflowExecutionResponse"][];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_execution_status_api_v1_workflows_executions__execution_id__get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                execution_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["WorkflowExecutionResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_execution_node_details_api_v1_workflows_executions__execution_id__nodes_get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                execution_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["NodeExecutionResponse"][];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    cancel_execution_api_v1_workflows_executions__execution_id__cancel_post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                execution_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_available_node_types_api_v1_workflows_node_types_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
        };
    };
    activate_workflow_api_v1_workflows__workflow_id__activate_post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                workflow_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    deactivate_workflow_api_v1_workflows__workflow_id__deactivate_post: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                workflow_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    stream_execution_logs_api_v1_workflows_executions__execution_id__stream_get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                execution_id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    workflow_service_health_api_v1_workflows_health_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
        };
    };
    get_available_providers_api_v1_ai_providers_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
        };
    };
    parse_requirements_api_v1_ai_parse_requirements_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["RequirementsParseRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["RequirementsParseResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    analyze_code_api_v1_ai_analyze_code_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["CodeAnalysisRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["CodeAnalysisResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    generate_documentation_api_v1_ai_generate_documentation_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["DocumentationRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DocumentationResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    generate_ai_content_api_v1_ai_generate_post: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["AIGenerationRequest"];
            };
        };
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["AIGenerationResponse"];
                };
            };
            /** @description Validation Error */
            422: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["HTTPValidationError"];
                };
            };
        };
    };
    get_ai_capabilities_api_v1_ai_capabilities_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
        };
    };
    ai_service_health_api_v1_ai_health_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
        };
    };
    root__get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
        };
    };
    health_check_health_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
        };
    };
    metrics_metrics_get: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successful Response */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": unknown;
                };
            };
        };
    };
}
