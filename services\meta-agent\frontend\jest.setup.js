import '@testing-library/jest-dom'

// Global test setup
beforeAll(() => {
  // Set test environment variables - ensure backend is available
  process.env.NEXT_PUBLIC_API_URL = 'http://localhost:8000'
  
  // Mock window.fetch only if backend is not available
  // For integration tests, we want real API calls
  if (process.env.NODE_ENV !== 'test-integration') {
    global.fetch = jest.fn()
  }
})

afterEach(() => {
  // Clear all mocks after each test
  jest.clearAllMocks()
})

// Mock Next.js router
jest.mock('next/router', () => ({
  useRouter() {
    return {
      route: '/',
      pathname: '/',
      query: '',
      asPath: '/',
      push: jest.fn(),
      pop: jest.fn(),
      reload: jest.fn(),
      back: jest.fn(),
      prefetch: jest.fn().mockResolvedValue(undefined),
      beforePopState: jest.fn(),
      events: {
        on: jest.fn(),
        off: jest.fn(),
        emit: jest.fn(),
      },
      isFallback: false,
    }
  },
}))

// Mock Next.js navigation
jest.mock('next/navigation', () => ({
  useRouter() {
    return {
      push: jest.fn(),
      replace: jest.fn(),
      refresh: jest.fn(),
      back: jest.fn(),
      forward: jest.fn(),
      prefetch: jest.fn(),
    }
  },
  useSearchParams() {
    return new URLSearchParams()
  },
  usePathname() {
    return '/'
  },
}))

// Global test utilities
global.testUtils = {
  // Helper to wait for async operations
  waitFor: (ms = 100) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // Helper to generate test data
  generateTestUser: () => ({
    username: `testuser_${Date.now()}`,
    email: `test_${Date.now()}@example.com`,
    password: 'TestPassword123!',
    full_name: 'Test User'
  }),
  
  generateTestAgent: () => ({
    name: `Test Agent ${Date.now()}`,
    description: 'A test agent for integration testing',
    type: 'assistant',
    config: {
      max_concurrent_tasks: 5,
      timeout_seconds: 300
    },
    capabilities: ['text_analysis', 'data_processing']
  })
}