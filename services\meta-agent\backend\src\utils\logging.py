"""Structured logging setup for the AI agent platform."""

import logging
import json
import sys
from typing import Dict, Any, Optional
from datetime import datetime
import traceback
from functools import wraps

class StructuredFormatter(logging.Formatter):
    """JSON structured log formatter."""
    
    def __init__(self, service_name: str = "ai-agent-platform"):
        super().__init__()
        self.service_name = service_name
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as JSON."""
        log_data = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "service": self.service_name,
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno
        }
        
        # Add exception info if present
        if record.exc_info:
            log_data["exception"] = {
                "type": record.exc_info[0].__name__,
                "message": str(record.exc_info[1]),
                "traceback": traceback.format_exception(*record.exc_info)
            }
        
        # Add extra fields
        for key, value in record.__dict__.items():
            if key not in {"name", "msg", "args", "levelname", "levelno", "pathname",
                          "filename", "module", "lineno", "funcName", "created",
                          "msecs", "relativeCreated", "thread", "threadName",
                          "processName", "process", "getMessage", "exc_info",
                          "exc_text", "stack_info"}:
                log_data["extra"] = log_data.get("extra", {})
                log_data["extra"][key] = value
        
        return json.dumps(log_data)

def setup_logging(
    level: str = "INFO",
    service_name: str = "ai-agent-platform",
    enable_structured: bool = True,
    log_file: Optional[str] = None
) -> None:
    """Setup application logging configuration."""
    
    # Remove existing handlers
    logging.root.handlers = []
    
    # Set logging level
    numeric_level = getattr(logging, level.upper(), logging.INFO)
    logging.root.setLevel(numeric_level)
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    
    if enable_structured:
        console_handler.setFormatter(StructuredFormatter(service_name))
    else:
        console_handler.setFormatter(
            logging.Formatter(
                "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            )
        )
    
    logging.root.addHandler(console_handler)
    
    # File handler if specified
    if log_file:
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(StructuredFormatter(service_name))
        logging.root.addHandler(file_handler)
    
    # Silence noisy libraries
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("httpcore").setLevel(logging.WARNING)
    logging.getLogger("openai").setLevel(logging.WARNING)
    logging.getLogger("anthropic").setLevel(logging.WARNING)

def get_logger(name: str, **extra_fields) -> logging.Logger:
    """Get logger with optional extra fields."""
    logger = logging.getLogger(name)
    
    if extra_fields:
        # Create adapter to add extra fields to all log records
        class ExtraAdapter(logging.LoggerAdapter):
            def process(self, msg, kwargs):
                extra = kwargs.get("extra", {})
                extra.update(self.extra)
                kwargs["extra"] = extra
                return msg, kwargs
        
        return ExtraAdapter(logger, extra_fields)
    
    return logger

def log_function_call(logger: Optional[logging.Logger] = None):
    """Decorator to log function calls and performance."""
    def decorator(func):
        nonlocal logger
        if logger is None:
            logger = get_logger(func.__module__)
        
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = datetime.now()
            function_name = f"{func.__module__}.{func.__name__}"
            
            logger.info(
                f"Function call started: {function_name}",
                extra={
                    "function": function_name,
                    "args_count": len(args),
                    "kwargs_keys": list(kwargs.keys())
                }
            )
            
            try:
                result = await func(*args, **kwargs)
                duration = (datetime.now() - start_time).total_seconds()
                
                logger.info(
                    f"Function call completed: {function_name}",
                    extra={
                        "function": function_name,
                        "duration_seconds": duration,
                        "success": True
                    }
                )
                
                return result
                
            except Exception as e:
                duration = (datetime.now() - start_time).total_seconds()
                
                logger.error(
                    f"Function call failed: {function_name}",
                    extra={
                        "function": function_name,
                        "duration_seconds": duration,
                        "success": False,
                        "error_type": type(e).__name__,
                        "error_message": str(e)
                    },
                    exc_info=True
                )
                
                raise
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = datetime.now()
            function_name = f"{func.__module__}.{func.__name__}"
            
            logger.info(
                f"Function call started: {function_name}",
                extra={
                    "function": function_name,
                    "args_count": len(args),
                    "kwargs_keys": list(kwargs.keys())
                }
            )
            
            try:
                result = func(*args, **kwargs)
                duration = (datetime.now() - start_time).total_seconds()
                
                logger.info(
                    f"Function call completed: {function_name}",
                    extra={
                        "function": function_name,
                        "duration_seconds": duration,
                        "success": True
                    }
                )
                
                return result
                
            except Exception as e:
                duration = (datetime.now() - start_time).total_seconds()
                
                logger.error(
                    f"Function call failed: {function_name}",
                    extra={
                        "function": function_name,
                        "duration_seconds": duration,
                        "success": False,
                        "error_type": type(e).__name__,
                        "error_message": str(e)
                    },
                    exc_info=True
                )
                
                raise
        
        # Return appropriate wrapper based on function type
        if hasattr(func, "__code__") and "await" in func.__code__.co_code:
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

class LoggerMixin:
    """Mixin class to add logging capabilities to any class."""
    
    @property
    def logger(self) -> logging.Logger:
        """Get logger for this class."""
        if not hasattr(self, '_logger'):
            class_name = f"{self.__class__.__module__}.{self.__class__.__name__}"
            self._logger = get_logger(
                class_name,
                class_name=self.__class__.__name__
            )
        return self._logger
    
    def log_operation(self, operation: str, **extra):
        """Log an operation with consistent format."""
        self.logger.info(
            f"Operation: {operation}",
            extra={
                "operation": operation,
                "class": self.__class__.__name__,
                **extra
            }
        )
    
    def log_error(self, operation: str, error: Exception, **extra):
        """Log an error with consistent format."""
        self.logger.error(
            f"Operation failed: {operation}",
            extra={
                "operation": operation,
                "class": self.__class__.__name__,
                "error_type": type(error).__name__,
                "error_message": str(error),
                **extra
            },
            exc_info=True
        )