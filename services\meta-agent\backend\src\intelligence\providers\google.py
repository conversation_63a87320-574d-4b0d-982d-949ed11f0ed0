"""Google AI provider implementation."""

import google.generativeai as genai
from google.cloud import aiplatform
from typing import List, Optional, Dict, Any, AsyncIterator
from .base import BaseAIProvider, AIMessage, AIResponse, ModelInfo, ModelCapability
import asyncio
import base64

class GoogleAIProvider(BaseAIProvider):
    """Google AI provider implementation."""
    
    def __init__(self, api_key: str, **kwargs):
        super().__init__(api_key, **kwargs)
        self.project_id = kwargs.get('project_id')
        self.location = kwargs.get('location', 'us-central1')
        self.genai_initialized = False
        self.vertex_initialized = False
        
    async def initialize(self) -> bool:
        """Initialize Google AI clients."""
        try:
            # Initialize Generative AI
            genai.configure(api_key=self.api_key)
            self.genai_initialized = True
            
            # Initialize Vertex AI if project_id provided
            if self.project_id:
                aiplatform.init(project=self.project_id, location=self.location)
                self.vertex_initialized = True
                
            return True
        except Exception as e:
            print(f"Google AI initialization failed: {e}")
            return False
    
    async def chat_completion(
        self,
        messages: List[AIMessage],
        model: str,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        stream: bool = False,
        **kwargs
    ) -> AIResponse:
        """Generate chat completion using Gemini."""
        if not self.genai_initialized:
            await self.initialize()
        
        try:
            if model.startswith("gemini") and self.genai_initialized:
                gemini_model = genai.GenerativeModel(model)
                
                # Convert messages to Gemini format
                chat_history = []
                user_input = ""
                
                for msg in messages:
                    if msg.role == "system":
                        # System messages are handled differently in Gemini
                        user_input = msg.content + "\n\n" + user_input
                    elif msg.role == "user":
                        user_input = msg.content
                    elif msg.role == "assistant":
                        chat_history.append({
                            "role": "user",
                            "parts": [user_input] if user_input else [""]
                        })
                        chat_history.append({
                            "role": "model", 
                            "parts": [msg.content]
                        })
                        user_input = ""
                
                # Start chat with history
                chat = gemini_model.start_chat(history=chat_history)
                
                generation_config = genai.types.GenerationConfig(
                    temperature=temperature,
                    max_output_tokens=max_tokens
                )
                
                response = await asyncio.to_thread(
                    chat.send_message,
                    user_input,
                    generation_config=generation_config
                )
                
                return AIResponse(
                    content=response.text,
                    model=model,
                    usage={
                        "prompt_tokens": response.usage_metadata.prompt_token_count,
                        "completion_tokens": response.usage_metadata.candidates_token_count,
                        "total_tokens": response.usage_metadata.total_token_count
                    },
                    metadata={"safety_ratings": str(response.candidates[0].safety_ratings)}
                )
            
            else:
                # Fallback for other models
                raise NotImplementedError(f"Model {model} not supported")
                
        except Exception as e:
            raise Exception(f"Google AI chat completion failed: {e}")
    
    async def stream_completion(
        self,
        messages: List[AIMessage],
        model: str,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        **kwargs
    ) -> AsyncIterator[str]:
        """Stream chat completion from Gemini."""
        if not self.genai_initialized:
            await self.initialize()
        
        try:
            if model.startswith("gemini"):
                gemini_model = genai.GenerativeModel(model)
                
                # Convert messages
                user_input = ""
                for msg in messages:
                    if msg.role in ["user", "system"]:
                        user_input += msg.content + "\n"
                
                generation_config = genai.types.GenerationConfig(
                    temperature=temperature,
                    max_output_tokens=max_tokens
                )
                
                response_stream = await asyncio.to_thread(
                    gemini_model.generate_content,
                    user_input,
                    generation_config=generation_config,
                    stream=True
                )
                
                for chunk in response_stream:
                    if chunk.text:
                        yield chunk.text
                        
        except Exception as e:
            raise Exception(f"Google AI stream completion failed: {e}")
    
    async def generate_embedding(
        self,
        text: str,
        model: Optional[str] = None
    ) -> List[float]:
        """Generate embeddings using Google models."""
        if not self.genai_initialized:
            await self.initialize()
        
        model = model or "models/embedding-001"
        
        try:
            response = await asyncio.to_thread(
                genai.embed_content,
                model=model,
                content=text,
                task_type="retrieval_document"
            )
            return response['embedding']
        except Exception as e:
            raise Exception(f"Google AI embedding generation failed: {e}")
    
    async def analyze_image(
        self,
        image_data: bytes,
        prompt: str,
        model: Optional[str] = None
    ) -> AIResponse:
        """Analyze image using Gemini Vision."""
        if not self.genai_initialized:
            await self.initialize()
        
        model = model or "gemini-1.5-flash"
        
        try:
            gemini_model = genai.GenerativeModel(model)
            
            # Create image part
            image_part = genai.types.BlobDict(
                mime_type="image/jpeg",
                data=image_data
            )
            
            response = await asyncio.to_thread(
                gemini_model.generate_content,
                [prompt, image_part]
            )
            
            return AIResponse(
                content=response.text,
                model=model,
                usage={
                    "prompt_tokens": response.usage_metadata.prompt_token_count,
                    "completion_tokens": response.usage_metadata.candidates_token_count,
                    "total_tokens": response.usage_metadata.total_token_count
                }
            )
        except Exception as e:
            raise Exception(f"Google AI image analysis failed: {e}")
    
    async def function_calling(
        self,
        messages: List[AIMessage],
        functions: List[Dict[str, Any]],
        model: str,
        **kwargs
    ) -> AIResponse:
        """Execute function calling with Gemini."""
        if not self.genai_initialized:
            await self.initialize()
        
        try:
            # Convert functions to Gemini format
            tools = []
            for func in functions:
                tool = genai.types.Tool(
                    function_declarations=[
                        genai.types.FunctionDeclaration(
                            name=func["name"],
                            description=func.get("description", ""),
                            parameters=func.get("parameters", {})
                        )
                    ]
                )
                tools.append(tool)
            
            gemini_model = genai.GenerativeModel(model, tools=tools)
            
            # Convert messages
            user_input = ""
            for msg in messages:
                if msg.role in ["user", "system"]:
                    user_input += msg.content + "\n"
            
            response = await asyncio.to_thread(
                gemini_model.generate_content,
                user_input
            )
            
            content = response.text
            function_calls = []
            
            # Check for function calls
            if hasattr(response.candidates[0].content, 'parts'):
                for part in response.candidates[0].content.parts:
                    if hasattr(part, 'function_call'):
                        function_calls.append({
                            "name": part.function_call.name,
                            "arguments": dict(part.function_call.args)
                        })
            
            return AIResponse(
                content=content,
                model=model,
                metadata={"function_calls": function_calls} if function_calls else None,
                usage={
                    "prompt_tokens": response.usage_metadata.prompt_token_count,
                    "completion_tokens": response.usage_metadata.candidates_token_count,
                    "total_tokens": response.usage_metadata.total_token_count
                }
            )
        except Exception as e:
            raise Exception(f"Google AI function calling failed: {e}")
    
    def get_available_models(self) -> List[ModelInfo]:
        """Get available Google AI models."""
        return [
            ModelInfo(
                name="gemini-1.5-pro",
                provider="google",
                capabilities=[
                    ModelCapability.TEXT_GENERATION,
                    ModelCapability.CHAT_COMPLETION,
                    ModelCapability.CODE_GENERATION,
                    ModelCapability.FUNCTION_CALLING,
                    ModelCapability.IMAGE_ANALYSIS
                ],
                max_tokens=2000000,
                cost_per_token=0.000001,
                supports_streaming=True,
                supports_functions=True
            ),
            ModelInfo(
                name="gemini-1.5-flash",
                provider="google",
                capabilities=[
                    ModelCapability.TEXT_GENERATION,
                    ModelCapability.CHAT_COMPLETION,
                    ModelCapability.CODE_GENERATION,
                    ModelCapability.FUNCTION_CALLING,
                    ModelCapability.IMAGE_ANALYSIS
                ],
                max_tokens=1000000,
                cost_per_token=0.0000001,
                supports_streaming=True,
                supports_functions=True
            ),
            ModelInfo(
                name="models/embedding-001",
                provider="google",
                capabilities=[ModelCapability.EMBEDDING],
                max_tokens=2048,
                cost_per_token=0.0000001
            )
        ]
    
    def estimate_cost(
        self,
        input_tokens: int,
        output_tokens: int,
        model: str
    ) -> float:
        """Estimate cost for Google AI usage."""
        model_costs = {
            "gemini-1.5-pro": {"input": 0.0000013, "output": 0.0000052},
            "gemini-1.5-flash": {"input": 0.000000075, "output": 0.0000003},
            "models/embedding-001": {"input": 0.0000001, "output": 0}
        }
        
        if model not in model_costs:
            return 0.0
            
        costs = model_costs[model]
        return (input_tokens * costs["input"]) + (output_tokens * costs["output"])
    
    @property
    def provider_name(self) -> str:
        return "google"