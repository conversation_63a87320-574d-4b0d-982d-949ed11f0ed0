/* tslint:disable */
/* eslint-disable */
/**
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface DealDetailsAllOfDealStage
 */
export interface DealDetailsAllOfDealStage {
    /**
     * 
     * @type {string}
     * @memberof DealDetailsAllOfDealStage
     */
    id?: string;
    /**
     * 
     * @type {string}
     * @memberof DealDetailsAllOfDealStage
     */
    name?: string;
    /**
     * 
     * @type {number}
     * @memberof DealDetailsAllOfDealStage
     */
    pipelineOrder?: number;
    /**
     * 
     * @type {boolean}
     * @memberof DealDetailsAllOfDealStage
     */
    isClosedWon?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof DealDetailsAllOfDealStage
     */
    isClosedLost?: boolean;
}

/**
 * Check if a given object implements the DealDetailsAllOfDealStage interface.
 */
export function instanceOfDealDetailsAllOfDealStage(value: object): value is DealDetailsAllOfDealStage {
    return true;
}

export function DealDetailsAllOfDealStageFromJSON(json: any): DealDetailsAllOfDealStage {
    return DealDetailsAllOfDealStageFromJSONTyped(json, false);
}

export function DealDetailsAllOfDealStageFromJSONTyped(json: any, ignoreDiscriminator: boolean): DealDetailsAllOfDealStage {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'] == null ? undefined : json['id'],
        'name': json['name'] == null ? undefined : json['name'],
        'pipelineOrder': json['pipeline_order'] == null ? undefined : json['pipeline_order'],
        'isClosedWon': json['is_closed_won'] == null ? undefined : json['is_closed_won'],
        'isClosedLost': json['is_closed_lost'] == null ? undefined : json['is_closed_lost'],
    };
}

  export function DealDetailsAllOfDealStageToJSON(json: any): DealDetailsAllOfDealStage {
      return DealDetailsAllOfDealStageToJSONTyped(json, false);
  }

  export function DealDetailsAllOfDealStageToJSONTyped(value?: DealDetailsAllOfDealStage | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'name': value['name'],
        'pipeline_order': value['pipelineOrder'],
        'is_closed_won': value['isClosedWon'],
        'is_closed_lost': value['isClosedLost'],
    };
}

