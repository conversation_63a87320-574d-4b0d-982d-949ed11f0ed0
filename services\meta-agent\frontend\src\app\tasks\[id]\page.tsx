/**
 * AI Agent Platform - Task Detail Page
 * Detailed view and management of individual tasks
 */

'use client';

import React, { useState, useEffect } from 'react';
import { AppLayout } from '@/components/Layout/AppLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
// import { Textarea } from '@/components/ui/textarea'; // Component not available
import { 
  ArrowLeft,
  Play,
  Pause,
  Square,
  RefreshCw,
  MoreVertical,
  Clock,
  CheckCircle2,
  XCircle,
  AlertCircle,
  Copy,
  Edit,
  Trash2,
  Activity,
  BarChart3,
  FileText,
  User
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';

interface Task {
  id: string;
  title: string;
  description?: string;
  type: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  agent_id?: string;
  agent_name?: string;
  orchestration_id?: string;
  orchestration_name?: string;
  progress: number;
  created_at: string;
  updated_at: string;
  estimated_duration?: number;
  actual_duration?: number;
  error_message?: string;
  result_summary?: string;
  configuration?: Record<string, any>;
  execution_logs?: string[];
  resource_usage?: {
    cpu_percent: number;
    memory_mb: number;
    network_io: number;
  };
}

const statusColors = {
  pending: 'secondary',
  running: 'default',
  completed: 'default',
  failed: 'destructive',
  cancelled: 'outline',
} as const;

const statusIcons = {
  pending: Clock,
  running: RefreshCw,
  completed: CheckCircle2,
  failed: XCircle,
  cancelled: AlertCircle,
};

const priorityColors = {
  low: 'secondary',
  medium: 'default',
  high: 'outline',
  urgent: 'destructive',
} as const;

const getStatusColor = (status: Task['status']) => {
  switch (status) {
    case 'pending': return 'bg-gray-400';
    case 'running': return 'bg-blue-500';
    case 'completed': return 'bg-green-500';
    case 'failed': return 'bg-red-500';
    case 'cancelled': return 'bg-orange-500';
    default: return 'bg-gray-400';
  }
};

export default function TaskDetailPage() {
  const params = useParams();
  const router = useRouter();
  const taskId = params?.id as string;
  const [task, setTask] = useState<Task | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [logs, setLogs] = useState<string[]>([]);
  const [metrics, setMetrics] = useState<any>(null);

  // Simple toast replacement
  const toast = (opts: any) => {
    alert(`${opts.title}: ${opts.description}`);
  };

  useEffect(() => {
    fetchTask();
  }, [taskId]);

  const fetchTask = async () => {
    try {
      setLoading(true);
      
      // Mock API call with detailed task data
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockTask: Task = {
        id: taskId,
        title: 'Process User Data Export',
        description: 'Extract and process user data for quarterly reporting. This task involves connecting to multiple data sources, normalizing the data formats, and generating comprehensive reports.',
        type: 'data_processing',
        status: 'running',
        priority: 'high',
        agent_id: 'agent-001',
        agent_name: 'Data Processing Agent',
        progress: 67,
        created_at: new Date(Date.now() - 3600000).toISOString(),
        updated_at: new Date(Date.now() - 1800000).toISOString(),
        estimated_duration: 1800,
        actual_duration: 1200,
        configuration: {
          data_sources: ['database', 'api', 'files'],
          output_format: 'json',
          compression: true,
          notifications: ['email', 'slack']
        },
        execution_logs: [
          '[2024-01-15 10:30:00] Task started',
          '[2024-01-15 10:30:05] Connecting to database...',
          '[2024-01-15 10:30:10] Database connection established',
          '[2024-01-15 10:32:15] Processing user data batch 1/5',
          '[2024-01-15 10:35:22] Processing user data batch 2/5',
          '[2024-01-15 10:38:45] Processing user data batch 3/5',
          '[2024-01-15 10:42:10] Processing user data batch 4/5',
          '[2024-01-15 10:45:30] Processing user data batch 5/5',
          '[2024-01-15 10:48:00] Generating reports...',
          '[2024-01-15 10:50:15] Task 67% complete'
        ],
        resource_usage: {
          cpu_percent: 45.2,
          memory_mb: 512,
          network_io: 125.6
        }
      };

      setTask(mockTask);
      setLogs(mockTask.execution_logs || []);
      setMetrics({
        total_records_processed: 15420,
        processing_rate: '1.2k records/min',
        average_processing_time: '0.05s per record',
        errors_encountered: 3,
        warnings_generated: 7
      });
    } catch (error) {
      console.error('Failed to fetch task:', error);
      toast({
        title: 'Error loading task',
        description: 'Failed to load task details. Please try again.',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchTask();
    setRefreshing(false);
  };

  const handleStartTask = async () => {
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      toast({
        title: 'Task Started',
        description: 'Task execution has been initiated',
      });
      handleRefresh();
    } catch (error) {
      toast({
        title: 'Error starting task',
        description: 'Failed to start task. Please try again.',
      });
    }
  };

  const handlePauseTask = async () => {
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      toast({
        title: 'Task Paused',
        description: 'Task execution has been paused',
      });
      handleRefresh();
    } catch (error) {
      toast({
        title: 'Error pausing task',
        description: 'Failed to pause task. Please try again.',
      });
    }
  };

  const handleStopTask = async () => {
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      toast({
        title: 'Task Stopped',
        description: 'Task execution has been stopped',
      });
      handleRefresh();
    } catch (error) {
      toast({
        title: 'Error stopping task',
        description: 'Failed to stop task. Please try again.',
      });
    }
  };

  const handleDeleteTask = async () => {
    if (window.confirm('Are you sure you want to delete this task? This action cannot be undone.')) {
      try {
        // Mock API call
        await new Promise(resolve => setTimeout(resolve, 500));
        
        toast({
          title: 'Task Deleted',
          description: 'Task has been deleted successfully',
        });
        router.push('/tasks');
      } catch (error) {
        toast({
          title: 'Error deleting task',
          description: 'Failed to delete task. Please try again.',
        });
      }
    }
  };

  const formatDuration = (seconds?: number) => {
    if (!seconds) return 'N/A';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const formatRelativeTime = (date: string) => {
    const now = new Date();
    const then = new Date(date);
    const diff = now.getTime() - then.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  const getTaskActions = () => {
    if (!task) return [];

    const actions = [];

    if (task.status === 'pending') {
      actions.push({ label: 'Start', icon: Play, action: handleStartTask });
    }

    if (task.status === 'running') {
      actions.push({ label: 'Pause', icon: Pause, action: handlePauseTask });
      actions.push({ label: 'Stop', icon: Square, action: handleStopTask });
    }

    if (['completed', 'failed', 'cancelled'].includes(task.status)) {
      actions.push({ label: 'Delete', icon: Trash2, action: handleDeleteTask });
    }

    return actions;
  };

  if (loading) {
    return (
      <AppLayout>
        <div className="space-y-6">
          <div className="h-8 bg-muted rounded w-1/4 animate-pulse"></div>
          <div className="h-32 bg-muted rounded animate-pulse"></div>
          <div className="h-96 bg-muted rounded animate-pulse"></div>
        </div>
      </AppLayout>
    );
  }

  if (!task) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-destructive">
                <AlertCircle className="h-5 w-5" />
                Task Not Found
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-4">
                The requested task could not be found.
              </p>
              <Button asChild className="w-full">
                <Link href="/tasks">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Tasks
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </AppLayout>
    );
  }

  const StatusIcon = statusIcons[task.status];
  const actions = getTaskActions();

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" asChild>
              <Link href="/tasks">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Tasks
              </Link>
            </Button>
            <div>
              <div className="flex items-center gap-3">
                <StatusIcon className={cn("h-6 w-6", 
                  task.status === 'running' && "animate-spin",
                  task.status === 'completed' && "text-green-500",
                  task.status === 'failed' && "text-red-500",
                  task.status === 'pending' && "text-gray-500",
                  task.status === 'cancelled' && "text-orange-500"
                )} />
                <h1 className="text-3xl font-bold tracking-tight">{task.title}</h1>
              </div>
              <p className="text-muted-foreground mt-1">
                Task ID: {task.id}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={refreshing}
            >
              <RefreshCw className={cn("h-4 w-4 mr-2", refreshing && "animate-spin")} />
              Refresh
            </Button>
            {actions.map((action) => (
              <Button
                key={action.label}
                variant={action.label === 'Delete' ? 'destructive' : 'default'}
                size="sm"
                onClick={action.action}
              >
                <action.icon className="h-4 w-4 mr-2" />
                {action.label}
              </Button>
            ))}
          </div>
        </div>

        {/* Status Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center gap-3">
                <div className={cn("h-3 w-3 rounded-full", getStatusColor(task.status))} />
                <div>
                  <Badge variant={statusColors[task.status]}>
                    {task.status.toUpperCase()}
                  </Badge>
                  <p className="text-sm text-muted-foreground mt-1">Current Status</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center gap-3">
                <Badge variant={priorityColors[task.priority]}>
                  {task.priority.toUpperCase()}
                </Badge>
                <div>
                  <p className="text-sm text-muted-foreground">Priority Level</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div>
                <p className="text-2xl font-bold">{task.progress}%</p>
                <p className="text-sm text-muted-foreground">Progress</p>
                {task.status === 'running' && (
                  <Progress value={task.progress} className="h-2 mt-2" />
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div>
                <p className="text-lg font-semibold">
                  {task.agent_name || 'Unassigned'}
                </p>
                <p className="text-sm text-muted-foreground">Assigned Agent</p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Task Details */}
          <div className="lg:col-span-2 space-y-6">
            <Tabs defaultValue="details" className="w-full">
              <TabsList>
                <TabsTrigger value="details">
                  <FileText className="h-4 w-4 mr-2" />
                  Details
                </TabsTrigger>
                <TabsTrigger value="logs">
                  <Activity className="h-4 w-4 mr-2" />
                  Execution Logs
                </TabsTrigger>
                <TabsTrigger value="metrics">
                  <BarChart3 className="h-4 w-4 mr-2" />
                  Metrics
                </TabsTrigger>
              </TabsList>

              <TabsContent value="details" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Task Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {task.description && (
                      <div>
                        <h4 className="font-medium mb-2">Description</h4>
                        <p className="text-muted-foreground">{task.description}</p>
                      </div>
                    )}
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <h4 className="font-medium mb-1">Type</h4>
                        <Badge variant="secondary">{task.type.replace('_', ' ')}</Badge>
                      </div>
                      <div>
                        <h4 className="font-medium mb-1">Duration</h4>
                        <p className="text-sm">
                          {task.status === 'running' || task.status === 'completed'
                            ? formatDuration(task.actual_duration)
                            : formatDuration(task.estimated_duration)}
                        </p>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <h4 className="font-medium mb-1">Created</h4>
                        <p className="text-sm text-muted-foreground">
                          {formatRelativeTime(task.created_at)}
                        </p>
                      </div>
                      <div>
                        <h4 className="font-medium mb-1">Last Updated</h4>
                        <p className="text-sm text-muted-foreground">
                          {formatRelativeTime(task.updated_at)}
                        </p>
                      </div>
                    </div>

                    {task.configuration && (
                      <div>
                        <h4 className="font-medium mb-2">Configuration</h4>
                        <pre className="bg-muted p-3 rounded-lg text-sm overflow-auto">
                          {JSON.stringify(task.configuration, null, 2)}
                        </pre>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Error or Success Messages */}
                {task.status === 'failed' && task.error_message && (
                  <Card className="border-destructive">
                    <CardHeader>
                      <CardTitle className="text-destructive flex items-center gap-2">
                        <XCircle className="h-5 w-5" />
                        Error Details
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-4">
                        <p className="text-sm text-destructive font-mono">
                          {task.error_message}
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {task.status === 'completed' && task.result_summary && (
                  <Card className="border-green-200">
                    <CardHeader>
                      <CardTitle className="text-green-700 flex items-center gap-2">
                        <CheckCircle2 className="h-5 w-5" />
                        Task Result
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                        <p className="text-sm text-green-700">
                          {task.result_summary}
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>

              <TabsContent value="logs">
                <Card>
                  <CardHeader>
                    <CardTitle>Execution Logs</CardTitle>
                    <CardDescription>
                      Real-time execution logs for this task
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="bg-black text-green-400 p-4 rounded-lg font-mono text-sm max-h-96 overflow-auto">
                      {logs.length > 0 ? (
                        logs.map((log, index) => (
                          <div key={index} className="mb-1">
                            {log}
                          </div>
                        ))
                      ) : (
                        <div className="text-gray-500">No logs available</div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="metrics">
                <Card>
                  <CardHeader>
                    <CardTitle>Performance Metrics</CardTitle>
                    <CardDescription>
                      Task execution statistics and performance data
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {metrics ? (
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-3">
                          <div>
                            <p className="text-sm text-muted-foreground">Total Records Processed</p>
                            <p className="text-2xl font-bold">{metrics.total_records_processed?.toLocaleString()}</p>
                          </div>
                          <div>
                            <p className="text-sm text-muted-foreground">Processing Rate</p>
                            <p className="text-lg font-semibold">{metrics.processing_rate}</p>
                          </div>
                        </div>
                        <div className="space-y-3">
                          <div>
                            <p className="text-sm text-muted-foreground">Average Processing Time</p>
                            <p className="text-lg font-semibold">{metrics.average_processing_time}</p>
                          </div>
                          <div>
                            <p className="text-sm text-muted-foreground">Errors/Warnings</p>
                            <p className="text-lg font-semibold">
                              {metrics.errors_encountered}/{metrics.warnings_generated}
                            </p>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <p className="text-muted-foreground">No metrics available</p>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>

          {/* Right Column - Resource Usage & Actions */}
          <div className="space-y-6">
            {task.resource_usage && (
              <Card>
                <CardHeader>
                  <CardTitle>Resource Usage</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>CPU Usage</span>
                      <span>{task.resource_usage.cpu_percent}%</span>
                    </div>
                    <Progress value={task.resource_usage.cpu_percent} className="h-2" />
                  </div>
                  
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Memory</span>
                      <span>{task.resource_usage.memory_mb} MB</span>
                    </div>
                    <Progress 
                      value={(task.resource_usage.memory_mb / 1024) * 100} 
                      className="h-2" 
                    />
                  </div>
                  
                  <div>
                    <p className="text-sm text-muted-foreground mb-1">Network I/O</p>
                    <p className="text-lg font-semibold">{task.resource_usage.network_io} MB/s</p>
                  </div>
                </CardContent>
              </Card>
            )}

            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button variant="outline" className="w-full justify-start">
                  <Copy className="h-4 w-4 mr-2" />
                  Copy Task ID
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Task
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <User className="h-4 w-4 mr-2" />
                  View Agent Details
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}