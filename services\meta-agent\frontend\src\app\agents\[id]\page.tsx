/**
 * AI Agent Platform - Agent Detail Page
 * Detailed view and management of individual agents
 */

'use client';

import React, { useState, useEffect } from 'react';
import { AppLayout } from '@/components/Layout/AppLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Bot, Edit3, Save, X, AlertCircle, CheckCircle } from 'lucide-react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { agentService } from '@/services/agent.service';
import { Agent } from '@/types/api';

export default function AgentDetailPage() {
  const params = useParams();
  const agentId = params?.id as string;
  
  const [agent, setAgent] = useState<Agent | null>(null);
  const [loading, setLoading] = useState(true);
  const [editing, setEditing] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  // Form state
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    requirements: '',
    user_prompt: '',
    capabilities: [] as string[]
  });

  useEffect(() => {
    loadAgent();
  }, [agentId]);

  const loadAgent = async () => {
    try {
      setLoading(true);
      const agentData = await agentService.getAgent(agentId);
      setAgent(agentData);
      
      // Extract requirements and user_prompt from config
      const requirements = agentData.config?.requirements || '';
      const user_prompt = agentData.config?.user_prompt || '';
      
      setFormData({
        name: agentData.name,
        description: agentData.description || '',
        requirements,
        user_prompt,
        capabilities: agentData.capabilities || []
      });
    } catch (err) {
      setError('Failed to load agent details');
      console.error('Error loading agent:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      setError(null);
      
      await agentService.updateAgent(agentId, {
        name: formData.name,
        description: formData.description,
        requirements: formData.requirements,
        user_prompt: formData.user_prompt,
        capabilities: formData.capabilities
      });
      
      setSuccess('Agent updated successfully!');
      setEditing(false);
      
      // Reload agent data
      await loadAgent();
      
      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(null), 3000);
    } catch (err) {
      setError('Failed to update agent');
      console.error('Error updating agent:', err);
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    if (agent) {
      const requirements = agent.config?.requirements || '';
      const user_prompt = agent.config?.user_prompt || '';
      
      setFormData({
        name: agent.name,
        description: agent.description || '',
        requirements,
        user_prompt,
        capabilities: agent.capabilities || []
      });
    }
    setEditing(false);
    setError(null);
  };

  if (loading) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center min-h-64">
          <div className="text-center">
            <Bot className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">Loading agent details...</p>
          </div>
        </div>
      </AppLayout>
    );
  }

  if (!agent) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center min-h-64">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
            <p className="text-destructive">Agent not found</p>
          </div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" asChild>
              <Link href="/agents">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Agents
              </Link>
            </Button>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">{agent.name}</h1>
              <p className="text-muted-foreground">
                Agent ID: {agentId}
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            {success && (
              <div className="flex items-center gap-2 text-green-600">
                <CheckCircle className="h-4 w-4" />
                <span className="text-sm">{success}</span>
              </div>
            )}
            
            {!editing ? (
              <Button onClick={() => setEditing(true)}>
                <Edit3 className="h-4 w-4 mr-2" />
                Edit Agent
              </Button>
            ) : (
              <div className="flex gap-2">
                <Button onClick={handleSave} disabled={saving}>
                  <Save className="h-4 w-4 mr-2" />
                  {saving ? 'Saving...' : 'Save'}
                </Button>
                <Button variant="outline" onClick={handleCancel}>
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </Button>
              </div>
            )}
          </div>
        </div>

        {error && (
          <Card className="border-destructive">
            <CardContent className="py-4">
              <div className="flex items-center gap-2 text-destructive">
                <AlertCircle className="h-4 w-4" />
                <span>{error}</span>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Agent Details */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="name">Name</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  disabled={!editing}
                />
              </div>
              
              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  disabled={!editing}
                  rows={3}
                />
              </div>
              
              <div>
                <Label>Status</Label>
                <div className="mt-2">
                  <Badge variant={agent.status === 'running' ? 'default' : 'secondary'}>
                    {agent.status}
                  </Badge>
                </div>
              </div>
              
              <div>
                <Label>Type</Label>
                <p className="text-sm text-muted-foreground mt-1">{agent.type}</p>
              </div>
            </CardContent>
          </Card>

          {/* Requirements & Prompts */}
          <Card>
            <CardHeader>
              <CardTitle>Requirements & Prompts</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="user_prompt">Original User Prompt</Label>
                <Textarea
                  id="user_prompt"
                  value={formData.user_prompt}
                  onChange={(e) => setFormData(prev => ({ ...prev, user_prompt: e.target.value }))}
                  disabled={!editing}
                  placeholder="Enter the original user prompt..."
                  rows={3}
                />
              </div>
              
              <div>
                <Label htmlFor="requirements">Detailed Requirements</Label>
                <Textarea
                  id="requirements"
                  value={formData.requirements}
                  onChange={(e) => setFormData(prev => ({ ...prev, requirements: e.target.value }))}
                  disabled={!editing}
                  placeholder="Enter detailed requirements..."
                  rows={6}
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Capabilities */}
        <Card>
          <CardHeader>
            <CardTitle>Capabilities</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {agent.capabilities.map((capability, index) => (
                <Badge key={index} variant="outline">
                  {capability}
                </Badge>
              ))}
              {agent.capabilities.length === 0 && (
                <p className="text-sm text-muted-foreground">No capabilities defined</p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Configuration */}
        <Card>
          <CardHeader>
            <CardTitle>Configuration</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="text-sm bg-muted p-4 rounded-md overflow-auto">
              {JSON.stringify(agent.config, null, 2)}
            </pre>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}