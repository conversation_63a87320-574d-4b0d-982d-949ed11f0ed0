/* tslint:disable */
/* eslint-disable */
/**
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { DealStageInfo } from './DealStageInfo';
import {
    DealStageInfoFromJSON,
    DealStageInfoFromJSONTyped,
    DealStageInfoToJSON,
    DealStageInfoToJSONTyped,
} from './DealStageInfo';
import type { DealCompanyInfo } from './DealCompanyInfo';
import {
    DealCompanyInfoFromJSON,
    DealCompanyInfoFromJSONTyped,
    DealCompanyInfoToJSON,
    DealCompanyInfoToJSONTyped,
} from './DealCompanyInfo';

/**
 * 
 * @export
 * @interface DealWithDetails
 */
export interface DealWithDetails {
    /**
     * 
     * @type {string}
     * @memberof DealWithDetails
     */
    id?: string;
    /**
     * 
     * @type {string}
     * @memberof DealWithDetails
     */
    title?: string;
    /**
     * 
     * @type {string}
     * @memberof DealWithDetails
     */
    description?: string;
    /**
     * 
     * @type {number}
     * @memberof DealWithDetails
     */
    estimatedValue?: number;
    /**
     * 
     * @type {string}
     * @memberof DealWithDetails
     */
    companyId?: string;
    /**
     * 
     * @type {string}
     * @memberof DealWithDetails
     */
    dealStageId?: string;
    /**
     * 
     * @type {Date}
     * @memberof DealWithDetails
     */
    expectedCloseDate?: Date;
    /**
     * 
     * @type {string}
     * @memberof DealWithDetails
     */
    createdBy?: string;
    /**
     * 
     * @type {Date}
     * @memberof DealWithDetails
     */
    createdAt?: Date;
    /**
     * 
     * @type {Date}
     * @memberof DealWithDetails
     */
    updatedAt?: Date;
    /**
     * 
     * @type {DealCompanyInfo}
     * @memberof DealWithDetails
     */
    company?: DealCompanyInfo;
    /**
     * 
     * @type {DealStageInfo}
     * @memberof DealWithDetails
     */
    dealStage?: DealStageInfo;
}

/**
 * Check if a given object implements the DealWithDetails interface.
 */
export function instanceOfDealWithDetails(value: object): value is DealWithDetails {
    return true;
}

export function DealWithDetailsFromJSON(json: any): DealWithDetails {
    return DealWithDetailsFromJSONTyped(json, false);
}

export function DealWithDetailsFromJSONTyped(json: any, ignoreDiscriminator: boolean): DealWithDetails {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'] == null ? undefined : json['id'],
        'title': json['title'] == null ? undefined : json['title'],
        'description': json['description'] == null ? undefined : json['description'],
        'estimatedValue': json['estimated_value'] == null ? undefined : json['estimated_value'],
        'companyId': json['company_id'] == null ? undefined : json['company_id'],
        'dealStageId': json['deal_stage_id'] == null ? undefined : json['deal_stage_id'],
        'expectedCloseDate': json['expected_close_date'] == null ? undefined : (new Date(json['expected_close_date'])),
        'createdBy': json['created_by'] == null ? undefined : json['created_by'],
        'createdAt': json['created_at'] == null ? undefined : (new Date(json['created_at'])),
        'updatedAt': json['updated_at'] == null ? undefined : (new Date(json['updated_at'])),
        'company': json['company'] == null ? undefined : DealCompanyInfoFromJSON(json['company']),
        'dealStage': json['deal_stage'] == null ? undefined : DealStageInfoFromJSON(json['deal_stage']),
    };
}

  export function DealWithDetailsToJSON(json: any): DealWithDetails {
      return DealWithDetailsToJSONTyped(json, false);
  }

  export function DealWithDetailsToJSONTyped(value?: DealWithDetails | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'title': value['title'],
        'description': value['description'],
        'estimated_value': value['estimatedValue'],
        'company_id': value['companyId'],
        'deal_stage_id': value['dealStageId'],
        'expected_close_date': value['expectedCloseDate'] == null ? undefined : ((value['expectedCloseDate']).toISOString().substring(0,10)),
        'created_by': value['createdBy'],
        'created_at': value['createdAt'] == null ? undefined : ((value['createdAt']).toISOString()),
        'updated_at': value['updatedAt'] == null ? undefined : ((value['updatedAt']).toISOString()),
        'company': DealCompanyInfoToJSON(value['company']),
        'deal_stage': DealStageInfoToJSON(value['dealStage']),
    };
}

