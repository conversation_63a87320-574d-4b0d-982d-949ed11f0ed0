load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "handlers",
    srcs = [
        "companies.go",
        "companies_impl.go",
        "contacts.go",
        "contacts_impl.go",
        "deals.go",
        "deals_impl.go",
        "helpers.go",
        "users.go",
        "users_impl.go",
    ],
    importpath = "github.com/TwoDotAi/mono/services/orbit/crm_backend/handlers",
    visibility = ["//visibility:public"],
    deps = [
        "//shared/go/logging",
        "//services/orbit/crm_backend/database",
        "//generated/orbit:openapi_server",
        "@com_github_gin_gonic_gin//:gin",
        "@com_github_google_uuid//:uuid",
        "@com_github_oapi_codegen_runtime//types",
        "@org_uber_go_zap//:zap",
    ],
)
