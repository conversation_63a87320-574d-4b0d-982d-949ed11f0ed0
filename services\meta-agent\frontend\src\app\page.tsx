/**
 * AI Agent Platform - Home Page (Redirects to Dashboard)
 */

'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
export default function HomePage() {
  const { isAuthenticated, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    // Add timeout to prevent infinite loading
    const timeout = setTimeout(() => {
      if (loading) {
        console.log('Auth loading timeout, redirecting to dashboard');
        router.replace('/dashboard');
      }
    }, 5000); // 5 second timeout

    if (!loading) {
      clearTimeout(timeout);
      // Always redirect to dashboard regardless of auth status
      router.replace('/dashboard');
    }

    return () => clearTimeout(timeout);
  }, [isAuthenticated, loading, router]);

  return (
    <div className="flex items-center justify-center h-screen">
      <div className="space-y-4 text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
        <p className="text-lg">Loading AI Agent Platform...</p>
        <p className="text-sm text-gray-600">
          {loading ? 'Checking authentication...' : 'Redirecting...'}
        </p>
      </div>
    </div>
  );
}