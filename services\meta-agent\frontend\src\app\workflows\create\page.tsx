/**
 * AI Agent Platform - Create Workflow Page
 * Visual workflow builder interface
 */

'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { AppLayout } from '@/components/Layout/AppLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  ArrowLeft,
  Save,
  Play,
  Eye,
  Settings,
  Plus,
  Trash2,
  Copy,
  Move,
  Zap,
  Database,
  Mail,
  Webhook,
  Clock,
  GitBranch,
  Filter,
  Code,
  MessageSquare,
  FileText,
  Globe,
  Smartphone
} from 'lucide-react';
import Link from 'next/link';

// Node types for the workflow builder
const nodeTypes = [
  {
    category: 'Triggers',
    nodes: [
      { id: 'webhook', name: 'Webhook', icon: Webhook, description: 'Trigger on HTTP request' },
      { id: 'schedule', name: 'Schedule', icon: Clock, description: 'Trigger on schedule' },
      { id: 'email', name: 'Email', icon: Mail, description: 'Trigger on email' },
    ]
  },
  {
    category: 'Actions',
    nodes: [
      { id: 'http-request', name: 'HTTP Request', icon: Globe, description: 'Make HTTP request' },
      { id: 'email-send', name: 'Send Email', icon: Mail, description: 'Send email message' },
      { id: 'database', name: 'Database', icon: Database, description: 'Database operations' },
      { id: 'sms', name: 'SMS', icon: Smartphone, description: 'Send SMS message' },
    ]
  },
  {
    category: 'Logic',
    nodes: [
      { id: 'condition', name: 'Condition', icon: GitBranch, description: 'Conditional logic' },
      { id: 'filter', name: 'Filter', icon: Filter, description: 'Filter data' },
      { id: 'transform', name: 'Transform', icon: Code, description: 'Transform data' },
    ]
  },
  {
    category: 'AI',
    nodes: [
      { id: 'ai-chat', name: 'AI Chat', icon: MessageSquare, description: 'AI conversation' },
      { id: 'ai-analyze', name: 'AI Analyze', icon: Zap, description: 'AI analysis' },
      { id: 'ai-generate', name: 'AI Generate', icon: FileText, description: 'AI content generation' },
    ]
  }
];

export default function CreateWorkflowPage() {
  const router = useRouter();
  const [workflowName, setWorkflowName] = useState('');
  const [workflowDescription, setWorkflowDescription] = useState('');
  const [selectedNodes, setSelectedNodes] = useState<any[]>([]);
  const [connections, setConnections] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState('design');
  const [saving, setSaving] = useState(false);

  const handleSave = async () => {
    setSaving(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      router.push('/workflows');
    } catch (error) {
      console.error('Failed to save workflow:', error);
    } finally {
      setSaving(false);
    }
  };

  const addNode = (nodeType: any) => {
    const newNode = {
      id: `${nodeType.id}-${Date.now()}`,
      type: nodeType.id,
      name: nodeType.name,
      icon: nodeType.icon,
      x: Math.random() * 400 + 100,
      y: Math.random() * 300 + 100,
      config: {}
    };
    setSelectedNodes([...selectedNodes, newNode]);
  };

  const removeNode = (nodeId: string) => {
    setSelectedNodes(selectedNodes.filter(node => node.id !== nodeId));
    setConnections(connections.filter(conn => conn.source !== nodeId && conn.target !== nodeId));
  };

  return (
    <AppLayout>
      <div className="container mx-auto py-8 px-4 max-w-7xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Link href="/workflows">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Workflows
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold">Create Workflow</h1>
              <p className="text-muted-foreground">
                Build visual workflows with drag-and-drop components
              </p>
            </div>
          </div>
          <div className="flex gap-3">
            <Button variant="outline">
              <Eye className="h-4 w-4 mr-2" />
              Preview
            </Button>
            <Button variant="outline">
              <Play className="h-4 w-4 mr-2" />
              Test Run
            </Button>
            <Button onClick={handleSave} disabled={saving}>
              <Save className="h-4 w-4 mr-2" />
              {saving ? 'Saving...' : 'Save Workflow'}
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Sidebar - Node Palette */}
          <div className="lg:col-span-1">
            <Card className="h-fit">
              <CardHeader>
                <CardTitle className="text-lg">Components</CardTitle>
                <CardDescription>
                  Drag components to build your workflow
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {nodeTypes.map((category) => (
                    <div key={category.category}>
                      <h4 className="font-medium text-sm text-muted-foreground mb-2">
                        {category.category}
                      </h4>
                      <div className="space-y-2">
                        {category.nodes.map((node) => {
                          const IconComponent = node.icon;
                          return (
                            <div
                              key={node.id}
                              className="flex items-center gap-3 p-3 rounded-lg border border-dashed hover:border-primary/50 cursor-pointer transition-colors"
                              onClick={() => addNode(node)}
                            >
                              <IconComponent className="h-5 w-5 text-primary" />
                              <div className="flex-1">
                                <p className="font-medium text-sm">{node.name}</p>
                                <p className="text-xs text-muted-foreground">
                                  {node.description}
                                </p>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="design">Design</TabsTrigger>
                <TabsTrigger value="settings">Settings</TabsTrigger>
                <TabsTrigger value="code">Code View</TabsTrigger>
              </TabsList>

              {/* Design Tab */}
              <TabsContent value="design" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Workflow Canvas</CardTitle>
                    <CardDescription>
                      Design your workflow by connecting components
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="relative bg-gray-50 rounded-lg border-2 border-dashed border-gray-200 min-h-[500px] p-4">
                      {selectedNodes.length === 0 ? (
                        <div className="absolute inset-0 flex items-center justify-center">
                          <div className="text-center">
                            <Plus className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                            <h3 className="text-lg font-medium mb-2">Start Building</h3>
                            <p className="text-muted-foreground">
                              Add components from the sidebar to get started
                            </p>
                          </div>
                        </div>
                      ) : (
                        <div className="space-y-4">
                          {selectedNodes.map((node) => {
                            const IconComponent = node.icon;
                            return (
                              <div
                                key={node.id}
                                className="inline-block bg-white rounded-lg border shadow-sm p-4 m-2 min-w-[200px]"
                                style={{ position: 'absolute', left: node.x, top: node.y }}
                              >
                                <div className="flex items-center justify-between mb-2">
                                  <div className="flex items-center gap-2">
                                    <IconComponent className="h-5 w-5 text-primary" />
                                    <span className="font-medium">{node.name}</span>
                                  </div>
                                  <div className="flex gap-1">
                                    <Button size="sm" variant="ghost">
                                      <Settings className="h-4 w-4" />
                                    </Button>
                                    <Button 
                                      size="sm" 
                                      variant="ghost"
                                      onClick={() => removeNode(node.id)}
                                    >
                                      <Trash2 className="h-4 w-4" />
                                    </Button>
                                  </div>
                                </div>
                                <p className="text-sm text-muted-foreground">
                                  Click to configure
                                </p>
                              </div>
                            );
                          })}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Settings Tab */}
              <TabsContent value="settings" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Workflow Settings</CardTitle>
                    <CardDescription>
                      Configure your workflow properties
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="workflow-name">Workflow Name</Label>
                      <Input
                        id="workflow-name"
                        placeholder="Enter workflow name..."
                        value={workflowName}
                        onChange={(e) => setWorkflowName(e.target.value)}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="workflow-description">Description</Label>
                      <Textarea
                        id="workflow-description"
                        placeholder="Describe what this workflow does..."
                        value={workflowDescription}
                        onChange={(e) => setWorkflowDescription(e.target.value)}
                        rows={3}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Execution Settings</Label>
                      <div className="space-y-3">
                        <div className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            id="auto-retry"
                            className="rounded border-border"
                          />
                          <Label htmlFor="auto-retry">Enable automatic retry on failure</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            id="parallel-execution"
                            className="rounded border-border"
                          />
                          <Label htmlFor="parallel-execution">Allow parallel execution</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            id="logging"
                            className="rounded border-border"
                            defaultChecked
                          />
                          <Label htmlFor="logging">Enable detailed logging</Label>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>Timeout (minutes)</Label>
                      <Input
                        type="number"
                        placeholder="30"
                        min="1"
                        max="1440"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Tags</Label>
                      <Input
                        placeholder="Add tags separated by commas..."
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Code View Tab */}
              <TabsContent value="code" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Workflow Definition</CardTitle>
                    <CardDescription>
                      JSON representation of your workflow
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="bg-gray-900 text-gray-100 rounded-lg p-4 font-mono text-sm">
                      <pre>{JSON.stringify({
                        name: workflowName || 'Untitled Workflow',
                        description: workflowDescription || 'No description',
                        nodes: selectedNodes.map(node => ({
                          id: node.id,
                          type: node.type,
                          name: node.name,
                          position: { x: node.x, y: node.y },
                          config: node.config
                        })),
                        connections: connections,
                        settings: {
                          autoRetry: false,
                          parallelExecution: false,
                          logging: true,
                          timeout: 30
                        }
                      }, null, 2)}</pre>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
