# Artifact Registry Module for Docker Images
# Creates Docker repository for storing container images

terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
  }
}

# Enable Artifact Registry API
resource "google_project_service" "artifactregistry" {
  service = "artifactregistry.googleapis.com"
  
  disable_dependent_services = true
}

# Docker repository for platform services
resource "google_artifact_registry_repository" "docker_repo" {
  repository_id = "${var.project_name}-docker"
  location      = var.region
  description   = "Docker repository for ${var.project_name} platform services"
  format        = "DOCKER"

  docker_config {
    immutable_tags = var.immutable_tags
  }

  cleanup_policies {
    id     = "delete-old-images"
    action = "DELETE"
    
    condition {
      tag_state    = "TAGGED"
      tag_prefixes = var.cleanup_tag_prefixes
      older_than   = "${var.cleanup_older_than_days * 86400}s"
    }
  }

  cleanup_policies {
    id     = "delete-untagged-images"
    action = "DELETE"
    
    condition {
      tag_state  = "UNTAGGED"
      older_than = "${var.cleanup_untagged_older_than_days * 86400}s"
    }
  }

  depends_on = [google_project_service.artifactregistry]
}

# IAM binding for Cloud Build to push images
resource "google_artifact_registry_repository_iam_binding" "cloud_build_writer" {
  count = var.enable_cloud_build_access ? 1 : 0
  
  repository = google_artifact_registry_repository.docker_repo.name
  location   = google_artifact_registry_repository.docker_repo.location
  role       = "roles/artifactregistry.writer"

  members = [
    "serviceAccount:${data.google_project.current.number}@cloudbuild.gserviceaccount.com",
  ]
}

# IAM binding for Compute Engine service account to pull images
resource "google_artifact_registry_repository_iam_binding" "compute_reader" {
  count = var.enable_compute_access ? 1 : 0
  
  repository = google_artifact_registry_repository.docker_repo.name
  location   = google_artifact_registry_repository.docker_repo.location
  role       = "roles/artifactregistry.reader"

  members = [
    "serviceAccount:${data.google_project.current.number}-<EMAIL>",
  ]
}

# Custom IAM bindings for additional service accounts
resource "google_artifact_registry_repository_iam_binding" "custom_readers" {
  for_each = var.additional_readers
  
  repository = google_artifact_registry_repository.docker_repo.name
  location   = google_artifact_registry_repository.docker_repo.location
  role       = "roles/artifactregistry.reader"

  members = each.value
}

resource "google_artifact_registry_repository_iam_binding" "custom_writers" {
  for_each = var.additional_writers
  
  repository = google_artifact_registry_repository.docker_repo.name
  location   = google_artifact_registry_repository.docker_repo.location
  role       = "roles/artifactregistry.writer"

  members = each.value
}

# Data source to get current project information
data "google_project" "current" {
  project_id = var.project_id
}

# Create Cloud Build configuration for automated builds
resource "google_cloudbuild_trigger" "docker_build" {
  count = var.enable_automated_builds ? 1 : 0
  
  name        = "${var.project_name}-docker-build"
  description = "Automated Docker builds for ${var.project_name} services"

  github {
    owner = var.github_repo_owner
    name  = var.github_repo_name
    push {
      branch = var.build_branch_pattern
    }
  }

  substitutions = {
    _REGISTRY_URL = "${var.region}-docker.pkg.dev/${data.google_project.current.project_id}/${google_artifact_registry_repository.docker_repo.repository_id}"
    _PROJECT_ID   = data.google_project.current.project_id
  }

  filename = var.cloudbuild_file_path

  depends_on = [
    google_project_service.cloudbuild,
    google_artifact_registry_repository.docker_repo
  ]
}

# Enable Cloud Build API for automated builds
resource "google_project_service" "cloudbuild" {
  count   = var.enable_automated_builds ? 1 : 0
  service = "cloudbuild.googleapis.com"
  
  disable_dependent_services = true
}