#!/usr/bin/env python3
"""
Generate OpenAPI spec from the actual FastAPI application
This ensures all Pydantic schemas are included correctly
"""

import json
import yaml
import os
import sys
from pathlib import Path

# Add src to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def generate_openapi_spec():
    """Generate OpenAPI specification from the FastAPI app"""
    try:
        # Import the FastAPI app
        from main import app
        
        # Get the OpenAPI schema
        openapi_schema = app.openapi()
        
        # Ensure output directory exists
        output_dir = Path("openapi")
        output_dir.mkdir(exist_ok=True)
        
        # Write JSON version
        json_path = output_dir / "openapi.json"
        with open(json_path, 'w') as f:
            json.dump(openapi_schema, f, indent=2)
        
        # Write YAML version
        yaml_path = output_dir / "openapi.yaml"
        with open(yaml_path, 'w') as f:
            yaml.dump(openapi_schema, f, default_flow_style=False, sort_keys=False)
        
        print(f"✅ OpenAPI specification generated successfully!")
        print(f"📄 JSON: {json_path}")
        print(f"📄 YAML: {yaml_path}")
        
        # Print some stats
        schemas = openapi_schema.get('components', {}).get('schemas', {})
        paths = openapi_schema.get('paths', {})
        
        print(f"📊 Generated {len(schemas)} schemas and {len(paths)} paths")
        print(f"🔧 Schemas: {', '.join(list(schemas.keys())[:10])}{'...' if len(schemas) > 10 else ''}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Failed to import FastAPI app: {e}")
        print("Make sure all dependencies are installed and the app can be imported")
        return False
    except Exception as e:
        print(f"❌ Failed to generate OpenAPI spec: {e}")
        return False

if __name__ == "__main__":
    success = generate_openapi_spec()
    sys.exit(0 if success else 1)
