#!/bin/bash
# Run frontend locally connecting to dev backend

set -euo pipefail

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_error() {
    echo -e "${RED}❌ Error: $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "🚀 RUNNING FRONTEND WITH DEV BACKEND"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""

# Change to workspace directory
if [ -n "$BUILD_WORKSPACE_DIRECTORY" ]; then
    cd "$BUILD_WORKSPACE_DIRECTORY"
elif [ -n "$TEST_WORKSPACE" ]; then
    cd "$TEST_WORKSPACE"
else
    # Find the workspace root by looking for WORKSPACE file
    CURRENT_DIR="$PWD"
    while [ "$CURRENT_DIR" != "/" ]; do
        if [ -f "$CURRENT_DIR/WORKSPACE" ]; then
            cd "$CURRENT_DIR"
            break
        fi
        CURRENT_DIR="$(dirname "$CURRENT_DIR")"
    done
fi

echo "Working directory: $(pwd)"
echo ""

# Get the dev load balancer IP from Terraform
print_info "Getting dev backend URL from Terraform..."
cd terraform/environments/dev

# Check if terraform is initialized
if [ ! -d ".terraform" ]; then
    print_info "Initializing Terraform..."
    terraform init
fi

# Get the instance IP (direct connection to bypass load balancer CORS issues)
INSTANCE_IP=$(terraform output -raw instance_external_ip 2>/dev/null || echo "")

if [ -z "$INSTANCE_IP" ] || [ "$INSTANCE_IP" = "null" ]; then
    print_error "Could not retrieve instance IP from Terraform"
    print_info "Please ensure Terraform is applied:"
    echo "  cd terraform/environments/dev"
    echo "  terraform apply"
    exit 1
fi

# Return to web directory
cd ../../../services/orbit/web

# Create .env.local file with dev backend URL
print_info "Creating .env.local with dev backend configuration..."
cat > .env.local << EOF
# Dev Backend Configuration - Direct instance connection (bypasses load balancer CORS issues)
VITE_API_BASE_URL=http://${INSTANCE_IP}/api/v1

# Environment info (for display purposes)
VITE_ENVIRONMENT=local-with-dev-backend
EOF

print_success "Configuration created:"
echo "  🌐 Backend URL: http://${INSTANCE_IP}/api/v1"
echo ""

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    print_info "Installing dependencies..."
    npm install
fi

# Generate API client to ensure it's up to date
print_info "Regenerating API client..."
if ! bazel run //services/orbit/web/api-client:generate_client; then
    print_warning "Failed to regenerate API client, continuing with existing client"
fi

echo ""
print_info "Starting development server..."
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""
echo "📝 Configuration:"
echo "  🖥️  Frontend: http://localhost:8080"
echo "  🔧 Backend: http://${INSTANCE_IP}/api/v1 (direct instance connection)"
echo "  🌐 Dev Website: http://${INSTANCE_IP} (direct) or http://${LOAD_BALANCER_IP:-unknown} (load balancer)"
echo ""
echo "🔑 OAuth Callback:"
echo "  When signing in with Google, the OAuth callback will redirect to:"
echo "  http://localhost:8080/auth/callback/google"
echo ""
echo "⚡ Hot reload is enabled - changes will be reflected immediately"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""

# Check if port 8080 is in use and kill the process
print_info "Checking port 8080..."
if lsof -Pi :8080 -sTCP:LISTEN -t >/dev/null 2>&1; then
    print_warning "Port 8080 is in use, attempting to free it..."
    lsof -Pi :8080 -sTCP:LISTEN -t | xargs kill -9 2>/dev/null || true
    sleep 2
fi

# Run the dev server on port 8080
npm run dev -- --port 8080