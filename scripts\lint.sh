#!/bin/bash
set -e

# Go linting script using golangci-lint
# This script can be run directly or via Bazel

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Find the root directory by looking for go.mod
if [ -n "${BUILD_WORKSPACE_DIRECTORY:-}" ] && [ -f "${BUILD_WORKSPACE_DIRECTORY}/go.mod" ]; then
    # We're running from Bazel, use the workspace directory
    ROOT_DIR="$BUILD_WORKSPACE_DIRECTORY"
elif [ -f "go.mod" ]; then
    ROOT_DIR="$(pwd)"
elif [ -f "../go.mod" ]; then
    ROOT_DIR="$(cd ".." && pwd)"
elif [ -f "../../go.mod" ]; then
    ROOT_DIR="$(cd "../.." && pwd)"
else
    # Fallback to script directory approach
    ROOT_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"
fi

# Change to the root directory
cd "$ROOT_DIR"

# Set up PATH to include Go bin directory
export PATH="$PATH:$(go env GOPATH)/bin"

# Install golangci-lint if not present
if ! command -v golangci-lint &> /dev/null; then
    echo "Installing golangci-lint..."
    if [[ "${BAZEL_TEST:-}" == "1" ]]; then
        echo "❌ golangci-lint not found and cannot install in Bazel test environment"
        echo "Please install golangci-lint manually: curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b \$(go env GOPATH)/bin v1.57.1"
        exit 1
    else
        curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $(go env GOPATH)/bin v1.57.1
    fi
fi

# Run golangci-lint on specific packages
echo "Running golangci-lint..."
GOWORK=off golangci-lint run --config .golangci.yml \
    ./services/orbit/auth/... \
    ./services/orbit/crm_backend/... \
    ./services/orbit/gateway/... \
    ./shared/go/...

echo "✅ Go linting completed successfully!"