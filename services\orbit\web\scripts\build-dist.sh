#!/bin/bash
# Build production dist folder for GCP bucket deployment

set -e

# Get the workspace root - when running via <PERSON><PERSON>, we're in the execroot
# so we need to go back to the actual workspace
if [ -n "$BUILD_WORKSPACE_DIRECTORY" ]; then
    cd "$BUILD_WORKSPACE_DIRECTORY"
elif [ -n "$TEST_WORKSPACE" ]; then
    cd "$TEST_WORKSPACE/.."
else
    # Find the workspace root by looking for WORKSPACE file
    CURRENT_DIR="$PWD"
    while [ "$CURRENT_DIR" != "/" ]; do
        if [ -f "$CURRENT_DIR/WORKSPACE" ]; then
            cd "$CURRENT_DIR"
            break
        fi
        CURRENT_DIR="$(dirname "$CURRENT_DIR")"
    done
fi

echo "Building production dist for GCP bucket deployment..."
echo "Working directory: $(pwd)"

# Change to services/orbit/web directory
cd services/orbit/web

# Verify we're in the right place
if [ ! -f "package.json" ]; then
    echo "Error: package.json not found in $(pwd)"
    exit 1
fi

# Clean any existing dist folder
rm -rf dist

# Install dependencies if needed
echo "Installing npm dependencies..."
npm install

# Set production environment variables for GCP deployment
echo "Configuring production environment for GCP..."
cat > .env.production << 'EOF'
# Production environment for GCP bucket deployment
VITE_API_BASE_URL=https://your-api-domain.com/api/v1
NODE_ENV=production
EOF

echo "✅ Production environment configured"
echo "Note: Update VITE_API_BASE_URL in .env.production with your actual API URL"

# Build for production
echo "Building for production..."
npm run build

# Verify dist folder was created
if [ ! -d "dist" ]; then
    echo "Error: dist folder was not created"
    exit 1
fi

# Create a deployment info file
echo "Creating deployment info..."
cat > dist/deployment-info.json << EOF
{
  "buildDate": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "buildHost": "$(hostname)",
  "gitCommit": "$(git rev-parse HEAD 2>/dev/null || echo 'unknown')",
  "gitBranch": "$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo 'unknown')",
  "environment": "production",
  "deploymentTarget": "gcp-bucket"
}
EOF

# Create a simple health check endpoint
echo "Creating health check endpoint..."
cat > dist/health.json << 'EOF'
{
  "status": "ok",
  "service": "crm-web",
  "environment": "production"
}
EOF

# Create robots.txt for production
echo "Creating robots.txt..."
cat > dist/robots.txt << 'EOF'
User-agent: *
Allow: /

Sitemap: https://your-domain.com/sitemap.xml
EOF

echo "✅ Production build completed successfully!"
echo ""
echo "📦 Dist folder contents:"
find dist -type f | head -20
if [ $(find dist -type f | wc -l) -gt 20 ]; then
    echo "... and $(( $(find dist -type f | wc -l) - 20 )) more files"
fi

echo ""
echo "📊 Build statistics:"
echo "Total files: $(find dist -type f | wc -l)"
echo "Total size: $(du -sh dist | cut -f1)"
echo ""
echo "🚀 Ready for GCP bucket deployment!"
echo ""
echo "Next steps:"
echo "1. Update VITE_API_BASE_URL in .env.production with your actual API URL"
echo "2. Upload the dist/ folder contents to your GCP bucket"
echo "3. Configure bucket for static website hosting"
echo "4. Set up CDN if needed"