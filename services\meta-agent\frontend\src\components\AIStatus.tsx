/**
 * AI Status Component - Shows AI provider health and status
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Brain,
  CheckCircle2,
  XCircle,
  AlertCircle,
  RefreshCw,
  Loader2,
  Sparkles
} from 'lucide-react';
import { ApiClient } from '@/lib/api/client';

const apiClient = new ApiClient();

interface AIProviderStatus {
  status: 'healthy' | 'unhealthy';
  model?: string;
  response_time?: string;
  error?: string;
}

interface AIHealthData {
  status: string;
  providers: Record<string, AIProviderStatus>;
  total_providers: number;
  healthy_providers: number;
}

export function AIStatus() {
  const [healthData, setHealthData] = useState<AIHealthData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadAIHealth();
    // Refresh every 30 seconds
    const interval = setInterval(loadAIHealth, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadAIHealth = async () => {
    try {
      setError(null);
      if (!loading) setLoading(true); // Only show loading on initial load
      
      const health = await apiClient.getAIHealth();
      setHealthData(health);
    } catch (error) {
      console.error('Failed to load AI health:', error);
      setError('Failed to load AI status');
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      case 'unhealthy':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'unhealthy':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    }
  };

  if (loading && !healthData) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            AI Providers
          </CardTitle>
        </CardHeader>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            AI Providers
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={loadAIHealth}
            className="mt-3"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (!healthData) {
    return null;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Brain className="h-5 w-5" />
          AI Providers
          <Badge variant="outline" className="ml-auto">
            {healthData.healthy_providers}/{healthData.total_providers}
          </Badge>
        </CardTitle>
        <CardDescription>
          AI service health and provider status
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Overall Status */}
        <div className={`flex items-center gap-2 p-3 rounded-lg border ${getStatusColor(healthData.status)}`}>
          {getStatusIcon(healthData.status)}
          <span className="font-medium capitalize">
            {healthData.status === 'healthy' ? 'All Systems Operational' : 
             healthData.status === 'no_providers' ? 'No Providers Configured' :
             'Some Issues Detected'}
          </span>
        </div>

        {/* Provider Details */}
        {Object.keys(healthData.providers).length > 0 ? (
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Provider Status</h4>
            {Object.entries(healthData.providers).map(([provider, status]) => (
              <div key={provider} className="flex items-center justify-between p-2 rounded border bg-muted/20">
                <div className="flex items-center gap-2">
                  {getStatusIcon(status.status)}
                  <span className="font-medium capitalize">
                    {provider}
                  </span>
                  {status.model && (
                    <Badge variant="secondary" className="text-xs">
                      {status.model}
                    </Badge>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  {status.response_time && (
                    <Badge variant="outline" className="text-xs">
                      {status.response_time}
                    </Badge>
                  )}
                  <Badge variant={status.status === 'healthy' ? 'default' : 'destructive'} className="text-xs">
                    {status.status}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <Alert>
            <Sparkles className="h-4 w-4" />
            <AlertDescription>
              No AI providers are currently configured. Add API keys in your environment configuration.
            </AlertDescription>
          </Alert>
        )}

        {/* Refresh Button */}
        <Button 
          variant="outline" 
          size="sm" 
          onClick={loadAIHealth}
          disabled={loading}
          className="w-full"
        >
          {loading ? (
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <RefreshCw className="h-4 w-4 mr-2" />
          )}
          Refresh Status
        </Button>
      </CardContent>
    </Card>
  );
}