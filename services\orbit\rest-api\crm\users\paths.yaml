# User profile endpoints
/users/profile:
  get:
    tags: [User Management]
    summary: Get user profile
    description: Retrieve the current user's profile information
    responses:
      '200':
        description: User profile
        content:
          application/json:
            schema:
              $ref: './schemas.yaml#/UserProfile'
      '401':
        description: Not authenticated

  put:
    tags: [User Management]
    summary: Update user profile
    description: Update the current user's profile information
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: './schemas.yaml#/UserProfileUpdate'
    responses:
      '200':
        description: Profile updated successfully
        content:
          application/json:
            schema:
              $ref: './schemas.yaml#/UserProfile'
      '400':
        description: Invalid input