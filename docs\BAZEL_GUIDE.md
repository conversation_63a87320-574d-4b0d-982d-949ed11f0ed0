# Bazel-First Development Guide

This guide explains how to work with the CRM monorepo using <PERSON>zel as the primary build system with Make as a convenient wrapper.

## Philosophy

- **Bazel First**: All build, test, and deployment operations go through Bazel
- **Make as Wrapper**: Make provides convenient shortcuts for common Bazel operations
- **Reproducible Builds**: Consistent builds across all environments
- **Incremental Builds**: Only rebuild what's changed

## Common Workflows

### 1. Development Setup

```bash
# Quick setup (uses <PERSON><PERSON> under the hood)
make dev-setup

# Manual setup
make db-start
make db-migrate
make api-clients
```

### 2. Building Services

```bash
# Build all services
make build
# Equivalent to: bazel build //...

# Build specific service
make crm-backend
# Equivalent to: bazel build //services/orbit/crm_backend:crm_backend

# Build with verbose output
bazel build //services/orbit/crm_backend:crm_backend --verbose_failures
```

### 3. Running Tests

```bash
# All tests
make test
# Equivalent to: bazel test //...

# Database tests only
make db-test
# Equivalent to: bazel test //db/tests:database_test_suite

# Unit tests only
make test-unit
# Equivalent to: bazel test //... --test_tag_filters=-integration

# Integration tests only
make test-integration
# Equivalent to: bazel test //... --test_tag_filters=integration
```

### 4. Database Management

```bash
# Start database
make db-start
# Equivalent to: bazel run //db:start

# Run migrations
make db-migrate
# Equivalent to: bazel run //db:migrate

# Run database tests
make db-test
# Equivalent to: bazel test //db/tests:database_test_suite

# Setup test database
make db-setup-test
# Equivalent to: bazel run //db:setup_test_db
```

### 5. Running Services

```bash
# Run CRM backend
make run-crm-backend
# Equivalent to: bazel run //services/orbit/crm_backend:crm_backend

# Run with custom environment
bazel run //services/orbit/crm_backend:crm_backend --run_under="env DATABASE_URL=custom_url"
```

## Understanding Bazel Targets

### Target Format
```
//package:target
```

### Common Targets
```bash
# Services
//services/orbit/crm_backend:crm_backend           # CRM backend binary
//services/examples/go:example_go             # Go example service
//services/examples/python:example_python     # Python example service

# Libraries
//go_lib/utils:utils                # Go utilities library
//python_lib:python_lib             # Python utilities library

# Tests
//db/tests:migration_test           # Go migration tests
//db/tests:sql_integration_test     # SQL integration tests
//services/orbit/crm_backend:crm_backend_test      # CRM backend unit tests

# Database operations
//db:start                          # Start database
//db:migrate                        # Run migrations
//db:setup_test_db                  # Setup test database
```

## Advanced Bazel Usage

### 1. Selective Testing
```bash
# Run tests matching pattern
bazel test //... --test_name_filter="TestMigration.*"

# Run tests with specific tags
bazel test //... --test_tag_filters="integration"
bazel test //... --test_tag_filters="-integration"  # Exclude integration

# Run tests in specific package
bazel test //db/tests:all
```

### 2. Build Configuration
```bash
# Debug build
bazel build //services/orbit/crm_backend:crm_backend --compilation_mode=dbg

# Optimized build
bazel build //services/orbit/crm_backend:crm_backend --compilation_mode=opt

# Build with specific Go version
bazel build //services/orbit/crm_backend:crm_backend --@io_bazel_rules_go//go/toolchain:sdk_version=1.21.0
```

### 3. Parallel Execution
```bash
# Use specific number of parallel jobs
bazel build //... --jobs=4

# Show build progress
bazel build //... --show_progress
```

### 4. Cache Management
```bash
# Clean build cache
bazel clean

# Clean everything
bazel clean --expunge

# Show cache statistics
bazel info
```

## Debugging Builds

### 1. Verbose Output
```bash
# Show all build commands
bazel build //services/orbit/crm_backend:crm_backend --verbose_failures --sandbox_debug

# Show test output
bazel test //db/tests:migration_test --test_output=all
```

### 2. Dependency Analysis
```bash
# Show dependency graph
bazel query --output=graph //services/orbit/crm_backend:crm_backend

# Show reverse dependencies
bazel query "rdeps(//..., //go_lib/utils:utils)"

# Show what depends on a target
bazel query "deps(//services/orbit/crm_backend:crm_backend)"
```

### 3. Build Performance
```bash
# Profile build
bazel build //... --profile=profile.out

# Show build event log
bazel build //... --build_event_json_file=build_events.json
```

## Integration with IDEs

### VS Code
1. Install Bazel extension
2. Configure `.vscode/settings.json`:
```json
{
  "bazel.buildifierExecutable": "buildifier",
  "bazel.queriesShareServer": true
}
```

### GoLand/IntelliJ
1. Install Bazel plugin
2. Import project as Bazel project
3. Configure Go SDK path

## Best Practices

### 1. Target Naming
- Use descriptive target names
- Match target name to output binary name
- Group related targets in same BUILD file

### 2. Dependencies
- Keep dependencies minimal
- Use `//visibility:public` sparingly
- Prefer specific visibility lists

### 3. Testing
- Tag tests appropriately (unit, integration, manual)
- Use test data files properly
- Set appropriate timeouts

### 4. Build Files
- Keep BUILD files simple and readable
- Use variables for repeated values
- Document complex rules

## Troubleshooting

### Common Issues

1. **Build fails after dependency changes**
   ```bash
   bazel clean
   bazel build //...
   ```

2. **Test database connection issues**
   ```bash
   make db-cleanup-test
   make db-setup-test
   ```

3. **Go module issues**
   ```bash
   bazel run //:gazelle
   ```

4. **Stale cache issues**
   ```bash
   bazel clean --expunge
   ```

### Getting Help
```bash
# Show available targets
bazel query //...

# Show target info
bazel query --output=build //services/orbit/crm_backend:crm_backend

# Show available test targets
bazel query 'tests(//...)'

# Show make targets
make help
```

## Migration from Other Build Systems

### From Make
- Replace `make build` with `bazel build //...`
- Replace `make test` with `bazel test //...`
- Use our Make wrapper for convenience

### From Go CLI
- Replace `go build` with `bazel build //package:target`
- Replace `go test` with `bazel test //package:target_test`
- Dependencies managed through go.mod + Gazelle

### From npm/yarn
- Frontend builds still use npm (in services/orbit/web/)
- API client generation uses Bazel
- Integration tests use Bazel

## Performance Tips

1. **Use remote caching** (if available)
2. **Minimize dependency chains**
3. **Use appropriate visibility**
4. **Keep BUILD files focused**
5. **Use test tags effectively**

This Bazel-first approach ensures consistent, reproducible builds while maintaining developer productivity through the Make wrapper interface.