"""
Vector Database Service using Qdrant
Provides semantic search and knowledge management for AI agents
"""

import logging
import uuid
from typing import Dict, List, Optional, Any
from datetime import datetime

from qdrant_client import QdrantClient
from qdrant_client.http import models
from sentence_transformers import SentenceTransformer
import numpy as np

from config.settings import settings
from database.connection import get_db
from database.models import User, Agent

logger = logging.getLogger(__name__)


class VectorDatabase:
    """Vector database service for semantic search and knowledge management"""
    
    def __init__(self):
        self.client = None
        self.encoder = None
        self.embedding_size = settings.vector_db.embedding_dimension
        self.collection_prefix = settings.vector_db.collection_prefix
        
    async def initialize(self) -> None:
        """Initialize Qdrant client and embedding model"""
        try:
            # Initialize Qdrant client
            self.client = QdrantClient(
                host=settings.vector_db.qdrant_host,
                port=settings.vector_db.qdrant_port,
                api_key=settings.vector_db.qdrant_api_key,
                timeout=settings.vector_db.qdrant_timeout
            )
            
            # Initialize sentence transformer
            self.encoder = SentenceTransformer(settings.vector_db.embedding_model)
            
            # Create default collections
            await self._create_collections()
            
            logger.info("Vector database initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize vector database: {e}")
            raise
    
    async def _create_collections(self) -> None:
        """Create Qdrant collections for different data types"""
        collections = [
            {
                "name": f"{self.collection_prefix}agent_knowledge",
                "description": "Agent knowledge base and documentation"
            },
            {
                "name": f"{self.collection_prefix}conversation_history",
                "description": "Agent conversation history for context"
            },
            {
                "name": f"{self.collection_prefix}code_snippets",
                "description": "Code examples and templates"
            },
            {
                "name": f"{self.collection_prefix}system_logs",
                "description": "System logs and error patterns"
            }
        ]
        
        for collection in collections:
            try:
                # Check if collection exists
                collections_info = self.client.get_collections()
                existing_names = [c.name for c in collections_info.collections]
                
                if collection["name"] not in existing_names:
                    self.client.create_collection(
                        collection_name=collection["name"],
                        vectors_config=models.VectorParams(
                            size=self.embedding_size,
                            distance=models.Distance.COSINE
                        )
                    )
                    logger.info(f"Created collection: {collection['name']}")
                
            except Exception as e:
                logger.error(f"Failed to create collection {collection['name']}: {e}")
    
    def _generate_embedding(self, text: str) -> List[float]:
        """Generate embedding vector for text"""
        if not self.encoder:
            raise RuntimeError("Encoder not initialized")
        
        embedding = self.encoder.encode([text])[0]
        return embedding.tolist()
    
    async def store_agent_knowledge(
        self,
        agent_id: str,
        content: str,
        metadata: Optional[Dict[str, Any]] = None,
        category: str = "general"
    ) -> str:
        """Store knowledge for an agent"""
        try:
            collection_name = f"{self.collection_prefix}agent_knowledge"
            
            # Generate embedding
            embedding = self._generate_embedding(content)
            
            # Prepare payload
            payload = {
                "agent_id": agent_id,
                "content": content,
                "category": category,
                "created_at": datetime.utcnow().isoformat(),
                "metadata": metadata or {}
            }
            
            # Generate unique point ID
            point_id = str(uuid.uuid4())
            
            # Store in Qdrant
            self.client.upsert(
                collection_name=collection_name,
                points=[
                    models.PointStruct(
                        id=point_id,
                        vector=embedding,
                        payload=payload
                    )
                ]
            )
            
            logger.info(f"Stored knowledge for agent {agent_id}: {point_id}")
            return point_id
            
        except Exception as e:
            logger.error(f"Failed to store agent knowledge: {e}")
            raise
    
    async def search_agent_knowledge(
        self,
        query: str,
        agent_id: Optional[str] = None,
        category: Optional[str] = None,
        limit: int = 10,
        score_threshold: float = 0.7
    ) -> List[Dict[str, Any]]:
        """Search agent knowledge base"""
        try:
            collection_name = f"{self.collection_prefix}agent_knowledge"
            
            # Generate query embedding
            query_embedding = self._generate_embedding(query)
            
            # Build filter conditions
            filter_conditions = []
            
            if agent_id:
                filter_conditions.append(
                    models.FieldCondition(
                        key="agent_id",
                        match=models.MatchValue(value=agent_id)
                    )
                )
            
            if category:
                filter_conditions.append(
                    models.FieldCondition(
                        key="category",
                        match=models.MatchValue(value=category)
                    )
                )
            
            search_filter = None
            if filter_conditions:
                if len(filter_conditions) == 1:
                    search_filter = filter_conditions[0]
                else:
                    search_filter = models.Filter(
                        must=filter_conditions
                    )
            
            # Search
            results = self.client.search(
                collection_name=collection_name,
                query_vector=query_embedding,
                query_filter=search_filter,
                limit=limit,
                score_threshold=score_threshold
            )
            
            # Format results
            formatted_results = []
            for result in results:
                formatted_results.append({
                    "id": result.id,
                    "score": result.score,
                    "content": result.payload.get("content", ""),
                    "agent_id": result.payload.get("agent_id", ""),
                    "category": result.payload.get("category", ""),
                    "metadata": result.payload.get("metadata", {}),
                    "created_at": result.payload.get("created_at", "")
                })
            
            logger.info(f"Found {len(formatted_results)} knowledge results for query: {query[:50]}...")
            return formatted_results
            
        except Exception as e:
            logger.error(f"Failed to search agent knowledge: {e}")
            raise
    
    async def store_conversation_context(
        self,
        agent_id: str,
        user_id: str,
        conversation_id: str,
        message: str,
        role: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Store conversation context for future reference"""
        try:
            collection_name = f"{self.collection_prefix}conversation_history"
            
            # Generate embedding
            embedding = self._generate_embedding(message)
            
            # Prepare payload
            payload = {
                "agent_id": agent_id,
                "user_id": user_id,
                "conversation_id": conversation_id,
                "message": message,
                "role": role,  # 'user' or 'assistant'
                "created_at": datetime.utcnow().isoformat(),
                "metadata": metadata or {}
            }
            
            # Generate unique point ID
            point_id = str(uuid.uuid4())
            
            # Store in Qdrant
            self.client.upsert(
                collection_name=collection_name,
                points=[
                    models.PointStruct(
                        id=point_id,
                        vector=embedding,
                        payload=payload
                    )
                ]
            )
            
            return point_id
            
        except Exception as e:
            logger.error(f"Failed to store conversation context: {e}")
            raise
    
    async def get_conversation_context(
        self,
        agent_id: str,
        query: str,
        limit: int = 5,
        score_threshold: float = 0.6
    ) -> List[Dict[str, Any]]:
        """Get relevant conversation context for current query"""
        try:
            collection_name = f"{self.collection_prefix}conversation_history"
            
            # Generate query embedding
            query_embedding = self._generate_embedding(query)
            
            # Search with agent filter
            results = self.client.search(
                collection_name=collection_name,
                query_vector=query_embedding,
                query_filter=models.FieldCondition(
                    key="agent_id",
                    match=models.MatchValue(value=agent_id)
                ),
                limit=limit,
                score_threshold=score_threshold
            )
            
            # Format results
            formatted_results = []
            for result in results:
                formatted_results.append({
                    "id": result.id,
                    "score": result.score,
                    "message": result.payload.get("message", ""),
                    "role": result.payload.get("role", ""),
                    "conversation_id": result.payload.get("conversation_id", ""),
                    "created_at": result.payload.get("created_at", ""),
                    "metadata": result.payload.get("metadata", {})
                })
            
            return formatted_results
            
        except Exception as e:
            logger.error(f"Failed to get conversation context: {e}")
            raise
    
    async def store_code_snippet(
        self,
        title: str,
        code: str,
        language: str,
        description: str,
        tags: List[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Store code snippet for agent reference"""
        try:
            collection_name = f"{self.collection_prefix}code_snippets"
            
            # Create searchable text from title, description, and code
            searchable_text = f"{title}\n{description}\n{code}"
            embedding = self._generate_embedding(searchable_text)
            
            # Prepare payload
            payload = {
                "title": title,
                "code": code,
                "language": language,
                "description": description,
                "tags": tags or [],
                "created_at": datetime.utcnow().isoformat(),
                "metadata": metadata or {}
            }
            
            # Generate unique point ID
            point_id = str(uuid.uuid4())
            
            # Store in Qdrant
            self.client.upsert(
                collection_name=collection_name,
                points=[
                    models.PointStruct(
                        id=point_id,
                        vector=embedding,
                        payload=payload
                    )
                ]
            )
            
            logger.info(f"Stored code snippet: {title}")
            return point_id
            
        except Exception as e:
            logger.error(f"Failed to store code snippet: {e}")
            raise
    
    async def search_code_snippets(
        self,
        query: str,
        language: Optional[str] = None,
        tags: List[str] = None,
        limit: int = 10,
        score_threshold: float = 0.6
    ) -> List[Dict[str, Any]]:
        """Search code snippets"""
        try:
            collection_name = f"{self.collection_prefix}code_snippets"
            
            # Generate query embedding
            query_embedding = self._generate_embedding(query)
            
            # Build filter conditions
            filter_conditions = []
            
            if language:
                filter_conditions.append(
                    models.FieldCondition(
                        key="language",
                        match=models.MatchValue(value=language)
                    )
                )
            
            if tags:
                for tag in tags:
                    filter_conditions.append(
                        models.FieldCondition(
                            key="tags",
                            match=models.MatchAny(any=[tag])
                        )
                    )
            
            search_filter = None
            if filter_conditions:
                if len(filter_conditions) == 1:
                    search_filter = filter_conditions[0]
                else:
                    search_filter = models.Filter(
                        must=filter_conditions
                    )
            
            # Search
            results = self.client.search(
                collection_name=collection_name,
                query_vector=query_embedding,
                query_filter=search_filter,
                limit=limit,
                score_threshold=score_threshold
            )
            
            # Format results
            formatted_results = []
            for result in results:
                formatted_results.append({
                    "id": result.id,
                    "score": result.score,
                    "title": result.payload.get("title", ""),
                    "code": result.payload.get("code", ""),
                    "language": result.payload.get("language", ""),
                    "description": result.payload.get("description", ""),
                    "tags": result.payload.get("tags", []),
                    "created_at": result.payload.get("created_at", ""),
                    "metadata": result.payload.get("metadata", {})
                })
            
            return formatted_results
            
        except Exception as e:
            logger.error(f"Failed to search code snippets: {e}")
            raise
    
    async def delete_collection_data(self, collection_name: str, filter_conditions: Dict[str, Any]) -> bool:
        """Delete data from collection based on filter conditions"""
        try:
            full_collection_name = f"{self.collection_prefix}{collection_name}"
            
            # Build delete filter
            delete_filter = models.Filter(
                must=[
                    models.FieldCondition(
                        key=key,
                        match=models.MatchValue(value=value)
                    ) for key, value in filter_conditions.items()
                ]
            )
            
            # Delete points
            self.client.delete(
                collection_name=full_collection_name,
                points_selector=models.FilterSelector(filter=delete_filter)
            )
            
            logger.info(f"Deleted data from {full_collection_name} with filter: {filter_conditions}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete collection data: {e}")
            return False
    
    async def get_collection_info(self, collection_name: str) -> Dict[str, Any]:
        """Get information about a collection"""
        try:
            full_collection_name = f"{self.collection_prefix}{collection_name}"
            
            collection_info = self.client.get_collection(full_collection_name)
            
            return {
                "name": collection_info.config.name,
                "status": collection_info.status,
                "optimizer_status": collection_info.optimizer_status,
                "vectors_count": collection_info.vectors_count,
                "indexed_vectors_count": collection_info.indexed_vectors_count,
                "points_count": collection_info.points_count,
                "segments_count": collection_info.segments_count
            }
            
        except Exception as e:
            logger.error(f"Failed to get collection info: {e}")
            return {}
    
    async def health_check(self) -> Dict[str, Any]:
        """Check vector database health"""
        try:
            if not self.client:
                return {"status": "error", "message": "Client not initialized"}
            
            # Get collections info
            collections = self.client.get_collections()
            
            # Check encoder
            encoder_status = "ok" if self.encoder else "error"
            
            return {
                "status": "ok",
                "collections_count": len(collections.collections),
                "collections": [c.name for c in collections.collections],
                "encoder_status": encoder_status,
                "embedding_size": self.embedding_size
            }
            
        except Exception as e:
            logger.error(f"Vector database health check failed: {e}")
            return {"status": "error", "message": str(e)}


# Global vector database instance
vector_db = VectorDatabase()


async def get_vector_db() -> VectorDatabase:
    """Get vector database instance"""
    if not vector_db.client:
        await vector_db.initialize()
    return vector_db


# Utility functions for common operations
async def store_agent_context(
    agent_id: str,
    context_type: str,
    content: str,
    metadata: Optional[Dict[str, Any]] = None
) -> str:
    """Store context for an agent (knowledge, conversation, etc.)"""
    db = await get_vector_db()
    
    if context_type == "knowledge":
        return await db.store_agent_knowledge(agent_id, content, metadata)
    elif context_type == "conversation":
        # Extract required fields from metadata
        user_id = metadata.get("user_id", "unknown")
        conversation_id = metadata.get("conversation_id", str(uuid.uuid4()))
        role = metadata.get("role", "user")
        return await db.store_conversation_context(
            agent_id, user_id, conversation_id, content, role, metadata
        )
    else:
        raise ValueError(f"Unknown context type: {context_type}")


async def search_agent_context(
    query: str,
    agent_id: Optional[str] = None,
    context_type: str = "knowledge",
    limit: int = 10
) -> List[Dict[str, Any]]:
    """Search agent context"""
    db = await get_vector_db()
    
    if context_type == "knowledge":
        return await db.search_agent_knowledge(query, agent_id, limit=limit)
    elif context_type == "conversation":
        if not agent_id:
            raise ValueError("agent_id required for conversation context search")
        return await db.get_conversation_context(agent_id, query, limit=limit)
    else:
        raise ValueError(f"Unknown context type: {context_type}")