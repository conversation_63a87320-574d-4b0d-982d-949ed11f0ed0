package tests

import (
	"database/sql"
	"fmt"
	"testing"
	"time"

	_ "github.com/lib/pq"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

const (
	testDBURL = "postgres://postgres:testpassword@localhost:5433/crm_db_test?sslmode=disable"
)

func setupTestDB(t *testing.T) *sql.DB {
	db, err := sql.Open("postgres", testDBURL)
	require.NoError(t, err)
	require.NoError(t, db.Ping())
	return db
}

func TestMigration_TablesExist(t *testing.T) {
	db := setupTestDB(t)
	defer db.Close()

	tables := []string{
		"users",
		"user_profiles",
		"company_statuses",
		"companies",
		"contacts",
		"deal_stages",
		"deals",
		"interaction_types",
		"interactions",
		"arli_documents",
		"arli_content_blocks",
	}

	for _, table := range tables {
		t.Run(fmt.Sprintf("Table_%s_exists", table), func(t *testing.T) {
			var exists bool
			err := db.QueryRow(`
				SELECT EXISTS (
					SELECT FROM information_schema.tables 
					WHERE table_schema = 'public' 
					AND table_name = $1
				)`, table).Scan(&exists)
			
			require.NoError(t, err)
			assert.True(t, exists, "Table %s should exist", table)
		})
	}
}

func TestMigration_UserTableStructure(t *testing.T) {
	db := setupTestDB(t)
	defer db.Close()

	columns := map[string]string{
		"id":                 "uuid",
		"email":              "character varying",
		"username":           "character varying",
		"full_name":          "character varying",
		"password_hash":      "character varying",
		"email_confirmed_at": "timestamp with time zone",
		"is_deleted":         "boolean",
		"created_at":         "timestamp with time zone",
		"updated_at":         "timestamp with time zone",
	}

	for colName, expectedType := range columns {
		t.Run(fmt.Sprintf("users_%s_column", colName), func(t *testing.T) {
			var dataType string
			err := db.QueryRow(`
				SELECT data_type 
				FROM information_schema.columns 
				WHERE table_name = 'users' 
				AND column_name = $1`, colName).Scan(&dataType)
			
			require.NoError(t, err)
			assert.Equal(t, expectedType, dataType, "Column %s should be of type %s", colName, expectedType)
		})
	}
}

func TestMigration_CompanyTableStructure(t *testing.T) {
	db := setupTestDB(t)
	defer db.Close()

	columns := map[string]string{
		"id":                "uuid",
		"name":              "character varying",
		"website":           "text",
		"phone":             "character varying",
		"address":           "text",
		"notes":             "text",
		"company_status_id": "uuid",
		"is_deleted":        "boolean",
		"created_by":        "uuid",
		"created_at":        "timestamp with time zone",
		"updated_at":        "timestamp with time zone",
	}

	for colName, expectedType := range columns {
		t.Run(fmt.Sprintf("companies_%s_column", colName), func(t *testing.T) {
			var dataType string
			err := db.QueryRow(`
				SELECT data_type 
				FROM information_schema.columns 
				WHERE table_name = 'companies' 
				AND column_name = $1`, colName).Scan(&dataType)
			
			require.NoError(t, err)
			assert.Equal(t, expectedType, dataType, "Column %s should be of type %s", colName, expectedType)
		})
	}
}

func TestMigration_Indexes(t *testing.T) {
	db := setupTestDB(t)
	defer db.Close()

	indexes := []struct {
		table     string
		indexName string
	}{
		{"users", "idx_users_email"},
		{"companies", "idx_companies_name"},
		{"companies", "idx_companies_status"},
		{"companies", "idx_companies_deleted"},
		{"contacts", "idx_contacts_name"},
		{"contacts", "idx_contacts_email"},
		{"contacts", "idx_contacts_company"},
		{"deals", "idx_deals_title"},
		{"deals", "idx_deals_company"},
		{"deals", "idx_deals_stage"},
		{"interactions", "idx_interactions_company"},
		{"interactions", "idx_interactions_contact"},
		{"arli_documents", "idx_arli_documents_type"},
	}

	for _, idx := range indexes {
		t.Run(fmt.Sprintf("Index_%s", idx.indexName), func(t *testing.T) {
			var exists bool
			err := db.QueryRow(`
				SELECT EXISTS (
					SELECT 1 
					FROM pg_indexes 
					WHERE tablename = $1 
					AND indexname = $2
				)`, idx.table, idx.indexName).Scan(&exists)
			
			require.NoError(t, err)
			assert.True(t, exists, "Index %s on table %s should exist", idx.indexName, idx.table)
		})
	}
}

func TestMigration_ForeignKeys(t *testing.T) {
	db := setupTestDB(t)
	defer db.Close()

	foreignKeys := []struct {
		table          string
		column         string
		foreignTable   string
		foreignColumn  string
	}{
		{"user_profiles", "id", "users", "id"},
		{"companies", "company_status_id", "company_statuses", "id"},
		{"companies", "created_by", "users", "id"},
		{"contacts", "company_id", "companies", "id"},
		{"contacts", "created_by", "users", "id"},
		{"deals", "company_id", "companies", "id"},
		{"deals", "deal_stage_id", "deal_stages", "id"},
		{"deals", "created_by", "users", "id"},
		{"interactions", "interaction_type_id", "interaction_types", "id"},
		{"interactions", "company_id", "companies", "id"},
		{"interactions", "contact_id", "contacts", "id"},
		{"interactions", "created_by", "users", "id"},
		{"arli_documents", "created_by", "users", "id"},
		{"arli_content_blocks", "document_id", "arli_documents", "id"},
	}

	for _, fk := range foreignKeys {
		t.Run(fmt.Sprintf("FK_%s_%s", fk.table, fk.column), func(t *testing.T) {
			var count int
			err := db.QueryRow(`
				SELECT COUNT(*) 
				FROM information_schema.key_column_usage kcu
				JOIN information_schema.table_constraints tc 
					ON tc.constraint_name = kcu.constraint_name
				WHERE tc.constraint_type = 'FOREIGN KEY'
					AND kcu.table_name = $1
					AND kcu.column_name = $2`, fk.table, fk.column).Scan(&count)
			
			require.NoError(t, err)
			assert.Greater(t, count, 0, "Foreign key from %s.%s should exist", fk.table, fk.column)
		})
	}
}

func TestMigration_DefaultData(t *testing.T) {
	db := setupTestDB(t)
	defer db.Close()

	t.Run("CompanyStatuses", func(t *testing.T) {
		expectedStatuses := []struct {
			name  string
			order int
		}{
			{"Lead", 1},
			{"Prospect", 2},
			{"Customer", 3},
			{"Former Customer", 4},
		}

		for _, expected := range expectedStatuses {
			var count int
			err := db.QueryRow(`
				SELECT COUNT(*) 
				FROM company_statuses 
				WHERE name = $1 AND pipeline_order = $2`, 
				expected.name, expected.order).Scan(&count)
			
			require.NoError(t, err)
			assert.Equal(t, 1, count, "Company status '%s' with order %d should exist", expected.name, expected.order)
		}
	})

	t.Run("DealStages", func(t *testing.T) {
		expectedStages := []struct {
			name         string
			order        int
			isClosedWon  bool
			isClosedLost bool
		}{
			{"Prospecting", 1, false, false},
			{"Qualification", 2, false, false},
			{"Proposal", 3, false, false},
			{"Negotiation", 4, false, false},
			{"Closed Won", 5, true, false},
			{"Closed Lost", 6, false, true},
		}

		for _, expected := range expectedStages {
			var count int
			err := db.QueryRow(`
				SELECT COUNT(*) 
				FROM deal_stages 
				WHERE name = $1 
					AND pipeline_order = $2 
					AND is_closed_won = $3 
					AND is_closed_lost = $4`, 
				expected.name, expected.order, expected.isClosedWon, expected.isClosedLost).Scan(&count)
			
			require.NoError(t, err)
			assert.Equal(t, 1, count, "Deal stage '%s' should exist with correct properties", expected.name)
		}
	})

	t.Run("InteractionTypes", func(t *testing.T) {
		expectedTypes := []string{"Email", "Phone Call", "Meeting", "Note", "Task"}

		for _, typeName := range expectedTypes {
			var count int
			err := db.QueryRow(`
				SELECT COUNT(*) 
				FROM interaction_types 
				WHERE name = $1`, typeName).Scan(&count)
			
			require.NoError(t, err)
			assert.Equal(t, 1, count, "Interaction type '%s' should exist", typeName)
		}
	})
}

func TestMigration_Triggers(t *testing.T) {
	db := setupTestDB(t)
	defer db.Close()

	triggers := []struct {
		table   string
		trigger string
	}{
		{"users", "update_users_updated_at"},
		{"user_profiles", "update_user_profiles_updated_at"},
		{"companies", "update_companies_updated_at"},
		{"contacts", "update_contacts_updated_at"},
		{"deals", "update_deals_updated_at"},
		{"interactions", "update_interactions_updated_at"},
		{"arli_documents", "update_arli_documents_updated_at"},
		{"arli_content_blocks", "update_arli_content_blocks_updated_at"},
	}

	for _, trig := range triggers {
		t.Run(fmt.Sprintf("Trigger_%s", trig.trigger), func(t *testing.T) {
			var exists bool
			err := db.QueryRow(`
				SELECT EXISTS (
					SELECT 1 
					FROM information_schema.triggers 
					WHERE trigger_name = $1 
					AND event_object_table = $2
				)`, trig.trigger, trig.table).Scan(&exists)
			
			require.NoError(t, err)
			assert.True(t, exists, "Trigger %s on table %s should exist", trig.trigger, trig.table)
		})
	}
}

func TestMigration_UpdatedAtTriggerFunctionality(t *testing.T) {
	db := setupTestDB(t)
	defer db.Close()

	// Test that the updated_at trigger works
	t.Run("UpdatedAtTrigger", func(t *testing.T) {
		// Insert a test user
		var userID string
		err := db.QueryRow(`
			INSERT INTO users (email, username) 
			VALUES ('<EMAIL>', 'testuser') 
			RETURNING id`).Scan(&userID)
		require.NoError(t, err)

		// Get initial timestamps
		var createdAt, updatedAt1 time.Time
		err = db.QueryRow(`
			SELECT created_at, updated_at 
			FROM users 
			WHERE id = $1`, userID).Scan(&createdAt, &updatedAt1)
		require.NoError(t, err)

		// Wait a moment to ensure timestamp difference
		time.Sleep(10 * time.Millisecond)

		// Update the user
		_, err = db.Exec(`
			UPDATE users 
			SET full_name = 'Test User' 
			WHERE id = $1`, userID)
		require.NoError(t, err)

		// Get updated timestamp
		var updatedAt2 time.Time
		err = db.QueryRow(`
			SELECT updated_at 
			FROM users 
			WHERE id = $1`, userID).Scan(&updatedAt2)
		require.NoError(t, err)

		// Verify updated_at changed
		assert.True(t, updatedAt2.After(updatedAt1), "updated_at should be updated after UPDATE operation")
		assert.Equal(t, createdAt, createdAt, "created_at should not change")

		// Cleanup
		_, err = db.Exec("DELETE FROM users WHERE id = $1", userID)
		require.NoError(t, err)
	})
}

func TestMigration_Constraints(t *testing.T) {
	db := setupTestDB(t)
	defer db.Close()

	t.Run("UniqueConstraints", func(t *testing.T) {
		constraints := []struct {
			table  string
			column string
		}{
			{"users", "email"},
			{"users", "username"},
		}

		for _, c := range constraints {
			var constraintName string
			err := db.QueryRow(`
				SELECT constraint_name
				FROM information_schema.constraint_column_usage
				WHERE table_name = $1 
					AND column_name = $2
					AND constraint_name LIKE '%_key'`, c.table, c.column).Scan(&constraintName)
			
			require.NoError(t, err)
			assert.NotEmpty(t, constraintName, "Unique constraint on %s.%s should exist", c.table, c.column)
		}
	})

	t.Run("NotNullConstraints", func(t *testing.T) {
		constraints := []struct {
			table  string
			column string
		}{
			{"users", "email"},
			{"users", "username"},
			{"companies", "name"},
			{"contacts", "first_name"},
			{"contacts", "last_name"},
			{"contacts", "created_by"},
			{"deals", "title"},
			{"deals", "company_id"},
			{"deals", "deal_stage_id"},
			{"deals", "created_by"},
		}

		for _, c := range constraints {
			var isNullable string
			err := db.QueryRow(`
				SELECT is_nullable
				FROM information_schema.columns
				WHERE table_name = $1 
					AND column_name = $2`, c.table, c.column).Scan(&isNullable)
			
			require.NoError(t, err)
			assert.Equal(t, "NO", isNullable, "Column %s.%s should be NOT NULL", c.table, c.column)
		}
	})
}

func TestMigration_DefaultValues(t *testing.T) {
	db := setupTestDB(t)
	defer db.Close()

	defaults := []struct {
		table        string
		column       string
		defaultValue string
	}{
		{"users", "is_deleted", "false"},
		{"companies", "is_deleted", "false"},
		{"contacts", "is_deleted", "false"},
		{"deal_stages", "is_closed_won", "false"},
		{"deal_stages", "is_closed_lost", "false"},
	}

	for _, d := range defaults {
		t.Run(fmt.Sprintf("Default_%s_%s", d.table, d.column), func(t *testing.T) {
			var columnDefault sql.NullString
			err := db.QueryRow(`
				SELECT column_default
				FROM information_schema.columns
				WHERE table_name = $1 
					AND column_name = $2`, d.table, d.column).Scan(&columnDefault)
			
			require.NoError(t, err)
			assert.True(t, columnDefault.Valid, "Default value should be set for %s.%s", d.table, d.column)
			assert.Contains(t, columnDefault.String, d.defaultValue, "Default value for %s.%s should contain %s", d.table, d.column, d.defaultValue)
		})
	}
}