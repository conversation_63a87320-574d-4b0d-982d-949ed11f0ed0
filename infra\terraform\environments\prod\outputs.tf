# Production Environment Outputs

# Network outputs
output "vpc_name" {
  description = "Name of the VPC network"
  value       = module.network.vpc_name
}

output "vpc_id" {
  description = "ID of the VPC network"
  value       = module.network.vpc_id
}

# Database outputs
output "database_instance" {
  description = "Name of the database instance"
  value       = module.database.instance_name
}

output "database_ip" {
  description = "Private IP of the database instance"
  value       = module.database.instance_private_ip
  sensitive   = true
}

output "database_private_ip" {
  description = "Private IP address of the database"
  value       = module.database.instance_private_ip
}

output "database_name" {
  description = "Name of the application database"
  value       = module.database.database_name
}

output "database_user" {
  description = "Database username"
  value       = module.database.database_user
}

# Load balancer outputs
output "load_balancer_ip" {
  description = "External IP address of the load balancer"
  value       = module.loadbalancer.load_balancer_ip
}

# Storage outputs
output "web_bucket_name" {
  description = "Name of the web storage bucket"
  value       = module.storage.bucket_name
}

output "website_url" {
  description = "URL of the website"
  value       = module.storage.website_url
}

# Registry outputs
output "registry_repo" {
  description = "Name of the container registry repository"
  value       = module.registry.repository_name
}

# Compute outputs
output "instance_name" {
  description = "Name of the compute instance"
  value       = module.compute.instance_name
}

output "instance_external_ip" {
  description = "External IP of the compute instance"
  value       = module.compute.instance_external_ip
}

# Service endpoints
output "service_endpoints" {
  description = "Service endpoint URLs"
  value = {
    gateway     = "http://${module.compute.instance_external_ip}:8085"
    auth_service = "http://${module.compute.instance_external_ip}:8004" 
    crm_backend = "http://${module.compute.instance_external_ip}:8003"
  }
}

# SSH command
output "ssh_command" {
  description = "SSH command to connect to the instance"
  value       = "gcloud compute ssh ${module.compute.instance_name} --zone=${var.zone}"
}

# Website URLs
output "website_urls" {
  description = "Website URLs"
  value = {
    http  = "http://${module.loadbalancer.load_balancer_ip}"
    https = "https://${module.loadbalancer.load_balancer_ip}"
  }
}

# Domain configuration
output "ssl_domains" {
  description = "SSL domains configured"
  value       = var.ssl_domains
}

# Environment info
output "environment_info" {
  description = "Production environment information"
  value = {
    environment = "prod"
    region      = var.region
    zone        = var.zone
    project_id  = var.project_id
  }
}

# Production access info (consistent with dev_access_info)
output "prod_access_info" {
  description = "Information for production access"
  value = {
    environment = "prod"
    project_id  = var.project_id
    region      = var.region
    zone        = var.zone
    
    # Quick access URLs
    website = "https://${module.loadbalancer.load_balancer_ip}"
    api_base = "https://${module.loadbalancer.load_balancer_ip}/api"
    
    # Production tools
    ssh_access = "gcloud compute ssh ${module.compute.instance_name} --zone=${var.zone}"
    
    # Database access
    database_connection = "postgresql://${module.database.database_user}@${module.database.instance_private_ip}:5432/${module.database.database_name}"
    database_secret = "gcloud secrets versions access latest --secret=platform-db-password --project=${var.project_id}"
    
    # Useful commands
    commands = {
      ssh_instance = "gcloud compute ssh ${module.compute.instance_name} --zone=${var.zone}"
      view_logs = "gcloud logging read 'resource.type=gce_instance' --limit=50 --format=json --project=${var.project_id}"
      restart_services = "gcloud compute ssh ${module.compute.instance_name} --zone=${var.zone} --command='sudo systemctl restart platform-services'"
      update_images = "gcloud compute ssh ${module.compute.instance_name} --zone=${var.zone} --command='sudo /opt/platform/update-services.sh'"
    }
  }
}

# Security info
output "security_features" {
  description = "Enabled security features"
  value = {
    deletion_protection    = "enabled"
    ssl_certificates      = "managed"
    backup_automation     = "enabled"
    migration_automation  = "enabled"
    high_availability     = "enabled"
  }
}

# Cost information
output "estimated_monthly_cost" {
  description = "Estimated monthly cost information"
  value       = "Production environment with high availability - see GCP pricing calculator"
}