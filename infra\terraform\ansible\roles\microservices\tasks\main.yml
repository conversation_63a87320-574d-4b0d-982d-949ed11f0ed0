---
- name: Create microservices directories
  file:
    path: "{{ item }}"
    state: directory
    mode: '0755'
  loop:
    - "{{ microservices_root }}"
    - "{{ microservices_root }}/env"
    - "{{ microservices_root }}/configs"
    - /var/lib/microservices
  tags: ['microservices', 'directories']

- name: Create Docker network
  docker_network:
    name: "{{ docker_network_name | default('microservices') }}"
    driver: bridge
    ipam_config:
      - subnet: "{{ docker_network_subnet | default('**********/16') }}"
  tags: ['microservices', 'network']

- name: Retrieve secrets from GCP Secret Manager
  include_tasks: fetch-secrets.yml
  when: gcp_project_id is defined
  tags: ['microservices', 'secrets']

- name: Generate service environment files
  template:
    src: "service-env.j2"
    dest: "{{ microservices_root }}/env/{{ item.key }}.env"
    mode: '0600'
  loop: "{{ services | dict2items }}"
  no_log: true
  tags: ['microservices', 'config']

- name: Deploy Docker Compose configuration
  template:
    src: docker-compose.yml.j2
    dest: "{{ microservices_root }}/docker-compose.yml"
    mode: '0644'
    backup: yes
  tags: ['microservices', 'compose']

- name: Validate Docker Compose configuration
  community.docker.docker_compose_v2:
    project_src: "{{ microservices_root }}"
    state: present
    pull: no
    services: []
  check_mode: yes
  register: compose_validation
  tags: ['microservices', 'validate']

- name: Pull service images
  docker_image:
    name: "{{ item.value.image }}"
    source: pull
    force_source: "{{ force_pull | default(false) }}"
  loop: "{{ services | dict2items }}"
  register: image_pull_results
  tags: ['microservices', 'images']

- name: Deploy services
  community.docker.docker_compose_v2:
    project_src: "{{ microservices_root }}"
    project_name: "microservices{% if deployment_strategy == 'blue-green' %}-{{ deployment_color }}{% endif %}"
    state: present
    remove_orphans: yes
  register: deployment_result
  tags: ['microservices', 'deploy']

- name: Wait for services to be healthy
  include_tasks: health-check.yml
  loop: "{{ services | dict2items }}"
  when: item.value.health_check is defined
  tags: ['microservices', 'health']

- name: Set up service monitoring
  include_tasks: monitoring.yml
  when: enable_monitoring | default(true)
  tags: ['microservices', 'monitoring']