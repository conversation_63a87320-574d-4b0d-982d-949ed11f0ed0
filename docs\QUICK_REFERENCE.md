# Quick Reference Card

## System Requirements

✅ **Bazel**: 8.3.1 (via Bazelisk 1.26.0)
✅ **Node.js**: 24.3.0+ with npm 11.4.2+
✅ **Python**: 3.12+ (for Python services)
✅ **Go**: 1.21+ (for Go services)
✅ **Docker**: Latest (for containerization)

## Quick Commands

### Build & Run
```bash
# Build everything
bazel build //...

# Run CRM web app
bazel run //services/orbit/web:dev

# Run CRM backend
bazel run //services/orbit/crm_backend:crm_backend

# Run all tests
bazel test //...
```

### API Client Generation
```bash
# Generate TypeScript client
./services/orbit/rest-api/generate-simple-client.sh

# Generate all clients
./services/orbit/scripts/generate-all-clients.sh

# Build CRM web with fresh client
cd services/orbit/web && npm run build:with-client
```

### Development
```bash
# Start development environment
make dev-setup

# Run specific service
make crm-backend
make run-crm-backend

# Database operations
make db-start
make db-migrate
make db-test
```

## Key Technologies

- **Frontend**: React 18.3.1 + TypeScript 5.5.3 + Vite 5.4.1
- **Backend**: Go 1.21 + Python 3.12 + FastAPI + Gin
- **Database**: PostgreSQL 17
- **Build**: Bazel 8.3.1 (Bzlmod)
- **API**: OpenAPI 3.0 with generated clients

## File Structure

```
mono/
├── services/orbit/web/      # React TypeScript app
├── services/orbit/crm_backend/       # Go REST API server
├── services/orbit/rest-api/          # OpenAPI specifications
├── generated/       # Generated API clients
├── services/orbit/db/               # Database schema & migrations
├── services/examples/*/        # Example services
└── tools/            # Build tools
```

## Common Issues

1. **Bazel build fails**: Check `.bazelrc` has `--enable_bzlmod`
2. **Node.js version**: Use Node 24.3.0+ for CRM web app
3. **API client errors**: Regenerate with `./services/orbit/rest-api/generate-simple-client.sh`
4. **Database issues**: Run `make db-start` then `make db-migrate`

## URLs

- **CRM Web App**: http://localhost:8080/
- **CRM Backend**: http://localhost:8000/
- **Database**: postgresql://localhost:5432/crm_db

## Documentation

- **Main README**: `README.md`
- **Claude Instructions**: `CLAUDE.md`
- **Version Info**: `VERSION_INFO.md`
- **API Docs**: `services/orbit/rest-api/README.md`
- **Web App API**: `services/orbit/web/src/api-client/README.md`