"""
AI Service Integration Layer
Provides unified interface for various AI/ML services and models
"""

import asyncio
import json
import logging
from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Union
from enum import Enum
import aiohttp
import openai
from pydantic import BaseModel, Field

from config.settings import settings

logger = logging.getLogger(__name__)


class AIProvider(str, Enum):
    """Supported AI providers"""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GOOGLE = "google"
    AZURE = "azure"
    HUGGINGFACE = "huggingface"
    LOCAL = "local"


class TaskType(str, Enum):
    """AI task types"""
    TEXT_COMPLETION = "text_completion"
    CHAT = "chat"
    CODE_GENERATION = "code_generation"
    SUMMARIZATION = "summarization"
    TRANSLATION = "translation"
    CLASSIFICATION = "classification"
    EXTRACTION = "extraction"
    EMBEDDINGS = "embeddings"
    IMAGE_ANALYSIS = "image_analysis"
    SPEECH_TO_TEXT = "speech_to_text"
    TEXT_TO_SPEECH = "text_to_speech"


@dataclass
class AIRequest:
    """AI service request structure"""
    task_type: TaskType
    provider: AIProvider
    model: str
    input_data: Dict[str, Any]
    parameters: Dict[str, Any] = None
    context: Dict[str, Any] = None
    timeout: int = 300


@dataclass
class AIResponse:
    """AI service response structure"""
    task_id: str
    provider: AIProvider
    model: str
    result: Any
    metadata: Dict[str, Any]
    usage: Dict[str, Any] = None
    execution_time: float = 0
    success: bool = True
    error: Optional[str] = None


class AIProviderInterface(ABC):
    """Abstract interface for AI providers"""
    
    @abstractmethod
    async def initialize(self) -> bool:
        """Initialize the provider"""
        pass
    
    @abstractmethod
    async def process_request(self, request: AIRequest) -> AIResponse:
        """Process an AI request"""
        pass
    
    @abstractmethod
    async def get_models(self) -> List[str]:
        """Get available models"""
        pass
    
    @abstractmethod
    async def health_check(self) -> bool:
        """Check provider health"""
        pass


class OpenAIProvider(AIProviderInterface):
    """OpenAI API provider"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.client = None
    
    async def initialize(self) -> bool:
        """Initialize OpenAI client"""
        try:
            self.client = openai.AsyncOpenAI(api_key=self.api_key)
            return True
        except Exception as e:
            logger.error(f"Failed to initialize OpenAI provider: {e}")
            return False
    
    async def process_request(self, request: AIRequest) -> AIResponse:
        """Process OpenAI request"""
        import time
        start_time = time.time()
        
        try:
            if request.task_type == TaskType.CHAT:
                result = await self._handle_chat(request)
            elif request.task_type == TaskType.TEXT_COMPLETION:
                result = await self._handle_completion(request)
            elif request.task_type == TaskType.EMBEDDINGS:
                result = await self._handle_embeddings(request)
            elif request.task_type == TaskType.CODE_GENERATION:
                result = await self._handle_code_generation(request)
            else:
                raise ValueError(f"Unsupported task type: {request.task_type}")
            
            return AIResponse(
                task_id=request.context.get("task_id", "unknown"),
                provider=AIProvider.OPENAI,
                model=request.model,
                result=result,
                metadata={"provider": "openai", "model": request.model},
                execution_time=time.time() - start_time,
                success=True
            )
        
        except Exception as e:
            logger.error(f"OpenAI request failed: {e}")
            return AIResponse(
                task_id=request.context.get("task_id", "unknown"),
                provider=AIProvider.OPENAI,
                model=request.model,
                result=None,
                metadata={"provider": "openai", "model": request.model},
                execution_time=time.time() - start_time,
                success=False,
                error=str(e)
            )
    
    async def _handle_chat(self, request: AIRequest) -> Dict[str, Any]:
        """Handle chat completion"""
        messages = request.input_data.get("messages", [])
        parameters = request.parameters or {}
        
        response = await self.client.chat.completions.create(
            model=request.model,
            messages=messages,
            temperature=parameters.get("temperature", 0.7),
            max_tokens=parameters.get("max_tokens", 1000),
            top_p=parameters.get("top_p", 1.0),
            frequency_penalty=parameters.get("frequency_penalty", 0.0),
            presence_penalty=parameters.get("presence_penalty", 0.0),
        )
        
        return {
            "content": response.choices[0].message.content,
            "role": response.choices[0].message.role,
            "finish_reason": response.choices[0].finish_reason,
            "usage": {
                "prompt_tokens": response.usage.prompt_tokens,
                "completion_tokens": response.usage.completion_tokens,
                "total_tokens": response.usage.total_tokens
            }
        }
    
    async def _handle_completion(self, request: AIRequest) -> Dict[str, Any]:
        """Handle text completion"""
        prompt = request.input_data.get("prompt", "")
        parameters = request.parameters or {}
        
        # Convert to chat format for newer models
        messages = [{"role": "user", "content": prompt}]
        
        response = await self.client.chat.completions.create(
            model=request.model,
            messages=messages,
            temperature=parameters.get("temperature", 0.7),
            max_tokens=parameters.get("max_tokens", 1000),
        )
        
        return {
            "text": response.choices[0].message.content,
            "finish_reason": response.choices[0].finish_reason,
            "usage": {
                "prompt_tokens": response.usage.prompt_tokens,
                "completion_tokens": response.usage.completion_tokens,
                "total_tokens": response.usage.total_tokens
            }
        }
    
    async def _handle_embeddings(self, request: AIRequest) -> Dict[str, Any]:
        """Handle embeddings generation"""
        text = request.input_data.get("text", "")
        
        response = await self.client.embeddings.create(
            model=request.model,
            input=text
        )
        
        return {
            "embeddings": response.data[0].embedding,
            "model": response.model,
            "usage": {
                "prompt_tokens": response.usage.prompt_tokens,
                "total_tokens": response.usage.total_tokens
            }
        }
    
    async def _handle_code_generation(self, request: AIRequest) -> Dict[str, Any]:
        """Handle code generation"""
        prompt = request.input_data.get("prompt", "")
        language = request.input_data.get("language", "python")
        
        messages = [
            {"role": "system", "content": f"You are a helpful code assistant. Generate {language} code based on the user's request."},
            {"role": "user", "content": prompt}
        ]
        
        response = await self.client.chat.completions.create(
            model=request.model,
            messages=messages,
            temperature=0.2,  # Lower temperature for code generation
            max_tokens=request.parameters.get("max_tokens", 2000),
        )
        
        return {
            "code": response.choices[0].message.content,
            "language": language,
            "usage": {
                "prompt_tokens": response.usage.prompt_tokens,
                "completion_tokens": response.usage.completion_tokens,
                "total_tokens": response.usage.total_tokens
            }
        }
    
    async def get_models(self) -> List[str]:
        """Get available OpenAI models"""
        try:
            models = await self.client.models.list()
            return [model.id for model in models.data]
        except Exception as e:
            logger.error(f"Failed to fetch OpenAI models: {e}")
            return ["gpt-3.5-turbo", "gpt-4", "gpt-4-turbo-preview"]  # Fallback list
    
    async def health_check(self) -> bool:
        """Check OpenAI API health"""
        try:
            await self.client.models.list()
            return True
        except Exception:
            return False


class AnthropicProvider(AIProviderInterface):
    """Anthropic Claude API provider"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.anthropic.com/v1"
    
    async def initialize(self) -> bool:
        """Initialize Anthropic provider"""
        return True  # No special initialization needed
    
    async def process_request(self, request: AIRequest) -> AIResponse:
        """Process Anthropic request"""
        import time
        start_time = time.time()
        
        try:
            if request.task_type == TaskType.CHAT:
                result = await self._handle_chat(request)
            elif request.task_type == TaskType.TEXT_COMPLETION:
                result = await self._handle_completion(request)
            else:
                raise ValueError(f"Unsupported task type: {request.task_type}")
            
            return AIResponse(
                task_id=request.context.get("task_id", "unknown"),
                provider=AIProvider.ANTHROPIC,
                model=request.model,
                result=result,
                metadata={"provider": "anthropic", "model": request.model},
                execution_time=time.time() - start_time,
                success=True
            )
        
        except Exception as e:
            logger.error(f"Anthropic request failed: {e}")
            return AIResponse(
                task_id=request.context.get("task_id", "unknown"),
                provider=AIProvider.ANTHROPIC,
                model=request.model,
                result=None,
                metadata={"provider": "anthropic", "model": request.model},
                execution_time=time.time() - start_time,
                success=False,
                error=str(e)
            )
    
    async def _handle_chat(self, request: AIRequest) -> Dict[str, Any]:
        """Handle Claude chat"""
        messages = request.input_data.get("messages", [])
        parameters = request.parameters or {}
        
        # Convert OpenAI format to Anthropic format
        prompt = self._convert_messages_to_prompt(messages)
        
        headers = {
            "x-api-key": self.api_key,
            "anthropic-version": "2023-06-01",
            "content-type": "application/json"
        }
        
        data = {
            "model": request.model,
            "prompt": prompt,
            "max_tokens": parameters.get("max_tokens", 1000),
            "temperature": parameters.get("temperature", 0.7),
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.base_url}/complete",
                headers=headers,
                json=data
            ) as response:
                result = await response.json()
                
                if response.status != 200:
                    raise Exception(f"Anthropic API error: {result.get('error', 'Unknown error')}")
                
                return {
                    "content": result.get("completion", "").strip(),
                    "role": "assistant",
                    "finish_reason": "stop",
                    "usage": {
                        "prompt_tokens": result.get("usage", {}).get("input_tokens", 0),
                        "completion_tokens": result.get("usage", {}).get("output_tokens", 0),
                        "total_tokens": result.get("usage", {}).get("input_tokens", 0) + result.get("usage", {}).get("output_tokens", 0)
                    }
                }
    
    async def _handle_completion(self, request: AIRequest) -> Dict[str, Any]:
        """Handle text completion"""
        return await self._handle_chat(request)
    
    def _convert_messages_to_prompt(self, messages: List[Dict[str, str]]) -> str:
        """Convert OpenAI messages format to Anthropic prompt format"""
        prompt = ""
        for message in messages:
            role = message.get("role", "user")
            content = message.get("content", "")
            
            if role == "system":
                prompt += f"System: {content}\n\n"
            elif role == "user":
                prompt += f"Human: {content}\n\n"
            elif role == "assistant":
                prompt += f"Assistant: {content}\n\n"
        
        prompt += "Assistant: "
        return prompt
    
    async def get_models(self) -> List[str]:
        """Get available Anthropic models"""
        return ["claude-3-sonnet-20240229", "claude-3-opus-20240229", "claude-2.1", "claude-2.0"]
    
    async def health_check(self) -> bool:
        """Check Anthropic API health"""
        try:
            headers = {
                "x-api-key": self.api_key,
                "anthropic-version": "2023-06-01",
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.base_url}/models", headers=headers) as response:
                    return response.status == 200
        except Exception:
            return False


class AIIntegrationService:
    """Main AI integration service"""
    
    def __init__(self):
        self.providers: Dict[AIProvider, AIProviderInterface] = {}
        self.model_mapping: Dict[str, AIProvider] = {}
        self.initialized = False
    
    async def initialize(self):
        """Initialize all configured AI providers"""
        try:
            # Initialize OpenAI if configured
            if hasattr(settings, 'openai') and settings.openai.api_key:
                openai_provider = OpenAIProvider(settings.openai.api_key)
                if await openai_provider.initialize():
                    self.providers[AIProvider.OPENAI] = openai_provider
                    # Map OpenAI models
                    openai_models = await openai_provider.get_models()
                    for model in openai_models:
                        self.model_mapping[model] = AIProvider.OPENAI
            
            # Initialize Anthropic if configured
            if hasattr(settings, 'anthropic') and settings.anthropic.api_key:
                anthropic_provider = AnthropicProvider(settings.anthropic.api_key)
                if await anthropic_provider.initialize():
                    self.providers[AIProvider.ANTHROPIC] = anthropic_provider
                    # Map Anthropic models
                    anthropic_models = await anthropic_provider.get_models()
                    for model in anthropic_models:
                        self.model_mapping[model] = AIProvider.ANTHROPIC
            
            self.initialized = True
            logger.info(f"AI Integration Service initialized with providers: {list(self.providers.keys())}")
            
        except Exception as e:
            logger.error(f"Failed to initialize AI Integration Service: {e}")
            raise
    
    async def process_ai_request(self, request: AIRequest) -> AIResponse:
        """Process an AI request through appropriate provider"""
        if not self.initialized:
            await self.initialize()
        
        # Determine provider from model or use specified provider
        if request.provider == AIProvider.LOCAL:
            provider = self.providers.get(AIProvider.LOCAL)
        else:
            provider_type = self.model_mapping.get(request.model, request.provider)
            provider = self.providers.get(provider_type)
        
        if not provider:
            available_providers = list(self.providers.keys())
            return AIResponse(
                task_id=request.context.get("task_id", "unknown"),
                provider=request.provider,
                model=request.model,
                result=None,
                metadata={"available_providers": available_providers},
                success=False,
                error=f"Provider {request.provider} not available. Available: {available_providers}"
            )
        
        return await provider.process_request(request)
    
    async def get_available_models(self) -> Dict[str, List[str]]:
        """Get all available models by provider"""
        if not self.initialized:
            await self.initialize()
        
        models = {}
        for provider_type, provider in self.providers.items():
            try:
                provider_models = await provider.get_models()
                models[provider_type.value] = provider_models
            except Exception as e:
                logger.error(f"Failed to get models from {provider_type}: {e}")
                models[provider_type.value] = []
        
        return models
    
    async def health_check(self) -> Dict[str, bool]:
        """Check health of all providers"""
        if not self.initialized:
            await self.initialize()
        
        health_status = {}
        for provider_type, provider in self.providers.items():
            try:
                health_status[provider_type.value] = await provider.health_check()
            except Exception as e:
                logger.error(f"Health check failed for {provider_type}: {e}")
                health_status[provider_type.value] = False
        
        return health_status
    
    async def get_optimal_provider(self, task_type: TaskType, requirements: Dict[str, Any] = None) -> Optional[AIProvider]:
        """Get optimal provider for a task type based on requirements"""
        requirements = requirements or {}
        
        # Simple provider selection logic - can be enhanced with more sophisticated routing
        if task_type == TaskType.CODE_GENERATION:
            # Prefer OpenAI for code generation
            if AIProvider.OPENAI in self.providers:
                return AIProvider.OPENAI
        elif task_type == TaskType.CHAT:
            # Consider response time requirements
            if requirements.get("low_latency", False):
                if AIProvider.OPENAI in self.providers:
                    return AIProvider.OPENAI
            else:
                if AIProvider.ANTHROPIC in self.providers:
                    return AIProvider.ANTHROPIC
        elif task_type == TaskType.EMBEDDINGS:
            # Only OpenAI supports embeddings in this implementation
            if AIProvider.OPENAI in self.providers:
                return AIProvider.OPENAI
        
        # Default to first available provider
        return next(iter(self.providers.keys())) if self.providers else None


# Global instance
ai_integration_service = AIIntegrationService()