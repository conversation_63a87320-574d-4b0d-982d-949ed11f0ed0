load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "database",
    srcs = [
        "db.go",
        "migrations.go",
    ],
    importpath = "github.com/TwoDotAi/mono/services/orbit/crm_backend/database",
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_golang_migrate_migrate_v4//:migrate",
        "@com_github_golang_migrate_migrate_v4//database/postgres",
        "@com_github_golang_migrate_migrate_v4//source/file",
        "@com_github_lib_pq//:pq",
    ],
)
