---
- name: Update Service Images
  hosts: microservices
  become: yes
  gather_facts: yes
  
  vars:
    environment: "{{ deployment_environment | default('dev') }}"
    deployment_color: "{{ 'green' if current_color | default('blue') == 'blue' else 'blue' }}"
    
  pre_tasks:
    - name: Load environment-specific variables
      include_vars: "../vars/{{ environment }}.yml"
      
    - name: Record pre-update state
      docker_container_info:
        name: "{{ item.key }}"
      loop: "{{ services | dict2items }}"
      register: pre_update_containers
      tags: ['audit']
      
  tasks:
    - name: Pull new container images
      docker_image:
        name: "{{ item.value.image }}"
        source: pull
        force_source: yes
      loop: "{{ services | dict2items }}"
      register: image_pull_results
      tags: ['pull']
      
    - name: Deploy services with blue-green strategy
      include_role:
        name: ../roles/microservices
        tasks_from: blue-green-deploy
      vars:
        target_color: "{{ deployment_color }}"
      tags: ['deploy']
      
    - name: Health check new deployment
      uri:
        url: "http://localhost:{{ item.value.port }}{{ item.value.health_check.path }}"
        method: GET
        status_code: 200
      retries: 30
      delay: 10
      loop: "{{ services | dict2items }}"
      when: item.value.health_check is defined
      tags: ['health-check']
      
    - name: Switch traffic to new deployment
      include_role:
        name: ../roles/nginx
        tasks_from: switch-traffic
      vars:
        target_color: "{{ deployment_color }}"
      tags: ['switch']
      
    - name: Clean up old deployment
      community.docker.docker_compose_v2:
        project_name: "microservices-{{ current_color }}"
        state: absent
      when: current_color is defined
      tags: ['cleanup']
      
  post_tasks:
    - name: Update deployment metadata
      copy:
        content: |
          deployment_time: {{ ansible_date_time.iso8601 }}
          deployment_color: {{ deployment_color }}
          previous_color: {{ current_color | default('none') }}
          services:
          {% for service, config in services.items() %}
            {{ service }}: 
              image: {{ config.image }}
              {% if service in image_pull_results.results %}
              updated: {{ image_pull_results.results[loop.index0].changed }}
              {% endif %}
          {% endfor %}
        dest: /var/lib/microservices/current-deployment.yml
      tags: ['metadata']