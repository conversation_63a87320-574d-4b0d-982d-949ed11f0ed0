#!/bin/bash
# Quick setup for DEV environment
# Usage: source setup.sh (must be sourced to set environment variables)

echo "🎯 Setting up DEV environment..."

# Source the main environment setup script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"

if [[ -f "$PROJECT_ROOT/terraform/ansible/setup-env.sh" ]]; then
    source "$PROJECT_ROOT/terraform/ansible/setup-env.sh"
else
    echo "❌ Could not find setup-env.sh script"
    return 1
fi

# Dev-specific overrides
export ENVIRONMENT="dev"
export GCP_PROJECT_ID="${GCP_PROJECT_ID:-crm-platform-dev}"
export GCP_REGION="us-central1"
export GCP_ZONE="us-central1-a"

# Dev-specific settings
export LOG_LEVEL="debug"
export ENABLE_DEBUG="true"
export ENABLE_SWAGGER="true"
export CORS_ALLOWED_ORIGINS="http://localhost:8080,http://localhost:3000"

echo ""
echo "🚀 DEV environment ready!"
echo "💡 Quick commands:"
echo "   terraform init"
echo "   terraform plan"
echo "   terraform apply"
echo "   ansible-test"
echo ""