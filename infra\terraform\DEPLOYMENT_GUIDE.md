# Platform Deployment Guide - Australia 🇦🇺

This guide walks you through deploying your platform infrastructure to Google Cloud Platform using Terraform, configured for **Australia Southeast 1** region with project **agent-dev-459718**.

## 🏗️ Infrastructure Overview

Your platform has been configured with the following architecture:

```
Internet → Load Balancer → Cloud CDN (Static Files)
                       → Compute Engine (Docker Compose)
                           ├── Gateway :8085
                           ├── Auth Service :8004
                           └── CRM Backend :8003
                               └── Cloud SQL PostgreSQL
```

### Components Created

1. **VPC Network** - Secure networking with public/private subnets
2. **Cloud SQL PostgreSQL** - Managed database with automated backups
3. **Artifact Registry** - Docker image storage with automated builds
4. **Compute Engine** - VM running Docker Compose services
5. **Cloud Storage** - Static web file hosting with CDN
6. **Load Balancer** - HTTPS termination and traffic routing
7. **Security** - Cloud Armor, SSL certificates, and IAM roles

## 🚀 Quick Start for Australia

### Prerequisites

1. **GCP Project**: Using `agent-dev-459718` (already configured)
2. **Region**: Australia Southeast 1 (`australia-southeast1`)
3. **Terraform**: Install Terraform 1.5+
4. **Authentication**: 
   ```bash
   gcloud auth login
   gcloud auth application-default login
   gcloud config set project agent-dev-459718
   ```

### Automated Setup

Use the provided initialization script:

```bash
cd terraform
./init-australia.sh
```

This script will:
- ✅ Set up your GCP project (agent-dev-459718)
- ✅ Enable all required APIs
- ✅ Create Terraform state bucket in Australia
- ✅ Create initial terraform.tfvars

### Environment Setup

Choose your environment and follow the steps:

#### Development Environment (Recommended First)

```bash
cd terraform/environments/dev
cp terraform.tfvars.example terraform.tfvars
# Edit terraform.tfvars with your values

terraform init
terraform plan
terraform apply
```

#### Staging Environment

```bash
cd terraform/environments/staging
cp terraform.tfvars.example terraform.tfvars
# Edit terraform.tfvars with your values

terraform init
terraform plan
terraform apply
```

#### Production Environment

```bash
cd terraform/environments/prod
cp terraform.tfvars.example terraform.tfvars
# Edit terraform.tfvars with your values

terraform init
terraform plan
terraform apply
```

## 📋 Configuration Checklist

### Required Variables

For each environment, you must configure:

- `project_id` - `agent-dev-459718` (already set)
- `github_repo_owner` - Your GitHub username/organization
- `github_repo_name` - Your repository name (probably `mono`)
- `region` - `australia-southeast1` (already set)
- `zone` - `australia-southeast1-a` (already set)

### Production-Specific Requirements

- `ssl_domains` - Your production domains
- `admin_ip_ranges` - Restricted IP ranges for admin access
- `notification_email` - Email for alerts

## 🐳 Docker Image Deployment

### Build Images with Bazel

```bash
# Build all Docker images
bazel build //platform/auth:auth_service_image
bazel build //platform/crm_backend:crm_backend_image
bazel build //platform/gateway:gateway_image
bazel build //platform/web:web_image

# Tag and push to Artifact Registry
docker tag bazel/platform/auth:auth_service_image \
  ${REGISTRY_URL}/auth-service:latest
docker push ${REGISTRY_URL}/auth-service:latest

# Repeat for other services...
```

### Automated Builds

Set up GitHub Actions or Cloud Build for automated deployment:

1. **Cloud Build Triggers** are automatically created
2. **Push to main branch** triggers builds
3. **Images are automatically pushed** to Artifact Registry

## 🗄️ Database Management

### Initial Setup

1. **Migrations run automatically** on first deployment
2. **Database is created** with proper schema
3. **User and permissions** are configured

### Manual Migration

```bash
# Trigger manual migration
gcloud builds triggers run platform-db-migration
```

### Access Database

```bash
# Connect via Cloud SQL Proxy
gcloud sql connect platform-postgres-dev --user=platform_user
```

## 🌐 DNS Configuration

### Development
- Use the provided load balancer IP directly
- Example: `http://*************`

### Production
1. Point your domain A records to the load balancer IP
2. SSL certificates will be automatically provisioned
3. Example DNS records:
   ```
   yourdomain.com.     A    *************
   www.yourdomain.com. A    *************
   api.yourdomain.com. A    *************
   ```

## 📊 Monitoring & Operations

### Health Checks

- **Load Balancer**: `https://yourdomain.com/health`
- **Direct Gateway**: `http://INSTANCE_IP:8085/health`
- **Auth Service**: `http://INSTANCE_IP:8004/health`
- **CRM Backend**: `http://INSTANCE_IP:8003/health`

### Logs

```bash
# Application logs
gcloud logging read 'resource.type=gce_instance'

# Database logs
gcloud logging read 'resource.type=cloudsql_database'

# Load balancer logs
gcloud logging read 'resource.type=http_load_balancer'
```

### SSH Access

```bash
# SSH into compute instance
gcloud compute ssh platform-platform-dev --zone=us-central1-a

# View Docker containers
sudo docker ps

# View service logs
sudo docker logs platform-gateway
```

## 🛡️ Security Features

### Development
- HTTP only (no SSL)
- External IP for debugging
- Open SSH access
- Minimal security policies

### Staging
- SSL with managed certificates
- Restricted admin access
- Cloud Armor enabled
- Private networking

### Production
- Full SSL/TLS encryption
- Cloud Armor with rate limiting
- Geographic blocking
- Comprehensive monitoring
- Automated backups
- High availability

## 💰 Cost Optimization

### Development (~$70-140 AUD/month)
- Single f1-micro database in Australia Southeast 1
- e2-standard-2 compute instance
- Regional storage in Australia
- No redundancy
- HTTP only (no SSL costs)

### Staging (~$200-350 AUD/month)
- db-standard-1 database in Australia Southeast 1
- e2-standard-2 compute instance
- Regional storage in Australia
- Basic monitoring
- Optional SSL certificates

### Production (~$400-700 AUD/month)
- db-standard-2 database (regional HA)
- e2-standard-4 compute instance
- Multi-region storage for redundancy
- Full monitoring & alerting
- Advanced security with Cloud Armor
- SSL certificates and CDN

*Note: Prices in AUD and may vary based on actual usage patterns and Google Cloud pricing changes.*

## 🔧 Troubleshooting

### Common Issues

1. **SSL Certificate Pending**
   - Certificates take 10-20 minutes to provision
   - Ensure DNS points to load balancer IP

2. **Services Not Starting**
   - Check Docker logs: `sudo docker logs platform-gateway`
   - Verify database connectivity
   - Check secret manager access

3. **Migration Failures**
   - Verify database credentials
   - Check Cloud Build logs
   - Ensure migration files are valid

4. **Load Balancer 502 Errors**
   - Verify backend health checks
   - Check firewall rules
   - Ensure services are listening on correct ports

### Support Commands

```bash
# Restart all services
gcloud compute ssh INSTANCE_NAME --command='sudo systemctl restart platform-services'

# Update Docker images
gcloud compute ssh INSTANCE_NAME --command='sudo /opt/platform/update-services.sh'

# Check service status
gcloud compute ssh INSTANCE_NAME --command='sudo docker compose ps'
```

## 📚 Next Steps

1. **Configure Domain**: Point your domain to the load balancer IP
2. **Deploy Code**: Push changes to trigger automated builds
3. **Set Up Monitoring**: Configure alerts and monitoring dashboards
4. **Security Review**: Implement additional security measures for production
5. **Backup Strategy**: Set up automated backups and disaster recovery
6. **Performance Tuning**: Optimize based on actual usage patterns

## 📞 Getting Help

- **Terraform Docs**: Check module documentation in `modules/`
- **GCP Console**: Monitor resources in the GCP console
- **Logs**: Use Cloud Logging for debugging
- **Terraform State**: Use `terraform show` to inspect current state

Your platform infrastructure is now ready for deployment! 🎉