#!/bin/bash
set -e

echo "Checking database status..."

# Get the workspace root - when running via <PERSON><PERSON>, we need to navigate to the workspace
if [ -n "$BUILD_WORKSPACE_DIRECTORY" ]; then
    cd "$BUILD_WORKSPACE_DIRECTORY"
elif [ -n "$TEST_WORKSPACE" ]; then
    cd "$TEST_WORKSPACE/.."
else
    # Find the workspace root by looking for WORKSPACE file
    CURRENT_DIR="$PWD"
    while [ "$CURRENT_DIR" != "/" ]; do
        if [ -f "$CURRENT_DIR/WORKSPACE" ]; then
            cd "$CURRENT_DIR"
            break
        fi
        CURRENT_DIR="$(dirname "$CURRENT_DIR")"
    done
fi

# Now navigate to the platform/db directory
cd platform/db

# Check if PostgreSQL is running (check both docker-compose and docker)
if docker-compose ps 2>/dev/null | grep -q "Up" || docker ps | grep -q "platform_postgres"; then
    echo "PostgreSQL is running"
    
    # Show migration status using Flyway
    if command -v flyway &> /dev/null; then
        flyway -configFiles=flyway.conf info
    else
        # Get the network name - try different approaches to find it
        NETWORK=$(docker inspect platform_postgres -f '{{range $key, $value := .NetworkSettings.Networks}}{{$key}}{{end}}' 2>/dev/null | head -n1)
        
        if [ -z "$NETWORK" ]; then
            # Try using the default network name from docker-compose
            NETWORK="platform_db_default"
            # Check if the network exists
            if ! docker network ls | grep -q "$NETWORK"; then
                # Fall back to host networking
                NETWORK="host"
            fi
        fi
        
        # Run Flyway info using Docker with proper network configuration
        if [ "$NETWORK" = "host" ]; then
            docker run --rm \
                --network="host" \
                -v "$(pwd)/migrations:/flyway/sql" \
                -v "$(pwd)/flyway.conf:/flyway/conf/flyway.conf" \
                -e FLYWAY_URL=************************************** \
                -e FLYWAY_USER=postgres \
                -e FLYWAY_PASSWORD=postgres \
                flyway/flyway:10-alpine \
                info
        else
            docker run --rm \
                --network="$NETWORK" \
                -v "$(pwd)/migrations:/flyway/sql" \
                -v "$(pwd)/flyway.conf:/flyway/conf/flyway.conf" \
                -e FLYWAY_URL=********************************************** \
                -e FLYWAY_USER=postgres \
                -e FLYWAY_PASSWORD=postgres \
                flyway/flyway:10-alpine \
                info
        fi
    fi
else
    echo "PostgreSQL is not running"
    exit 1
fi