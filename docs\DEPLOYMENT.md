# Deployment Guide

This document outlines the available deployment options for the platform, including database migrations and web deployment.

## Available Deployment Commands

### 🏗️ Infrastructure Deployment
```bash
# Full infrastructure deployment (terraform apply + migration + web)
bazel run //terraform:deploy_infrastructure_dev

# Full deployment (migration + web) - assumes infrastructure exists
bazel run //terraform:deploy_full_dev

# Platform deployment (alias for deploy_full_dev)
bazel run //:deploy_platform_dev
```

### 🗄️ Database Operations
```bash
# Run database migrations only
bazel run //terraform:migrate_db_dev

# Direct migration (bypasses terraform wrapper)
bazel run //services/orbit/db:migrate_gcp_dev_simple
```

### 🌐 Web Deployment
```bash
# Build and deploy web app (with migration)
bazel run //terraform:deploy_web_dev_with_migration

# Build and deploy web app (without migration)
bazel run //terraform:deploy_web_dev

# Deploy existing build (no rebuild)
bazel run //terraform:deploy_only_web_dev
```

### ⚡ Quick Deployment
```bash
# Quick deployment: migrate DB + deploy web
bazel run //:deploy_quick_dev
```

### 🛠️ Local Development
```bash
# Set up local development environment
bazel run //:dev_setup

# Individual local services
bazel run //services/orbit/db:start
bazel run //services/orbit/db:migrate
bazel run //services/orbit/db:test_data
bazel run //services/orbit/web:dev
```

## Deployment Flow

### 1. Infrastructure Deployment (`deploy_infrastructure_dev`)
1. **Terraform Plan & Apply** - Provisions GCP resources
2. **Wait for Infrastructure** - Ensures compute instance is ready
3. **Database Migration** - Runs migrations on Cloud SQL
4. **Web Build & Deploy** - Builds React app and deploys to GCS

### 2. Full Deployment (`deploy_full_dev`)
1. **Verify Prerequisites** - Checks authentication and terraform state
2. **Database Migration** - Runs migrations on Cloud SQL
3. **Web Build** - Builds React app with API client generation
4. **Web Deploy** - Deploys to GCS bucket with proper headers
5. **Verification** - Tests health endpoints

### 3. Quick Deployment (`deploy_quick_dev`)
1. **Database Migration** - Runs migrations only
2. **Web Deploy** - Deploys existing build or builds if needed

## Database Migration Details

### How it works:
1. **Gets connection details** from Terraform outputs
2. **Retrieves password** from GCP Secret Manager
3. **Connects to compute instance** via gcloud SSH
4. **Creates migration files** inline on the instance
5. **Runs Flyway** via Docker with host networking
6. **Applies migrations** to Cloud SQL PostgreSQL

### Available migration commands:
```bash
# Via Terraform wrapper (recommended)
bazel run //terraform:migrate_db_dev

# Direct migration
bazel run //services/orbit/db:migrate_gcp_dev_simple

# Local development
bazel run //services/orbit/db:migrate
bazel run //services/orbit/db:status
```

## Prerequisites

### Required Tools:
- **gcloud CLI** - Authenticated with proper permissions
- **Terraform** - For infrastructure management
- **Docker** - For running Flyway migrations
- **Bazel** - For build system

### Authentication:
```bash
# Authenticate with gcloud
gcloud auth login

# Set project
gcloud config set project agent-dev-459718
```

### Terraform Setup:
```bash
# Initialize terraform (if not already done)
cd terraform/environments/dev
terraform init
```

## Environment Configuration

### Dev Environment (`agent-dev-459718`)
- **Database**: `platform-postgres-dev` (PostgreSQL 15)
- **Compute**: `platform-platform-dev` (e2-standard-2)
- **Storage**: `platform-web-dev-*` (GCS bucket)
- **Region**: `australia-southeast1`
- **Zone**: `australia-southeast1-a`

### Connection Details:
- **Database**: `platform_dev_db`
- **User**: `platform_dev_user`
- **Password**: Stored in Secret Manager (`platform-db-password`)
- **Private IP**: Retrieved from Terraform outputs

## Troubleshooting

### Common Issues:

1. **Authentication Error**
   ```bash
   gcloud auth login
   gcloud config set project agent-dev-459718
   ```

2. **Terraform State Issues**
   ```bash
   cd terraform/environments/dev
   terraform init
   terraform plan
   ```

3. **Database Connection Issues**
   - Check if compute instance is running
   - Verify database instance status in GCP console
   - Check Secret Manager for password

4. **Migration Failures**
   - Review migration files in `services/orbit/db/migrations/`
   - Check Flyway logs for SQL errors
   - Verify database schema manually

### Logs and Monitoring:
```bash
# View instance logs
gcloud logging read 'resource.type=gce_instance' --limit=50

# SSH to instance
gcloud compute ssh platform-platform-dev --zone=australia-southeast1-a

# Check bucket contents
gsutil ls -la gs://platform-web-dev-*/
```

## Best Practices

1. **Always run migrations before deploying web app**
2. **Use infrastructure deployment for first-time setup**
3. **Use full deployment for regular updates**
4. **Use quick deployment for minor changes**
5. **Test locally before deploying to GCP**
6. **Monitor logs after deployment**
7. **Verify health endpoints after deployment**

## Integration with CI/CD

The deployment scripts are designed to work in CI/CD pipelines:

```yaml
# Example GitHub Actions workflow
- name: Deploy to Dev
  run: bazel run //terraform:deploy_full_dev
  
- name: Run Migrations Only
  run: bazel run //terraform:migrate_db_dev
```

All scripts handle authentication, error checking, and provide detailed output for monitoring deployment progress.