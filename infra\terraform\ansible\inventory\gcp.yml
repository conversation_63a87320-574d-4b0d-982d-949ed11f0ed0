---
plugin: gcp_compute
auth_kind: serviceaccount
service_account_file: "{{ lookup('env', 'GCP_SERVICE_ACCOUNT_FILE') | default('') }}"
projects:
  - "{{ lookup('env', 'GCP_PROJECT_ID') | default('crm-platform-dev') }}"
regions:
  - us-central1
filters:
  - 'labels.ansible-managed = true'
  - 'status = RUNNING'
groups:
  microservices: "'microservices' in name"
  dev: "'dev' in name or labels.environment == 'dev'"
  staging: "'staging' in name or labels.environment == 'staging'"
  prod: "'prod' in name or labels.environment == 'prod'"
hostnames:
  - name
  - public_ip
  - private_ip
compose:
  ansible_host: networkInterfaces[0].accessConfigs[0].natIP | default(networkInterfaces[0].networkIP)
  ansible_user: "{{ lookup('env', 'ANSIBLE_USER') | default('ansible') }}"
  ansible_ssh_private_key_file: "{{ lookup('env', 'ANSIBLE_SSH_KEY') | default('~/.ssh/id_rsa') }}"
  ansible_ssh_common_args: '-o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null'
  gcp_zone: zone.split('/')[-1]
  gcp_project: project.split('/')[-1]
  gcp_name: name
  gcp_status: status
  environment: labels.environment | default('dev')
  deployment_color: labels.deployment-color | default('blue')