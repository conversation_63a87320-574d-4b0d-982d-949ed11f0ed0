# Auth Service

A dedicated authentication service for handling user login, token generation, and token validation.

## Overview

This service handles:
- User authentication (email/password)
- JWT token generation
- Token validation for other services
- OAuth authentication (planned)

## API Endpoints

### Public Endpoints

- `POST /api/v1/auth/signin` - User login with email/password
- `POST /api/v1/auth/signin/oauth` - OAuth login (placeholder)
- `POST /api/v1/auth/signout` - User logout
- `GET /api/v1/auth/session` - Check session status

### Internal Service Endpoints

- `POST /api/v1/auth/validate` - Validate JWT token (used by other services)

### Protected Endpoints

- `GET /api/v1/auth/user` - Get current user information (requires valid token)

## Running the Service

### With Bazel
```bash
bazel run //auth_service:auth_service
```

### With Docker
```bash
# Build the image
bazel build //auth_service:auth_service_image

# Run the container
docker run -p 8004:8004 bazel/auth_service:auth_service_image
```

### Local Development
```bash
cd auth_service
go run main.go
```

## Configuration

The service uses environment variables for configuration:

- `JWT_SECRET` - Secret key for JWT signing (required in production)
- `DATABASE_URL` - PostgreSQL connection string
- `ENVIRONMENT` - Environment name (development/production)
- `PORT` - Service port (default: 8004)

## Integration with Gateway

The gateway routes all `/api/v1/auth/*` requests to this service on port 8004.

## Token Validation

Other services can validate tokens by calling the `/api/v1/auth/validate` endpoint with a POST request containing the token to validate.