# Auth Service Testing

This document describes the test suite for the auth service.

## Test Structure

The auth service has comprehensive tests organized by package:

### Config Package (`//platform/auth/config:config_test`)
- Tests environment variable loading and configuration initialization
- Tests default values and environment overrides
- Validates configuration structure

### Database Package (`//platform/auth/database:database_test`)
- Tests database initialization with various URL formats
- Tests database connection getter and cleanup functions
- Validates error handling for invalid database configurations

### Middleware Package (`//platform/auth/middleware:middleware_test`)
- Tests JWT authentication middleware with various token scenarios
- Tests CORS middleware with different HTTP methods and headers
- Validates proper request processing and error handling

### Types Package (`//platform/auth/types:types_test`)
- Tests JSON serialization and deserialization of all data structures
- Validates request/response type definitions
- Tests proper field mapping and type conversion

### Handlers Package (`//platform/auth/handlers:handlers_test`)
- Tests request validation for all endpoints
- Tests OAuth flow initialization
- Tests session management and token validation
- Validates proper HTTP status codes and response formats

## Running Tests

### Run All Auth Tests
```bash
# Recommended: Run all tests in auth and subpackages
bazel test //platform/auth/...

# Alternative: Run specific test suite
bazel test //platform/auth:auth_tests
```

### Run Individual Package Tests
```bash
# Config tests
bazel test //platform/auth/config:config_test

# Database tests
bazel test //platform/auth/database:database_test

# Middleware tests
bazel test //platform/auth/middleware:middleware_test

# Types tests
bazel test //platform/auth/types:types_test

# Handlers tests
bazel test //platform/auth/handlers:handlers_test
```

### Verbose Test Output
```bash
bazel test //platform/auth:auth_tests --test_output=all
```

## Test Coverage

The test suite covers:
- ✅ Configuration loading and validation
- ✅ Database connection management
- ✅ JWT authentication and validation
- ✅ CORS middleware functionality
- ✅ Request/response serialization
- ✅ HTTP endpoint validation
- ✅ Error handling and edge cases

## Test Environment

Tests use:
- Go's built-in testing framework
- Gin test mode for HTTP testing
- Environment variable mocking for configuration tests
- JWT token generation and validation for auth tests

## Notes

- Tests are designed to be independent and can run in parallel
- Database tests use minimal mocking to avoid external dependencies
- All tests include proper setup and teardown
- Tests focus on validation and serialization rather than database integration