#!/bin/bash
set -e

# Logging setup
exec > >(tee /var/log/startup-script.log)
exec 2>&1
echo "=== Platform Services Startup Script - $(date) ==="

# Configure Docker authentication for COS
echo "Configuring Docker authentication for COS..."

# Get access token for Docker authentication  
echo "Getting access token for Docker..."
ACCESS_TOKEN=$(curl -s -H "Metadata-Flavor: Google" \
  "http://metadata.google.internal/computeMetadata/v1/instance/service-accounts/default/token" | \
  python3 -c "import sys, json; print(json.load(sys.stdin)['access_token'])" 2>/dev/null || echo "")

if [ -n "$ACCESS_TOKEN" ]; then
  # Create Docker config in writable location for COS
  mkdir -p /var/lib/docker
  export DOCKER_CONFIG=/var/lib/docker
  echo "$ACCESS_TOKEN" | docker login -u oauth2accesstoken --password-stdin australia-southeast1-docker.pkg.dev 2>/dev/null
  echo "Docker authentication configured for COS"
else
  echo "ERROR: Could not get access token for Docker authentication"
  exit 1
fi

# Create platform services directory in writable location for COS
mkdir -p /var/lib/platform
cd /var/lib/platform

# Get database password from Secret Manager
echo "Retrieving database password from Secret Manager..."

# Debug: Check if we have the access token
if [ -z "$ACCESS_TOKEN" ]; then
  echo "ERROR: No access token available for Secret Manager"
  exit 1
fi

# Use REST API since gcloud is not available on Container-Optimized OS
echo "Making Secret Manager API call..."
SECRET_RESPONSE=$(curl -s -H "Authorization: Bearer $ACCESS_TOKEN" \
  "https://secretmanager.googleapis.com/v1/projects/twodot-agent-prod/secrets/platform-db-password/versions/latest:access" 2>/dev/null || echo "")

echo "Secret Manager response length: ${#SECRET_RESPONSE}"

if [ -n "$SECRET_RESPONSE" ] && [ "$SECRET_RESPONSE" != "" ]; then
  echo "Decoding secret..."
  DB_PASSWORD=$(echo "$SECRET_RESPONSE" | python3 -c "
import sys, json, base64
try:
    data = json.load(sys.stdin)
    if 'payload' in data and 'data' in data['payload']:
        decoded = base64.b64decode(data['payload']['data']).decode()
        print(decoded)
    else:
        print('placeholder')
except Exception as e:
    print('placeholder')
" 2>/dev/null || echo "placeholder")
else
  DB_PASSWORD="placeholder"
fi

if [ -z "$DB_PASSWORD" ] || [ "$DB_PASSWORD" = "placeholder" ]; then
  echo "ERROR: Could not retrieve database password from Secret Manager"
  echo "SECRET_RESPONSE preview: ${SECRET_RESPONSE:0:100}..."
  echo "Using placeholder password - services will fail to start"
  DB_PASSWORD="placeholder"
else
  echo "Database password retrieved successfully: ${#DB_PASSWORD} characters"
  # URL encode the password for PostgreSQL connection string
  echo "URL encoding password for database connection..."
  DB_PASSWORD_ENCODED=$(python3 -c "import urllib.parse; print(urllib.parse.quote('$DB_PASSWORD', safe=''))" 2>/dev/null || echo "$DB_PASSWORD")
  echo "Password URL encoded successfully: ${#DB_PASSWORD_ENCODED} characters"
fi

# Create Docker network
docker network create platform-network || true

# Run database migrations automatically
echo "=== Running Database Migrations ==="
echo "Creating temporary directory for migration..."
TEMP_DIR=$(mktemp -d)
cd $TEMP_DIR

# Create migration files
mkdir -p migrations

echo "Creating V001 migration..."
cat > migrations/V001__initial_schema.sql << 'MIGRATION_EOF'
-- Initial database schema
-- V001__initial_schema.sql

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    full_name VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create index on email for faster lookups
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);

-- Create a trigger to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE
    ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
MIGRATION_EOF

echo "Creating V002 migration..."
cat > migrations/V002__crm_schema.sql << 'MIGRATION_EOF'
-- CRM Database Schema Migration
-- V002__crm_schema.sql

-- Update users table to support authentication
ALTER TABLE users ADD COLUMN IF NOT EXISTS password_hash VARCHAR(255);
ALTER TABLE users ADD COLUMN IF NOT EXISTS email_confirmed_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS is_deleted BOOLEAN DEFAULT FALSE;

-- Create user profiles table
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    full_name VARCHAR(255),
    avatar_url TEXT,
    timezone VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create company statuses table (pipeline stages for companies)
CREATE TABLE IF NOT EXISTS company_statuses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    pipeline_order INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create companies table
CREATE TABLE IF NOT EXISTS companies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    website TEXT,
    phone VARCHAR(50),
    address TEXT,
    notes TEXT,
    company_status_id UUID REFERENCES company_statuses(id),
    is_deleted BOOLEAN DEFAULT FALSE,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create contacts table
CREATE TABLE IF NOT EXISTS contacts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(50),
    job_title VARCHAR(255),
    company_id UUID REFERENCES companies(id) ON DELETE SET NULL,
    created_by UUID NOT NULL REFERENCES users(id),
    is_deleted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create deal stages table (pipeline stages for deals)
CREATE TABLE IF NOT EXISTS deal_stages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    pipeline_order INTEGER NOT NULL,
    is_closed_won BOOLEAN DEFAULT FALSE,
    is_closed_lost BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create deals table
CREATE TABLE IF NOT EXISTS deals (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    estimated_value DECIMAL(15,2),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    deal_stage_id UUID NOT NULL REFERENCES deal_stages(id),
    expected_close_date DATE,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create interaction types table
CREATE TABLE IF NOT EXISTS interaction_types (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create interactions table (communication tracking)
CREATE TABLE IF NOT EXISTS interactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    subject VARCHAR(255),
    content TEXT,
    interaction_type_id UUID REFERENCES interaction_types(id),
    company_id UUID REFERENCES companies(id) ON DELETE CASCADE,
    contact_id UUID REFERENCES contacts(id) ON DELETE SET NULL,
    deal_id UUID REFERENCES deals(id) ON DELETE SET NULL,
    created_by UUID NOT NULL REFERENCES users(id),
    interaction_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create arli documents table (AI document processing)
CREATE TABLE IF NOT EXISTS arli_documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    content TEXT,
    metadata JSONB,
    source_url TEXT,
    document_type VARCHAR(100),
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create arli content blocks table
CREATE TABLE IF NOT EXISTS arli_content_blocks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID NOT NULL REFERENCES arli_documents(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    block_type VARCHAR(50) NOT NULL,
    metadata JSONB,
    order_index INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_companies_name ON companies(name);
CREATE INDEX IF NOT EXISTS idx_companies_status ON companies(company_status_id);
CREATE INDEX IF NOT EXISTS idx_companies_deleted ON companies(is_deleted);
CREATE INDEX IF NOT EXISTS idx_companies_created_by ON companies(created_by);

CREATE INDEX IF NOT EXISTS idx_contacts_name ON contacts(first_name, last_name);
CREATE INDEX IF NOT EXISTS idx_contacts_email ON contacts(email);
CREATE INDEX IF NOT EXISTS idx_contacts_company ON contacts(company_id);
CREATE INDEX IF NOT EXISTS idx_contacts_deleted ON contacts(is_deleted);
CREATE INDEX IF NOT EXISTS idx_contacts_created_by ON contacts(created_by);

CREATE INDEX IF NOT EXISTS idx_deals_title ON deals(title);
CREATE INDEX IF NOT EXISTS idx_deals_company ON deals(company_id);
CREATE INDEX IF NOT EXISTS idx_deals_stage ON deals(deal_stage_id);
CREATE INDEX IF NOT EXISTS idx_deals_created_by ON deals(created_by);
CREATE INDEX IF NOT EXISTS idx_deals_close_date ON deals(expected_close_date);

CREATE INDEX IF NOT EXISTS idx_interactions_company ON interactions(company_id);
CREATE INDEX IF NOT EXISTS idx_interactions_contact ON interactions(contact_id);
CREATE INDEX IF NOT EXISTS idx_interactions_deal ON interactions(deal_id);
CREATE INDEX IF NOT EXISTS idx_interactions_type ON interactions(interaction_type_id);
CREATE INDEX IF NOT EXISTS idx_interactions_date ON interactions(interaction_date);
CREATE INDEX IF NOT EXISTS idx_interactions_created_by ON interactions(created_by);

CREATE INDEX IF NOT EXISTS idx_arli_documents_type ON arli_documents(document_type);
CREATE INDEX IF NOT EXISTS idx_arli_documents_created_by ON arli_documents(created_by);
CREATE INDEX IF NOT EXISTS idx_arli_content_blocks_document ON arli_content_blocks(document_id);
CREATE INDEX IF NOT EXISTS idx_arli_content_blocks_type ON arli_content_blocks(block_type);
CREATE INDEX IF NOT EXISTS idx_arli_content_blocks_order ON arli_content_blocks(document_id, order_index);

-- Create triggers for updated_at columns
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE
    ON user_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_companies_updated_at BEFORE UPDATE
    ON companies FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_contacts_updated_at BEFORE UPDATE
    ON contacts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_deals_updated_at BEFORE UPDATE
    ON deals FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_interactions_updated_at BEFORE UPDATE
    ON interactions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_arli_documents_updated_at BEFORE UPDATE
    ON arli_documents FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_arli_content_blocks_updated_at BEFORE UPDATE
    ON arli_content_blocks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
MIGRATION_EOF

# Create flyway configuration  
echo "Creating Flyway configuration..."
cat > flyway.conf << 'FLYWAY_EOF'
# Flyway Configuration for GCP Cloud SQL
flyway.url=**************************************************
flyway.user=platform_prod_user
flyway.password=${DB_PASSWORD}
flyway.schemas=public
flyway.locations=filesystem:/flyway/sql
flyway.baselineOnMigrate=true
flyway.baselineVersion=0
flyway.validateOnMigrate=true
flyway.cleanDisabled=true
flyway.connectRetries=5
flyway.connectRetriesInterval=10
FLYWAY_EOF

# Run Flyway migration using Docker
echo "Running Flyway migrations..."
docker run --rm \
    --network="host" \
    -v "$(pwd)/migrations:/flyway/sql" \
    -v "$(pwd)/flyway.conf:/flyway/conf/flyway.conf" \
    flyway/flyway:10-alpine \
    migrate

echo "Migration completed successfully!"

# Clean up migration files
cd /var/lib/platform
rm -rf $TEMP_DIR

# Deploy services
echo "=== Deploying Platform Services ==="

# Pull and run Auth service
echo "Deploying Auth service..."
docker pull australia-southeast1-docker.pkg.dev/twodot-agent-prod/platform-docker/auth:latest
docker stop auth || true
docker rm auth || true
docker run -d --name auth \
  --network platform-network \
  -p 8004:8004 \
  -e PORT=8004 \
  -e DATABASE_URL=************************************************************************************* \
  -e LOG_LEVEL=info \
  --restart unless-stopped \
  australia-southeast1-docker.pkg.dev/twodot-agent-prod/platform-docker/auth:latest

# Pull and run CRM Backend service
echo "Deploying CRM Backend service..."
docker pull australia-southeast1-docker.pkg.dev/twodot-agent-prod/platform-docker/crm-backend:latest
docker stop crm-backend || true
docker rm crm-backend || true
docker run -d --name crm-backend \
  --network platform-network \
  -p 8003:8003 \
  -e PORT=8003 \
  -e DATABASE_URL=************************************************************************************* \
  -e LOG_LEVEL=info \
  --restart unless-stopped \
  australia-southeast1-docker.pkg.dev/twodot-agent-prod/platform-docker/crm-backend:latest

# Pull and run Gateway service
echo "Deploying Gateway service..."
docker pull australia-southeast1-docker.pkg.dev/twodot-agent-prod/platform-docker/gateway:latest
docker stop gateway || true
docker rm gateway || true
docker run -d --name gateway \
  --network platform-network \
  -p 8085:8085 \
  -e PORT=8085 \
  -e AUTH_SERVICE_URL=http://auth:8004 \
  -e CRM_BACKEND_URL=http://crm-backend:8003 \
  -e LOG_LEVEL=info \
  --restart unless-stopped \
  australia-southeast1-docker.pkg.dev/twodot-agent-prod/platform-docker/gateway:latest

# Wait for services to start
sleep 30

# Verify services are running
echo "=== Service Status ==="
docker ps --filter network=platform-network

echo "=== Startup script completed successfully - $(date) ==="