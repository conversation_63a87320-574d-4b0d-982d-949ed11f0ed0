/* tslint:disable */
/* eslint-disable */
/**
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface DealCompanyInfo
 */
export interface DealCompanyInfo {
    /**
     * 
     * @type {string}
     * @memberof DealCompanyInfo
     */
    id?: string;
    /**
     * 
     * @type {string}
     * @memberof DealCompanyInfo
     */
    name?: string;
}

/**
 * Check if a given object implements the DealCompanyInfo interface.
 */
export function instanceOfDealCompanyInfo(value: object): value is DealCompanyInfo {
    return true;
}

export function DealCompanyInfoFromJSON(json: any): DealCompanyInfo {
    return DealCompanyInfoFromJSONTyped(json, false);
}

export function DealCompanyInfoFromJSONTyped(json: any, ignoreDiscriminator: boolean): DealCompanyInfo {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'] == null ? undefined : json['id'],
        'name': json['name'] == null ? undefined : json['name'],
    };
}

  export function DealCompanyInfoToJSON(json: any): DealCompanyInfo {
      return DealCompanyInfoToJSONTyped(json, false);
  }

  export function DealCompanyInfoToJSONTyped(value?: DealCompanyInfo | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'name': value['name'],
    };
}

