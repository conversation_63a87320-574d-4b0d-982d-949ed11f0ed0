"""
AI Agent Platform - Configuration Management
"""

from functools import lru_cache
from typing import List, Optional
from pydantic import Field
from pydantic_settings import BaseSettings


class DatabaseSettings(BaseSettings):
    """Database configuration settings"""
    
    # PostgreSQL Settings
    postgres_server: str = Field("localhost", env="POSTGRES_SERVER")
    postgres_user: str = Field("ai_agent", env="POSTGRES_USER")
    postgres_password: str = Field("ai_agent_password", env="POSTGRES_PASSWORD")
    postgres_db: str = Field("ai_agent_platform", env="POSTGRES_DB")
    postgres_port: int = Field(5432, env="POSTGRES_PORT")
    
    # Redis Settings
    redis_server: str = Field("localhost", env="REDIS_SERVER")
    redis_port: int = Field(6379, env="REDIS_PORT")
    redis_password: Optional[str] = Field(None, env="REDIS_PASSWORD")
    redis_db: int = Field(0, env="REDIS_DB")
    
    @property
    def postgres_dsn(self) -> str:
        return f"postgresql://{self.postgres_user}:{self.postgres_password}@{self.postgres_server}:{self.postgres_port}/{self.postgres_db}"
    
    @property
    def redis_dsn(self) -> str:
        auth = f":{self.redis_password}@" if self.redis_password else ""
        return f"redis://{auth}{self.redis_server}:{self.redis_port}/{self.redis_db}"


class MessageQueueSettings(BaseSettings):
    """Message queue configuration settings"""
    
    kafka_bootstrap_servers: List[str] = Field(["localhost:9092"], env="KAFKA_BOOTSTRAP_SERVERS")
    kafka_group_id: str = Field("ai_agent_platform", env="KAFKA_GROUP_ID")
    kafka_auto_offset_reset: str = Field("earliest", env="KAFKA_AUTO_OFFSET_RESET")
    
    # Topic configurations
    agent_events_topic: str = Field("agent.events", env="KAFKA_AGENT_EVENTS_TOPIC")
    orchestration_topic: str = Field("orchestration.commands", env="KAFKA_ORCHESTRATION_TOPIC")
    intelligence_topic: str = Field("intelligence.requests", env="KAFKA_INTELLIGENCE_TOPIC")


class SecuritySettings(BaseSettings):
    """Security and authentication settings"""
    
    secret_key: str = Field("development-secret-key-change-in-production", env="SECRET_KEY")
    algorithm: str = Field("HS256", env="JWT_ALGORITHM")
    access_token_expire_minutes: int = Field(30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    refresh_token_expire_days: int = Field(7, env="REFRESH_TOKEN_EXPIRE_DAYS")


class OAuthSettings(BaseSettings):
    """OAuth provider settings"""
    
    # Google OAuth
    google_client_id: Optional[str] = Field(None, env="GOOGLE_CLIENT_ID")
    google_client_secret: Optional[str] = Field(None, env="GOOGLE_CLIENT_SECRET")
    
    # GitHub OAuth
    github_client_id: Optional[str] = Field(None, env="GITHUB_CLIENT_ID")
    github_client_secret: Optional[str] = Field(None, env="GITHUB_CLIENT_SECRET")
    
    # Microsoft OAuth
    microsoft_client_id: Optional[str] = Field(None, env="MICROSOFT_CLIENT_ID")
    microsoft_client_secret: Optional[str] = Field(None, env="MICROSOFT_CLIENT_SECRET")


class MFASettings(BaseSettings):
    """Multi-Factor Authentication settings"""
    
    # Encryption key for storing sensitive MFA data
    mfa_encryption_key: Optional[str] = Field(None, env="MFA_ENCRYPTION_KEY")
    
    # SMS provider settings
    sms_provider: str = Field("mock", env="SMS_PROVIDER")  # "twilio", "aws_sns", "mock"
    
    # Twilio settings
    twilio_account_sid: Optional[str] = Field(None, env="TWILIO_ACCOUNT_SID")
    twilio_auth_token: Optional[str] = Field(None, env="TWILIO_AUTH_TOKEN")
    twilio_from_number: Optional[str] = Field(None, env="TWILIO_FROM_NUMBER")
    
    # AWS SNS settings
    aws_access_key_id: Optional[str] = Field(None, env="AWS_ACCESS_KEY_ID")
    aws_secret_access_key: Optional[str] = Field(None, env="AWS_SECRET_ACCESS_KEY")
    aws_region: str = Field("us-east-1", env="AWS_REGION")
    
    # TOTP settings
    totp_issuer: str = Field("AI Agent Platform", env="TOTP_ISSUER")
    backup_codes_count: int = Field(10, env="BACKUP_CODES_COUNT")


class VectorDBSettings(BaseSettings):
    """Vector database configuration settings"""
    
    # Qdrant settings
    qdrant_host: str = Field("localhost", env="QDRANT_HOST")
    qdrant_port: int = Field(6333, env="QDRANT_PORT")
    qdrant_api_key: Optional[str] = Field(None, env="QDRANT_API_KEY")
    qdrant_timeout: float = Field(30.0, env="QDRANT_TIMEOUT")
    
    # Embedding model settings
    embedding_model: str = Field("all-MiniLM-L6-v2", env="EMBEDDING_MODEL")
    embedding_dimension: int = Field(384, env="EMBEDDING_DIMENSION")
    
    # Collection settings
    collection_prefix: str = Field("ai_agent_platform_", env="VECTOR_COLLECTION_PREFIX")


class AIServiceSettings(BaseSettings):
    """AI service integration settings"""
    
    # OpenAI
    openai_api_key: Optional[str] = Field(None, env="OPENAI_API_KEY")
    openai_organization: Optional[str] = Field(None, env="OPENAI_ORGANIZATION")
    
    # Anthropic
    anthropic_api_key: Optional[str] = Field(None, env="ANTHROPIC_API_KEY")
    
    # Google AI / Vertex AI
    google_ai_project_id: Optional[str] = Field(None, env="GOOGLE_AI_PROJECT_ID")
    google_ai_location: str = Field("us-central1", env="GOOGLE_AI_LOCATION")
    google_api_key: Optional[str] = Field(None, env="GOOGLE_API_KEY")
    google_service_account_json: Optional[str] = Field(None, env="GOOGLE_SERVICE_ACCOUNT_JSON")
    
    # Model settings
    default_model: str = Field("gpt-4", env="DEFAULT_AI_MODEL")
    max_tokens: int = Field(4096, env="MAX_AI_TOKENS")
    temperature: float = Field(0.7, env="AI_TEMPERATURE")


class AgentSettings(BaseSettings):
    """Agent runtime configuration"""
    
    max_concurrent_agents: int = Field(1000, env="MAX_CONCURRENT_AGENTS")
    agent_startup_timeout: int = Field(30, env="AGENT_STARTUP_TIMEOUT")
    agent_idle_timeout: int = Field(300, env="AGENT_IDLE_TIMEOUT")
    
    # Resource limits
    max_memory_mb: int = Field(512, env="AGENT_MAX_MEMORY_MB")
    max_cpu_percent: float = Field(50.0, env="AGENT_MAX_CPU_PERCENT")


class MonitoringSettings(BaseSettings):
    """Monitoring and observability settings"""
    
    log_level: str = Field("INFO", env="LOG_LEVEL")
    log_format: str = Field("json", env="LOG_FORMAT")
    
    # Metrics
    prometheus_enabled: bool = Field(True, env="PROMETHEUS_ENABLED")
    prometheus_port: int = Field(8000, env="PROMETHEUS_PORT")
    
    # Tracing
    jaeger_enabled: bool = Field(False, env="JAEGER_ENABLED")
    jaeger_endpoint: Optional[str] = Field(None, env="JAEGER_ENDPOINT")
    
    # Sentry
    sentry_dsn: Optional[str] = Field(None, env="SENTRY_DSN")
    sentry_environment: str = Field("development", env="SENTRY_ENVIRONMENT")


class Settings(BaseSettings):
    """Main application settings"""
    
    # App info
    app_name: str = Field("AI Agent Platform", env="APP_NAME")
    version: str = Field("1.0.0", env="APP_VERSION")
    description: str = Field("Enterprise AI Agent Platform", env="APP_DESCRIPTION")
    environment: str = Field("development", env="ENVIRONMENT")
    debug: bool = Field(True, env="DEBUG")
    
    # Server settings
    host: str = Field("0.0.0.0", env="HOST")
    port: int = Field(8000, env="PORT")
    workers: int = Field(1, env="WORKERS")
    
    # CORS
    allowed_hosts: List[str] = Field(["*"])
    cors_origins: List[str] = Field(["http://localhost:3000", "http://127.0.0.1:3000"])
    
    # URLs
    backend_url: str = Field("http://localhost:8000", env="BACKEND_URL")
    frontend_url: str = Field("http://localhost:3000", env="FRONTEND_URL")
    
    # Security
    secret_key: str = Field(..., env="SECRET_KEY")
    jwt_secret_key: str = Field(..., env="JWT_SECRET_KEY")
    algorithm: str = Field("HS256", env="JWT_ALGORITHM")
    access_token_expire_minutes: int = Field(30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    refresh_token_expire_days: int = Field(7, env="REFRESH_TOKEN_EXPIRE_DAYS")
    
    # Agent Settings
    max_concurrent_agents: int = Field(1000, env="MAX_CONCURRENT_AGENTS")
    agent_startup_timeout: int = Field(30, env="AGENT_STARTUP_TIMEOUT")
    agent_idle_timeout: int = Field(300, env="AGENT_IDLE_TIMEOUT")
    max_memory_mb: int = Field(512, env="AGENT_MAX_MEMORY_MB")
    max_cpu_percent: float = Field(50.0, env="AGENT_MAX_CPU_PERCENT")
    
    # Monitoring
    log_level: str = Field("INFO", env="LOG_LEVEL")
    
    @property
    def agents(self):
        """Agent settings compatibility"""
        return type('AgentSettings', (), {
            'max_concurrent_agents': self.max_concurrent_agents,
            'agent_startup_timeout': self.agent_startup_timeout,
            'agent_idle_timeout': self.agent_idle_timeout,
            'max_memory_mb': self.max_memory_mb,
            'max_cpu_percent': self.max_cpu_percent,
        })()
        
    @property
    def monitoring(self):
        """Monitoring settings compatibility"""
        return type('MonitoringSettings', (), {
            'log_level': self.log_level,
        })()
    
    @property
    def oauth(self):
        """OAuth settings compatibility"""
        return OAuthSettings()
    
    @property 
    def mfa(self):
        """MFA settings compatibility"""
        return MFASettings()
    
    @property
    def mfa_encryption_key(self):
        """Direct access to MFA encryption key"""
        return self.mfa.mfa_encryption_key
    
    @property
    def message_queue(self):
        """Message queue settings compatibility"""
        return MessageQueueSettings()
    
    @property
    def vector_db(self):
        """Vector database settings compatibility"""
        return VectorDBSettings()
    
    @property
    def ai_services(self):
        """AI service settings compatibility"""
        return AIServiceSettings()
    
    @property
    def security(self):
        """Security settings compatibility"""
        return SecuritySettings(
            secret_key=self.jwt_secret_key,
            algorithm=self.algorithm,
            access_token_expire_minutes=self.access_token_expire_minutes,
            refresh_token_expire_days=self.refresh_token_expire_days,
        )
    
    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "case_sensitive": False,
        "extra": "ignore"
    }


@lru_cache()
def get_settings() -> Settings:
    """Get cached application settings"""
    return Settings()


# Global settings instance
settings = get_settings()