// Migration service using typed API client
import { api } from '@/lib/api';
// Migration types are not yet available in the generated types
// Using placeholder types until the backend schemas are properly implemented
type ApplicationAnalysis = any;
type MigrationPlan = any;
type MigrationProject = any;
type RiskItem = any;

export class MigrationService {
  // Analysis operations
  static async analyzeApplication(appPath: string): Promise<ApplicationAnalysis> {
    return api.migration.analyze({ app_path: appPath });
  }

  static async getAnalysis(analysisId: string): Promise<ApplicationAnalysis> {
    return api.migration.getAnalysis(analysisId);
  }

  static async listAnalyses(): Promise<ApplicationAnalysis[]> {
    return api.migration.listAnalyses();
  }

  // Plan operations
  static async createMigrationPlan(
    analysisId: string, 
    customStrategy?: MigrationPlan['strategy']
  ): Promise<MigrationPlan> {
    return api.migration.createPlan({ 
      analysis_id: analysisId, 
      custom_strategy: customStrategy 
    });
  }

  static async getMigrationPlan(planId: string): Promise<MigrationPlan> {
    return api.migration.getPlan(planId);
  }

  static async listMigrationPlans(): Promise<MigrationPlan[]> {
    return api.migration.listPlans();
  }

  // Migration project operations
  static async startMigration(planId: string): Promise<{
    project_id: string;
    status: string;
    estimated_timeline_days: number;
  }> {
    return api.migration.start({ plan_id: planId });
  }

  static async getMigrationStatus(projectId: string): Promise<MigrationProject> {
    return api.migration.getStatus(projectId);
  }

  static async listMigrationProjects(): Promise<MigrationProject[]> {
    return api.migration.listProjects();
  }

  static async getMigrationSummary(): Promise<{
    total_analyses: number;
    total_migration_plans: number;
    total_projects: number;
    active_migrations: number;
    completed_migrations: number;
    failed_migrations: number;
    success_rate: number;
    status_distribution: Record<string, number>;
    most_common_strategies: Record<string, number>;
    average_complexity: number;
  }> {
    return api.migration.getSummary();
  }

  // Utility methods
  static getStrategyLabel(strategy: MigrationPlan['strategy']): string {
    const labels: Record<MigrationPlan['strategy'], string> = {
      wrapper: 'Wrapper Approach',
      rewrite: 'Complete Rewrite',
      hybrid: 'Hybrid Migration',
      interface: 'Interface Layer',
      decompose: 'Microservices Decomposition'
    };
    return labels[strategy] || strategy;
  }

  static getStrategyDescription(strategy: MigrationPlan['strategy']): string {
    const descriptions: Record<MigrationPlan['strategy'], string> = {
      wrapper: 'Create an AI wrapper around the existing application with minimal code changes',
      rewrite: 'Completely rebuild the application as AI-native agents from scratch',
      hybrid: 'Gradually migrate parts of the application while maintaining compatibility',
      interface: 'Add an AI interface layer on top of the existing application',
      decompose: 'Break down the monolithic application into microservice-based agents'
    };
    return descriptions[strategy] || 'Custom migration strategy';
  }

  static getComplexityLabel(score: number): { label: string; color: string } {
    if (score <= 3) {
      return { label: 'Low', color: 'text-green-600' };
    } else if (score <= 6) {
      return { label: 'Medium', color: 'text-yellow-600' };
    } else if (score <= 8) {
      return { label: 'High', color: 'text-orange-600' };
    } else {
      return { label: 'Very High', color: 'text-red-600' };
    }
  }

  static getFeasibilityLabel(score: number): { label: string; color: string } {
    if (score >= 8) {
      return { label: 'Excellent', color: 'text-green-600' };
    } else if (score >= 6) {
      return { label: 'Good', color: 'text-blue-600' };
    } else if (score >= 4) {
      return { label: 'Fair', color: 'text-yellow-600' };
    } else {
      return { label: 'Poor', color: 'text-red-600' };
    }
  }

  static getStatusIcon(status: MigrationProject['status']): string {
    const icons: Record<MigrationProject['status'], string> = {
      analyzing: '🔍',
      planning: '📋',
      migrating: '⚙️',
      testing: '🧪',
      completed: '✅',
      failed: '❌'
    };
    return icons[status] || '❓';
  }

  static formatDuration(hours: number): string {
    if (hours < 24) {
      return `${hours} hours`;
    }
    const days = Math.floor(hours / 24);
    const remainingHours = hours % 24;
    
    if (remainingHours === 0) {
      return `${days} ${days === 1 ? 'day' : 'days'}`;
    }
    
    return `${days}d ${remainingHours}h`;
  }

  static calculateEstimatedCost(effortHours: number, hourlyRate: number = 100): {
    development: number;
    infrastructure: number;
    total: number;
  } {
    const development = effortHours * hourlyRate;
    const infrastructure = Math.max(500, development * 0.1); // Min $500, or 10% of dev cost
    
    return {
      development,
      infrastructure,
      total: development + infrastructure
    };
  }

  static getRiskLevel(risks: RiskItem[]): { level: string; color: string } {
    const highRisks = risks.filter(r => r.impact === 'high' || r.probability === 'high').length;
    const totalRisks = risks.length;
    
    if (highRisks === 0) {
      return { level: 'Low', color: 'text-green-600' };
    } else if (highRisks / totalRisks < 0.5) {
      return { level: 'Medium', color: 'text-yellow-600' };
    } else {
      return { level: 'High', color: 'text-red-600' };
    }
  }
}

export default MigrationService;