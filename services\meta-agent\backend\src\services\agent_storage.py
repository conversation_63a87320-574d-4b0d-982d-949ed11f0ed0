"""
Agent File Storage Service
Manages persistent storage of generated agent code and files
"""

import os
import shutil
import json
from pathlib import Path
from typing import Dict, Any, List, Optional
from uuid import UUID
import structlog

logger = structlog.get_logger()

class AgentStorageService:
    """Manages file storage for generated agent code"""
    
    def __init__(self, base_storage_path: str = None):
        # Use environment variable or default path
        self.base_path = Path(base_storage_path or os.getenv(
            "AGENT_STORAGE_PATH", 
            "/tmp/ai_agent_storage/generated_agents"
        ))
        self.base_path.mkdir(parents=True, exist_ok=True)
        logger.info("Agent storage initialized", base_path=str(self.base_path))
    
    def get_agent_path(self, agent_id: str) -> Path:
        """Get the storage path for a specific agent"""
        return self.base_path / f"agent_{agent_id}"
    
    def agent_exists(self, agent_id: str) -> bool:
        """Check if agent files exist"""
        agent_path = self.get_agent_path(agent_id)
        return agent_path.exists() and agent_path.is_dir()
    
    def save_generated_code(self, agent_id: str, generated_code: Dict[str, Any]) -> Dict[str, Any]:
        """Save generated code files to disk"""
        try:
            agent_path = self.get_agent_path(agent_id)
            
            # Remove existing files if they exist
            if agent_path.exists():
                shutil.rmtree(agent_path)
            
            agent_path.mkdir(parents=True, exist_ok=True)
            
            # Save metadata
            metadata = {
                "agent_id": agent_id,
                "agent_name": generated_code.get("agent_name"),
                "language": generated_code.get("language"),
                "framework": generated_code.get("framework"),
                "generated_at": generated_code.get("generated_at"),
                "deployment_config": generated_code.get("deployment_config", {}),
                "file_structure": {}
            }
            
            # Save components to files
            components = generated_code.get("components", {})
            
            for component_type, files in components.items():
                if isinstance(files, dict):
                    for filename, content in files.items():
                        # Handle nested directory structure
                        if "/" in filename:
                            file_path = agent_path / filename
                            file_path.parent.mkdir(parents=True, exist_ok=True)
                        else:
                            file_path = agent_path / filename
                        
                        # Write file content (ensure it's a string)
                        with open(file_path, 'w', encoding='utf-8') as f:
                            if isinstance(content, list):
                                # Join list items with newlines
                                f.write('\n'.join(str(item) for item in content))
                            elif isinstance(content, dict):
                                # Convert dict to JSON string
                                f.write(json.dumps(content, indent=2))
                            else:
                                # Write as string
                                f.write(str(content))
                        
                        # Track in metadata
                        if component_type not in metadata["file_structure"]:
                            metadata["file_structure"][component_type] = []
                        metadata["file_structure"][component_type].append(filename)
                        
                        logger.debug("Saved file", agent_id=agent_id, file=filename)
            
            # Save metadata file
            metadata_file = agent_path / "agent_metadata.json"
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, default=str)
            
            logger.info("Agent code saved successfully", 
                       agent_id=agent_id, 
                       files_count=sum(len(files) if isinstance(files, list) else len(files) if isinstance(files, dict) else 0 
                                     for files in metadata["file_structure"].values()))
            
            return {
                "success": True,
                "agent_path": str(agent_path),
                "files_saved": metadata["file_structure"]
            }
            
        except Exception as e:
            logger.error("Failed to save agent code", agent_id=agent_id, error=str(e))
            raise
    
    def load_agent_metadata(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """Load agent metadata from disk"""
        try:
            agent_path = self.get_agent_path(agent_id)
            metadata_file = agent_path / "agent_metadata.json"
            
            if not metadata_file.exists():
                return None
            
            with open(metadata_file, 'r', encoding='utf-8') as f:
                return json.load(f)
                
        except Exception as e:
            logger.error("Failed to load agent metadata", agent_id=agent_id, error=str(e))
            return None
    
    def load_generated_code(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """Load generated code structure for deployment"""
        try:
            agent_path = self.get_agent_path(agent_id)
            metadata = self.load_agent_metadata(agent_id)
            
            if not metadata or not agent_path.exists():
                return None
            
            # Reconstruct the generated_code structure
            generated_code = {
                "agent_name": metadata.get("agent_name"),
                "language": metadata.get("language"),
                "framework": metadata.get("framework"),
                "components": {},
                "deployment_config": metadata.get("deployment_config", {}),
                "generated_at": metadata.get("generated_at")
            }
            
            # Load file contents back into components structure
            for component_type, filenames in metadata.get("file_structure", {}).items():
                generated_code["components"][component_type] = {}
                
                for filename in filenames:
                    file_path = agent_path / filename
                    if file_path.exists():
                        with open(file_path, 'r', encoding='utf-8') as f:
                            generated_code["components"][component_type][filename] = f.read()
            
            return generated_code
            
        except Exception as e:
            logger.error("Failed to load generated code", agent_id=agent_id, error=str(e))
            return None
    
    def get_agent_files_list(self, agent_id: str) -> List[str]:
        """Get list of all files for an agent"""
        try:
            agent_path = self.get_agent_path(agent_id)
            if not agent_path.exists():
                return []
            
            files = []
            for file_path in agent_path.rglob("*"):
                if file_path.is_file() and file_path.name != "agent_metadata.json":
                    relative_path = file_path.relative_to(agent_path)
                    files.append(str(relative_path))
            
            return sorted(files)
            
        except Exception as e:
            logger.error("Failed to list agent files", agent_id=agent_id, error=str(e))
            return []
    
    def delete_agent_files(self, agent_id: str) -> bool:
        """Delete all files for an agent"""
        try:
            agent_path = self.get_agent_path(agent_id)
            if agent_path.exists():
                shutil.rmtree(agent_path)
                logger.info("Agent files deleted", agent_id=agent_id)
                return True
            return False
            
        except Exception as e:
            logger.error("Failed to delete agent files", agent_id=agent_id, error=str(e))
            return False
    
    def copy_agent_files_for_deployment(self, agent_id: str, deployment_path: Path) -> bool:
        """Copy agent files to deployment directory"""
        try:
            agent_path = self.get_agent_path(agent_id)
            
            if not agent_path.exists():
                logger.error("Agent files not found", agent_id=agent_id)
                return False
            
            # Create deployment directory
            deployment_path.mkdir(parents=True, exist_ok=True)
            
            # Copy all files except metadata
            for file_path in agent_path.rglob("*"):
                if file_path.is_file() and file_path.name != "agent_metadata.json":
                    relative_path = file_path.relative_to(agent_path)
                    dest_path = deployment_path / relative_path
                    dest_path.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(file_path, dest_path)
            
            logger.info("Agent files copied for deployment", 
                       agent_id=agent_id, 
                       deployment_path=str(deployment_path))
            return True
            
        except Exception as e:
            logger.error("Failed to copy agent files for deployment", 
                        agent_id=agent_id, 
                        error=str(e))
            return False
    
    def get_storage_stats(self) -> Dict[str, Any]:
        """Get storage statistics"""
        try:
            stats = {
                "total_agents": 0,
                "total_size_mb": 0,
                "agents": []
            }
            
            if not self.base_path.exists():
                return stats
            
            for agent_dir in self.base_path.iterdir():
                if agent_dir.is_dir() and agent_dir.name.startswith("agent_"):
                    agent_id = agent_dir.name.replace("agent_", "")
                    metadata = self.load_agent_metadata(agent_id)
                    
                    # Calculate directory size
                    size_bytes = sum(f.stat().st_size for f in agent_dir.rglob('*') if f.is_file())
                    size_mb = size_bytes / (1024 * 1024)
                    
                    stats["agents"].append({
                        "agent_id": agent_id,
                        "name": metadata.get("agent_name") if metadata else "Unknown",
                        "size_mb": round(size_mb, 2),
                        "files_count": len(self.get_agent_files_list(agent_id)),
                        "generated_at": metadata.get("generated_at") if metadata else None
                    })
                    
                    stats["total_agents"] += 1
                    stats["total_size_mb"] += size_mb
            
            stats["total_size_mb"] = round(stats["total_size_mb"], 2)
            return stats
            
        except Exception as e:
            logger.error("Failed to get storage stats", error=str(e))
            return {"error": str(e)}

# Global instance
agent_storage = AgentStorageService()