"""
Enhanced Agent-to-Agent (A2A) Protocol Handler
Provides secure and efficient communication between AI agents
"""

import json
import uuid
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Callable
from enum import Enum

from pydantic import BaseModel, Field
from sqlalchemy.orm import Session

from database.models import Agent, User
from services.messaging import message_queue_service
from services.vector_db import get_vector_db
from auth.dependencies import verify_agent_token

logger = logging.getLogger(__name__)


class MessageType(str, Enum):
    """A2A message types"""
    REQUEST = "request"
    RESPONSE = "response"
    BROADCAST = "broadcast"
    HANDSHAKE = "handshake"
    HEARTBEAT = "heartbeat"
    TERMINATE = "terminate"
    ERROR = "error"

# Alias for backward compatibility
A2AMessageType = MessageType


class MessagePriority(str, Enum):
    """Message priority levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ProtocolVersion(str, Enum):
    """A2A protocol versions"""
    V1 = "1.0"
    V2 = "2.0"


class A2AMessage(BaseModel):
    """Agent-to-Agent message structure"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    version: ProtocolVersion = Field(default=ProtocolVersion.V2)
    type: MessageType
    priority: MessagePriority = Field(default=MessagePriority.MEDIUM)
    
    # Sender/Receiver info
    sender_id: str
    sender_type: str
    receiver_id: Optional[str] = None  # None for broadcasts
    receiver_type: Optional[str] = None
    
    # Message content
    subject: str
    payload: Dict[str, Any]
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    # Timing
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    expires_at: Optional[datetime] = None
    
    # Security
    signature: Optional[str] = None
    encrypted: bool = False
    
    # Routing
    reply_to: Optional[str] = None  # Message ID to reply to
    correlation_id: Optional[str] = None  # For tracking conversations
    route_path: List[str] = Field(default_factory=list)  # Agent IDs in routing path


class A2AResponse(BaseModel):
    """Response structure for A2A communications"""
    success: bool
    message_id: str
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class AgentCapability(BaseModel):
    """Agent capability definition"""
    name: str
    description: str
    version: str
    parameters: Dict[str, Any] = Field(default_factory=dict)
    constraints: Dict[str, Any] = Field(default_factory=dict)


class AgentHandshake(BaseModel):
    """Handshake information for agent discovery"""
    agent_id: str
    agent_type: str
    capabilities: List[AgentCapability]
    protocols: List[ProtocolVersion]
    metadata: Dict[str, Any] = Field(default_factory=dict)


class A2AProtocolHandler:
    """Enhanced Agent-to-Agent communication protocol handler"""
    
    def __init__(self):
        self.active_connections: Dict[str, Dict[str, Any]] = {}
        self.message_handlers: Dict[MessageType, List[Callable]] = {
            msg_type: [] for msg_type in MessageType
        }
        self.capability_registry: Dict[str, List[AgentCapability]] = {}
        self.conversation_contexts: Dict[str, Dict[str, Any]] = {}
        self._heartbeat_interval = 30  # seconds
        self._message_ttl = 300  # seconds (5 minutes)
        
    async def initialize(self):
        """Initialize the A2A protocol handler"""
        logger.info("Initializing A2A protocol handler")
        
        # Start heartbeat monitor
        asyncio.create_task(self._heartbeat_monitor())
        
        # Subscribe to A2A message topics
        await message_queue_service.subscribe(
            "a2a.messages",
            self._handle_queue_message
        )
        
    async def register_agent(
        self,
        agent_id: str,
        agent_type: str,
        capabilities: List[AgentCapability],
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Register an agent with the A2A protocol"""
        try:
            # Store agent connection info
            self.active_connections[agent_id] = {
                "agent_type": agent_type,
                "capabilities": capabilities,
                "metadata": metadata or {},
                "registered_at": datetime.utcnow(),
                "last_heartbeat": datetime.utcnow(),
                "status": "active"
            }
            
            # Register capabilities
            self.capability_registry[agent_id] = capabilities
            
            # Broadcast agent availability
            await self._broadcast_agent_status(agent_id, "online")
            
            logger.info(f"Agent {agent_id} registered with A2A protocol")
            return True
            
        except Exception as e:
            logger.error(f"Failed to register agent {agent_id}: {e}")
            return False
    
    async def unregister_agent(self, agent_id: str) -> bool:
        """Unregister an agent from the A2A protocol"""
        try:
            if agent_id in self.active_connections:
                # Broadcast agent offline status
                await self._broadcast_agent_status(agent_id, "offline")
                
                # Clean up connections
                del self.active_connections[agent_id]
                
                # Clean up capabilities
                if agent_id in self.capability_registry:
                    del self.capability_registry[agent_id]
                
                # Clean up conversations
                self._cleanup_agent_conversations(agent_id)
                
                logger.info(f"Agent {agent_id} unregistered from A2A protocol")
                return True
                
            return False
            
        except Exception as e:
            logger.error(f"Failed to unregister agent {agent_id}: {e}")
            return False
    
    async def send_message(
        self,
        message: A2AMessage,
        wait_for_response: bool = False,
        timeout: int = 30
    ) -> A2AResponse:
        """Send a message to another agent"""
        try:
            # Validate message
            if not await self._validate_message(message):
                return A2AResponse(
                    success=False,
                    message_id=message.id,
                    error="Message validation failed"
                )
            
            # Check if receiver is online (for direct messages)
            if message.receiver_id and message.receiver_id not in self.active_connections:
                return A2AResponse(
                    success=False,
                    message_id=message.id,
                    error=f"Receiver {message.receiver_id} is not online"
                )
            
            # Add routing information
            message.route_path.append(message.sender_id)
            
            # Store message for tracking
            await self._store_message(message)
            
            # Send via message queue
            await message_queue_service.publish(
                "a2a.messages",
                message.dict()
            )
            
            # Wait for response if requested
            if wait_for_response:
                response = await self._wait_for_response(
                    message.id,
                    timeout
                )
                return response
            
            return A2AResponse(
                success=True,
                message_id=message.id,
                data={"status": "sent"}
            )
            
        except Exception as e:
            logger.error(f"Failed to send A2A message: {e}")
            return A2AResponse(
                success=False,
                message_id=message.id,
                error=str(e)
            )
    
    async def broadcast_message(
        self,
        sender_id: str,
        subject: str,
        payload: Dict[str, Any],
        agent_filter: Optional[Callable[[Dict[str, Any]], bool]] = None
    ) -> A2AResponse:
        """Broadcast a message to multiple agents"""
        try:
            # Create broadcast message
            message = A2AMessage(
                type=MessageType.BROADCAST,
                sender_id=sender_id,
                sender_type=self.active_connections[sender_id]["agent_type"],
                subject=subject,
                payload=payload
            )
            
            # Get target agents
            target_agents = []
            for agent_id, agent_info in self.active_connections.items():
                if agent_id != sender_id and agent_info["status"] == "active":
                    if not agent_filter or agent_filter(agent_info):
                        target_agents.append(agent_id)
            
            # Send to each target
            sent_count = 0
            for target_id in target_agents:
                message.receiver_id = target_id
                message.receiver_type = self.active_connections[target_id]["agent_type"]
                
                await message_queue_service.publish(
                    "a2a.messages",
                    message.dict()
                )
                sent_count += 1
            
            return A2AResponse(
                success=True,
                message_id=message.id,
                data={
                    "broadcast_to": sent_count,
                    "target_agents": target_agents
                }
            )
            
        except Exception as e:
            logger.error(f"Failed to broadcast message: {e}")
            return A2AResponse(
                success=False,
                message_id=str(uuid.uuid4()),
                error=str(e)
            )
    
    async def establish_conversation(
        self,
        initiator_id: str,
        participant_ids: List[str],
        context: Dict[str, Any]
    ) -> str:
        """Establish a conversation context between agents"""
        conversation_id = str(uuid.uuid4())
        
        self.conversation_contexts[conversation_id] = {
            "id": conversation_id,
            "initiator": initiator_id,
            "participants": [initiator_id] + participant_ids,
            "context": context,
            "created_at": datetime.utcnow(),
            "messages": [],
            "status": "active"
        }
        
        # Notify participants
        for participant_id in participant_ids:
            await self.send_message(
                A2AMessage(
                    type=MessageType.HANDSHAKE,
                    sender_id=initiator_id,
                    sender_type=self.active_connections[initiator_id]["agent_type"],
                    receiver_id=participant_id,
                    receiver_type=self.active_connections[participant_id]["agent_type"],
                    subject="conversation.invite",
                    payload={
                        "conversation_id": conversation_id,
                        "context": context
                    },
                    correlation_id=conversation_id
                )
            )
        
        return conversation_id
    
    async def query_agent_capabilities(
        self,
        agent_id: Optional[str] = None,
        capability_name: Optional[str] = None
    ) -> Dict[str, List[AgentCapability]]:
        """Query available agent capabilities"""
        results = {}
        
        if agent_id:
            # Query specific agent
            if agent_id in self.capability_registry:
                capabilities = self.capability_registry[agent_id]
                if capability_name:
                    capabilities = [
                        cap for cap in capabilities
                        if cap.name == capability_name
                    ]
                results[agent_id] = capabilities
        else:
            # Query all agents
            for aid, capabilities in self.capability_registry.items():
                if capability_name:
                    capabilities = [
                        cap for cap in capabilities
                        if cap.name == capability_name
                    ]
                if capabilities:
                    results[aid] = capabilities
        
        return results
    
    async def negotiate_protocol(
        self,
        agent1_id: str,
        agent2_id: str
    ) -> ProtocolVersion:
        """Negotiate protocol version between two agents"""
        # For now, return the highest common version
        # In a real implementation, this would involve handshake
        return ProtocolVersion.V2
    
    def register_message_handler(
        self,
        message_type: MessageType,
        handler: Callable
    ):
        """Register a handler for specific message types"""
        self.message_handlers[message_type].append(handler)
    
    async def _handle_queue_message(self, message_data: Dict[str, Any]):
        """Handle incoming messages from the queue"""
        try:
            message = A2AMessage(**message_data)
            
            # Update heartbeat for sender
            if message.sender_id in self.active_connections:
                self.active_connections[message.sender_id]["last_heartbeat"] = datetime.utcnow()
            
            # Route message to appropriate handler
            handlers = self.message_handlers.get(message.type, [])
            for handler in handlers:
                try:
                    await handler(message)
                except Exception as e:
                    logger.error(f"Handler error for message {message.id}: {e}")
            
            # Store in conversation context if applicable
            if message.correlation_id and message.correlation_id in self.conversation_contexts:
                self.conversation_contexts[message.correlation_id]["messages"].append(message)
            
            # Handle specific message types
            if message.type == MessageType.HEARTBEAT:
                await self._handle_heartbeat(message)
            elif message.type == MessageType.HANDSHAKE:
                await self._handle_handshake(message)
            elif message.type == MessageType.TERMINATE:
                await self._handle_terminate(message)
                
        except Exception as e:
            logger.error(f"Failed to handle queue message: {e}")
    
    async def _handle_heartbeat(self, message: A2AMessage):
        """Handle heartbeat messages"""
        if message.sender_id in self.active_connections:
            self.active_connections[message.sender_id]["last_heartbeat"] = datetime.utcnow()
            self.active_connections[message.sender_id]["status"] = "active"
    
    async def _handle_handshake(self, message: A2AMessage):
        """Handle handshake messages"""
        if message.subject == "conversation.invite":
            # Auto-accept conversation invites for now
            conversation_id = message.payload.get("conversation_id")
            if conversation_id and conversation_id in self.conversation_contexts:
                # Add to participants if not already there
                if message.receiver_id not in self.conversation_contexts[conversation_id]["participants"]:
                    self.conversation_contexts[conversation_id]["participants"].append(message.receiver_id)
    
    async def _handle_terminate(self, message: A2AMessage):
        """Handle termination messages"""
        if message.correlation_id and message.correlation_id in self.conversation_contexts:
            self.conversation_contexts[message.correlation_id]["status"] = "terminated"
    
    async def _heartbeat_monitor(self):
        """Monitor agent heartbeats"""
        while True:
            try:
                current_time = datetime.utcnow()
                timeout_threshold = current_time - timedelta(seconds=self._heartbeat_interval * 2)
                
                for agent_id, info in list(self.active_connections.items()):
                    if info["last_heartbeat"] < timeout_threshold and info["status"] == "active":
                        # Mark as inactive
                        self.active_connections[agent_id]["status"] = "inactive"
                        await self._broadcast_agent_status(agent_id, "inactive")
                
                await asyncio.sleep(self._heartbeat_interval)
                
            except Exception as e:
                logger.error(f"Heartbeat monitor error: {e}")
                await asyncio.sleep(5)
    
    async def _broadcast_agent_status(self, agent_id: str, status: str):
        """Broadcast agent status change"""
        await message_queue_service.publish(
            "a2a.status",
            {
                "agent_id": agent_id,
                "status": status,
                "timestamp": datetime.utcnow().isoformat()
            }
        )
    
    async def _validate_message(self, message: A2AMessage) -> bool:
        """Validate A2A message"""
        # Check expiration
        if message.expires_at and message.expires_at < datetime.utcnow():
            return False
        
        # Check sender exists
        if message.sender_id not in self.active_connections:
            return False
        
        # Validate routing loop
        if message.sender_id in message.route_path:
            return False
        
        return True
    
    async def _store_message(self, message: A2AMessage):
        """Store message for tracking and history"""
        # In production, this would persist to database
        # For now, we'll use vector DB for semantic search
        try:
            vector_db = await get_vector_db()
            
            # Create searchable content
            content = f"{message.subject} {json.dumps(message.payload)}"
            
            await vector_db.store_agent_knowledge(
                agent_id=message.sender_id,
                content=content,
                metadata={
                    "message_id": message.id,
                    "type": message.type,
                    "receiver_id": message.receiver_id,
                    "timestamp": message.timestamp.isoformat(),
                    "correlation_id": message.correlation_id
                },
                category="a2a_message"
            )
        except Exception as e:
            logger.error(f"Failed to store message: {e}")
    
    async def _wait_for_response(self, message_id: str, timeout: int) -> A2AResponse:
        """Wait for a response to a message"""
        # In production, this would use proper async response tracking
        # For now, return a timeout response
        await asyncio.sleep(1)  # Simulate wait
        
        return A2AResponse(
            success=False,
            message_id=message_id,
            error="Response timeout"
        )
    
    def _cleanup_agent_conversations(self, agent_id: str):
        """Clean up conversations for a disconnected agent"""
        for conv_id, conv in list(self.conversation_contexts.items()):
            if agent_id in conv["participants"]:
                conv["participants"].remove(agent_id)
                if len(conv["participants"]) < 2:
                    # Remove conversation if less than 2 participants
                    del self.conversation_contexts[conv_id]
    
    async def get_conversation_history(
        self,
        conversation_id: str,
        limit: int = 50
    ) -> List[A2AMessage]:
        """Get conversation history"""
        if conversation_id in self.conversation_contexts:
            messages = self.conversation_contexts[conversation_id]["messages"]
            return messages[-limit:]
        return []
    
    async def search_messages(
        self,
        query: str,
        agent_id: Optional[str] = None,
        message_type: Optional[MessageType] = None,
        limit: int = 20
    ) -> List[Dict[str, Any]]:
        """Search through A2A messages using semantic search"""
        try:
            vector_db = await get_vector_db()
            
            results = await vector_db.search_agent_knowledge(
                query=query,
                agent_id=agent_id,
                category="a2a_message",
                limit=limit
            )
            
            # Filter by message type if specified
            if message_type:
                results = [
                    r for r in results
                    if r.get("metadata", {}).get("type") == message_type
                ]
            
            return results
            
        except Exception as e:
            logger.error(f"Failed to search messages: {e}")
            return []


# Global A2A protocol handler instance
a2a_protocol = A2AProtocolHandler()


async def get_a2a_protocol() -> A2AProtocolHandler:
    """Get the A2A protocol handler instance"""
    return a2a_protocol