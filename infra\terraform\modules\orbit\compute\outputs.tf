# Compute Engine Module Outputs

output "instance_name" {
  description = "Name of the compute instance"
  value       = google_compute_instance.platform_instance.name
}

output "instance_id" {
  description = "ID of the compute instance"
  value       = google_compute_instance.platform_instance.instance_id
}

output "instance_self_link" {
  description = "Self link of the compute instance"
  value       = google_compute_instance.platform_instance.self_link
}

output "instance_zone" {
  description = "Zone of the compute instance"
  value       = google_compute_instance.platform_instance.zone
}

output "instance_internal_ip" {
  description = "Internal IP address of the compute instance"
  value       = google_compute_instance.platform_instance.network_interface[0].network_ip
}

output "instance_external_ip" {
  description = "External IP address of the compute instance (if assigned)"
  value       = length(google_compute_instance.platform_instance.network_interface[0].access_config) > 0 ? google_compute_instance.platform_instance.network_interface[0].access_config[0].nat_ip : null
}

output "static_ip_address" {
  description = "Static IP address (if created)"
  value       = var.create_static_ip ? google_compute_address.platform_ip[0].address : null
}

output "service_account_email" {
  description = "Email of the service account used by the instance"
  value       = google_service_account.compute_sa.email
}

output "service_account_id" {
  description = "ID of the service account used by the instance"
  value       = google_service_account.compute_sa.id
}

# Instance group outputs
output "instance_group_name" {
  description = "Name of the instance group"
  value       = google_compute_instance_group.platform_group.name
}

output "instance_group_self_link" {
  description = "Self link of the instance group"
  value       = google_compute_instance_group.platform_group.self_link
}

# Backend service outputs
output "backend_service_name" {
  description = "Name of the backend service"
  value       = google_compute_backend_service.platform_backend.name
}

output "backend_service_self_link" {
  description = "Self link of the backend service"
  value       = google_compute_backend_service.platform_backend.self_link
}

# Health check outputs
output "health_check_name" {
  description = "Name of the health check"
  value       = google_compute_health_check.gateway_health_check.name
}

output "health_check_self_link" {
  description = "Self link of the health check"
  value       = google_compute_health_check.gateway_health_check.self_link
}

# Service endpoints
output "service_endpoints" {
  description = "Service endpoints for the platform"
  value = {
    auth_service = "http://${google_compute_instance.platform_instance.network_interface[0].network_ip}:${var.auth_service_port}"
    crm_backend  = "http://${google_compute_instance.platform_instance.network_interface[0].network_ip}:${var.crm_service_port}"
    gateway      = "http://${google_compute_instance.platform_instance.network_interface[0].network_ip}:${var.gateway_port}"
  }
}

# SSH connection command
output "ssh_command" {
  description = "Command to SSH into the instance"
  value       = "gcloud compute ssh ${google_compute_instance.platform_instance.name} --zone=${google_compute_instance.platform_instance.zone}"
}

# Docker management commands
output "docker_commands" {
  description = "Useful Docker commands for managing the platform"
  value = {
    logs_auth    = "gcloud compute ssh ${google_compute_instance.platform_instance.name} --zone=${google_compute_instance.platform_instance.zone} --command='docker logs platform-auth'"
    logs_crm     = "gcloud compute ssh ${google_compute_instance.platform_instance.name} --zone=${google_compute_instance.platform_instance.zone} --command='docker logs platform-crm-backend'"
    logs_gateway = "gcloud compute ssh ${google_compute_instance.platform_instance.name} --zone=${google_compute_instance.platform_instance.zone} --command='docker logs platform-gateway'"
    status       = "gcloud compute ssh ${google_compute_instance.platform_instance.name} --zone=${google_compute_instance.platform_instance.zone} --command='docker compose -f /opt/platform/docker-compose.yml ps'"
    restart      = "gcloud compute ssh ${google_compute_instance.platform_instance.name} --zone=${google_compute_instance.platform_instance.zone} --command='sudo systemctl restart platform-services'"
    update       = "gcloud compute ssh ${google_compute_instance.platform_instance.name} --zone=${google_compute_instance.platform_instance.zone} --command='sudo /opt/platform/update-services.sh'"
  }
}# Additional outputs for Ansible integration

output "ansible_ssh_private_key_path" {
  description = "Path to the Ansible SSH private key"
  value       = local_sensitive_file.ansible_private_key.filename
  sensitive   = true
}

output "ansible_inventory_path" {
  description = "Path to the generated Ansible inventory file"
  value       = local_file.ansible_inventory.filename
}

output "ansible_vars_path" {
  description = "Path to the generated Ansible variables file"
  value       = local_file.ansible_vars.filename
}

output "ansible_user" {
  description = "Username for Ansible SSH access"
  value       = "ansible"
}

output "ansible_commands" {
  description = "Useful Ansible commands for managing the platform"
  value = {
    deploy_services = "cd ${path.root}/../ansible && ansible-playbook -i inventory/terraform-${var.environment}.yml playbooks/deploy-services.yml"
    update_config   = "cd ${path.root}/../ansible && ansible-playbook -i inventory/terraform-${var.environment}.yml playbooks/update-config.yml"
    update_images   = "cd ${path.root}/../ansible && ansible-playbook -i inventory/terraform-${var.environment}.yml playbooks/update-images.yml"
    rollback        = "cd ${path.root}/../ansible && ansible-playbook -i inventory/terraform-${var.environment}.yml playbooks/rollback.yml"
    check_health    = "cd ${path.root}/../ansible && ansible ${google_compute_instance.platform_instance.name} -i inventory/terraform-${var.environment}.yml -m uri -a 'url=http://localhost/health'"
  }
}

output "deployment_metadata" {
  description = "Deployment metadata for tracking"
  value = {
    deployment_color = var.initial_deployment_color
    service_versions = var.service_versions
    environment      = var.environment
    instance_labels  = google_compute_instance.platform_instance.labels
  }
}