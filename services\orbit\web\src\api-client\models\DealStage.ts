/* tslint:disable */
/* eslint-disable */
/**
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface DealStage
 */
export interface DealStage {
    /**
     * 
     * @type {string}
     * @memberof DealStage
     */
    id?: string;
    /**
     * 
     * @type {string}
     * @memberof DealStage
     */
    name?: string;
    /**
     * 
     * @type {number}
     * @memberof DealStage
     */
    pipelineOrder?: number;
    /**
     * 
     * @type {boolean}
     * @memberof DealStage
     */
    isClosedWon?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof DealStage
     */
    isClosedLost?: boolean;
    /**
     * 
     * @type {Date}
     * @memberof DealStage
     */
    createdAt?: Date;
}

/**
 * Check if a given object implements the DealStage interface.
 */
export function instanceOfDealStage(value: object): value is DealStage {
    return true;
}

export function DealStageFromJSON(json: any): DealStage {
    return DealStageFromJSONTyped(json, false);
}

export function DealStageFromJSONTyped(json: any, ignoreDiscriminator: boolean): DealStage {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'] == null ? undefined : json['id'],
        'name': json['name'] == null ? undefined : json['name'],
        'pipelineOrder': json['pipeline_order'] == null ? undefined : json['pipeline_order'],
        'isClosedWon': json['is_closed_won'] == null ? undefined : json['is_closed_won'],
        'isClosedLost': json['is_closed_lost'] == null ? undefined : json['is_closed_lost'],
        'createdAt': json['created_at'] == null ? undefined : (new Date(json['created_at'])),
    };
}

  export function DealStageToJSON(json: any): DealStage {
      return DealStageToJSONTyped(json, false);
  }

  export function DealStageToJSONTyped(value?: DealStage | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'name': value['name'],
        'pipeline_order': value['pipelineOrder'],
        'is_closed_won': value['isClosedWon'],
        'is_closed_lost': value['isClosedLost'],
        'created_at': value['createdAt'] == null ? undefined : ((value['createdAt']).toISOString()),
    };
}

