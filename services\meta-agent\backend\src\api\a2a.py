"""
Agent-to-Agent (A2A) Protocol API endpoints
"""

import logging
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, Query
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session

from database.connection import get_db
from database.models import User, Agent
from protocols.a2a import (
    a2a_protocol, get_a2a_protocol, A2AMessage, A2AResponse,
    MessageType, MessagePriority, ProtocolVersion, AgentCapability,
    AgentHandshake, A2AProtocolHandler
)
from auth.dependencies import get_current_user

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/a2a", tags=["a2a-protocol"])


# Request/Response Models
class RegisterAgentRequest(BaseModel):
    agent_id: str = Field(..., description="Agent ID to register")
    agent_type: str = Field(..., description="Type of agent")
    capabilities: List[AgentCapability] = Field(..., description="Agent capabilities")
    metadata: Optional[Dict[str, Any]] = Field(default=None, description="Additional metadata")


class RegisterAgentResponse(BaseModel):
    success: bool
    message: str


class SendMessageRequest(BaseModel):
    receiver_id: Optional[str] = Field(None, description="Target agent ID (null for broadcast)")
    receiver_type: Optional[str] = Field(None, description="Target agent type")
    subject: str = Field(..., description="Message subject")
    payload: Dict[str, Any] = Field(..., description="Message payload")
    priority: MessagePriority = Field(default=MessagePriority.MEDIUM)
    type: MessageType = Field(default=MessageType.REQUEST)
    reply_to: Optional[str] = Field(None, description="Message ID to reply to")
    correlation_id: Optional[str] = Field(None, description="Conversation correlation ID")
    expires_in_seconds: Optional[int] = Field(None, ge=1, le=3600, description="Message expiration time")
    wait_for_response: bool = Field(default=False, description="Wait for response")
    timeout: int = Field(default=30, ge=1, le=300, description="Response timeout in seconds")


class BroadcastMessageRequest(BaseModel):
    subject: str = Field(..., description="Broadcast subject")
    payload: Dict[str, Any] = Field(..., description="Message payload")
    filter_agent_types: Optional[List[str]] = Field(None, description="Filter by agent types")
    filter_capabilities: Optional[List[str]] = Field(None, description="Filter by capabilities")
    priority: MessagePriority = Field(default=MessagePriority.MEDIUM)


class EstablishConversationRequest(BaseModel):
    participant_ids: List[str] = Field(..., description="List of participant agent IDs")
    context: Dict[str, Any] = Field(..., description="Conversation context")


class EstablishConversationResponse(BaseModel):
    success: bool
    conversation_id: str
    participants: List[str]


class QueryCapabilitiesRequest(BaseModel):
    agent_id: Optional[str] = Field(None, description="Specific agent ID to query")
    capability_name: Optional[str] = Field(None, description="Specific capability to search for")


class QueryCapabilitiesResponse(BaseModel):
    capabilities: Dict[str, List[AgentCapability]]
    total_agents: int


class SearchMessagesRequest(BaseModel):
    query: str = Field(..., min_length=1, max_length=1000, description="Search query")
    agent_id: Optional[str] = Field(None, description="Filter by agent ID")
    message_type: Optional[MessageType] = Field(None, description="Filter by message type")
    limit: int = Field(default=20, ge=1, le=100, description="Maximum results")


class MessageSearchResult(BaseModel):
    id: str
    score: float
    content: str
    agent_id: str
    metadata: Dict[str, Any]
    created_at: str


class SearchMessagesResponse(BaseModel):
    success: bool
    results: List[MessageSearchResult]
    total_found: int


class ConversationMessage(BaseModel):
    id: str
    type: MessageType
    sender_id: str
    receiver_id: Optional[str]
    subject: str
    payload: Dict[str, Any]
    timestamp: str


class ConversationHistoryResponse(BaseModel):
    success: bool
    conversation_id: str
    messages: List[ConversationMessage]
    total_messages: int


class AgentStatusResponse(BaseModel):
    agent_id: str
    status: str
    agent_type: str
    capabilities: List[AgentCapability]
    last_heartbeat: str
    registered_at: str
    metadata: Dict[str, Any]


class ActiveAgentsResponse(BaseModel):
    agents: List[AgentStatusResponse]
    total_active: int
    total_inactive: int


# API Endpoints

@router.post("/register", response_model=RegisterAgentResponse)
async def register_agent(
    request: RegisterAgentRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    protocol: A2AProtocolHandler = Depends(get_a2a_protocol)
):
    """Register an agent with the A2A protocol"""
    try:
        # Verify agent ownership
        agent = db.query(Agent).filter(
            Agent.id == request.agent_id,
            Agent.user_id == current_user.id
        ).first()
        
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent not found or access denied"
            )
        
        # Register with protocol
        success = await protocol.register_agent(
            agent_id=request.agent_id,
            agent_type=request.agent_type,
            capabilities=request.capabilities,
            metadata=request.metadata
        )
        
        if success:
            return RegisterAgentResponse(
                success=True,
                message=f"Agent {request.agent_id} registered successfully"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to register agent"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to register agent: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to register agent: {str(e)}"
        )


@router.delete("/unregister/{agent_id}", response_model=RegisterAgentResponse)
async def unregister_agent(
    agent_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    protocol: A2AProtocolHandler = Depends(get_a2a_protocol)
):
    """Unregister an agent from the A2A protocol"""
    try:
        # Verify agent ownership
        agent = db.query(Agent).filter(
            Agent.id == agent_id,
            Agent.user_id == current_user.id
        ).first()
        
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent not found or access denied"
            )
        
        # Unregister from protocol
        success = await protocol.unregister_agent(agent_id)
        
        if success:
            return RegisterAgentResponse(
                success=True,
                message=f"Agent {agent_id} unregistered successfully"
            )
        else:
            return RegisterAgentResponse(
                success=False,
                message=f"Agent {agent_id} was not registered"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to unregister agent: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to unregister agent: {str(e)}"
        )


@router.post("/send/{sender_id}", response_model=A2AResponse)
async def send_message(
    sender_id: str,
    request: SendMessageRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    protocol: A2AProtocolHandler = Depends(get_a2a_protocol)
):
    """Send a message from one agent to another"""
    try:
        # Verify sender ownership
        sender = db.query(Agent).filter(
            Agent.id == sender_id,
            Agent.user_id == current_user.id
        ).first()
        
        if not sender:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Sender agent not found or access denied"
            )
        
        # Get sender info from protocol
        if sender_id not in protocol.active_connections:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Sender agent is not registered with A2A protocol"
            )
        
        sender_info = protocol.active_connections[sender_id]
        
        # Create message
        message = A2AMessage(
            type=request.type,
            priority=request.priority,
            sender_id=sender_id,
            sender_type=sender_info["agent_type"],
            receiver_id=request.receiver_id,
            receiver_type=request.receiver_type,
            subject=request.subject,
            payload=request.payload,
            reply_to=request.reply_to,
            correlation_id=request.correlation_id
        )
        
        # Set expiration if specified
        if request.expires_in_seconds:
            message.expires_at = datetime.utcnow() + timedelta(seconds=request.expires_in_seconds)
        
        # Send message
        response = await protocol.send_message(
            message,
            wait_for_response=request.wait_for_response,
            timeout=request.timeout
        )
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to send message: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to send message: {str(e)}"
        )


@router.post("/broadcast/{sender_id}", response_model=A2AResponse)
async def broadcast_message(
    sender_id: str,
    request: BroadcastMessageRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    protocol: A2AProtocolHandler = Depends(get_a2a_protocol)
):
    """Broadcast a message to multiple agents"""
    try:
        # Verify sender ownership
        sender = db.query(Agent).filter(
            Agent.id == sender_id,
            Agent.user_id == current_user.id
        ).first()
        
        if not sender:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Sender agent not found or access denied"
            )
        
        # Create filter function
        def agent_filter(agent_info: Dict[str, Any]) -> bool:
            # Filter by agent types
            if request.filter_agent_types:
                if agent_info["agent_type"] not in request.filter_agent_types:
                    return False
            
            # Filter by capabilities
            if request.filter_capabilities:
                agent_capabilities = [cap.name for cap in agent_info.get("capabilities", [])]
                if not any(cap in agent_capabilities for cap in request.filter_capabilities):
                    return False
            
            return True
        
        # Broadcast message
        response = await protocol.broadcast_message(
            sender_id=sender_id,
            subject=request.subject,
            payload=request.payload,
            agent_filter=agent_filter if (request.filter_agent_types or request.filter_capabilities) else None
        )
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to broadcast message: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to broadcast message: {str(e)}"
        )


@router.post("/conversation/establish/{initiator_id}", response_model=EstablishConversationResponse)
async def establish_conversation(
    initiator_id: str,
    request: EstablishConversationRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    protocol: A2AProtocolHandler = Depends(get_a2a_protocol)
):
    """Establish a conversation between multiple agents"""
    try:
        # Verify initiator ownership
        initiator = db.query(Agent).filter(
            Agent.id == initiator_id,
            Agent.user_id == current_user.id
        ).first()
        
        if not initiator:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Initiator agent not found or access denied"
            )
        
        # Establish conversation
        conversation_id = await protocol.establish_conversation(
            initiator_id=initiator_id,
            participant_ids=request.participant_ids,
            context=request.context
        )
        
        return EstablishConversationResponse(
            success=True,
            conversation_id=conversation_id,
            participants=[initiator_id] + request.participant_ids
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to establish conversation: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to establish conversation: {str(e)}"
        )


@router.get("/conversation/{conversation_id}/history", response_model=ConversationHistoryResponse)
async def get_conversation_history(
    conversation_id: str,
    limit: int = Query(default=50, ge=1, le=200),
    current_user: User = Depends(get_current_user),
    protocol: A2AProtocolHandler = Depends(get_a2a_protocol)
):
    """Get conversation history"""
    try:
        # Get conversation history
        messages = await protocol.get_conversation_history(conversation_id, limit)
        
        # Convert to response format
        conversation_messages = [
            ConversationMessage(
                id=msg.id,
                type=msg.type,
                sender_id=msg.sender_id,
                receiver_id=msg.receiver_id,
                subject=msg.subject,
                payload=msg.payload,
                timestamp=msg.timestamp.isoformat()
            )
            for msg in messages
        ]
        
        return ConversationHistoryResponse(
            success=True,
            conversation_id=conversation_id,
            messages=conversation_messages,
            total_messages=len(conversation_messages)
        )
        
    except Exception as e:
        logger.error(f"Failed to get conversation history: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get conversation history: {str(e)}"
        )


@router.post("/capabilities/query", response_model=QueryCapabilitiesResponse)
async def query_capabilities(
    request: QueryCapabilitiesRequest,
    current_user: User = Depends(get_current_user),
    protocol: A2AProtocolHandler = Depends(get_a2a_protocol)
):
    """Query agent capabilities"""
    try:
        capabilities = await protocol.query_agent_capabilities(
            agent_id=request.agent_id,
            capability_name=request.capability_name
        )
        
        return QueryCapabilitiesResponse(
            capabilities=capabilities,
            total_agents=len(capabilities)
        )
        
    except Exception as e:
        logger.error(f"Failed to query capabilities: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to query capabilities: {str(e)}"
        )


@router.post("/messages/search", response_model=SearchMessagesResponse)
async def search_messages(
    request: SearchMessagesRequest,
    current_user: User = Depends(get_current_user),
    protocol: A2AProtocolHandler = Depends(get_a2a_protocol)
):
    """Search through A2A messages"""
    try:
        results = await protocol.search_messages(
            query=request.query,
            agent_id=request.agent_id,
            message_type=request.message_type,
            limit=request.limit
        )
        
        # Convert to response format
        search_results = [
            MessageSearchResult(
                id=result["id"],
                score=result["score"],
                content=result["content"],
                agent_id=result["agent_id"],
                metadata=result["metadata"],
                created_at=result["created_at"]
            )
            for result in results
        ]
        
        return SearchMessagesResponse(
            success=True,
            results=search_results,
            total_found=len(search_results)
        )
        
    except Exception as e:
        logger.error(f"Failed to search messages: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to search messages: {str(e)}"
        )


@router.get("/agents/active", response_model=ActiveAgentsResponse)
async def get_active_agents(
    current_user: User = Depends(get_current_user),
    protocol: A2AProtocolHandler = Depends(get_a2a_protocol)
):
    """Get list of active agents"""
    try:
        agents = []
        total_active = 0
        total_inactive = 0
        
        for agent_id, info in protocol.active_connections.items():
            agent_status = AgentStatusResponse(
                agent_id=agent_id,
                status=info["status"],
                agent_type=info["agent_type"],
                capabilities=info.get("capabilities", []),
                last_heartbeat=info["last_heartbeat"].isoformat(),
                registered_at=info["registered_at"].isoformat(),
                metadata=info.get("metadata", {})
            )
            agents.append(agent_status)
            
            if info["status"] == "active":
                total_active += 1
            else:
                total_inactive += 1
        
        return ActiveAgentsResponse(
            agents=agents,
            total_active=total_active,
            total_inactive=total_inactive
        )
        
    except Exception as e:
        logger.error(f"Failed to get active agents: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get active agents: {str(e)}"
        )


@router.get("/agents/{agent_id}/status", response_model=AgentStatusResponse)
async def get_agent_status(
    agent_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    protocol: A2AProtocolHandler = Depends(get_a2a_protocol)
):
    """Get status of a specific agent"""
    try:
        # Verify agent exists
        agent = db.query(Agent).filter(Agent.id == agent_id).first()
        
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent not found"
            )
        
        # Get status from protocol
        if agent_id not in protocol.active_connections:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent is not registered with A2A protocol"
            )
        
        info = protocol.active_connections[agent_id]
        
        return AgentStatusResponse(
            agent_id=agent_id,
            status=info["status"],
            agent_type=info["agent_type"],
            capabilities=info.get("capabilities", []),
            last_heartbeat=info["last_heartbeat"].isoformat(),
            registered_at=info["registered_at"].isoformat(),
            metadata=info.get("metadata", {})
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get agent status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get agent status: {str(e)}"
        )