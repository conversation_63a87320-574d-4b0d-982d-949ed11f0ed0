# Development Environment Terraform Configuration
# This configuration deploys the platform to the dev environment

terraform {
  required_version = ">= 1.5"
  
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
    random = {
      source  = "hashicorp/random"
      version = "~> 3.1"
    }
  }

  backend "gcs" {
    bucket = "agent-dev-459718-terraform-state"  # GCS bucket for Terraform state
    prefix = "terraform/dev"
  }
}

# Configure the Google Cloud Provider
provider "google" {
  project = var.project_id
  region  = var.region
  zone    = var.zone
}

# Local values for dev environment
locals {
  environment = "dev"
  project_name = var.project_name
  
  # Dev-specific configurations
  machine_types = {
    compute = "e2-standard-2"
    database = "db-f1-micro"
  }
  
  # Networking
  vpc_cidr = "10.0.0.0/16"
  public_subnet_cidr = "10.0.1.0/24"
  private_subnet_cidr = "10.0.2.0/24"
  
  # Database settings
  database_config = {
    postgres_version = "POSTGRES_15"
    disk_size_gb = 20
    max_disk_size_gb = 100
    backup_retention_days = 7
    availability_type = "ZONAL"
    deletion_protection = false  # Allow deletion in dev
  }
  
  # Security settings (relaxed for dev)
  enable_ssl = true  # Enable SSL for custom domains
  enable_cloud_armor = false
  enable_public_access = true
  
  # SSL domains for dev
  ssl_domains = var.ssl_domains
  
  # Service ports
  service_ports = {
    auth_service = 8004
    crm_backend = 8003
    gateway = 8085
  }
}

# VPC Network
module "network" {
  source = "../../modules/network"
  
  project_name = local.project_name
  region = var.region
  
  public_subnet_cidr = local.public_subnet_cidr
  private_subnet_cidr = local.private_subnet_cidr
  
  # Allow SSH from anywhere in dev
  ssh_source_ranges = ["0.0.0.0/0"]
}

# Database
module "database" {
  source = "../../modules/database"
  
  project_name = local.project_name
  environment = local.environment
  region = var.region
  
  vpc_network = module.network.vpc_self_link
  
  postgres_version = local.database_config.postgres_version
  instance_tier = local.machine_types.database
  availability_type = local.database_config.availability_type
  disk_size_gb = local.database_config.disk_size_gb
  max_disk_size_gb = local.database_config.max_disk_size_gb
  backup_retention_days = local.database_config.backup_retention_days
  deletion_protection = local.database_config.deletion_protection
  
  database_name = "platform_dev_db"
  database_user = "platform_dev_user"
  
  # TODO: Re-enable migration automation once Cloud Build trigger issues are resolved
  # The Cloud Build trigger was failing with "invalid argument" error
  # Need to investigate GitHub repository connection and permissions
  enable_migration_automation = false
  github_repo_owner = var.github_repo_owner
  github_repo_name = var.github_repo_name
  migration_branch = "main"
}

# OAuth Secrets (with destroy protection)
module "secrets" {
  source = "../../modules/secrets"
  
  project_id = var.project_id
  environment = local.environment
}

# Artifact Registry
module "registry" {
  source = "../../modules/registry"
  
  project_name = local.project_name
  region = var.region
  
  # Less strict cleanup in dev
  cleanup_older_than_days = 7
  cleanup_untagged_older_than_days = 1
  
  enable_cloud_build_access = true
  enable_compute_access = true
  
  # Enable automated builds from main branch
  enable_automated_builds = false
  github_repo_owner = var.github_repo_owner
  github_repo_name = var.github_repo_name
  build_branch_pattern = "main"
  cloudbuild_file_path = "cloudbuild.yaml"
}

# Cloud Storage for static files
module "storage" {
  source = "../../modules/storage"
  
  project_name = local.project_name
  environment = local.environment
  bucket_location = var.region
  
  enable_public_access = true  # Organization policy now allows public access
  enable_versioning = false  # Disable versioning in dev
  
  # Simple lifecycle rules for dev
  lifecycle_rules = [
    {
      action = "Delete"
      age_days = 30
      storage_classes = ["STANDARD"]
    }
  ]
  
  enable_automated_deployment = false
  github_repo_owner = var.github_repo_owner
  github_repo_name = var.github_repo_name
  deploy_branch = "main"
  
  # SPA routing configuration - serve index.html for all routes
  not_found_page = "index.html"
  
  upload_default_files = false
}

# Compute Engine instance
module "compute" {
  source = "../../modules/compute"
  
  project_name = local.project_name
  environment = local.environment
  region = var.region
  zone = var.zone
  
  machine_type = local.machine_types.compute
  boot_disk_size_gb = 20
  docker_volumes_disk_size_gb = 30
  
  subnet_name = module.network.private_subnet_name
  assign_external_ip = true  # Allow external IP for dev debugging
  create_static_ip = false
  
  # Registry configuration
  registry_url = module.registry.registry_hostname
  
  # Database configuration
  database_host = module.database.instance_private_ip
  database_name = module.database.database_name
  database_user = module.database.database_user
  database_password_secret = module.database.database_password_secret_name
  
  # OAuth configuration (using protected secrets)
  google_oauth_client_id_secret = module.secrets.oauth_secrets.client_id
  google_oauth_client_secret_secret = module.secrets.oauth_secrets.client_secret
  oauth_state_secret = module.secrets.oauth_secrets.state_secret
  
  # Service ports
  auth_service_port = local.service_ports.auth_service
  crm_service_port = local.service_ports.crm_backend
  gateway_port = local.service_ports.gateway
}

# Load Balancer
module "loadbalancer" {
  source = "../../modules/loadbalancer"
  
  project_name = local.project_name
  environment = local.environment
  
  storage_bucket_name = module.storage.bucket_name
  api_backend_service = module.compute.backend_service_self_link
  
  # Enable SSL with custom domains
  enable_ssl = local.enable_ssl
  use_managed_ssl = true
  ssl_domains = local.ssl_domains
  enable_http_redirect = false  # Disable HTTP to HTTPS redirect for testing
  
  # Disable CDN for dev (no caching during active development)
  enable_cdn = false
  
  # No security policies in dev
  enable_cloud_armor = local.enable_cloud_armor
}