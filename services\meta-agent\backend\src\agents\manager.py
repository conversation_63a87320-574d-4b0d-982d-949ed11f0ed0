"""
AI Agent Platform - Agent Manager
"""

import asyncio
from typing import Dict, List, Optional, Any
from uuid import UUID
import structlog

from .runtime import Agent<PERSON>unt<PERSON>, RuntimeStatus
from database.models import Agent, AgentStatus
from config.settings import settings

logger = structlog.get_logger()


class AgentManager:
    """Manages multiple agent runtimes"""
    
    def __init__(self):
        self.runtimes: Dict[UUID, AgentRuntime] = {}
        self.max_concurrent_agents = settings.agents.max_concurrent_agents
        self.startup_timeout = settings.agents.agent_startup_timeout
        
        logger.info("Agent manager initialized", max_agents=self.max_concurrent_agents)
    
    async def start_agent(self, agent: Agent) -> bool:
        """Start an agent runtime"""
        try:
            agent_id = agent.id
            
            # Check if agent is already running
            if agent_id in self.runtimes:
                runtime = self.runtimes[agent_id]
                if runtime.status in [RuntimeStatus.RUNNING, RuntimeStatus.READY]:
                    logger.warning("Agent already running", agent_id=str(agent_id))
                    return True
            
            # Check concurrent agent limit
            running_count = len([r for r in self.runtimes.values() 
                               if r.status == RuntimeStatus.RUNNING])
            
            if running_count >= self.max_concurrent_agents:
                logger.error(
                    "Maximum concurrent agents exceeded",
                    current_count=running_count,
                    max_allowed=self.max_concurrent_agents
                )
                return False
            
            # Create and configure runtime
            runtime_config = {
                **agent.config,
                'capabilities': agent.capabilities,
                'constraints': agent.constraints,
                'type': agent.type
            }
            
            runtime = AgentRuntime(agent_id, runtime_config)
            
            # Register event callbacks
            await self._register_runtime_callbacks(runtime)
            
            # Start runtime with timeout
            start_task = asyncio.create_task(runtime.start())
            
            try:
                success = await asyncio.wait_for(start_task, timeout=self.startup_timeout)
                
                if success:
                    self.runtimes[agent_id] = runtime
                    logger.info("Agent started successfully", agent_id=str(agent_id))
                    return True
                else:
                    logger.error("Agent startup failed", agent_id=str(agent_id))
                    return False
                    
            except asyncio.TimeoutError:
                logger.error(
                    "Agent startup timed out",
                    agent_id=str(agent_id),
                    timeout=self.startup_timeout
                )
                start_task.cancel()
                return False
            
        except Exception as e:
            logger.error("Failed to start agent", agent_id=str(agent.id), error=str(e))
            return False
    
    async def stop_agent(self, agent_id: UUID, graceful: bool = True) -> bool:
        """Stop an agent runtime"""
        try:
            if agent_id not in self.runtimes:
                logger.warning("Agent runtime not found", agent_id=str(agent_id))
                return False
            
            runtime = self.runtimes[agent_id]
            
            # Stop runtime
            success = await runtime.stop(graceful)
            
            if success:
                # Remove from active runtimes
                del self.runtimes[agent_id]
                logger.info("Agent stopped successfully", agent_id=str(agent_id))
                return True
            else:
                logger.error("Failed to stop agent", agent_id=str(agent_id))
                return False
                
        except Exception as e:
            logger.error("Error stopping agent", agent_id=str(agent_id), error=str(e))
            return False
    
    async def pause_agent(self, agent_id: UUID) -> bool:
        """Pause an agent runtime"""
        try:
            if agent_id not in self.runtimes:
                return False
            
            runtime = self.runtimes[agent_id]
            return await runtime.pause()
            
        except Exception as e:
            logger.error("Error pausing agent", agent_id=str(agent_id), error=str(e))
            return False
    
    async def resume_agent(self, agent_id: UUID) -> bool:
        """Resume an agent runtime"""
        try:
            if agent_id not in self.runtimes:
                return False
            
            runtime = self.runtimes[agent_id]
            return await runtime.resume()
            
        except Exception as e:
            logger.error("Error resuming agent", agent_id=str(agent_id), error=str(e))
            return False
    
    async def execute_task(self, agent_id: UUID, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a task on a specific agent"""
        try:
            if agent_id not in self.runtimes:
                raise RuntimeError(f"Agent runtime not found: {agent_id}")
            
            runtime = self.runtimes[agent_id]
            return await runtime.execute_task(task_data)
            
        except Exception as e:
            logger.error(
                "Task execution failed",
                agent_id=str(agent_id),
                error=str(e)
            )
            raise
    
    async def add_task_to_queue(self, agent_id: UUID, task_data: Dict[str, Any]) -> bool:
        """Add task to agent's execution queue"""
        try:
            if agent_id not in self.runtimes:
                return False
            
            runtime = self.runtimes[agent_id]
            return await runtime.add_task_to_queue(task_data)
            
        except Exception as e:
            logger.error(
                "Failed to add task to queue",
                agent_id=str(agent_id),
                error=str(e)
            )
            return False
    
    def get_agent_runtime(self, agent_id: UUID) -> Optional[AgentRuntime]:
        """Get agent runtime"""
        return self.runtimes.get(agent_id)
    
    async def get_runtime_info(self, agent_id: UUID) -> Optional[Dict[str, Any]]:
        """Get agent runtime information"""
        try:
            if agent_id not in self.runtimes:
                return None
            
            runtime = self.runtimes[agent_id]
            return await runtime.get_runtime_info()
            
        except Exception as e:
            logger.error(
                "Failed to get runtime info",
                agent_id=str(agent_id),
                error=str(e)
            )
            return None
    
    async def list_active_agents(self) -> List[Dict[str, Any]]:
        """List all active agent runtimes"""
        try:
            active_agents = []
            
            for agent_id, runtime in self.runtimes.items():
                runtime_info = await runtime.get_runtime_info()
                active_agents.append(runtime_info)
            
            return active_agents
            
        except Exception as e:
            logger.error("Failed to list active agents", error=str(e))
            return []
    
    async def get_system_stats(self) -> Dict[str, Any]:
        """Get system statistics"""
        try:
            total_agents = len(self.runtimes)
            
            status_counts = {}
            for runtime in self.runtimes.values():
                status = runtime.status
                status_counts[status] = status_counts.get(status, 0) + 1
            
            # Calculate resource usage
            total_cpu = sum(r.cpu_usage for r in self.runtimes.values())
            total_memory = sum(r.memory_usage for r in self.runtimes.values())
            
            # Task statistics
            total_tasks_queued = sum(r.task_queue.qsize() for r in self.runtimes.values())
            total_tasks_completed = sum(len(r.task_history) for r in self.runtimes.values())
            
            return {
                'total_agents': total_agents,
                'max_concurrent_agents': self.max_concurrent_agents,
                'status_breakdown': status_counts,
                'resource_usage': {
                    'total_cpu_usage': total_cpu,
                    'total_memory_mb': total_memory,
                    'average_cpu_per_agent': total_cpu / total_agents if total_agents > 0 else 0,
                    'average_memory_per_agent': total_memory / total_agents if total_agents > 0 else 0
                },
                'task_statistics': {
                    'queued_tasks': total_tasks_queued,
                    'completed_tasks': total_tasks_completed
                }
            }
            
        except Exception as e:
            logger.error("Failed to get system stats", error=str(e))
            return {}
    
    async def shutdown_all_agents(self, graceful: bool = True) -> bool:
        """Shutdown all agent runtimes"""
        try:
            logger.info("Shutting down all agents", total_agents=len(self.runtimes))
            
            # Create shutdown tasks for all agents
            shutdown_tasks = []
            for agent_id in list(self.runtimes.keys()):
                task = asyncio.create_task(self.stop_agent(agent_id, graceful))
                shutdown_tasks.append(task)
            
            # Wait for all shutdowns to complete
            if shutdown_tasks:
                results = await asyncio.gather(*shutdown_tasks, return_exceptions=True)
                
                # Check results
                successful_shutdowns = sum(1 for result in results if result is True)
                logger.info(
                    "Agent shutdown completed",
                    successful=successful_shutdowns,
                    total=len(shutdown_tasks)
                )
            
            return True
            
        except Exception as e:
            logger.error("Failed to shutdown all agents", error=str(e))
            return False
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on all agents"""
        try:
            healthy_agents = 0
            unhealthy_agents = 0
            agent_statuses = {}
            
            for agent_id, runtime in self.runtimes.items():
                try:
                    runtime_info = await runtime.get_runtime_info()
                    status = runtime_info['status']
                    
                    agent_statuses[str(agent_id)] = {
                        'status': status,
                        'last_heartbeat': runtime_info.get('last_heartbeat'),
                        'cpu_usage': runtime_info.get('cpu_usage'),
                        'memory_usage': runtime_info.get('memory_usage')
                    }
                    
                    if status in [RuntimeStatus.RUNNING, RuntimeStatus.PAUSED]:
                        healthy_agents += 1
                    else:
                        unhealthy_agents += 1
                        
                except Exception as e:
                    unhealthy_agents += 1
                    agent_statuses[str(agent_id)] = {
                        'status': 'error',
                        'error': str(e)
                    }
            
            return {
                'healthy_agents': healthy_agents,
                'unhealthy_agents': unhealthy_agents,
                'total_agents': len(self.runtimes),
                'agent_statuses': agent_statuses,
                'timestamp': asyncio.get_event_loop().time()
            }
            
        except Exception as e:
            logger.error("Health check failed", error=str(e))
            return {
                'healthy_agents': 0,
                'unhealthy_agents': len(self.runtimes),
                'total_agents': len(self.runtimes),
                'error': str(e)
            }
    
    # Private methods
    
    async def _register_runtime_callbacks(self, runtime: AgentRuntime):
        """Register callbacks for runtime events"""
        
        async def heartbeat_callback(data: Dict[str, Any]):
            """Handle heartbeat events"""
            logger.debug("Agent heartbeat", agent_id=data['agent_id'])
        
        async def error_callback(data: Dict[str, Any]):
            """Handle error events"""
            logger.error("Agent runtime error", agent_id=data['agent_id'], error=data.get('error'))
        
        async def task_completed_callback(data: Dict[str, Any]):
            """Handle task completion events"""
            logger.info("Task completed", agent_id=data['agent_id'], task_id=data.get('task_id'))
        
        # Register callbacks
        runtime.register_event_callback('heartbeat', heartbeat_callback)
        runtime.register_event_callback('error', error_callback)
        runtime.register_event_callback('task_completed', task_completed_callback)


# Global agent manager instance
agent_manager = AgentManager()