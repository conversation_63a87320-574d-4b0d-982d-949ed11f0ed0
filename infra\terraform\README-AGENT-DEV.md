# Agent Dev Platform - Terraform Infrastructure 🇦🇺

Quick setup guide for deploying the Agent Dev platform to GCP in Australia.

## 📋 Project Details

- **Project ID**: `agent-dev-459718`
- **Region**: Australia Southeast 1 (`australia-southeast1`)
- **Zone**: `australia-southeast1-a`
- **Environment**: Development (ready for staging/prod later)

## 🚀 Quick Deployment

### 1. One-Time Setup

```bash
# Authenticate with GCP
gcloud auth login
gcloud auth application-default login

# Run the Australia initialization script
cd terraform
./init-australia.sh
```

### 2. Configure GitHub Integration

Edit `terraform/environments/dev/terraform.tfvars`:

```hcl
# Update these lines with your GitHub details
github_repo_owner = "your-github-username"  # Replace with your username
github_repo_name  = "mono"                  # Your repository name
```

### 3. Deploy Infrastructure

```bash
cd environments/dev
terraform init
terraform plan    # Review what will be created
terraform apply   # Deploy the infrastructure
```

### 4. Deploy Your Application

After infrastructure is ready, build and push your Docker images:

```bash
# From the project root
bazel build //platform/auth:auth_service_image
bazel build //platform/crm_backend:crm_backend_image  
bazel build //platform/gateway:gateway_image

# Get the registry URL from terraform output
REGISTRY_URL=$(cd terraform/environments/dev && terraform output -raw registry_url)

# Tag and push images (example for auth service)
docker tag bazel/platform/auth:auth_service_image ${REGISTRY_URL}/auth-service:latest
docker push ${REGISTRY_URL}/auth-service:latest
```

## 🌐 Access Your Application

After deployment, get the load balancer IP:

```bash
cd terraform/environments/dev
terraform output load_balancer_ip
```

Your application will be available at:
- **Website**: `http://[LOAD_BALANCER_IP]`
- **API**: `http://[LOAD_BALANCER_IP]/api/v1`
- **Health Check**: `http://[LOAD_BALANCER_IP]/health`

## 📊 Infrastructure Created

Your deployment creates:

1. **🗄️ Cloud SQL PostgreSQL** - Managed database in Australia
2. **🖥️ Compute Engine** - VM running your services via Docker Compose
3. **📦 Artifact Registry** - Docker image storage
4. **☁️ Cloud Storage** - Static web file hosting
5. **⚖️ Load Balancer** - Traffic routing and health checks
6. **🌐 VPC Network** - Secure networking with private subnets

## 💰 Estimated Costs

- **Monthly**: ~$70-140 AUD
- **Daily**: ~$2-5 AUD
- **Hourly**: ~$0.10-0.20 AUD

Cost optimized for development with:
- Single zone deployment
- Minimal database instance
- No SSL certificates (HTTP only)
- Standard storage tiers

## 🔧 Common Operations

### SSH into your instance
```bash
gcloud compute ssh platform-platform-dev --zone=australia-southeast1-a
```

### View service logs
```bash
# From your compute instance
sudo docker logs platform-gateway
sudo docker logs platform-auth
sudo docker logs platform-crm-backend
```

### Restart services
```bash
gcloud compute ssh platform-platform-dev --zone=australia-southeast1-a \
  --command='sudo systemctl restart platform-services'
```

### Update Docker images
```bash
gcloud compute ssh platform-platform-dev --zone=australia-southeast1-a \
  --command='sudo /opt/platform/update-services.sh'
```

## 🗄️ Database Access

Connect to your database:

```bash
# Get database password
gcloud secrets versions access latest --secret=platform-db-password

# Connect via Cloud SQL Proxy
gcloud sql connect platform-postgres-dev --user=platform_dev_user
```

## 🧹 Cleanup

To destroy all infrastructure (⚠️ **THIS WILL DELETE EVERYTHING**):

```bash
cd terraform/environments/dev
terraform destroy
```

## 🆘 Need Help?

1. **Check logs**: Use Cloud Logging in GCP Console
2. **Infrastructure issues**: Run `terraform plan` to see changes
3. **Service issues**: SSH into the instance and check Docker logs
4. **Database issues**: Check Cloud SQL logs in GCP Console

## 📈 Next Steps

1. **Add your domain**: Configure DNS to point to the load balancer IP
2. **Enable SSL**: Add domains to terraform.tfvars and redeploy
3. **Set up monitoring**: Enable Cloud Monitoring alerts
4. **Staging environment**: Deploy to `terraform/environments/staging`
5. **CI/CD**: Set up GitHub Actions for automated deployments

---

Your platform is now running in Australia! 🎉🇦🇺