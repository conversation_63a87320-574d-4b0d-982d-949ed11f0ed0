#!/bin/bash
# Auto-detect environment and set variables for Ansible integration
# Usage: source setup-env.sh (must be sourced to set environment variables)

# Function to detect environment from current directory
detect_environment() {
    local current_dir="$(pwd)"
    
    # Check if we're in a terraform environment directory
    if [[ "$current_dir" == *"/terraform/environments/dev"* ]]; then
        echo "dev"
    elif [[ "$current_dir" == *"/terraform/environments/staging"* ]]; then
        echo "staging"
    elif [[ "$current_dir" == *"/terraform/environments/prod"* ]]; then
        echo "prod"
    elif [[ "$current_dir" == *"/terraform"* ]]; then
        # If we're somewhere in terraform but not in environment folder, try to detect from terraform.tfvars
        if [[ -f "terraform.tfvars" ]]; then
            # Try to extract environment from terraform.tfvars
            local env_from_file=$(grep -E '^environment\s*=' terraform.tfvars | sed 's/.*=\s*"\([^"]*\)".*/\1/' | tr -d ' ')
            if [[ -n "$env_from_file" ]]; then
                echo "$env_from_file"
                return
            fi
        fi
        # Default to dev if in terraform directory but can't detect
        echo "dev"
    else
        # If not in terraform directory, default to dev
        echo "dev"
    fi
}

# Function to get project root directory
get_project_root() {
    local current_dir="$(pwd)"
    
    # Look for PROJECT_ROOT marker files
    local search_dir="$current_dir"
    while [[ "$search_dir" != "/" ]]; do
        if [[ -f "$search_dir/WORKSPACE" ]] || [[ -f "$search_dir/go.mod" ]] || [[ -f "$search_dir/terraform/BUILD.bazel" ]]; then
            echo "$search_dir"
            return
        fi
        search_dir="$(dirname "$search_dir")"
    done
    
    # Fallback: try to find based on terraform structure
    if [[ "$current_dir" == *"/terraform"* ]]; then
        echo "${current_dir%%/terraform*}"
    else
        echo "$current_dir"
    fi
}

# Detect environment and project root
DETECTED_ENV=$(detect_environment)
PROJECT_ROOT=$(get_project_root)
TERRAFORM_ROOT="$PROJECT_ROOT/terraform"
ANSIBLE_ROOT="$TERRAFORM_ROOT/ansible"

echo "🔍 Auto-detecting environment..."
echo "📂 Project root: $PROJECT_ROOT"
echo "🏗️  Terraform root: $TERRAFORM_ROOT"
echo "🤖 Ansible root: $ANSIBLE_ROOT"
echo "🎯 Detected environment: $DETECTED_ENV"

# Set common environment variables
export ENVIRONMENT="$DETECTED_ENV"
export PROJECT_ROOT="$PROJECT_ROOT"
export TERRAFORM_ROOT="$TERRAFORM_ROOT"
export ANSIBLE_ROOT="$ANSIBLE_ROOT"

# Set Ansible-specific environment variables
export ANSIBLE_CONFIG="$ANSIBLE_ROOT/ansible.cfg"
export ANSIBLE_HOST_KEY_CHECKING="False"
export ANSIBLE_USER="ansible"

# Try to read configuration from terraform.tfvars in the environment directory
ENV_DIR="$TERRAFORM_ROOT/environments/$DETECTED_ENV"
if [[ -f "$ENV_DIR/terraform.tfvars" ]]; then
    # Extract project_id from terraform.tfvars
    GCP_PROJECT_ID=$(grep -E '^project_id\s*=' "$ENV_DIR/terraform.tfvars" | cut -d'"' -f2)
    if [[ -n "$GCP_PROJECT_ID" ]]; then
        export GCP_PROJECT_ID="$GCP_PROJECT_ID"
        echo "📋 Found GCP Project ID: $GCP_PROJECT_ID"
    fi
    
    # Extract region from terraform.tfvars
    TFVARS_REGION=$(grep -E '^region\s*=' "$ENV_DIR/terraform.tfvars" | cut -d'"' -f2)
    if [[ -n "$TFVARS_REGION" ]]; then
        export GCP_REGION="$TFVARS_REGION"
        echo "🌍 Found GCP Region: $GCP_REGION"
    fi
    
    # Extract zone from terraform.tfvars
    TFVARS_ZONE=$(grep -E '^zone\s*=' "$ENV_DIR/terraform.tfvars" | cut -d'"' -f2)
    if [[ -n "$TFVARS_ZONE" ]]; then
        export GCP_ZONE="$TFVARS_ZONE"
        echo "📍 Found GCP Zone: $GCP_ZONE"
    fi
fi

# Set default GCP project ID if not found
if [[ -z "$GCP_PROJECT_ID" ]]; then
    case "$DETECTED_ENV" in
        "dev")
            export GCP_PROJECT_ID="crm-platform-dev"
            ;;
        "staging")
            export GCP_PROJECT_ID="crm-platform-staging"
            ;;
        "prod")
            export GCP_PROJECT_ID="crm-platform-prod"
            ;;
        *)
            export GCP_PROJECT_ID="crm-platform-dev"
            ;;
    esac
    echo "⚠️  Using default GCP Project ID: $GCP_PROJECT_ID"
fi

# Set SSH key path for Ansible
ANSIBLE_SSH_KEY="$TERRAFORM_ROOT/modules/compute/ansible-ssh-key.pem"
if [[ -f "$ANSIBLE_SSH_KEY" ]]; then
    export ANSIBLE_SSH_KEY="$ANSIBLE_SSH_KEY"
    echo "🔑 Found Ansible SSH key: $ANSIBLE_SSH_KEY"
else
    echo "⚠️  Ansible SSH key not found at: $ANSIBLE_SSH_KEY"
    echo "    This will be created when Terraform runs"
fi

# Set GCP credentials file path
GCP_CREDS_CANDIDATES=(
    "$HOME/.config/gcloud/application_default_credentials.json"
    "$ENV_DIR/gcp-key.json"
    "$TERRAFORM_ROOT/gcp-key.json"
    "/tmp/gcp-key.json"
)

for creds_file in "${GCP_CREDS_CANDIDATES[@]}"; do
    if [[ -f "$creds_file" ]]; then
        export GCP_SERVICE_ACCOUNT_FILE="$creds_file"
        # Also set GOOGLE_APPLICATION_CREDENTIALS for GCP dynamic inventory
        export GOOGLE_APPLICATION_CREDENTIALS="$creds_file"
        echo "🔐 Found GCP credentials: $creds_file"
        break
    fi
done

if [[ -z "$GCP_SERVICE_ACCOUNT_FILE" ]]; then
    echo "⚠️  No GCP credentials file found. Checked:"
    printf "    %s\n" "${GCP_CREDS_CANDIDATES[@]}"
    echo "    Using default application credentials"
fi

# Set environment-specific configuration (fallback if not found in terraform.tfvars)
if [[ -z "$GCP_REGION" ]]; then
    case "$DETECTED_ENV" in
        "dev")
            export GCP_REGION="us-central1"
            export GCP_ZONE="us-central1-a"
            ;;
        "staging")
            export GCP_REGION="us-central1"
            export GCP_ZONE="us-central1-b"
            ;;
        "prod")
            export GCP_REGION="us-east1"
            export GCP_ZONE="us-east1-a"
            ;;
    esac
fi

# Set environment-specific defaults
case "$DETECTED_ENV" in
    "dev")
        export LOG_LEVEL="${LOG_LEVEL:-debug}"
        export ENABLE_MONITORING="${ENABLE_MONITORING:-true}"
        ;;
    "staging")
        export LOG_LEVEL="${LOG_LEVEL:-info}"
        export ENABLE_MONITORING="${ENABLE_MONITORING:-true}"
        ;;
    "prod")
        export LOG_LEVEL="${LOG_LEVEL:-warn}"
        export ENABLE_MONITORING="${ENABLE_MONITORING:-true}"
        ;;
esac

echo "🌍 GCP Region: $GCP_REGION"
echo "📍 GCP Zone: $GCP_ZONE"

# Set up PATH to include Terraform and Ansible directories
if [[ ":$PATH:" != *":$TERRAFORM_ROOT:"* ]]; then
    export PATH="$TERRAFORM_ROOT:$PATH"
fi

if [[ ":$PATH:" != *":$ANSIBLE_ROOT:"* ]]; then
    export PATH="$ANSIBLE_ROOT:$PATH"
fi

# Create aliases for common commands
alias tf-plan="terraform plan"
alias tf-apply="terraform apply"
alias tf-destroy="terraform destroy"
alias ansible-deploy="cd '$ANSIBLE_ROOT' && ansible-playbook -i inventory/terraform-${DETECTED_ENV}.yml playbooks/deploy-services.yml"
alias ansible-update="cd '$ANSIBLE_ROOT' && ansible-playbook -i inventory/terraform-${DETECTED_ENV}.yml playbooks/update-images.yml"
alias ansible-config="cd '$ANSIBLE_ROOT' && ansible-playbook -i inventory/terraform-${DETECTED_ENV}.yml playbooks/update-config.yml"
alias ansible-rollback="cd '$ANSIBLE_ROOT' && ansible-playbook -i inventory/terraform-${DETECTED_ENV}.yml playbooks/rollback.yml"
alias ansible-ping="cd '$ANSIBLE_ROOT' && ansible all -i inventory/terraform-${DETECTED_ENV}.yml -m ping"
alias ansible-integration-test="cd '$ANSIBLE_ROOT' && ./test-integration.sh"

echo ""
echo "✅ Environment setup complete!"
echo ""
echo "🎯 Active Environment: $DETECTED_ENV"
echo "🏗️  Project: $GCP_PROJECT_ID"
echo "📍 Region/Zone: $GCP_REGION/$GCP_ZONE"
echo ""
echo "🚀 Available commands:"
echo "   tf-plan                  - Run terraform plan"
echo "   tf-apply                 - Run terraform apply"
echo "   ansible-deploy           - Deploy services with Ansible"
echo "   ansible-update           - Update service images"
echo "   ansible-config           - Update service configuration"
echo "   ansible-ping             - Test connectivity to instances"
echo "   ansible-integration-test - Run integration tests"
echo ""
echo "💡 To test the integration:"
echo "   ansible-integration-test"
echo ""
echo "💡 To deploy infrastructure and services:"
echo "   tf-apply"
echo ""

# Validate current setup
echo "🔍 Validating setup..."

# Check if we can access GCP
if command -v gcloud &> /dev/null; then
    if gcloud auth list --filter=status:ACTIVE --format="value(account)" &> /dev/null; then
        ACTIVE_ACCOUNT=$(gcloud auth list --filter=status:ACTIVE --format="value(account)" 2>/dev/null | head -1)
        echo "✅ GCP authenticated as: $ACTIVE_ACCOUNT"
    else
        echo "⚠️  Not authenticated with GCP. Run: gcloud auth login"
    fi
else
    echo "⚠️  gcloud CLI not found"
fi

# Check if we can access the project
if [[ -n "$GCP_PROJECT_ID" ]] && command -v gcloud &> /dev/null; then
    if gcloud projects describe "$GCP_PROJECT_ID" &> /dev/null; then
        echo "✅ GCP Project accessible: $GCP_PROJECT_ID"
    else
        echo "⚠️  Cannot access GCP project: $GCP_PROJECT_ID"
    fi
fi

# Check Terraform
if command -v terraform &> /dev/null; then
    echo "✅ Terraform available: $(terraform version | head -1)"
else
    echo "⚠️  Terraform not found"
fi

# Check Ansible
if command -v ansible &> /dev/null; then
    echo "✅ Ansible available: $(ansible --version | head -1)"
else
    echo "⚠️  Ansible not found"
fi

echo ""
echo "🎉 Setup complete! You can now run Terraform and Ansible commands."