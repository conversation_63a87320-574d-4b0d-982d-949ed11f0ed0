/**
 * TypeScript type definitions for the AI Agent Platform
 */

// API Response wrapper
export interface ApiResponse<T = any> {
  data: T;
  success: boolean;
  status: number;
  message?: string;
  errors?: string[];
}

// Agent types
export interface Agent {
  id: string;
  name: string;
  description: string;
  type: AgentType;
  status: AgentStatus;
  configuration: AgentConfiguration;
  capabilities: string[];
  created_at: string;
  updated_at: string;
  version: string;
  metadata?: Record<string, any>;
}

export enum AgentType {
  ASSISTANT = 'assistant',
  ANALYST = 'analyst',
  PROCESSOR = 'processor',
  MONITOR = 'monitor',
  SPECIALIST = 'specialist',
  COORDINATOR = 'coordinator'
}

export enum AgentStatus {
  INACTIVE = 'inactive',
  ACTIVE = 'active',
  RUNNING = 'running',
  ERROR = 'error',
  DEPLOYING = 'deploying',
  STOPPING = 'stopping'
}

export interface AgentConfiguration {
  model_provider?: string;
  model_name?: string;
  temperature?: number;
  max_tokens?: number;
  system_prompt?: string;
  tools?: string[];
  memory_type?: string;
  environment_variables?: Record<string, string>;
  resource_limits?: {
    cpu?: string;
    memory?: string;
  };
}

// Task types
export interface Task {
  id: string;
  name: string;
  description: string;
  type: TaskType;
  status: TaskStatus;
  agent_id?: string;
  parameters: Record<string, any>;
  result?: any;
  error?: string;
  created_at: string;
  updated_at: string;
  started_at?: string;
  completed_at?: string;
  priority: TaskPriority;
}

export enum TaskType {
  ANALYSIS = 'analysis',
  PROCESSING = 'processing',
  GENERATION = 'generation',
  COMMUNICATION = 'communication',
  MONITORING = 'monitoring',
  ORCHESTRATION = 'orchestration'
}

export enum TaskStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

export enum TaskPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

// Orchestration types
export interface OrchestrationPattern {
  id: string;
  name: string;
  description: string;
  type: OrchestrationPatternType;
  agents: string[];
  workflow: WorkflowStep[];
  configuration: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export enum OrchestrationPatternType {
  SEQUENTIAL = 'sequential',
  PARALLEL = 'parallel',
  HIERARCHICAL = 'hierarchical',
  P2P = 'p2p',
  EVENT_DRIVEN = 'event_driven'
}

export interface WorkflowStep {
  id: string;
  name: string;
  agent_id: string;
  task_type: string;
  parameters: Record<string, any>;
  dependencies: string[];
  timeout?: number;
  retry_count?: number;
}

// Generation types
export interface GenerationRequest {
  id?: string;
  description: string;
  requirements: Record<string, any>;
  constraints?: Record<string, any>;
  target_platform?: string;
  deployment_target?: string;
  priority?: string;
}

export interface GenerationResult {
  request_id: string;
  success: boolean;
  generated_agent?: Agent;
  artifacts?: Record<string, any>;
  workflow_log?: WorkflowLogEntry[];
  error?: string;
  duration?: number;
}

export interface WorkflowLogEntry {
  stage: string;
  timestamp: string;
  success: boolean;
  output_size?: number;
  duration?: number;
  error?: string;
}

// Deployment types
export interface DeploymentConfig {
  id?: string;
  agent_id: string;
  environment: string;
  strategy?: DeploymentStrategy;
  resources?: ResourceConfig;
  scaling?: ScalingConfig;
  ports?: PortAssignment[];
  env_vars?: Record<string, string>;
  health_check?: HealthCheckConfig;
}

export enum DeploymentStrategy {
  BLUE_GREEN = 'blue_green',
  ROLLING = 'rolling',
  CANARY = 'canary',
  RECREATE = 'recreate'
}

export interface ResourceConfig {
  cpu?: string;
  memory?: string;
  cpu_limit?: string;
  memory_limit?: string;
}

export interface ScalingConfig {
  min_replicas?: number;
  max_replicas?: number;
  target_cpu?: number;
}

export interface PortAssignment {
  port: number;
  protocol?: string;
  is_external?: boolean;
  service_name?: string;
  assigned_at?: string;
}

export interface HealthCheckConfig {
  endpoint?: string;
  initial_delay?: number;
  period?: number;
  timeout?: number;
  failure_threshold?: number;
}

export interface DeploymentStatus {
  deployment_id: string;
  agent_id: string;
  status: DeploymentStatusType;
  environment: string;
  ports: PortAssignment[];
  replicas?: {
    desired: number;
    ready: number;
    available: number;
  };
  start_time?: string;
  end_time?: string;
  error?: string;
  logs?: string[];
  metrics?: Record<string, any>;
}

export enum DeploymentStatusType {
  PENDING = 'pending',
  PREPARING = 'preparing',
  BUILDING = 'building',
  TESTING = 'testing',
  DEPLOYING = 'deploying',
  RUNNING = 'running',
  FAILED = 'failed',
  STOPPING = 'stopping',
  STOPPED = 'stopped',
  ROLLBACK = 'rollback'
}

// Health and monitoring types
export interface HealthStatus {
  agent_id: string;
  status: HealthStatusType;
  metrics: Record<string, HealthMetric>;
  issues: string[];
  last_check: string;
  uptime: number;
  error_rate: number;
  response_time: number;
}

export enum HealthStatusType {
  HEALTHY = 'healthy',
  WARNING = 'warning',
  CRITICAL = 'critical',
  FAILED = 'failed',
  RECOVERING = 'recovering',
  UNKNOWN = 'unknown'
}

export interface HealthMetric {
  name: string;
  value: number;
  unit: string;
  threshold_warning: number;
  threshold_critical: number;
  timestamp: string;
  status: HealthStatusType;
}

export interface HealingEvent {
  id: string;
  agent_id: string;
  trigger: string;
  action: HealingActionType;
  description: string;
  success: boolean;
  error?: string;
  timestamp: string;
  duration: number;
  details: Record<string, any>;
}

export enum HealingActionType {
  RESTART = 'restart',
  SCALE_UP = 'scale_up',
  SCALE_DOWN = 'scale_down',
  ROLLBACK = 'rollback',
  REDEPLOY = 'redeploy',
  CONFIG_UPDATE = 'config_update',
  RESOURCE_ADJUSTMENT = 'resource_adjustment',
  NETWORK_RESET = 'network_reset'
}

// Prompt operation types
export interface PromptOperation {
  id: string;
  user_prompt: string;
  operation_type?: PromptOperationType;
  context?: Record<string, any>;
  parsed_intent?: Record<string, any>;
  created_at: string;
  user_id?: string;
}

export enum PromptOperationType {
  CREATE_AGENT = 'create_agent',
  MODIFY_AGENT = 'modify_agent',
  EXECUTE_TASK = 'execute_task',
  DEPLOY_AGENT = 'deploy_agent',
  MONITOR_AGENT = 'monitor_agent',
  DEBUG_AGENT = 'debug_agent',
  OPTIMIZE_AGENT = 'optimize_agent',
  DELETE_AGENT = 'delete_agent'
}

export interface OperationResult {
  operation_id: string;
  success: boolean;
  result?: Record<string, any>;
  generated_code?: string;
  deployment_info?: Record<string, any>;
  error?: string;
  suggestions?: string[];
  follow_up_actions?: string[];
}

// A2A Protocol types
export interface A2AMessage {
  id: string;
  from_agent: string;
  to_agent: string;
  message_type: A2AMessageType;
  content: any;
  priority: MessagePriority;
  timestamp: string;
  conversation_id?: string;
}

export enum A2AMessageType {
  TASK_REQUEST = 'task_request',
  TASK_RESPONSE = 'task_response',
  CAPABILITY_QUERY = 'capability_query',
  CAPABILITY_RESPONSE = 'capability_response',
  NEGOTIATION = 'negotiation',
  HEARTBEAT = 'heartbeat'
}

export enum MessagePriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

// Authentication types
export interface User {
  id: string;
  username: string;
  email: string;
  full_name?: string;
  role: UserRole;
  permissions: string[];
  created_at: string;
  last_login?: string;
  is_active: boolean;
}

export enum UserRole {
  ADMIN = 'admin',
  DEVELOPER = 'developer',
  OPERATOR = 'operator',
  VIEWER = 'viewer'
}

export interface AuthToken {
  access_token: string;
  refresh_token?: string;
  token_type: string;
  expires_in: number;
  user: User;
}

// Vector database types
export interface VectorSearchResult {
  id: string;
  content: string;
  metadata: Record<string, any>;
  score: number;
}

export interface VectorDocument {
  id: string;
  content: string;
  metadata: Record<string, any>;
  embeddings?: number[];
  created_at: string;
}

// Form and UI types
export interface FormField {
  name: string;
  label: string;
  type: FieldType;
  required?: boolean;
  placeholder?: string;
  options?: { label: string; value: string }[];
  validation?: ValidationRule[];
  description?: string;
}

export enum FieldType {
  TEXT = 'text',
  EMAIL = 'email',
  PASSWORD = 'password',
  NUMBER = 'number',
  SELECT = 'select',
  MULTI_SELECT = 'multi_select',
  TEXTAREA = 'textarea',
  CHECKBOX = 'checkbox',
  RADIO = 'radio',
  DATE = 'date',
  FILE = 'file'
}

export interface ValidationRule {
  type: ValidationType;
  value?: any;
  message: string;
}

export enum ValidationType {
  REQUIRED = 'required',
  MIN_LENGTH = 'min_length',
  MAX_LENGTH = 'max_length',
  PATTERN = 'pattern',
  EMAIL = 'email',
  MIN_VALUE = 'min_value',
  MAX_VALUE = 'max_value'
}

// Dashboard and analytics types
export interface DashboardMetric {
  name: string;
  value: number | string;
  unit?: string;
  trend?: number;
  status?: HealthStatusType;
  icon?: string;
}

export interface ChartData {
  labels: string[];
  datasets: ChartDataset[];
}

export interface ChartDataset {
  label: string;
  data: number[];
  backgroundColor?: string | string[];
  borderColor?: string;
  borderWidth?: number;
}

// Notification types
export interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  action_url?: string;
  metadata?: Record<string, any>;
}

export enum NotificationType {
  INFO = 'info',
  SUCCESS = 'success',
  WARNING = 'warning',
  ERROR = 'error'
}

// Pagination types
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  per_page: number;
  total_pages: number;
  has_next: boolean;
  has_prev: boolean;
}

export interface PaginationParams {
  page?: number;
  per_page?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
  search?: string;
  filters?: Record<string, any>;
}