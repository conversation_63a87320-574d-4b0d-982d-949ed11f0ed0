package config

import (
	"os"
	"testing"
)

func TestGetEnv(t *testing.T) {
	tests := []struct {
		name         string
		key          string
		defaultValue string
		envValue     string
		want         string
	}{
		{
			name:         "returns environment variable when set",
			key:          "TEST_ENV_VAR",
			defaultValue: "default",
			envValue:     "custom",
			want:         "custom",
		},
		{
			name:         "returns default when environment variable not set",
			key:          "UNSET_ENV_VAR",
			defaultValue: "default",
			envValue:     "",
			want:         "default",
		},
		{
			name:         "returns empty string when both env and default are empty",
			key:          "EMPTY_ENV_VAR",
			defaultValue: "",
			envValue:     "",
			want:         "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.envValue != "" {
				os.Setenv(tt.key, tt.envValue)
				defer os.Unsetenv(tt.key)
			}

			got := getEnv(tt.key, tt.defaultValue)
			if got != tt.want {
				t.Errorf("getEnv() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestLoad(t *testing.T) {
	tests := []struct {
		name           string
		envVars        map[string]string
		wantJWTSecret  string
		wantDBURL      string
		wantEnv        string
	}{
		{
			name:          "loads default values",
			envVars:       map[string]string{},
			wantJWTSecret: "your-secret-key-change-in-production",
			wantDBURL:     "postgres://postgres:postgres@localhost:5432/appdb?sslmode=disable",
			wantEnv:       "development",
		},
		{
			name: "loads custom values from environment",
			envVars: map[string]string{
				"JWT_SECRET":    "custom-secret",
				"DATABASE_URL":  "********************************/customdb",
				"ENVIRONMENT":   "staging",
			},
			wantJWTSecret: "custom-secret",
			wantDBURL:     "********************************/customdb",
			wantEnv:       "staging",
		},
		{
			name: "works in production with custom JWT secret",
			envVars: map[string]string{
				"ENVIRONMENT": "production",
				"JWT_SECRET":  "production-secret",
			},
			wantJWTSecret: "production-secret",
			wantDBURL:     "postgres://postgres:postgres@localhost:5432/appdb?sslmode=disable",
			wantEnv:       "production",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Set environment variables
			for k, v := range tt.envVars {
				os.Setenv(k, v)
				defer os.Unsetenv(k)
			}


			cfg := Load()

			if cfg.JWTSecret != tt.wantJWTSecret {
				t.Errorf("Load().JWTSecret = %v, want %v", cfg.JWTSecret, tt.wantJWTSecret)
			}
			if cfg.DatabaseURL != tt.wantDBURL {
				t.Errorf("Load().DatabaseURL = %v, want %v", cfg.DatabaseURL, tt.wantDBURL)
			}
			if cfg.Environment != tt.wantEnv {
				t.Errorf("Load().Environment = %v, want %v", cfg.Environment, tt.wantEnv)
			}
		})
	}
}

