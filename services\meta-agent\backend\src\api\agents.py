"""
AI Agent Platform - Agent API Routes
"""

from typing import List, Optional, Dict, Any
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from sqlalchemy.ext.asyncio import AsyncSession
import structlog
import asyncio

from database.connection import get_db
from database.models import AgentStatus, User
from services.agents import AgentService
from services.code_generator import code_generator
from services.docker_deployment import docker_deployment
from auth.dependencies import get_current_user, get_current_user_optional
from .schemas import (
    AgentCreate, AgentResponse, AgentUpdate, 
    AgentListResponse, AgentStartResponse
)

logger = structlog.get_logger()

router = APIRouter(prefix="/agents", tags=["agents"])


@router.post("/", response_model=AgentResponse, status_code=status.HTTP_201_CREATED)
async def create_agent(
    request: Request,
    agent_data: AgentCreate,
    db: AsyncSession = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user_optional)
):
    """Create a new AI agent with full-stack code generation and deployment"""
    try:
        agent_service = AgentService(db)

        # Use authenticated user's ID as the owner, or use the test user if no auth
        owner_id = current_user.id if current_user else UUID("45758cd8-e46d-4984-88dc-7bd5d376a08a")

        # Prepare agent config with requirements and other generation parameters
        agent_config = agent_data.config or {}
        if agent_data.requirements:
            agent_config.update({
                "requirements": agent_data.requirements,
                "language": agent_data.language or "python",
                "framework": agent_data.framework or "fastapi",
                "deployment": agent_data.deployment.dict() if agent_data.deployment else {}
            })

        # Create agent record first
        agent = await agent_service.create_agent(
            owner_id=owner_id,
            name=agent_data.name,
            description=agent_data.description,
            agent_type=agent_data.type,
            config=agent_config,
            capabilities=agent_data.capabilities
        )

        # Create a background task for code generation and deployment if requirements are provided
        task_id = None
        if agent_data.requirements:
            from database.models import Task, TaskStatus
            from uuid import uuid4
            import uuid
            
            # Create a task record directly
            task_id = uuid4()
            task = Task(
                id=task_id,
                title=f"Generate and deploy agent: {agent_data.name}",
                description=f"Generate code and deploy agent with requirements: {agent_data.requirements[:100]}...",
                type="agent_generation",
                status=TaskStatus.PENDING,
                input_data={
                    "agent_id": str(agent.id),
                    "config": {
                        "name": agent_data.name,
                        "type": agent_data.type,
                        "language": agent_data.language or "python",
                        "framework": agent_data.framework or "fastapi",
                        "capabilities": agent_data.capabilities or [],
                        "deployment": agent_data.deployment.dict() if agent_data.deployment else {}
                    },
                    "requirements": agent_data.requirements
                },
                assigned_agent_id=agent.id
            )
            
            db.add(task)
            await db.commit()
            await db.refresh(task)
            
            logger.info("Created background task for agent generation",
                       agent_id=str(agent.id),
                       task_id=str(task_id))
            
            # Start the background task
            asyncio.create_task(_generate_and_deploy_agent(agent.id, task_id, {
                "name": agent_data.name,
                "type": agent_data.type,
                "language": agent_data.language or "python",
                "framework": agent_data.framework or "fastapi",
                "capabilities": agent_data.capabilities or [],
                "deployment": agent_data.deployment.dict() if agent_data.deployment else {}
            }, agent_data.requirements))

        # Return agent response immediately with task_id if background processing
        agent_response = AgentResponse.model_validate(agent)
        response_dict = agent_response.model_dump()
        
        if task_id:
            response_dict["task_id"] = str(task_id)
            response_dict["status_message"] = "Agent created successfully. Code generation and deployment in progress."
        
        return response_dict

    except Exception as e:
        logger.error("Failed to create agent via API", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create agent"
        )


async def _generate_and_deploy_agent(agent_id: UUID, task_id: UUID, config: dict, requirements: str):
    """Background task to generate and deploy agent code"""
    try:
        from database.connection import AsyncSessionLocal
        from database.models import Task, TaskStatus
        from sqlalchemy import select
        
        # Update task status to running
        async with AsyncSessionLocal() as db:
            result = await db.execute(select(Task).where(Task.id == task_id))
            task = result.scalar_one_or_none()
            if task:
                task.status = TaskStatus.IN_PROGRESS
                await db.commit()
        
        logger.info("Starting code generation for agent", agent_id=str(agent_id), task_id=str(task_id))

        try:
            # Generate complete agent codebase with timeout
            generated_code = await asyncio.wait_for(
                code_generator.generate_agent_code(
                    config=config,
                    requirements=requirements
                ),
                timeout=300  # 5 minute timeout
            )
            logger.info("Code generated successfully", agent_id=str(agent_id))
        except asyncio.TimeoutError:
            logger.warning("Code generation timed out, using fallback", agent_id=str(agent_id))
            # Use a simple fallback configuration
            generated_code = {
                "language": config.get("language", "python"),
                "framework": config.get("framework", "fastapi"),
                "components": {
                    "main": f"# Agent: {config.get('name', 'agent')}\n# TODO: Implement agent logic based on requirements\n",
                    "requirements": "fastapi==0.104.1\nuvicorn==0.24.0\n"
                },
                "generated_at": str(asyncio.get_event_loop().time())
            }

        # Deploy to Docker container with timeout
        logger.info("Deploying agent to Docker", agent_id=str(agent_id))
        
        try:
            # Choose deployment method based on generated components
            has_frontend = "frontend" in generated_code.get("components", {}) and \
                          generated_code["components"]["frontend"].get("status") == "success"
            
            if has_frontend:
                logger.info(f"Deploying full-stack agent {agent_id} with frontend")
                deployment_coro = docker_deployment.deploy_fullstack_agent(
                    agent_id=str(agent_id),
                    generated_code=generated_code
                )
                timeout = 300  # 5 minute timeout for full-stack deployment
            else:
                logger.info(f"Deploying backend-only agent {agent_id}")
                deployment_coro = docker_deployment.deploy_agent(
                    agent_id=str(agent_id),
                    generated_code=generated_code
                )
                timeout = 180  # 3 minute timeout for backend-only deployment
            
            deployment_info = await asyncio.wait_for(deployment_coro, timeout=timeout)
            deployment_url = deployment_info.get("frontend_url") or deployment_info.get("url") or deployment_info.get("backend_url")
        except asyncio.TimeoutError:
            logger.warning("Deployment timed out", agent_id=str(agent_id))
            deployment_info = {"status": "timeout", "message": "Deployment timed out"}
            deployment_url = None
        except Exception as deploy_error:
            logger.error("Deployment failed", agent_id=str(agent_id), error=str(deploy_error))
            deployment_info = {"status": "failed", "error": str(deploy_error)}
            deployment_url = None

        # Save generated code to filesystem
        from services.agent_storage import agent_storage
        
        try:
            storage_result = agent_storage.save_generated_code(str(agent_id), generated_code)
            logger.info("Generated code saved to storage", 
                       agent_id=str(agent_id), 
                       storage_result=storage_result)
        except Exception as storage_error:
            logger.error("Failed to save generated code to storage", 
                        agent_id=str(agent_id), 
                        error=str(storage_error))

        # Update agent with deployment information and storage reference
        async with AsyncSessionLocal() as db:
            agent_service = AgentService(db)
            updated_config = {
                "code_generated": True,
                "storage_path": str(agent_storage.get_agent_path(str(agent_id))),
                "deployment_info": deployment_info,
                "generated_code_summary": {
                    "language": generated_code.get("language"),
                    "framework": generated_code.get("framework"),
                    "components": list(generated_code.get("components", {}).keys()),
                    "generated_at": generated_code.get("generated_at"),
                    "files_count": len(agent_storage.get_agent_files_list(str(agent_id)))
                }
            }

            await agent_service.update_agent(
                agent_id=agent_id,
                config=updated_config
            )

        # Update task with results (success or partial success)
        async with AsyncSessionLocal() as db:
            result = await db.execute(select(Task).where(Task.id == task_id))
            task = result.scalar_one_or_none()
            if task:
                # Mark as completed even if deployment failed, since agent was created
                task.status = TaskStatus.COMPLETED
                task.output_data = {
                    "deployment_url": deployment_url,
                    "deployment_status": deployment_info.get("status", "success"),
                    "generated_code_summary": {
                        "language": generated_code.get("language"),
                        "framework": generated_code.get("framework"),
                        "components": list(generated_code.get("components", {}).keys()) if generated_code.get("components") else []
                    }
                }
                if not deployment_url and deployment_info.get("error"):
                    task.output_data["deployment_error"] = deployment_info.get("error")
                await db.commit()

        status_msg = "deployed successfully" if deployment_url else "created (deployment failed)"
        logger.info(f"Agent {status_msg}",
                   agent_id=str(agent_id),
                   deployment_url=deployment_url,
                   task_id=str(task_id))

    except Exception as e:
        logger.error("Failed to generate/deploy agent code",
                   agent_id=str(agent_id),
                   task_id=str(task_id),
                   error=str(e))
        
        # Update task with failure
        try:
            async with AsyncSessionLocal() as db:
                result = await db.execute(select(Task).where(Task.id == task_id))
                task = result.scalar_one_or_none()
                if task:
                    task.status = TaskStatus.FAILED
                    task.output_data = {"error": str(e)}
                    await db.commit()
        except Exception as task_error:
            logger.error("Failed to update task status", task_id=str(task_id), error=str(task_error))


@router.get("/{agent_id}", response_model=AgentResponse)
async def get_agent(
    agent_id: UUID,
    db: AsyncSession = Depends(get_db)
):
    """Get agent by ID"""
    try:
        agent_service = AgentService(db)
        agent = await agent_service.get_agent(agent_id)
        
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent not found"
            )
        
        return AgentResponse.model_validate(agent)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get agent via API", agent_id=str(agent_id), error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve agent"
        )


@router.get("/", response_model=AgentListResponse)
async def list_agents(
    status_filter: Optional[AgentStatus] = Query(None, alias="status"),
    limit: int = Query(100, ge=1, le=1000),
    offset: int = Query(0, ge=0),
    db: AsyncSession = Depends(get_db)
):
    """List agents with optional filters"""
    try:
        agent_service = AgentService(db)
        
        # Build query filters
        filters = {}
        if status_filter:
            filters['status'] = status_filter
        
        agents = await agent_service.list_agents(
            limit=limit,
            offset=offset,
            **filters
        )
        
        # Get total count for pagination
        total_count = await agent_service.count_agents(**filters)
        
        # Convert to response format
        agent_responses = []
        for agent in agents:
            try:
                agent_responses.append(AgentResponse.model_validate(agent))
            except Exception as e:
                logger.error("Failed to serialize agent", agent_id=str(agent.id), error=str(e))
                continue
        
        return AgentListResponse(
            agents=agent_responses,
            total=total_count,
            limit=limit,
            offset=offset
        )
        
    except Exception as e:
        logger.error("Failed to list agents via API", error=str(e), error_type=type(e).__name__)
        import traceback
        logger.error("Stack trace", stack_trace=traceback.format_exc())
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list agents: {str(e)}"
        )


@router.put("/{agent_id}", response_model=AgentResponse)
async def update_agent(
    agent_id: UUID,
    agent_data: AgentUpdate,
    db: AsyncSession = Depends(get_db)
):
    """Update an existing agent"""
    try:
        agent_service = AgentService(db)
        
        # Get current agent to verify it exists
        current_agent = await agent_service.get_agent(agent_id)
        if not current_agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent not found"
            )
        
        # TODO: Verify user owns this agent when auth is implemented
        
        # Prepare update data
        update_data = agent_data.dict(exclude_unset=True)
        
        # Handle requirements and user_prompt by storing them in config
        if 'requirements' in update_data or 'user_prompt' in update_data:
            # Get current config or create new one
            current_config = update_data.get('config', current_agent.config or {})
            if current_config is None:
                current_config = {}
            
            # Add requirements to config if provided
            if 'requirements' in update_data:
                current_config['requirements'] = update_data.pop('requirements')
            
            # Add user_prompt to config if provided  
            if 'user_prompt' in update_data:
                current_config['user_prompt'] = update_data.pop('user_prompt')
            
            # Update the config in update_data
            update_data['config'] = current_config
        
        updated_agent = await agent_service.update_agent(agent_id, **update_data)
        
        if not updated_agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent not found"
            )
        
        return AgentResponse.model_validate(updated_agent)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to update agent via API", agent_id=str(agent_id), error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update agent"
        )


@router.post("/{agent_id}/start", response_model=AgentStartResponse)
async def start_agent(
    agent_id: UUID,
    db: AsyncSession = Depends(get_db)
):
    """Start an agent"""
    try:
        agent_service = AgentService(db)
        
        # Verify agent exists and user owns it
        agent = await agent_service.get_agent(agent_id)
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent not found"
            )
        
        # TODO: Verify user owns this agent when auth is implemented
        
        success = await agent_service.start_agent(agent_id)
        
        return AgentStartResponse(
            agent_id=agent_id,
            success=success,
            message="Agent started successfully" if success else "Failed to start agent"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to start agent via API", agent_id=str(agent_id), error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start agent"
        )


@router.post("/{agent_id}/stop", response_model=AgentStartResponse)
async def stop_agent(
    agent_id: UUID,
    db: AsyncSession = Depends(get_db)
):
    """Stop an agent"""
    try:
        agent_service = AgentService(db)
        
        # Verify agent exists and user owns it
        agent = await agent_service.get_agent(agent_id)
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent not found"
            )
        
        success = await agent_service.stop_agent(agent_id)
        
        return AgentStartResponse(
            agent_id=agent_id,
            success=success,
            message="Agent stopped successfully" if success else "Failed to stop agent"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to stop agent via API", agent_id=str(agent_id), error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to stop agent"
        )


@router.get("/{agent_id}/deployment")
async def get_agent_deployment(
    agent_id: UUID,
    db: AsyncSession = Depends(get_db)
):
    """Get agent deployment status and information"""
    try:
        # Check if agent exists
        agent_service = AgentService(db)
        agent = await agent_service.get_agent(agent_id)

        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent not found"
            )

        # Get deployment status from Docker service
        deployment_status = await docker_deployment.get_agent_status(str(agent_id))

        # If not found in Docker service, check for manual deployment in config
        if not deployment_status:
            # Check if agent has manual deployment info in config
            deployment_info = agent.config.get("deployment_info")
            if deployment_info and deployment_info.get("status") == "deployed":
                # Return manual deployment status
                return {
                    "agent_id": str(agent_id),
                    "deployed": True,
                    "status": "running",
                    "url": deployment_info.get("frontend_url") or deployment_info.get("backend_url"),
                    "backend_url": deployment_info.get("backend_url"),
                    "frontend_url": deployment_info.get("frontend_url"),
                    "health_check_url": deployment_info.get("health_check_url"),
                    "deployment_type": deployment_info.get("deployment_type", "external_manual"),
                    "deployed_at": deployment_info.get("deployed_at"),
                    "health_status": "healthy",  # Assume healthy for manual deployments
                    "port": deployment_info.get("backend_url", "").split(":")[-1] if deployment_info.get("backend_url") else None
                }
            
            return {
                "agent_id": str(agent_id),
                "deployed": False,
                "status": "not_deployed",
                "message": "Agent has not been deployed"
            }

        return {
            "agent_id": str(agent_id),
            "deployed": True,
            **deployment_status
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get agent deployment status", agent_id=str(agent_id), error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get deployment status"
        )


def _create_basic_agent_code(agent) -> Dict[str, Any]:
    """Create basic agent code when no requirements are provided"""
    from datetime import datetime
    
    # Create basic FastAPI agent with A2A protocol
    main_py_content = f'''"""
Generated AI Agent: {agent.name}
Basic agent with A2A protocol support
"""

import os
import uuid
from datetime import datetime
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

# Initialize FastAPI app
app = FastAPI(
    title="{agent.name}",
    description="AI Agent with A2A Protocol Support",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Generate unique agent ID
AGENT_ID = str(uuid.uuid4())

@app.get("/")
async def root():
    """A2A agent information endpoint"""
    return {{
        "agent_id": AGENT_ID,
        "name": "{agent.name}",
        "type": "{agent.type}",
        "capabilities": {agent.capabilities or []},
        "status": "running",
        "version": "1.0.0",
        "description": "{agent.description or 'Basic AI Agent'}",
        "created_at": "{datetime.now().isoformat()}",
        "a2a_protocol": "v1.0",
        "endpoints": [
            {{"path": "/", "method": "GET", "description": "A2A agent information"}},
            {{"path": "/health", "method": "GET", "description": "Health check"}},
            {{"path": "/a2a/info", "method": "GET", "description": "A2A protocol details"}},
            {{"path": "/a2a/capabilities", "method": "GET", "description": "Agent capabilities"}}
        ]
    }}

@app.get("/health")
async def health():
    """Health check endpoint"""
    return {{
        "status": "healthy",
        "agent_id": AGENT_ID,
        "timestamp": datetime.now().isoformat()
    }}

@app.get("/a2a/info")
async def a2a_info():
    """A2A protocol information"""
    return {{
        "protocol_version": "v1.0",
        "agent_id": AGENT_ID,
        "supported_actions": ["ping", "get_capabilities", "get_status"],
        "communication_methods": ["http", "rest"]
    }}

@app.get("/a2a/capabilities")
async def a2a_capabilities():
    """Detailed agent capabilities"""
    return {{
        "capabilities": {agent.capabilities or []},
        "agent_type": "{agent.type}",
        "supported_formats": ["json"],
        "max_concurrent_requests": 100
    }}

@app.post("/a2a/ping")
async def a2a_ping():
    """A2A ping endpoint"""
    return {{
        "pong": True,
        "agent_id": AGENT_ID,
        "timestamp": datetime.now().isoformat()
    }}

# Basic functionality endpoints based on capabilities
{_generate_capability_endpoints(agent.capabilities or [])}

if __name__ == "__main__":
    import uvicorn
    port = int(os.getenv("PORT", 8000))
    uvicorn.run(app, host="0.0.0.0", port=port, log_level="info")
'''

    # Requirements file
    requirements_content = """fastapi==0.104.1
uvicorn==0.24.0
python-multipart==0.0.6
"""

    # Dockerfile
    dockerfile_content = f"""FROM python:3.9-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \\
    curl \\
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create non-root user
RUN useradd -m -u 1000 agent
RUN chown -R agent:agent /app
USER agent

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \\
  CMD curl -f http://localhost:8000/health || exit 1

# Run the application
CMD ["python", "main.py"]
"""

    return {{
        "agent_name": agent.name,
        "language": "python",
        "framework": "fastapi",
        "components": {{
            "api": {{
                "main.py": main_py_content,
                "requirements.txt": requirements_content
            }},
            "docker": {{
                "Dockerfile": dockerfile_content
            }}
        }},
        "deployment_config": {{}},
        "generated_at": datetime.now().isoformat()
    }}


def _generate_capability_endpoints(capabilities: list) -> str:
    """Generate basic endpoints based on agent capabilities"""
    endpoints = []
    
    if "custom_logic" in capabilities:
        endpoints.append('''
@app.post("/execute")
async def execute_custom_logic(data: dict = None):
    """Execute custom logic"""
    return {
        "result": "Custom logic executed",
        "input": data,
        "agent_id": AGENT_ID
    }
''')
    
    if "data_processing" in capabilities:
        endpoints.append('''
@app.post("/process")
async def process_data(data: dict):
    """Process data"""
    return {
        "processed": True,
        "result": f"Processed {len(str(data))} characters",
        "agent_id": AGENT_ID
    }
''')
    
    if "data_validation" in capabilities:
        endpoints.append('''
@app.post("/validate")
async def validate_data(data: dict):
    """Validate data"""
    return {
        "valid": True,
        "data": data,
        "validated_at": datetime.now().isoformat(),
        "agent_id": AGENT_ID
    }
''')
    
    return "\\n".join(endpoints)


@router.post("/{agent_id}/deploy")
async def deploy_agent_endpoint(
    agent_id: UUID,
    db: AsyncSession = Depends(get_db)
):
    """Deploy an existing agent (if not already deployed)"""
    try:
        agent_service = AgentService(db)
        agent = await agent_service.get_agent(agent_id)

        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent not found"
            )

        # Check if already deployed
        existing_deployment = await docker_deployment.get_agent_status(str(agent_id))
        if existing_deployment and existing_deployment.get("status") == "running":
            return {
                "message": "Agent is already deployed",
                "deployment_url": existing_deployment.get("url")
            }

        # Check if agent has generated code stored in filesystem
        from services.agent_storage import agent_storage
        
        logger.info("Checking agent for deployment", 
                   agent_id=str(agent_id),
                   config=agent.config,
                   code_generated=agent.config.get('code_generated') if agent.config else None,
                   storage_exists=agent_storage.agent_exists(str(agent_id)))
        
        if agent.config and agent.config.get('code_generated') and agent_storage.agent_exists(str(agent_id)):
            # Load generated code from filesystem
            generated_code = agent_storage.load_generated_code(str(agent_id))
            if generated_code:
                logger.info("Using stored generated code for deployment", 
                           agent_id=str(agent_id),
                           files_count=len(agent_storage.get_agent_files_list(str(agent_id))))
            else:
                logger.error("Failed to load stored code, falling back to generation", 
                            agent_id=str(agent_id))
                generated_code = None
        else:
            logger.info("No stored code found for agent", 
                       agent_id=str(agent_id),
                       reasons={
                           "has_config": bool(agent.config),
                           "code_generated": agent.config.get('code_generated') if agent.config else False,
                           "storage_exists": agent_storage.agent_exists(str(agent_id))
                       })
            generated_code = None
        
        # Fallback: generate code if not found in storage
        if not generated_code:
            # If no requirements, create a basic agent with A2A protocol
            if not agent.config or not agent.config.get('requirements'):
                logger.info("Creating basic agent without requirements", agent_id=str(agent_id))
                generated_code = _create_basic_agent_code(agent)
            else:
                logger.info("Generating code on-demand for deployment", agent_id=str(agent_id))
                
                # Generate code (fallback for agents without pre-generated code)
                generated_code = await code_generator.generate_agent_code(
                    config={
                        "name": agent.name,
                        "type": agent.type,
                        "language": agent.config.get("language", "python"),
                        "framework": agent.config.get("framework", "fastapi"),
                        "capabilities": agent.capabilities or [],
                        "deployment": agent.config.get("deployment", {})
                    },
                    requirements=agent.config.get('requirements', '')
                )
            
            # Save the newly generated code to storage for future use
            try:
                storage_result = agent_storage.save_generated_code(str(agent_id), generated_code)
                logger.info("On-demand generated code saved to storage", 
                           agent_id=str(agent_id), 
                           storage_result=storage_result)
                
                # Update agent config to mark code as generated
                agent_service = AgentService(db)
                await agent_service.update_agent(
                    agent_id=agent_id,
                    config={
                        **agent.config,
                        "code_generated": True,
                        "storage_path": str(agent_storage.get_agent_path(str(agent_id)))
                    }
                )
            except Exception as storage_error:
                logger.error("Failed to save on-demand generated code", 
                            agent_id=str(agent_id), 
                            error=str(storage_error))

        # Choose deployment method based on whether frontend files exist
        from services.agent_storage import agent_storage
        agent_path = agent_storage.get_agent_path(str(agent_id))
        frontend_path = agent_path / "frontend"
        has_frontend = frontend_path.exists() and (frontend_path / "package.json").exists()
        
        if has_frontend:
            logger.info(f"Deploying full-stack agent {agent_id} with frontend")
            deployment_info = await docker_deployment.deploy_fullstack_agent(
                agent_id=str(agent_id),
                generated_code=generated_code
            )
        else:
            logger.info(f"Deploying backend-only agent {agent_id}")
            deployment_info = await docker_deployment.deploy_agent(
                agent_id=str(agent_id),
                generated_code=generated_code
            )

        return {
            "message": "Agent deployed successfully",
            "deployment_url": deployment_info.get("frontend_url") or deployment_info.get("url") or deployment_info.get("backend_url"),
            "frontend_url": deployment_info.get("frontend_url"),
            "backend_url": deployment_info.get("backend_url") or deployment_info.get("url"),
            "deployment_info": deployment_info
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to deploy agent", agent_id=str(agent_id), error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to deploy agent"
        )


@router.delete("/{agent_id}/deployment")
async def stop_agent_deployment(
    agent_id: UUID,
    db: AsyncSession = Depends(get_db)
):
    """Stop and remove agent deployment"""
    try:
        # Check if agent exists
        agent_service = AgentService(db)
        agent = await agent_service.get_agent(agent_id)

        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent not found"
            )

        # Stop deployment
        success = await docker_deployment.stop_agent(str(agent_id))

        if success:
            return {"message": "Agent deployment stopped successfully"}
        else:
            return {"message": "Agent was not deployed or already stopped"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to stop agent deployment", agent_id=str(agent_id), error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to stop deployment"
        )


@router.delete("/{agent_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_agent(
    agent_id: UUID,
    db: AsyncSession = Depends(get_db)
):
    """Delete an agent"""
    try:
        agent_service = AgentService(db)
        
        # Verify agent exists and user owns it
        agent = await agent_service.get_agent(agent_id)
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent not found"
            )
        
        # Delete agent files from storage
        from services.agent_storage import agent_storage
        try:
            agent_storage.delete_agent_files(str(agent_id))
            logger.info("Agent files deleted from storage", agent_id=str(agent_id))
        except Exception as storage_error:
            logger.error("Failed to delete agent files from storage", 
                        agent_id=str(agent_id), 
                        error=str(storage_error))
        
        success = await agent_service.delete_agent(agent_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent not found"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to delete agent via API", agent_id=str(agent_id), error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete agent"
        )


@router.get("/storage/stats")
async def get_storage_stats():
    """Get agent storage statistics"""
    try:
        from services.agent_storage import agent_storage
        stats = agent_storage.get_storage_stats()
        return stats
    except Exception as e:
        logger.error("Failed to get storage stats", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get storage stats"
        )


@router.get("/{agent_id}/debug")
async def debug_agent(
    agent_id: UUID,
    db: AsyncSession = Depends(get_db)
):
    """Debug agent information for deployment troubleshooting"""
    try:
        # Check if agent exists
        agent_service = AgentService(db)
        agent = await agent_service.get_agent(agent_id)
        
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent not found"
            )
        
        from services.agent_storage import agent_storage
        
        return {
            "agent_id": str(agent_id),
            "name": agent.name,
            "type": agent.type,
            "status": agent.status,
            "capabilities": agent.capabilities,
            "config": agent.config,
            "storage": {
                "exists": agent_storage.agent_exists(str(agent_id)),
                "path": str(agent_storage.get_agent_path(str(agent_id))),
                "files": agent_storage.get_agent_files_list(str(agent_id)),
                "metadata": agent_storage.load_agent_metadata(str(agent_id))
            },
            "deployment_ready": {
                "has_config": bool(agent.config),
                "code_generated": agent.config.get('code_generated') if agent.config else False,
                "has_requirements": bool(agent.config and agent.config.get('requirements')) if agent.config else False,
                "storage_exists": agent_storage.agent_exists(str(agent_id)),
                "can_deploy": bool(
                    (agent.config and agent.config.get('code_generated') and agent_storage.agent_exists(str(agent_id))) or
                    (agent.config and agent.config.get('requirements'))
                )
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to debug agent", agent_id=str(agent_id), error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to debug agent"
        )


@router.get("/{agent_id}/files")
async def list_agent_files(
    agent_id: UUID,
    db: AsyncSession = Depends(get_db)
):
    """List all files for an agent"""
    try:
        # Check if agent exists
        agent_service = AgentService(db)
        agent = await agent_service.get_agent(agent_id)
        
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent not found"
            )
        
        from services.agent_storage import agent_storage
        files = agent_storage.get_agent_files_list(str(agent_id))
        metadata = agent_storage.load_agent_metadata(str(agent_id))
        
        return {
            "agent_id": str(agent_id),
            "files": files,
            "metadata": metadata,
            "storage_path": str(agent_storage.get_agent_path(str(agent_id)))
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to list agent files", agent_id=str(agent_id), error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list agent files"
        )


@router.post("/{agent_id}/heartbeat")
async def update_heartbeat(
    agent_id: UUID,
    db: AsyncSession = Depends(get_db)
):
    """Update agent heartbeat"""
    try:
        agent_service = AgentService(db)
        success = await agent_service.update_heartbeat(agent_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent not found"
            )
        
        return {"message": "Heartbeat updated successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to update heartbeat via API", agent_id=str(agent_id), error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update heartbeat"
        )