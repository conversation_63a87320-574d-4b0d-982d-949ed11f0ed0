import { useEffect, useState } from 'preact/hooks'
import { apiClient, AgentInfo } from '@/services/api'

export function ApiInfoPage() {
  const [agentInfo, setAgentInfo] = useState<AgentInfo | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchAgentInfo()
  }, [])

  const fetchAgentInfo = async () => {
    try {
      const info = await apiClient.getAgentInfo()
      setAgentInfo(info)
    } catch (err) {
      console.error('Failed to fetch agent info:', err)
    } finally {
      setLoading(false)
    }
  }

  if (loading) return <div class="text-center py-8">Loading...</div>

  return (
    <div class="max-w-4xl mx-auto">
      <div class="bg-white shadow rounded-lg p-6">
        <h1 class="text-2xl font-bold mb-4">API Information</h1>
        
        <div class="mb-6">
          <h2 class="text-lg font-semibold mb-2">A2A Protocol</h2>
          <p class="text-gray-600">{agentInfo?.a2a_protocol}</p>
        </div>
        
        <div>
          <h2 class="text-lg font-semibold mb-2">Available Endpoints</h2>
          <div class="space-y-2">
            {agentInfo?.endpoints.map(endpoint => (
              <div key={endpoint.path} class="border rounded p-3">
                <code class="text-sm font-mono text-blue-600">{endpoint.path}</code>
                <p class="text-gray-600 text-sm mt-1">{endpoint.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}