#!/bin/bash
set -e

echo "Running database migrations for GCP dev environment..."

# Navigate to terraform dev to get details
cd terraform/environments/dev

# Get connection details
INSTANCE_NAME=$(terraform output -raw instance_name)
DATABASE_NAME=$(terraform output -raw database_name)
DATABASE_USER=$(terraform output -raw database_user)
DATABASE_PRIVATE_IP=$(terraform output -raw database_private_ip)
ZONE=$(terraform output -json dev_access_info | jq -r '.zone')

echo "Connection details:"
echo "  Instance: $INSTANCE_NAME"
echo "  Database: $DATABASE_NAME"
echo "  User: $DATABASE_USER"
echo "  Private IP: $DATABASE_PRIVATE_IP"
echo "  Zone: $ZONE"

# Get database password
echo "Getting database password..."
DATABASE_PASSWORD=$(gcloud secrets versions access latest --secret=platform-db-password)

echo "Running migrations on compute instance..."
gcloud compute ssh "$INSTANCE_NAME" --zone="$ZONE" --command="
    set -e
    
    # Create temp directory
    TEMP_DIR=\$(mktemp -d)
    cd \$TEMP_DIR
    
    # Create flyway config
    cat > flyway.conf << 'EOF'
flyway.url=**********************************************************
flyway.user=$DATABASE_USER
flyway.password=$DATABASE_PASSWORD
flyway.schemas=public
flyway.locations=filesystem:./migrations
flyway.baselineOnMigrate=true
flyway.baselineVersion=0
flyway.validateOnMigrate=true
flyway.cleanDisabled=true
flyway.connectRetries=5
flyway.connectRetriesInterval=10
EOF

    # Create migrations directory
    mkdir -p migrations
    
    # Copy the migration files from the repo
    echo 'Creating migration files...'
" --quiet

# Copy migration files to the instance
cd ../../..
scp -r platform/db/migrations/* "$INSTANCE_NAME:~/temp_migrations/" --zone="$ZONE"

# Run the actual migration
gcloud compute ssh "$INSTANCE_NAME" --zone="$ZONE" --command="
    # Move migrations to temp directory
    mv ~/temp_migrations/* /tmp/migrations/
    cd /tmp
    
    # Run Flyway
    echo 'Running Flyway migrations...'
    sudo docker run --rm \
        --network='host' \
        -v '/tmp/migrations:/flyway/sql' \
        -v '/tmp/flyway.conf:/flyway/conf/flyway.conf' \
        flyway/flyway:10-alpine \
        migrate
    
    echo 'Migration completed!'
    
    # Cleanup
    rm -rf /tmp/migrations /tmp/flyway.conf
" --quiet

echo "Migration completed successfully!"