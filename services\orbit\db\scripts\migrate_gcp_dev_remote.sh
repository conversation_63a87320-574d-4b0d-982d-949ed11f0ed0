#!/bin/bash
set -e

echo "Running database migrations for GCP dev environment via compute instance..."

# Get the workspace root - when running via <PERSON><PERSON>, we need to navigate to the workspace
if [ -n "$BUILD_WORKSPACE_DIRECTORY" ]; then
    cd "$BUILD_WORKSPACE_DIRECTORY"
elif [ -n "$TEST_WORKSPACE" ]; then
    cd "$TEST_WORKSPACE/.."
else
    # Find the workspace root by looking for WORKSPACE file
    CURRENT_DIR="$PWD"
    while [ "$CURRENT_DIR" != "/" ]; do
        if [ -f "$CURRENT_DIR/WORKSPACE" ]; then
            cd "$CURRENT_DIR"
            break
        fi
        CURRENT_DIR="$(dirname "$CURRENT_DIR")"
    done
fi

# Navigate to terraform dev environment to get current values
cd terraform/environments/dev

# Get connection details from terraform outputs
echo "Getting connection details from Terraform..."
INSTANCE_NAME=$(terraform output -raw instance_name)
DATABASE_CONNECTION_NAME=$(terraform output -raw database_connection_name)
DATABASE_NAME=$(terraform output -raw database_name)
DATABASE_USER=$(terraform output -raw database_user)
DATABASE_PRIVATE_IP=$(terraform output -raw database_private_ip)
PROJECT_ID=$(terraform output -json dev_access_info | jq -r '.project_id')
REGION=$(terraform output -json dev_access_info | jq -r '.region')
ZONE=$(terraform output -json dev_access_info | jq -r '.zone')

echo "Connection details:"
echo "  Instance: $INSTANCE_NAME"
echo "  Database connection: $DATABASE_CONNECTION_NAME"
echo "  Database name: $DATABASE_NAME"
echo "  Database user: $DATABASE_USER"
echo "  Private IP: $DATABASE_PRIVATE_IP"
echo "  Project: $PROJECT_ID"
echo "  Zone: $ZONE"

# Return to workspace root
cd ../../..

# Navigate to platform/db directory
cd platform/db

# Create temporary directory for migration files
TEMP_DIR="/tmp/gcp_migration_$(date +%s)"
mkdir -p "$TEMP_DIR"

# Copy migration files to temporary directory
echo "Preparing migration files..."
cp -r migrations/ "$TEMP_DIR/"
cp flyway.conf "$TEMP_DIR/"

# Create migration script that will run on the compute instance
cat > "$TEMP_DIR/run_migration.sh" << 'EOF'
#!/bin/bash
set -e

echo "Running database migration on compute instance..."

# Install dependencies if needed
if ! command -v docker &> /dev/null; then
    echo "ERROR: Docker is not installed on the compute instance"
    exit 1
fi

# Get database password from Secret Manager
echo "Getting database password from Secret Manager..."
# Try different gcloud paths
if command -v gcloud &> /dev/null; then
    DATABASE_PASSWORD=$(gcloud secrets versions access latest --secret=platform-db-password)
elif [ -f /usr/bin/gcloud ]; then
    DATABASE_PASSWORD=$(/usr/bin/gcloud secrets versions access latest --secret=platform-db-password)
elif [ -f /snap/bin/gcloud ]; then
    DATABASE_PASSWORD=$(/snap/bin/gcloud secrets versions access latest --secret=platform-db-password)
else
    echo "ERROR: gcloud CLI not found on compute instance"
    exit 1
fi

if [ -z "$DATABASE_PASSWORD" ]; then
    echo "ERROR: Could not retrieve database password from Secret Manager"
    exit 1
fi

# Database connection details (passed as environment variables)
echo "Database connection details:"
echo "  Connection name: $DATABASE_CONNECTION_NAME"
echo "  Database name: $DATABASE_NAME"
echo "  Database user: $DATABASE_USER"
echo "  Private IP: $DATABASE_PRIVATE_IP"

# Create flyway configuration for GCP
cat > flyway-gcp.conf << FLYWAY_EOF
# Flyway Configuration for GCP Cloud SQL
flyway.url=jdbc:postgresql://${DATABASE_PRIVATE_IP}:5432/${DATABASE_NAME}
flyway.user=${DATABASE_USER}
flyway.password=${DATABASE_PASSWORD}
flyway.schemas=public
flyway.locations=filesystem:./migrations
flyway.baselineOnMigrate=true
flyway.baselineVersion=0
flyway.validateOnMigrate=true
flyway.cleanDisabled=true
flyway.connectRetries=5
flyway.connectRetriesInterval=10
FLYWAY_EOF

# Run Flyway migration using Docker
echo "Running Flyway migrations..."
docker run --rm \
    --network="host" \
    -v "$(pwd)/migrations:/flyway/sql" \
    -v "$(pwd)/flyway-gcp.conf:/flyway/conf/flyway.conf" \
    flyway/flyway:10-alpine \
    migrate

echo "Migration completed successfully!"

# Clean up
rm -f flyway-gcp.conf
EOF

# Make the migration script executable
chmod +x "$TEMP_DIR/run_migration.sh"

# Upload files to the compute instance
echo "Uploading migration files to compute instance..."
gcloud compute scp --recurse "$TEMP_DIR" "${INSTANCE_NAME}:~/migration_temp" --zone="$ZONE"

# Run the migration on the compute instance
echo "Executing migration on compute instance..."
gcloud compute ssh "$INSTANCE_NAME" --zone="$ZONE" --command="
    cd migration_temp/
    export DATABASE_CONNECTION_NAME='$DATABASE_CONNECTION_NAME'
    export DATABASE_NAME='$DATABASE_NAME'
    export DATABASE_USER='$DATABASE_USER'
    export DATABASE_PRIVATE_IP='$DATABASE_PRIVATE_IP'
    bash run_migration.sh
"

# Clean up temporary files
echo "Cleaning up..."
rm -rf "$TEMP_DIR"

# Clean up files on remote instance
gcloud compute ssh "$INSTANCE_NAME" --zone="$ZONE" --command="rm -rf migration_temp/"

echo ""
echo "GCP database migration completed successfully!"
echo ""
echo "Database connection details:"
echo "  Instance: $DATABASE_CONNECTION_NAME"
echo "  Database: $DATABASE_NAME"
echo "  Private IP: $DATABASE_PRIVATE_IP"