# Cloud Build configuration for database migrations
# This file is used by Terraform to automate database migrations

steps:
  # Step 1: Set up Cloud SQL Proxy for secure database connection
  - name: 'gcr.io/cloudsql-docker/gce-proxy:1.33.6'
    args:
      - '/cloud_sql_proxy'
      - '-instances=${_INSTANCE_CONNECTION_NAME}=tcp:5432'
      - '-credential_file=/workspace/sql-credentials.json'
    volumes:
      - name: 'sql-proxy'
        path: '/cloudsql'
    env:
      - 'GOOGLE_APPLICATION_CREDENTIALS=/workspace/sql-credentials.json'
    id: 'sql-proxy'
    detach: true

  # Step 2: Wait for SQL proxy to be ready
  - name: 'gcr.io/cloud-builders/gcloud'
    script: |
      #!/bin/bash
      echo "Waiting for Cloud SQL Proxy to be ready..."
      for i in {1..30}; do
        if nc -z localhost 5432; then
          echo "Cloud SQL Proxy is ready!"
          break
        fi
        echo "Waiting... ($i/30)"
        sleep 2
      done
    id: 'wait-for-proxy'
    waitFor: ['sql-proxy']

  # Step 3: Get database password from Secret Manager
  - name: 'gcr.io/cloud-builders/gcloud'
    script: |
      #!/bin/bash
      echo "Retrieving database password from Secret Manager..."
      gcloud secrets versions access latest --secret="${_SECRET_NAME}" --project="${PROJECT_ID}" > /workspace/db_password.txt
      echo "Database password retrieved successfully"
    id: 'get-db-password'

  # Step 4: Run database migrations using Flyway
  - name: 'flyway/flyway:9.16.0'
    script: |
      #!/bin/bash
      set -euo pipefail
      
      # Read database password
      DB_PASSWORD=$(cat /workspace/db_password.txt)
      
      # Configure Flyway
      export FLYWAY_URL="********************************/${_DATABASE_NAME}"
      export FLYWAY_USER="${_DATABASE_USER}"
      export FLYWAY_PASSWORD="$DB_PASSWORD"
      export FLYWAY_LOCATIONS="filesystem:/workspace/platform/db/migrations"
      export FLYWAY_BASELINE_ON_MIGRATE=true
      export FLYWAY_VALIDATE_ON_MIGRATE=true
      
      echo "Starting database migration..."
      echo "Database: ${_DATABASE_NAME}"
      echo "User: ${_DATABASE_USER}"
      echo "Migrations location: /workspace/platform/db/migrations"
      
      # Check connection
      flyway info
      
      # Run migrations
      flyway migrate
      
      # Validate migrations
      flyway validate
      
      echo "Database migration completed successfully!"
    waitFor: ['wait-for-proxy', 'get-db-password']
    id: 'run-migrations'

  # Step 5: Run database tests (optional)
  - name: 'postgres:15-alpine'
    script: |
      #!/bin/bash
      set -euo pipefail
      
      # Read database password
      DB_PASSWORD=$(cat /workspace/db_password.txt)
      
      # Set connection parameters
      export PGHOST=localhost
      export PGPORT=5432
      export PGDATABASE="${_DATABASE_NAME}"
      export PGUSER="${_DATABASE_USER}"
      export PGPASSWORD="$DB_PASSWORD"
      
      echo "Running database tests..."
      
      # Test database connection
      psql -c "SELECT version();"
      
      # Run any test scripts if they exist
      if [ -f "/workspace/platform/db/tests/integration_test.sql" ]; then
        echo "Running integration tests..."
        psql -f /workspace/platform/db/tests/integration_test.sql
      fi
      
      # Check database schema
      psql -c "\dt" # List all tables
      psql -c "SELECT schemaname, tablename FROM pg_tables WHERE schemaname = 'public';"
      
      echo "Database tests completed successfully!"
    waitFor: ['run-migrations']
    id: 'test-database'

  # Step 6: Clean up
  - name: 'gcr.io/cloud-builders/gcloud'
    script: |
      #!/bin/bash
      echo "Cleaning up..."
      
      # Remove sensitive files
      rm -f /workspace/db_password.txt
      rm -f /workspace/sql-credentials.json
      
      echo "Migration process completed successfully!"
    waitFor: ['test-database']
    id: 'cleanup'

# Timeout for the entire build
timeout: '1200s'  # 20 minutes

# Substitutions that will be provided by Terraform
substitutions:
  _INSTANCE_CONNECTION_NAME: 'PROJECT_ID:REGION:INSTANCE_NAME'
  _DATABASE_NAME: 'platform_db'
  _DATABASE_USER: 'platform_user'
  _SECRET_NAME: 'platform-db-password'

# IAM permissions needed for this build
serviceAccount: 'projects/${PROJECT_ID}/serviceAccounts/cloudbuild-db-migration@${PROJECT_ID}.iam.gserviceaccount.com'

# Options
options:
  logging: CLOUD_LOGGING_ONLY
  machineType: 'E2_STANDARD_2'
  substitutionOption: 'ALLOW_LOOSE'

# Log Cloud Build logs to Cloud Logging
logsBucket: 'gs://${PROJECT_ID}-build-logs'