#!/bin/bash
set -euo pipefail

# Initialize Terraform for Australia deployment with agent-dev-459718
# This script sets up the initial infrastructure in Australia Southeast 1

PROJECT_ID="agent-dev-459718"
REGION="australia-southeast1"
ZONE="australia-southeast1-a"

echo "🇦🇺 Initializing Terraform for Australia deployment..."
echo "Project: $PROJECT_ID"
echo "Region: $REGION"
echo "Zone: $ZONE"
echo ""

# Check if gcloud is authenticated
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n1 >/dev/null 2>&1; then
    echo "❌ Not authenticated with gcloud. Please run:"
    echo "   gcloud auth login"
    echo "   gcloud auth application-default login"
    exit 1
fi

# Set the project
echo "🔧 Setting GCP project..."
gcloud config set project $PROJECT_ID

# Enable required APIs
echo "🔌 Enabling required GCP APIs..."
gcloud services enable \
    compute.googleapis.com \
    sql-component.googleapis.com \
    artifactregistry.googleapis.com \
    storage.googleapis.com \
    cloudbuild.googleapis.com \
    secretmanager.googleapis.com \
    servicenetworking.googleapis.com \
    cloudscheduler.googleapis.com

# Create Terraform state bucket
BUCKET_NAME="${PROJECT_ID}-terraform-state"
echo "🪣 Creating Terraform state bucket: $BUCKET_NAME"

if ! gsutil ls gs://$BUCKET_NAME >/dev/null 2>&1; then
    gsutil mb -l $REGION gs://$BUCKET_NAME
    gsutil versioning set on gs://$BUCKET_NAME
    # Create lifecycle configuration file
    cat > /tmp/lifecycle.json <<EOF
{
  "lifecycle": {
    "rule": [
      {
        "action": {"type": "Delete"},
        "condition": {"age": 365}
      }
    ]
  }
}
EOF
    gsutil lifecycle set /tmp/lifecycle.json gs://$BUCKET_NAME
    rm /tmp/lifecycle.json
    echo "✅ Created Terraform state bucket"
else
    echo "✅ Terraform state bucket already exists"
fi

# Create terraform.tfvars for dev environment
DEV_TFVARS="environments/dev/terraform.tfvars"
if [ ! -f "$DEV_TFVARS" ]; then
    echo "📝 Creating dev terraform.tfvars..."
    cp environments/dev/terraform.tfvars.example "$DEV_TFVARS"
    
    # Update the example values
    sed -i.bak "s/your-github-username/YOUR_GITHUB_USERNAME/g" "$DEV_TFVARS"
    sed -i.bak "s/your-repo-name/mono/g" "$DEV_TFVARS"
    rm "${DEV_TFVARS}.bak"
    
    echo "✅ Created $DEV_TFVARS - please edit with your GitHub details"
else
    echo "✅ $DEV_TFVARS already exists"
fi

echo ""
echo "🎉 Initialization complete!"
echo ""
echo "Next steps:"
echo "1. Edit terraform/environments/dev/terraform.tfvars with your GitHub details"
echo "2. Initialize and deploy the dev environment:"
echo "   cd terraform/environments/dev"
echo "   terraform init"
echo "   terraform plan"
echo "   terraform apply"
echo ""
echo "📍 All resources will be created in Australia Southeast 1 region"
echo "💰 Estimated monthly cost for dev environment: ~$50-100 AUD"