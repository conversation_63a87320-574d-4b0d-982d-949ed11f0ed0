/* tslint:disable */
/* eslint-disable */
/**
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  UserProfile,
  UserProfileUpdate,
} from '../models/index';
import {
    UserProfileFromJSON,
    UserProfileToJSON,
    UserProfileUpdateFromJSON,
    UserProfileUpdateToJSON,
} from '../models/index';

export interface UsersProfilePutRequest {
    userProfileUpdate: UserProfileUpdate;
}

/**
 * 
 */
export class UserManagementApi extends runtime.BaseAPI {

    /**
     * Retrieve the current user\'s profile information
     * Get user profile
     */
    async usersProfileGetRaw(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<UserProfile>> {
        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("bearerAuth", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/users/profile`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => UserProfileFromJSON(jsonValue));
    }

    /**
     * Retrieve the current user\'s profile information
     * Get user profile
     */
    async usersProfileGet(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<UserProfile> {
        const response = await this.usersProfileGetRaw(initOverrides);
        return await response.value();
    }

    /**
     * Update the current user\'s profile information
     * Update user profile
     */
    async usersProfilePutRaw(requestParameters: UsersProfilePutRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<UserProfile>> {
        if (requestParameters['userProfileUpdate'] == null) {
            throw new runtime.RequiredError(
                'userProfileUpdate',
                'Required parameter "userProfileUpdate" was null or undefined when calling usersProfilePut().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("bearerAuth", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/users/profile`,
            method: 'PUT',
            headers: headerParameters,
            query: queryParameters,
            body: UserProfileUpdateToJSON(requestParameters['userProfileUpdate']),
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => UserProfileFromJSON(jsonValue));
    }

    /**
     * Update the current user\'s profile information
     * Update user profile
     */
    async usersProfilePut(requestParameters: UsersProfilePutRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<UserProfile> {
        const response = await this.usersProfilePutRaw(requestParameters, initOverrides);
        return await response.value();
    }

}
