openapi: 3.0.3
info:
  title: CRM API
  description: Complete API specification for the CRM system
  version: 1.0.0
  contact:
    name: CRM API Support
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
servers:
  - url: https://api.crm.example.com/v1
    description: Production server
  - url: https://staging-api.crm.example.com/v1
    description: Staging server
  - url: http://localhost:8003/api/v1
    description: Development server
security:
  - bearerAuth: []
tags:
  - name: Authentication
    description: User authentication and session management
  - name: Companies
    description: Company management operations
  - name: Contacts
    description: Contact management operations
  - name: Deals
    description: Deal pipeline management
  - name: Interactions
    description: Communication tracking
  - name: Arli
    description: AI-powered document processing
  - name: User Management
    description: User profile and settings
paths:
  /auth/session:
    get:
      tags:
        - Authentication
      summary: Get current session
      description: Retrieve the current user session
      security: []
      responses:
        '200':
          description: Session information
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Session'
        '401':
          description: Not authenticated
  /auth/signin:
    post:
      tags:
        - Authentication
      summary: Sign in with email/password
      description: Authenticate user with email and password
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              title: SignInRequest
              properties:
                email:
                  type: string
                  format: email
                password:
                  type: string
                  minLength: 6
              required:
                - email
                - password
      responses:
        '200':
          description: Authentication successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '401':
          description: Invalid credentials
  /auth/signin/oauth:
    post:
      tags:
        - Authentication
      summary: Sign in with OAuth
      description: Authenticate user with OAuth provider (Google)
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              title: OAuthSignInRequest
              properties:
                provider:
                  type: string
                  enum:
                    - google
                redirectTo:
                  type: string
                  format: uri
              required:
                - provider
      responses:
        '200':
          description: OAuth redirect URL
          content:
            application/json:
              schema:
                type: object
                title: OAuthRedirectResponse
                properties:
                  url:
                    type: string
                    format: uri
  /auth/callback/{provider}:
    get:
      tags:
        - Authentication
      summary: OAuth callback
      description: Handle OAuth provider callback with authorization code
      security: []
      parameters:
        - name: provider
          in: path
          required: true
          schema:
            type: string
            enum:
              - google
        - name: code
          in: query
          required: true
          schema:
            type: string
        - name: state
          in: query
          required: true
          schema:
            type: string
        - name: error
          in: query
          required: false
          schema:
            type: string
      responses:
        '200':
          description: OAuth authentication successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '400':
          description: Invalid request or OAuth error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Authentication failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /auth/signout:
    post:
      tags:
        - Authentication
      summary: Sign out
      description: End the current user session
      responses:
        '200':
          description: Successfully signed out
        '401':
          description: Not authenticated
  /auth/user:
    get:
      tags:
        - Authentication
      summary: Get current user
      description: Retrieve current authenticated user information
      responses:
        '200':
          description: User information
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '401':
          description: Not authenticated
  /users/profile:
    get:
      tags:
        - User Management
      summary: Get user profile
      description: Retrieve the current user's profile information
      responses:
        '200':
          description: User profile
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfile'
        '401':
          description: Not authenticated
    put:
      tags:
        - User Management
      summary: Update user profile
      description: Update the current user's profile information
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserProfileUpdate'
      responses:
        '200':
          description: Profile updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfile'
        '400':
          description: Invalid input
  /companies:
    get:
      tags:
        - Companies
      summary: List companies
      description: Retrieve a list of all active companies
      parameters:
        - name: status_id
          in: query
          schema:
            type: string
            format: uuid
          description: Filter by company status
        - name: include_deleted
          in: query
          schema:
            type: boolean
            default: false
          description: Include soft-deleted companies
      responses:
        '200':
          description: List of companies
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Company'
    post:
      tags:
        - Companies
      summary: Create company
      description: Create a new company
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CompanyCreate'
      responses:
        '201':
          description: Company created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Company'
        '400':
          description: Invalid input
  /companies/{id}:
    get:
      tags:
        - Companies
      summary: Get company
      description: Retrieve a specific company by ID
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Company details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanyDetails'
        '404':
          description: Company not found
    put:
      tags:
        - Companies
      summary: Update company
      description: Update an existing company
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CompanyUpdate'
      responses:
        '200':
          description: Company updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Company'
        '404':
          description: Company not found
    delete:
      tags:
        - Companies
      summary: Delete company
      description: Soft delete a company (sets is_deleted to true)
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '204':
          description: Company deleted successfully
        '404':
          description: Company not found
  /companies/{id}/status:
    put:
      tags:
        - Companies
      summary: Update company status
      description: Update the status of a company
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              title: CompanyStatusUpdateRequest
              properties:
                company_status_id:
                  type: string
                  format: uuid
              required:
                - company_status_id
      responses:
        '200':
          description: Company status updated successfully
        '404':
          description: Company not found
  /company-statuses:
    get:
      tags:
        - Companies
      summary: List company statuses
      description: Retrieve all company statuses ordered by pipeline order
      responses:
        '200':
          description: List of company statuses
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CompanyStatus'
  /contacts:
    get:
      tags:
        - Contacts
      summary: List contacts
      description: Retrieve a list of all active contacts with their associated companies
      parameters:
        - name: company_id
          in: query
          schema:
            type: string
            format: uuid
          description: Filter by company ID
        - name: unlinked
          in: query
          schema:
            type: boolean
          description: Only return contacts without a company
        - name: include_deleted
          in: query
          schema:
            type: boolean
            default: false
          description: Include soft-deleted contacts
      responses:
        '200':
          description: List of contacts
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ContactWithCompany'
        '400':
          description: Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/schemas_ValidationError'
    post:
      tags:
        - Contacts
      summary: Create contact
      description: Create a new contact
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ContactCreate'
      responses:
        '201':
          description: Contact created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Contact'
        '400':
          description: Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/schemas_ValidationError'
  /contacts/{id}:
    get:
      tags:
        - Contacts
      summary: Get contact
      description: Retrieve a specific contact by ID
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Contact details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ContactDetails'
        '404':
          description: Contact not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    put:
      tags:
        - Contacts
      summary: Update contact
      description: Update an existing contact
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ContactUpdate'
      responses:
        '200':
          description: Contact updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Contact'
        '400':
          description: Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/schemas_ValidationError'
        '404':
          description: Contact not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      tags:
        - Contacts
      summary: Delete contact
      description: Soft delete a contact (sets is_deleted to true)
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '204':
          description: Contact deleted successfully
        '404':
          description: Contact not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /contacts/{id}/company:
    put:
      tags:
        - Contacts
      summary: Link contact to company
      description: Associate a contact with a company
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              title: ContactCompanyUpdateRequest
              properties:
                company_id:
                  type: string
                  format: uuid
              required:
                - company_id
      responses:
        '200':
          description: Contact linked to company successfully
        '400':
          description: Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/schemas_ValidationError'
        '404':
          description: Contact or company not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      tags:
        - Contacts
      summary: Unlink contact from company
      description: Remove the association between a contact and company
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '204':
          description: Contact unlinked from company successfully
        '404':
          description: Contact not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /deals:
    get:
      tags:
        - Deals
      summary: List deals
      description: Retrieve a list of all deals with company and stage information
      parameters:
        - name: stage_id
          in: query
          schema:
            type: string
            format: uuid
          description: Filter by deal stage ID
        - name: company_id
          in: query
          schema:
            type: string
            format: uuid
          description: Filter by company ID
      responses:
        '200':
          description: List of deals
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/DealWithDetails'
        '400':
          description: Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/schemas_ValidationError'
    post:
      tags:
        - Deals
      summary: Create deal
      description: Create a new deal
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DealCreate'
      responses:
        '201':
          description: Deal created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Deal'
        '400':
          description: Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/schemas_ValidationError'
  /deals/{id}:
    get:
      tags:
        - Deals
      summary: Get deal
      description: Retrieve a specific deal by ID
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Deal details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DealDetails'
        '404':
          description: Deal not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    put:
      tags:
        - Deals
      summary: Update deal
      description: Update an existing deal
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DealUpdate'
      responses:
        '200':
          description: Deal updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Deal'
        '400':
          description: Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/schemas_ValidationError'
        '404':
          description: Deal not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      tags:
        - Deals
      summary: Delete deal
      description: Delete a deal
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '204':
          description: Deal deleted successfully
        '404':
          description: Deal not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /deal-stages:
    get:
      tags:
        - Deals
      summary: List deal stages
      description: Retrieve all deal stages ordered by pipeline order
      responses:
        '200':
          description: List of deal stages
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/DealStage'
  /interactions:
    get:
      tags:
        - Interactions
      summary: List interactions
      description: Retrieve a list of all interactions
      parameters:
        - name: company_id
          in: query
          schema:
            type: string
            format: uuid
          description: Filter by company ID
        - name: contact_id
          in: query
          schema:
            type: string
            format: uuid
          description: Filter by contact ID
        - name: interaction_type
          in: query
          schema:
            type: string
          description: Filter by interaction type
        - name: from_date
          in: query
          schema:
            type: string
            format: date-time
          description: Filter interactions from this date
        - name: to_date
          in: query
          schema:
            type: string
            format: date-time
          description: Filter interactions to this date
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
          description: Number of interactions to return
        - name: offset
          in: query
          schema:
            type: integer
            minimum: 0
            default: 0
          description: Number of interactions to skip
      responses:
        '200':
          description: List of interactions
          content:
            application/json:
              schema:
                type: object
                properties:
                  interactions:
                    type: array
                    items:
                      $ref: '#/components/schemas/InteractionWithDetails'
                  total_count:
                    type: integer
                  has_more:
                    type: boolean
        '400':
          description: Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/schemas_ValidationError'
    post:
      tags:
        - Interactions
      summary: Create interaction
      description: Create a new interaction record
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InteractionCreate'
      responses:
        '201':
          description: Interaction created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Interaction'
        '400':
          description: Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/schemas_ValidationError'
  /interactions/{id}:
    get:
      tags:
        - Interactions
      summary: Get interaction
      description: Retrieve a specific interaction by ID
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Interaction details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InteractionDetails'
        '404':
          description: Interaction not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    put:
      tags:
        - Interactions
      summary: Update interaction
      description: Update an existing interaction
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InteractionUpdate'
      responses:
        '200':
          description: Interaction updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Interaction'
        '400':
          description: Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/schemas_ValidationError'
        '404':
          description: Interaction not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      tags:
        - Interactions
      summary: Delete interaction
      description: Delete an interaction record
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '204':
          description: Interaction deleted successfully
        '404':
          description: Interaction not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /arli/documents:
    get:
      tags:
        - Arli
      summary: List documents
      description: Retrieve a list of processed documents
      parameters:
        - name: filter_tags
          in: query
          schema:
            type: array
            items:
              type: string
          description: Filter by tags
        - name: metadata_filter
          in: query
          schema:
            type: object
          description: Filter by metadata properties
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: offset
          in: query
          schema:
            type: integer
            minimum: 0
            default: 0
      responses:
        '200':
          description: List of documents
          content:
            application/json:
              schema:
                type: object
                properties:
                  documents:
                    type: array
                    items:
                      $ref: '#/components/schemas/Document'
                  total_count:
                    type: integer
                  has_more:
                    type: boolean
        '400':
          description: Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/schemas_ValidationError'
    post:
      tags:
        - Arli
      summary: Create document
      description: Create a new document for processing
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DocumentCreate'
      responses:
        '201':
          description: Document created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Document'
        '400':
          description: Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/schemas_ValidationError'
  /arli/documents/{id}:
    get:
      tags:
        - Arli
      summary: Get document
      description: Retrieve a specific document by ID
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Document details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentDetails'
        '404':
          description: Document not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    put:
      tags:
        - Arli
      summary: Update document
      description: Update an existing document
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DocumentUpdate'
      responses:
        '200':
          description: Document updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Document'
        '400':
          description: Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/schemas_ValidationError'
        '404':
          description: Document not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      tags:
        - Arli
      summary: Delete document
      description: Delete a document
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '204':
          description: Document deleted successfully
        '404':
          description: Document not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /arli/documents/vectorize:
    post:
      tags:
        - Arli
      summary: Vectorize document
      description: Process a document through the AI vectorization pipeline
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              title: ArliVectorizeRequest
              properties:
                document_id:
                  type: string
                  format: uuid
                content:
                  type: string
                metadata:
                  type: object
              required:
                - document_id
                - content
      responses:
        '200':
          description: Document vectorized successfully
          content:
            application/json:
              schema:
                type: object
                title: ArliVectorizeResponse
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  vector_id:
                    type: string
        '400':
          description: Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/schemas_ValidationError'
        '500':
          description: Vectorization failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /arli/filter-tags:
    get:
      tags:
        - Arli
      summary: List filter tags
      description: Retrieve all available filter tags
      responses:
        '200':
          description: List of filter tags
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/FilterTag'
    post:
      tags:
        - Arli
      summary: Create filter tag
      description: Create a new filter tag
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FilterTagCreate'
      responses:
        '201':
          description: Filter tag created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FilterTag'
        '400':
          description: Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/schemas_ValidationError'
components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  schemas:
    Session:
      type: object
      properties:
        access_token:
          type: string
        refresh_token:
          type: string
        expires_in:
          type: integer
        token_type:
          type: string
        user:
          $ref: '#/components/schemas/User'
    AuthResponse:
      type: object
      properties:
        access_token:
          type: string
        refresh_token:
          type: string
        expires_in:
          type: integer
        token_type:
          type: string
        user:
          $ref: '#/components/schemas/User'
    User:
      type: object
      properties:
        id:
          type: string
          format: uuid
        email:
          type: string
          format: email
        email_confirmed_at:
          type: string
          format: date-time
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
    UserProfile:
      type: object
      properties:
        id:
          type: string
          format: uuid
        full_name:
          type: string
        avatar_url:
          type: string
          format: uri
        timezone:
          type: string
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
    UserProfileUpdate:
      type: object
      properties:
        full_name:
          type: string
        avatar_url:
          type: string
          format: uri
        timezone:
          type: string
    Company:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        website:
          type: string
          format: uri
        phone:
          type: string
        address:
          type: string
        notes:
          type: string
        company_status_id:
          type: string
          format: uuid
        is_deleted:
          type: boolean
          default: false
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
    CompanyDetails:
      allOf:
        - $ref: '#/components/schemas/Company'
        - type: object
          properties:
            contacts:
              type: array
              items:
                $ref: '#/components/schemas/Contact'
    CompanyCreate:
      type: object
      properties:
        name:
          type: string
          minLength: 1
        website:
          type: string
          format: uri
        phone:
          type: string
        address:
          type: string
        notes:
          type: string
        company_status_id:
          type: string
          format: uuid
      required:
        - name
    CompanyUpdate:
      type: object
      properties:
        name:
          type: string
          minLength: 1
        website:
          type: string
          format: uri
        phone:
          type: string
        address:
          type: string
        notes:
          type: string
        company_status_id:
          type: string
          format: uuid
    CompanyStatus:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        pipeline_order:
          type: integer
        created_at:
          type: string
          format: date-time
    CompanyInfo:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        is_deleted:
          type: boolean
      nullable: true
    CompanyBasicInfo:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
      nullable: true
    Contact:
      type: object
      properties:
        id:
          type: string
          format: uuid
        first_name:
          type: string
        last_name:
          type: string
        email:
          type: string
          format: email
        phone:
          type: string
        job_title:
          type: string
        company_id:
          type: string
          format: uuid
          nullable: true
        created_by:
          type: string
          format: uuid
        is_deleted:
          type: boolean
          default: false
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
    ContactWithCompany:
      allOf:
        - $ref: '#/components/schemas/Contact'
        - type: object
          properties:
            company:
              $ref: '#/components/schemas/CompanyInfo'
    ContactDetails:
      allOf:
        - $ref: '#/components/schemas/Contact'
        - type: object
          properties:
            company:
              $ref: '#/components/schemas/CompanyBasicInfo'
    ContactCreate:
      type: object
      properties:
        first_name:
          type: string
          minLength: 1
        last_name:
          type: string
          minLength: 1
        email:
          type: string
          format: email
        phone:
          type: string
        job_title:
          type: string
        company_id:
          type: string
          format: uuid
      required:
        - first_name
        - last_name
    ContactUpdate:
      type: object
      properties:
        first_name:
          type: string
          minLength: 1
        last_name:
          type: string
          minLength: 1
        email:
          type: string
          format: email
        phone:
          type: string
        job_title:
          type: string
        company_id:
          type: string
          format: uuid
          nullable: true
    Deal:
      type: object
      properties:
        id:
          type: string
          format: uuid
        title:
          type: string
        description:
          type: string
        estimated_value:
          type: number
        company_id:
          type: string
          format: uuid
        deal_stage_id:
          type: string
          format: uuid
        expected_close_date:
          type: string
          format: date
        created_by:
          type: string
          format: uuid
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
    DealWithDetails:
      allOf:
        - $ref: '#/components/schemas/Deal'
        - type: object
          properties:
            company:
              $ref: '#/components/schemas/DealCompanyInfo'
            deal_stage:
              $ref: '#/components/schemas/DealStageInfo'
    DealCompanyInfo:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
    DealStageInfo:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        pipeline_order:
          type: integer
    DealDetails:
      allOf:
        - $ref: '#/components/schemas/Deal'
        - type: object
          properties:
            company:
              type: object
              properties:
                id:
                  type: string
                  format: uuid
                name:
                  type: string
                website:
                  type: string
                phone:
                  type: string
            deal_stage:
              type: object
              properties:
                id:
                  type: string
                  format: uuid
                name:
                  type: string
                pipeline_order:
                  type: integer
                is_closed_won:
                  type: boolean
                is_closed_lost:
                  type: boolean
    DealCreate:
      type: object
      properties:
        title:
          type: string
          minLength: 1
        description:
          type: string
        estimated_value:
          type: number
          minimum: 0
        company_id:
          type: string
          format: uuid
        deal_stage_id:
          type: string
          format: uuid
        expected_close_date:
          type: string
          format: date
      required:
        - title
        - company_id
        - deal_stage_id
    DealUpdate:
      type: object
      properties:
        title:
          type: string
          minLength: 1
        description:
          type: string
        estimated_value:
          type: number
          minimum: 0
        company_id:
          type: string
          format: uuid
        deal_stage_id:
          type: string
          format: uuid
        expected_close_date:
          type: string
          format: date
    DealStage:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        pipeline_order:
          type: integer
        is_closed_won:
          type: boolean
          default: false
        is_closed_lost:
          type: boolean
          default: false
        created_at:
          type: string
          format: date-time
    Interaction:
      type: object
      properties:
        id:
          type: string
          format: uuid
        company_id:
          type: string
          format: uuid
          nullable: true
        contact_id:
          type: string
          format: uuid
          nullable: true
        interaction_type:
          type: string
          enum:
            - email
            - phone
            - meeting
            - demo
            - proposal
            - follow-up
            - other
        notes:
          type: string
        interaction_datetime:
          type: string
          format: date-time
        created_by:
          type: string
          format: uuid
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
    InteractionWithDetails:
      allOf:
        - $ref: '#/components/schemas/Interaction'
        - type: object
          properties:
            company:
              type: object
              properties:
                id:
                  type: string
                  format: uuid
                name:
                  type: string
              nullable: true
            contact:
              type: object
              properties:
                id:
                  type: string
                  format: uuid
                first_name:
                  type: string
                last_name:
                  type: string
                email:
                  type: string
              nullable: true
    InteractionDetails:
      allOf:
        - $ref: '#/components/schemas/Interaction'
        - type: object
          properties:
            company:
              type: object
              properties:
                id:
                  type: string
                  format: uuid
                name:
                  type: string
                website:
                  type: string
                phone:
                  type: string
              nullable: true
            contact:
              type: object
              properties:
                id:
                  type: string
                  format: uuid
                first_name:
                  type: string
                last_name:
                  type: string
                email:
                  type: string
                phone:
                  type: string
                job_title:
                  type: string
              nullable: true
    InteractionCreate:
      type: object
      properties:
        company_id:
          type: string
          format: uuid
        contact_id:
          type: string
          format: uuid
        interaction_type:
          type: string
          enum:
            - email
            - phone
            - meeting
            - demo
            - proposal
            - follow-up
            - other
        notes:
          type: string
          minLength: 1
        interaction_datetime:
          type: string
          format: date-time
      required:
        - interaction_type
        - notes
        - interaction_datetime
    InteractionUpdate:
      type: object
      properties:
        interaction_type:
          type: string
          enum:
            - email
            - phone
            - meeting
            - demo
            - proposal
            - follow-up
            - other
        notes:
          type: string
          minLength: 1
        interaction_datetime:
          type: string
          format: date-time
        company_id:
          type: string
          format: uuid
        contact_id:
          type: string
          format: uuid
    Document:
      type: object
      properties:
        id:
          type: string
          format: uuid
        content:
          type: string
        metadata:
          type: object
        filter_tags:
          type: array
          items:
            type: string
        source_id:
          type: string
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
    DocumentDetails:
      allOf:
        - $ref: '#/components/schemas/Document'
        - type: object
          properties:
            vector_embeddings:
              type: array
              items:
                type: number
            processing_status:
              type: string
              enum:
                - pending
                - processing
                - completed
                - failed
    DocumentCreate:
      type: object
      properties:
        content:
          type: string
          minLength: 1
        metadata:
          type: object
        filter_tags:
          type: array
          items:
            type: string
        source_id:
          type: string
      required:
        - content
    DocumentUpdate:
      type: object
      properties:
        content:
          type: string
          minLength: 1
        metadata:
          type: object
        filter_tags:
          type: array
          items:
            type: string
        source_id:
          type: string
    FilterTag:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        color:
          type: string
          pattern: ^#[0-9a-fA-F]{6}$
        created_at:
          type: string
          format: date-time
    FilterTagCreate:
      type: object
      properties:
        name:
          type: string
          minLength: 1
        color:
          type: string
          pattern: ^#[0-9a-fA-F]{6}$
          default: '#3b82f6'
      required:
        - name
    Error:
      type: object
      properties:
        code:
          type: string
        message:
          type: string
        details:
          type: object
      required:
        - code
        - message
    ValidationError:
      type: object
      properties:
        code:
          type: string
          example: validation_error
        message:
          type: string
          example: Invalid input data
        details:
          type: object
          title: ValidationErrorDetails
          properties:
            field_errors:
              type: object
              additionalProperties:
                type: array
                items:
                  type: string
      required:
        - code
        - message
    schemas_ValidationError:
      type: object
      properties:
        code:
          type: string
          example: validation_error
        message:
          type: string
          example: Invalid input data
        details:
          type: object
    ErrorResponse:
      type: object
      properties:
        error:
          type: string
        message:
          type: string
