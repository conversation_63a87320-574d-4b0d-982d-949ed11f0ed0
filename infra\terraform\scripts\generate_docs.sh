#!/bin/bash
set -euo pipefail

# Terraform documentation generation script

echo "# Terraform Infrastructure Documentation"
echo ""
echo "Auto-generated documentation for Terraform configurations."
echo ""
echo "## Modules"
echo ""

# Find all Terraform modules
module_dirs=$(find . -path "*/modules/*" -name "*.tf" -exec dirname {} \; | sort -u)

for dir in $module_dirs; do
    module_name=$(basename "$dir")
    echo "### Module: $module_name"
    echo ""
    echo "Location: \`$dir\`"
    echo ""
    
    # Extract variables if main.tf exists
    if [ -f "$dir/variables.tf" ]; then
        echo "#### Variables"
        echo ""
        grep -A 3 "^variable" "$dir/variables.tf" || true
        echo ""
    fi
    
    # Extract outputs if outputs.tf exists
    if [ -f "$dir/outputs.tf" ]; then
        echo "#### Outputs"
        echo ""
        grep -A 3 "^output" "$dir/outputs.tf" || true
        echo ""
    fi
    
    echo "---"
    echo ""
done

echo "## Environments"
echo ""

# Find all environment configurations
env_dirs=$(find . -path "*/environments/*" -name "*.tf" -exec dirname {} \; | sort -u)

for dir in $env_dirs; do
    env_name=$(basename "$dir")
    echo "### Environment: $env_name"
    echo ""
    echo "Location: \`$dir\`"
    echo ""
    echo "---"
    echo ""
done

echo ""
echo "Generated on: $(date)"