/**
 * AI Agent Platform - Migration Page
 * Application analysis, migration planning and project management
 */

'use client';

import React, { useState, useEffect } from 'react';
import { AppLayout } from '@/components/Layout/AppLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Search,
  Upload,
  FileText,
  GitBranch,
  Settings,
  Play,
  Pause,
  CheckCircle2,
  XCircle,
  AlertCircle,
  Clock,
  RefreshCw,
  BarChart3,
  Layers,
  Zap,
  ArrowRight,
  FolderOpen,
  Database,
  Code,
  Globe
} from 'lucide-react';
import { cn } from '@/lib/utils';
import Link from 'next/link';
import { ApiClient } from '@/lib/api/client';
// Migration schemas are not yet available in the generated types
// Using placeholder types until the backend schemas are properly implemented
type ApplicationAnalysis = any;
type MigrationPlan = any;
type MigrationProject = any;

// Create API client instance
const apiClient = new ApiClient();

export default function MigrationPage() {
  const [analyses, setAnalyses] = useState<ApplicationAnalysis[]>([]);
  const [plans, setPlans] = useState<MigrationPlan[]>([]);
  const [projects, setProjects] = useState<MigrationProject[]>([]);
  const [summary, setSummary] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  // Simple toast replacement
  const toast = (opts: any) => {
    alert(`${opts.title}: ${opts.description}`);
  };

  useEffect(() => {
    // Temporarily disable migration API calls until backend endpoints are implemented
    // loadMigrationData();
    setLoading(false);
  }, []);

  const loadMigrationData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Fetch all migration data (with graceful fallbacks for unimplemented endpoints)
      const [analysesResponse, plansResponse, projectsResponse, summaryResponse] = await Promise.all([
        apiClient.listMigrationAnalyses().catch((err) => {
          console.warn('Migration analyses endpoint not available:', err.message);
          return [];
        }),
        apiClient.listMigrationPlans().catch((err) => {
          console.warn('Migration plans endpoint not available:', err.message);
          return [];
        }),
        apiClient.listMigrationProjects().catch((err) => {
          console.warn('Migration projects endpoint not available:', err.message);
          return [];
        }),
        apiClient.getMigrationSummary().catch((err) => {
          console.warn('Migration summary endpoint not available:', err.message);
          return null;
        })
      ]);
      
      setAnalyses(analysesResponse || []);
      setPlans(plansResponse || []);
      setProjects(projectsResponse || []);
      setSummary(summaryResponse);
      
    } catch (error) {
      console.error('Failed to load migration data:', error);
      setError('Failed to load migration data');
    } finally {
      setLoading(false);
    }
  };

  const handleAnalyzeApp = async () => {
    const appPath = prompt('Enter the application path to analyze:');
    if (!appPath) return;

    try {
      setLoading(true);
      const analysis = await apiClient.analyzeMigration({ app_path: appPath });
      toast({
        title: 'Analysis Started',
        description: `Analysis for ${appPath} has been initiated`,
      });
      await loadMigrationData();
    } catch (error) {
      toast({
        title: 'Analysis Failed',
        description: error instanceof Error ? error.message : 'Failed to start analysis',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCreatePlan = async (analysisId: string) => {
    try {
      const plan = await apiClient.createMigrationPlan({ analysis_id: analysisId });
      toast({
        title: 'Migration Plan Created',
        description: 'Migration plan has been generated successfully',
      });
      await loadMigrationData();
    } catch (error) {
      toast({
        title: 'Plan Creation Failed',
        description: error instanceof Error ? error.message : 'Failed to create plan',
        variant: 'destructive',
      });
    }
  };

  const handleStartMigration = async (planId: string) => {
    try {
      const project = await apiClient.startMigration({ plan_id: planId });
      toast({
        title: 'Migration Started',
        description: `Migration project ${project.project_id} has been started`,
      });
      await loadMigrationData();
    } catch (error) {
      toast({
        title: 'Migration Start Failed',
        description: error instanceof Error ? error.message : 'Failed to start migration',
        variant: 'destructive',
      });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'analyzing': return 'bg-blue-500';
      case 'planning': return 'bg-yellow-500';
      case 'migrating': return 'bg-purple-500';
      case 'testing': return 'bg-orange-500';
      case 'completed': return 'bg-green-500';
      case 'failed': return 'bg-red-500';
      default: return 'bg-gray-400';
    }
  };

  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'completed': return 'default';
      case 'failed': return 'destructive';
      case 'migrating': return 'default';
      default: return 'secondary';
    }
  };

  const formatRelativeTime = (date: string) => {
    const now = new Date();
    const then = new Date(date);
    const diff = now.getTime() - then.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  if (loading && !summary) {
    return (
      <AppLayout>
        <div className="space-y-6">
          <div className="h-8 bg-muted rounded w-1/4 animate-pulse"></div>
          <div className="h-32 bg-muted rounded animate-pulse"></div>
          <div className="h-96 bg-muted rounded animate-pulse"></div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Migration Center</h1>
            <p className="text-muted-foreground">
              Analyze applications, create migration plans, and manage projects
            </p>
          </div>
          <Button onClick={handleAnalyzeApp} disabled={true}>
            <Upload className="h-4 w-4 mr-2" />
            Analyze Application
          </Button>
        </div>

        {/* Coming Soon Notice */}
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center space-y-4">
            <GitBranch className="h-16 w-16 text-muted-foreground mx-auto" />
            <div>
              <h2 className="text-2xl font-bold">Migration Center Coming Soon</h2>
              <p className="text-muted-foreground max-w-md mx-auto">
                The migration functionality is currently under development.
                This feature will allow you to analyze applications and create migration plans to convert them into AI agents.
              </p>
            </div>
            <div className="flex items-center justify-center space-x-2 text-sm text-muted-foreground">
              <div className="h-2 w-2 bg-yellow-500 rounded-full animate-pulse"></div>
              <span>In Development</span>
            </div>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}

