/**
 * AI Agent Platform - Generator Page
 * Create custom AI agents with visual generator
 */

'use client';

import React, { useState, useEffect } from 'react';
import { AppLayout } from '@/components/Layout/AppLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';
import { 
  Download, 
  Code, 
  Settings, 
  Play, 
  RefreshCw,
  Eye,
  CheckCircle,
  XCircle,
  Info,
  AlertCircle,
  ChevronDown,
  ChevronRight
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface Template {
  name: string;
  description: string;
  agent_type: string;
  language: string;
  capabilities: string[];
  config_schema: any;
  examples: any[];
}

interface GenerationRequest {
  agent_name: string;
  agent_type: string;
  language: string;
  description: string;
  capabilities: string[];
  configuration: Record<string, any>;
  custom_logic?: string;
  deployment_config?: Record<string, any>;
}

interface GenerationStatus {
  generation_id: string;
  status: string;
  progress: number;
  message: string;
  download_url?: string;
  error?: string;
}

const agentTypes = [
  { value: 'assistant', label: 'Assistant', description: 'General-purpose conversational agents' },
  { value: 'analyst', label: 'Analyst', description: 'Data analysis and reporting agents' },
  { value: 'processor', label: 'Processor', description: 'Data processing and ETL agents' },
  { value: 'monitor', label: 'Monitor', description: 'System monitoring and alerting agents' },
  { value: 'specialist', label: 'Specialist', description: 'Domain-specific expert agents' },
  { value: 'coordinator', label: 'Coordinator', description: 'Multi-agent coordination agents' }
];

const languages = [
  { value: 'python', label: 'Python', description: 'Versatile and powerful' },
  { value: 'typescript', label: 'TypeScript', description: 'Type-safe JavaScript for web' },
  { value: 'javascript', label: 'JavaScript', description: 'Web-native scripting' }
];

const mockCapabilities = [
  { name: 'natural_language', description: 'Natural language processing and understanding' },
  { name: 'data_analysis', description: 'Data analysis and statistical processing' },
  { name: 'web_scraping', description: 'Web scraping and content extraction' },
  { name: 'api_integration', description: 'REST API integration and management' },
  { name: 'database_access', description: 'Database connectivity and operations' },
  { name: 'file_processing', description: 'File reading, writing, and processing' },
  { name: 'scheduling', description: 'Task scheduling and automation' },
  { name: 'monitoring', description: 'System and application monitoring' },
  { name: 'notifications', description: 'Email, SMS, and push notifications' },
  { name: 'machine_learning', description: 'ML model inference and training' }
];

const mockExamples = [
  {
    name: 'Data Processor',
    config: {
      agent_name: 'data-processor-example',
      agent_type: 'processor',
      language: 'python',
      description: 'Process and transform data from various sources',
      capabilities: ['data_analysis', 'file_processing', 'database_access']
    }
  },
  {
    name: 'API Monitor',
    config: {
      agent_name: 'api-monitor-example',
      agent_type: 'monitor',
      language: 'typescript',
      description: 'Monitor API endpoints for health and performance',
      capabilities: ['api_integration', 'monitoring', 'notifications']
    }
  },
  {
    name: 'Content Assistant',
    config: {
      agent_name: 'content-assistant-example',
      agent_type: 'assistant',
      language: 'python',
      description: 'Generate and manage content using AI',
      capabilities: ['natural_language', 'web_scraping', 'file_processing']
    }
  }
];

export default function AgentGeneratorPage() {
  const [templates, setTemplates] = useState<Template[]>([]);
  const [availableCapabilities, setAvailableCapabilities] = useState<any[]>(mockCapabilities);
  const [examples, setExamples] = useState<any[]>(mockExamples);
  
  const [formData, setFormData] = useState<GenerationRequest>({
    agent_name: '',
    agent_type: 'assistant',
    language: 'python',
    description: '',
    capabilities: [],
    configuration: {},
    custom_logic: '',
    deployment_config: {}
  });
  
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [validationWarnings, setValidationWarnings] = useState<string[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationStatus, setGenerationStatus] = useState<GenerationStatus | null>(null);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [showStatus, setShowStatus] = useState(false);
  const [expandedAccordion, setExpandedAccordion] = useState<string | null>(null);

  // Simple toast replacement
  const toast = (opts: any) => {
    alert(`${opts.title}: ${opts.description}`);
  };

  // Load initial data
  useEffect(() => {
    loadTemplates();
    loadCapabilities();
    loadExamples();
  }, []);

  const loadTemplates = async () => {
    try {
      const response = await fetch('/api/v1/generation/templates');
      if (response.ok) {
        const data = await response.json();
        setTemplates(data);
      }
    } catch (error) {
      console.error('Failed to load templates:', error);
    }
  };

  const loadCapabilities = async () => {
    try {
      const response = await fetch('/api/v1/generation/capabilities');
      if (response.ok) {
        const data = await response.json();
        setAvailableCapabilities(data.capabilities || mockCapabilities);
      }
    } catch (error) {
      console.error('Failed to load capabilities:', error);
      setAvailableCapabilities(mockCapabilities);
    }
  };

  const loadExamples = async () => {
    try {
      const response = await fetch('/api/v1/generation/examples');
      if (response.ok) {
        const data = await response.json();
        setExamples(data.examples || mockExamples);
      }
    } catch (error) {
      console.error('Failed to load examples:', error);
      setExamples(mockExamples);
    }
  };

  const validateForm = async () => {
    try {
      const response = await fetch('/api/v1/generation/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });
      
      if (response.ok) {
        const validation = await response.json();
        setValidationErrors(validation.errors || []);
        setValidationWarnings(validation.warnings || []);
        return validation.valid;
      } else {
        // Fallback to basic validation
        const errors = [];
        const warnings = [];
        
        if (!formData.agent_name) errors.push('Agent name is required');
        if (!formData.description) errors.push('Description is required');
        if (formData.capabilities.length === 0) errors.push('At least one capability must be selected');
        
        if (formData.agent_name && formData.agent_name.length < 3) {
          warnings.push('Agent name should be at least 3 characters long');
        }
        
        setValidationErrors(errors);
        setValidationWarnings(warnings);
        return errors.length === 0;
      }
    } catch (error) {
      console.error('Validation failed:', error);
      return false;
    }
  };

  const handleGenerate = async () => {
    const isValid = await validateForm();
    if (!isValid) {
      toast({
        title: 'Validation Failed',
        description: 'Please fix the validation errors before generating.',
      });
      return;
    }

    setIsGenerating(true);
    
    try {
      const response = await fetch('/api/v1/generation/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });
      
      if (response.ok) {
        const result = await response.json();
        const initialStatus: GenerationStatus = {
          generation_id: result.generation_id,
          status: result.status,
          progress: 0,
          message: result.message
        };
        
        setGenerationStatus(initialStatus);
        setShowStatus(true);
        
        // Start polling for status updates
        pollGenerationStatus(result.generation_id);
        
        toast({
          title: 'Generation Started',
          description: 'Your agent is being generated. You can track progress in the status dialog.',
        });
      } else {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to start generation');
      }
      
    } catch (error) {
      console.error('Generation failed:', error);
      toast({
        title: 'Generation Failed',
        description: error instanceof Error ? error.message : 'Failed to start agent generation. Please try again.',
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const pollGenerationStatus = async (generationId: string) => {
    const poll = async () => {
      try {
        const response = await fetch(`/api/v1/generation/status/${generationId}`);
        if (response.ok) {
          const status = await response.json();
          setGenerationStatus(status);
          
          if (status.status === 'completed') {
            toast({
              title: 'Generation Complete',
              description: 'Your agent has been generated successfully!',
            });
            return;
          } else if (status.status === 'failed') {
            toast({
              title: 'Generation Failed',
              description: status.error || 'Agent generation failed',
            });
            return;
          }
          
          // Continue polling if still in progress
          if (status.status === 'generating' || status.status === 'pending') {
            setTimeout(poll, 2000);
          }
        } else {
          console.error('Failed to fetch status');
          setTimeout(poll, 5000); // Retry after longer delay on error
        }
      } catch (error) {
        console.error('Error polling generation status:', error);
        setTimeout(poll, 5000); // Retry after longer delay on error
      }
    };
    
    // Start polling after a short delay
    setTimeout(poll, 2000);
  };

  const handleDownload = async () => {
    if (!generationStatus?.generation_id) return;
    
    try {
      const response = await fetch(`/api/v1/generation/download/${generationStatus.generation_id}`);
      
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `generated_agent_${generationStatus.generation_id}.zip`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        toast({
          title: 'Download Started',
          description: 'Your generated agent is being downloaded.',
        });
      } else {
        const error = await response.json();
        throw new Error(error.detail || 'Download failed');
      }
      
    } catch (error) {
      console.error('Download failed:', error);
      toast({
        title: 'Download Failed',
        description: error instanceof Error ? error.message : 'Failed to download the generated agent.',
      });
    }
  };

  const loadExample = (example: any) => {
    setFormData(example.config);
    toast({
      title: 'Example Loaded',
      description: `Loaded configuration for ${example.name}`,
    });
  };

  const handleCapabilityChange = (capability: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      capabilities: checked 
        ? [...prev.capabilities, capability]
        : prev.capabilities.filter(c => c !== capability)
    }));
  };

  return (
    <AppLayout>
      <div className="space-y-8">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">AI Agent Generator</h1>
          <p className="text-muted-foreground text-lg">
            Create custom AI agents with our visual generator
          </p>
        </div>

        {/* Examples Section */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Start Examples</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {examples.map((example, index) => (
                <Card key={index} className="border-2 border-dashed">
                  <CardContent className="pt-6">
                    <div className="space-y-3">
                      <h4 className="font-semibold">{example.name}</h4>
                      <p className="text-sm text-muted-foreground line-clamp-2">
                        {example.config.description}
                      </p>
                      <div className="flex gap-2">
                        <Badge variant="secondary">
                          {example.config.agent_type}
                        </Badge>
                        <Badge variant="outline">
                          {example.config.language}
                        </Badge>
                      </div>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => loadExample(example)}
                        className="w-full"
                      >
                        <Play className="h-4 w-4 mr-2" />
                        Load Example
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Main Form */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Column - Basic Settings */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Basic Configuration</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label htmlFor="agent-name" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Agent Name *</label>
                  <Input
                    id="agent-name"
                    placeholder="my-awesome-agent"
                    value={formData.agent_name}
                    onChange={(e) => setFormData(prev => ({ ...prev, agent_name: e.target.value }))}
                  />
                </div>

                <div className="space-y-2">
                  <label htmlFor="description" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Description *</label>
                  <textarea
                    id="description"
                    placeholder="Describe what your agent does..."
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    rows={3}
                    className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label htmlFor="agent-type" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Agent Type *</label>
                    <select
                      id="agent-type"
                      value={formData.agent_type}
                      onChange={(e) => setFormData(prev => ({ ...prev, agent_type: e.target.value }))}
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    >
                      {agentTypes.map(type => (
                        <option key={type.value} value={type.value}>
                          {type.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="language" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Language *</label>
                    <select
                      id="language"
                      value={formData.language}
                      onChange={(e) => setFormData(prev => ({ ...prev, language: e.target.value }))}
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    >
                      {languages.map(lang => (
                        <option key={lang.value} value={lang.value}>
                          {lang.label}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Capabilities</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 gap-3">
                  {availableCapabilities.map((capability) => (
                    <div key={capability.name} className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        id={`capability-${capability.name}`}
                        checked={formData.capabilities.includes(capability.name)}
                        onChange={(e) => 
                          handleCapabilityChange(capability.name, e.target.checked)
                        }
                        className="h-4 w-4 rounded border-border text-primary focus:ring-2 focus:ring-ring focus:ring-offset-2"
                      />
                      <div className="flex-1">
                        <label 
                          htmlFor={`capability-${capability.name}`}
                          className="text-sm font-medium cursor-pointer"
                        >
                          {capability.name.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())}
                        </label>
                        <p className="text-xs text-muted-foreground">
                          {capability.description}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Advanced Settings */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Advanced Settings</CardTitle>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => setShowAdvanced(!showAdvanced)}
                  >
                    <Settings className="h-4 w-4 mr-2" />
                    {showAdvanced ? 'Hide' : 'Show'}
                  </Button>
                </div>
              </CardHeader>
              {showAdvanced && (
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <label htmlFor="custom-logic" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Custom Logic</label>
                    <textarea
                      id="custom-logic"
                      placeholder="Add custom initialization code..."
                      value={formData.custom_logic}
                      onChange={(e) => setFormData(prev => ({ ...prev, custom_logic: e.target.value }))}
                      rows={6}
                      className="flex min-h-[150px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 font-mono"
                    />
                  </div>

                  {/* Runtime Configuration Accordion */}
                  <div className="border rounded-lg">
                    <button
                      className="flex w-full items-center justify-between p-4 text-left hover:bg-muted/50 transition-colors"
                      onClick={() => setExpandedAccordion(
                        expandedAccordion === 'runtime' ? null : 'runtime'
                      )}
                    >
                      <span className="font-medium">Runtime Configuration</span>
                      {expandedAccordion === 'runtime' ? (
                        <ChevronDown className="h-4 w-4" />
                      ) : (
                        <ChevronRight className="h-4 w-4" />
                      )}
                    </button>
                    {expandedAccordion === 'runtime' && (
                      <div className="p-4 border-t space-y-3">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <label htmlFor="max-tasks" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Max Concurrent Tasks</label>
                            <Input
                              id="max-tasks"
                              type="number"
                              value={formData.configuration.max_concurrent_tasks || 5}
                              onChange={(e) => setFormData(prev => ({
                                ...prev,
                                configuration: { ...prev.configuration, max_concurrent_tasks: parseInt(e.target.value) }
                              }))}
                            />
                          </div>
                          <div className="space-y-2">
                            <label htmlFor="timeout" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Timeout (seconds)</label>
                            <Input
                              id="timeout"
                              type="number"
                              value={formData.configuration.timeout_seconds || 300}
                              onChange={(e) => setFormData(prev => ({
                                ...prev,
                                configuration: { ...prev.configuration, timeout_seconds: parseInt(e.target.value) }
                              }))}
                            />
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Deployment Configuration Accordion */}
                  <div className="border rounded-lg">
                    <button
                      className="flex w-full items-center justify-between p-4 text-left hover:bg-muted/50 transition-colors"
                      onClick={() => setExpandedAccordion(
                        expandedAccordion === 'deployment' ? null : 'deployment'
                      )}
                    >
                      <span className="font-medium">Deployment Configuration</span>
                      {expandedAccordion === 'deployment' ? (
                        <ChevronDown className="h-4 w-4" />
                      ) : (
                        <ChevronRight className="h-4 w-4" />
                      )}
                    </button>
                    {expandedAccordion === 'deployment' && (
                      <div className="p-4 border-t space-y-3">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <label htmlFor="cpu-limit" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">CPU Limit</label>
                            <Input
                              id="cpu-limit"
                              placeholder="500m"
                              value={formData.deployment_config?.cpu_limit || ''}
                              onChange={(e) => setFormData(prev => ({
                                ...prev,
                                deployment_config: { ...prev.deployment_config, cpu_limit: e.target.value }
                              }))}
                            />
                          </div>
                          <div className="space-y-2">
                            <label htmlFor="memory-limit" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Memory Limit</label>
                            <Input
                              id="memory-limit"
                              placeholder="512Mi"
                              value={formData.deployment_config?.memory_limit || ''}
                              onChange={(e) => setFormData(prev => ({
                                ...prev,
                                deployment_config: { ...prev.deployment_config, memory_limit: e.target.value }
                              }))}
                            />
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              )}
            </Card>

            {/* Validation Results */}
            {(validationErrors.length > 0 || validationWarnings.length > 0) && (
              <div className="space-y-3">
                {validationErrors.map((error, index) => (
                  <div key={`error-${index}`} className="flex items-center space-x-2 rounded-lg border border-destructive bg-destructive/10 p-4">
                    <XCircle className="h-4 w-4 text-destructive" />
                    <div className="flex-1 text-sm text-destructive">{error}</div>
                  </div>
                ))}
                {validationWarnings.map((warning, index) => (
                  <div key={`warning-${index}`} className="flex items-center space-x-2 rounded-lg border border-yellow-200 bg-yellow-50 p-4">
                    <AlertCircle className="h-4 w-4 text-yellow-600" />
                    <div className="flex-1 text-sm text-yellow-700">{warning}</div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Button
                  variant="outline"
                  onClick={() => setShowPreview(!showPreview)}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  Preview Configuration
                </Button>
                <Button
                  variant="outline"
                  onClick={validateForm}
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Validate
                </Button>
              </div>
              
              <div className="flex items-center gap-4">
                {generationStatus && (
                  <Button
                    variant="outline"
                    onClick={() => setShowStatus(!showStatus)}
                  >
                    <Info className="h-4 w-4 mr-2" />
                    View Status
                  </Button>
                )}
                <Button
                  size="lg"
                  disabled={isGenerating || !formData.agent_name || !formData.description || formData.capabilities.length === 0}
                  onClick={handleGenerate}
                >
                  {isGenerating ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Code className="h-4 w-4 mr-2" />
                      Generate Agent
                    </>
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Configuration Preview */}
        {showPreview && (
          <Card>
            <CardHeader>
              <CardTitle>Configuration Preview</CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="bg-muted p-4 rounded-lg text-sm overflow-auto max-h-96 whitespace-pre-wrap">
                {JSON.stringify(formData, null, 2)}
              </pre>
            </CardContent>
          </Card>
        )}

        {/* Generation Status */}
        {showStatus && generationStatus && (
          <Card>
            <CardHeader>
              <CardTitle>Generation Status</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="font-medium">Status:</span>
                <Badge
                  variant={
                    generationStatus.status === 'completed' ? 'default' :
                    generationStatus.status === 'failed' ? 'destructive' :
                    generationStatus.status === 'generating' ? 'secondary' : 'outline'
                  }
                >
                  {generationStatus.status.toUpperCase()}
                </Badge>
              </div>
              
              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span className="font-medium">Progress:</span>
                  <span className="text-muted-foreground">{generationStatus.progress}%</span>
                </div>
                <Progress 
                  value={generationStatus.progress} 
                  className="h-2"
                />
              </div>
              
              <div>
                <span className="font-medium">Message:</span>
                <p className="text-sm text-muted-foreground mt-1">{generationStatus.message}</p>
              </div>
              
              {generationStatus.error && (
                <div className="flex items-center space-x-2 rounded-lg border border-destructive bg-destructive/10 p-4">
                  <XCircle className="h-4 w-4 text-destructive" />
                  <div className="flex-1">
                    <div className="font-medium text-destructive">Error:</div>
                    <div className="text-sm text-destructive">{generationStatus.error}</div>
                  </div>
                </div>
              )}
              
              {generationStatus.status === 'completed' && (
                <Button
                  onClick={handleDownload}
                  className="w-full"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download Generated Agent
                </Button>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </AppLayout>
  );
}