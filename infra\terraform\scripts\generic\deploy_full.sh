#!/bin/bash
# Complete deployment script for any environment
# Runs all deployment steps in sequence

# Source common utilities
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/../common/utils.sh"
source "$SCRIPT_DIR/../common/environment.sh"

# Script configuration
SCRIPT_NAME="deploy_full.sh"
SCRIPT_DESCRIPTION="Complete deployment pipeline for specified environment"

# Required tools
REQUIRED_TOOLS=("terraform" "gcloud" "bazel" "docker")

# Full deployment function
deploy_full() {
    local env="$1"
    
    print_header "🚀 FULL DEPLOYMENT PIPELINE"
    
    print_step "Step 1: Environment setup and validation"
    if ! setup_environment "$env"; then
        return 1
    fi
    
    print_environment_summary "$env"
    production_safety_check
    
    print_step "Step 2: Deploy infrastructure"
    print_info "Running infrastructure deployment..."
    
    if ! execute "$SCRIPT_DIR/deploy_infrastructure.sh" "$env"; then
        print_error "Infrastructure deployment failed"
        return 1
    fi
    
    print_success "Infrastructure deployment completed"
    
    print_step "Step 3: Run database migrations"
    print_info "Running database migrations..."
    
    if ! execute "$SCRIPT_DIR/migrate_database.sh" "$env"; then
        print_error "Database migration failed"
        return 1
    fi
    
    print_success "Database migrations completed"
    
    print_step "Step 4: Build and push Docker images"
    print_info "Building and pushing Docker images..."
    
    if ! execute "$SCRIPT_DIR/build_and_push_images.sh" "$env"; then
        print_error "Docker image build and push failed"
        return 1
    fi
    
    print_success "Docker images built and pushed"
    
    print_step "Step 5: Configure VM"
    print_info "Configuring VM with docker-compose..."
    
    if ! execute "$SCRIPT_DIR/configure_vm.sh" "$env"; then
        print_error "VM configuration failed"
        return 1
    fi
    
    print_success "VM configuration completed"
    
    print_step "Step 6: Start backend services"
    print_info "Starting backend services on VM..."
    
    # Get instance name from terraform
    cd "terraform/environments/$env"
    local instance_name
    if ! instance_name=$(terraform output -raw instance_name 2>/dev/null); then
        print_error "Could not get instance name"
        return 1
    fi
    
    cd - > /dev/null
    
    # Start services using the update-images script
    if ! gcloud compute ssh "$instance_name" --zone="$ZONE" --command="cd ~/platform && bash update-images.sh"; then
        print_error "Failed to start backend services"
        return 1
    fi
    
    print_success "Backend services started"
    
    print_step "Step 7: Deploy web application"
    print_info "Building and deploying web application..."
    
    if ! execute "$SCRIPT_DIR/deploy_web.sh" "$env"; then
        print_error "Web deployment failed"
        return 1
    fi
    
    print_success "Web application deployed"
    
    print_step "Step 8: Verify deployment"
    print_info "Verifying complete deployment..."
    
    # Wait for services to be ready
    sleep 30
    
    # Check service status
    print_info "Checking service status..."
    gcloud compute ssh "$instance_name" --zone="$ZONE" --command="cd ~/platform && docker ps --filter name=platform- --format 'table {{.Names}}\\t{{.Status}}\\t{{.Ports}}'"
    
    # Test health endpoints
    print_info "Testing health endpoints..."
    
    # Get external IP
    cd "terraform/environments/$env"
    local external_ip
    if external_ip=$(terraform output -raw instance_external_ip 2>/dev/null); then
        print_info "External IP: $external_ip"
        
        # Test health endpoint
        if retry_command 3 5 curl -f "http://$external_ip:80/health"; then
            print_success "Health endpoint responding"
        else
            print_warning "Health endpoint not responding"
        fi
        
        # Test nginx
        if retry_command 3 5 curl -f "http://$external_ip:80"; then
            print_success "Nginx responding"
        else
            print_warning "Nginx not responding"
        fi
    fi
    
    cd - > /dev/null
    
    print_success "Deployment verification completed"
    
    print_step "Step 9: Deployment summary"
    
    # Get deployment information
    cd "terraform/environments/$env"
    
    local project_id
    local load_balancer_ip
    local web_bucket_name
    local ssl_domains
    
    project_id=$(terraform output -raw project_id 2>/dev/null || echo "N/A")
    load_balancer_ip=$(terraform output -raw load_balancer_ip 2>/dev/null || echo "N/A")
    web_bucket_name=$(terraform output -raw web_bucket_name 2>/dev/null || echo "N/A")
    ssl_domains=$(terraform output -raw ssl_domains 2>/dev/null || echo "N/A")
    
    cd - > /dev/null
    
    print_success "🎉 FULL DEPLOYMENT COMPLETED SUCCESSFULLY!"
    
    echo ""
    print_info "📋 Deployment Summary:"
    echo "  🌍 Environment: $env"
    echo "  🏗️  Project ID: $project_id"
    echo "  📍 Region: $REGION"
    echo "  📍 Zone: $ZONE"
    echo "  🖥️  Instance: $instance_name"
    echo "  🌐 Load Balancer IP: $load_balancer_ip"
    echo "  📄 Web Bucket: $web_bucket_name"
    echo "  🔒 SSL Domains: $ssl_domains"
    echo ""
    
    print_info "🌐 Access URLs:"
    if [[ "$load_balancer_ip" != "N/A" ]]; then
        echo "  🔗 Load Balancer: http://$load_balancer_ip"
    fi
    if [[ "$ssl_domains" != "N/A" ]]; then
        # Parse SSL domains (comma-separated)
        IFS=',' read -ra DOMAIN_ARRAY <<< "$ssl_domains"
        for domain in "${DOMAIN_ARRAY[@]}"; do
            domain=$(echo "$domain" | xargs)  # trim whitespace
            echo "  🔒 HTTPS: https://$domain"
        done
    fi
    if [[ "$web_bucket_name" != "N/A" ]]; then
        echo "  💾 Direct Bucket: https://storage.googleapis.com/$web_bucket_name/index.html"
    fi
    echo ""
    
    print_info "🛠️  Management Commands:"
    echo "  • SSH to VM: gcloud compute ssh $instance_name --zone=$ZONE"
    echo "  • View logs: gcloud compute ssh $instance_name --zone=$ZONE --command='cd ~/platform && ./logs.sh'"
    echo "  • Check status: gcloud compute ssh $instance_name --zone=$ZONE --command='cd ~/platform && docker ps'"
    echo "  • Update images: ./scripts/generic/build_and_push_images.sh $env && gcloud compute ssh $instance_name --zone=$ZONE --command='cd ~/platform && bash update-images.sh'"
    echo "  • Update config: ./scripts/generic/configure_vm.sh update-config $env"
    echo "  • Deploy web: ./scripts/generic/deploy_web.sh $env"
    echo ""
    
    print_info "🔍 Monitoring:"
    echo "  • Service status: gcloud compute ssh $instance_name --zone=$ZONE --command='cd ~/platform && docker ps'"
    echo "  • Service logs: gcloud compute ssh $instance_name --zone=$ZONE --command='cd ~/platform && docker compose logs -f'"
    echo "  • Health check: curl http://$external_ip:80/health"
    echo "  • Nginx status: curl http://$external_ip:80"
    echo ""
    
    return 0
}

# Quick deployment (skip infrastructure)
deploy_quick() {
    local env="$1"
    
    print_header "⚡ QUICK DEPLOYMENT (SKIP INFRASTRUCTURE)"
    
    print_step "Step 1: Environment setup"
    if ! setup_environment "$env"; then
        return 1
    fi
    
    print_environment_summary "$env"
    production_safety_check
    
    print_step "Step 2: Build and push Docker images"
    if ! execute "$SCRIPT_DIR/build_and_push_images.sh" "$env"; then
        print_error "Docker image build and push failed"
        return 1
    fi
    
    print_step "Step 3: Update services on VM"
    # Get instance name
    cd "terraform/environments/$env"
    local instance_name
    if ! instance_name=$(terraform output -raw instance_name 2>/dev/null); then
        print_error "Could not get instance name"
        return 1
    fi
    cd - > /dev/null
    
    # Update images
    if ! gcloud compute ssh "$instance_name" --zone="$ZONE" --command="cd ~/platform && bash update-images.sh"; then
        print_error "Failed to update services"
        return 1
    fi
    
    print_step "Step 4: Deploy web application"
    if ! execute "$SCRIPT_DIR/deploy_web.sh" "$env"; then
        print_error "Web deployment failed"
        return 1
    fi
    
    print_success "Quick deployment completed successfully! 🎉"
    return 0
}

# Main script execution
main() {
    setup_error_handling
    
    # Parse command line arguments
    parse_args "$SCRIPT_NAME" "$@"
    
    # Print banner
    print_banner "$SCRIPT_NAME" "$SCRIPT_DESCRIPTION"
    
    # Check required tools
    if ! check_required_tools "${REQUIRED_TOOLS[@]}"; then
        exit 1
    fi
    
    # Change to workspace directory
    if ! change_to_workspace; then
        exit 1
    fi
    
    # Validate environment
    if ! validate_environment "$ENVIRONMENT"; then
        exit 1
    fi
    
    # Handle special commands
    case "$1" in
        "quick")
            if deploy_quick "$ENVIRONMENT"; then
                print_success "Quick deployment completed successfully! 🎉"
                exit 0
            else
                print_error "Quick deployment failed!"
                exit 1
            fi
            ;;
        *)
            # Full deployment
            if deploy_full "$ENVIRONMENT"; then
                print_success "Full deployment completed successfully! 🎉"
                exit 0
            else
                print_error "Full deployment failed!"
                exit 1
            fi
            ;;
    esac
}

# Show usage if no arguments provided
if [[ $# -eq 0 ]]; then
    print_usage "$SCRIPT_NAME"
    echo ""
    echo "Additional commands:"
    echo "  quick ENVIRONMENT    Quick deployment (skip infrastructure)"
    echo ""
    echo "Full deployment includes:"
    echo "  1. Deploy infrastructure (Terraform)"
    echo "  2. Run database migrations"
    echo "  3. Build and push Docker images"
    echo "  4. Configure VM with docker-compose"
    echo "  5. Start backend services"
    echo "  6. Deploy web application"
    echo "  7. Verify deployment"
    echo ""
    echo "Quick deployment includes:"
    echo "  1. Build and push Docker images"
    echo "  2. Update services on VM"
    echo "  3. Deploy web application"
    exit 1
fi

# Execute main function with all arguments
main "$@"