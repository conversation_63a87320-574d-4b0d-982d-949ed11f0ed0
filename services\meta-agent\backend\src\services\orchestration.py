"""
AI Agent Platform - Orchestration Service Layer
"""

from typing import List, Optional, Dict, Any
from uuid import UUID
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, func
from sqlalchemy.orm import selectinload
import structlog

from database.models import (
    Orchestration, OrchestrationPattern, OrchestrationMember,
    User, Agent, Task
)
from orchestration.manager import orchestration_manager

logger = structlog.get_logger()


class OrchestrationService:
    """Service for managing multi-agent orchestrations"""
    
    def __init__(self, db_session: AsyncSession):
        self.db = db_session
    
    async def create_orchestration(
        self,
        owner_id: UUID,
        name: str,
        description: Optional[str] = None,
        pattern: OrchestrationPattern = OrchestrationPattern.SEQUENTIAL,
        config: Dict[str, Any] = None,
        execution_plan: Dict[str, Any] = None,
        agent_ids: List[UUID] = None
    ) -> Orchestration:
        """Create a new orchestration"""
        try:
            # Create orchestration record
            orchestration = Orchestration(
                name=name,
                description=description,
                pattern=pattern,
                owner_id=owner_id,
                config=config or {},
                execution_plan=execution_plan or {},
                status="created"
            )
            
            self.db.add(orchestration)
            await self.db.flush()  # Get orchestration ID
            
            # Add orchestration members
            if agent_ids:
                for idx, agent_id in enumerate(agent_ids):
                    member = OrchestrationMember(
                        orchestration_id=orchestration.id,
                        agent_id=agent_id,
                        role="worker",  # TODO: Support different roles
                        join_order=idx
                    )
                    self.db.add(member)
            
            await self.db.commit()
            await self.db.refresh(orchestration)
            
            # Load relationships
            await self.db.execute(
                select(Orchestration)
                .options(selectinload(Orchestration.members))
                .where(Orchestration.id == orchestration.id)
            )
            
            logger.info(
                "Orchestration created successfully",
                orchestration_id=str(orchestration.id),
                name=name,
                pattern=pattern
            )
            
            return orchestration
            
        except Exception as e:
            await self.db.rollback()
            logger.error("Failed to create orchestration", error=str(e))
            raise
    
    async def get_orchestration(self, orchestration_id: UUID) -> Optional[Orchestration]:
        """Get orchestration by ID"""
        try:
            result = await self.db.execute(
                select(Orchestration)
                .options(
                    selectinload(Orchestration.owner),
                    selectinload(Orchestration.members).selectinload(OrchestrationMember.agent),
                    selectinload(Orchestration.tasks)
                )
                .where(Orchestration.id == orchestration_id)
            )
            return result.scalar_one_or_none()
            
        except Exception as e:
            logger.error(
                "Failed to get orchestration",
                orchestration_id=str(orchestration_id),
                error=str(e)
            )
            raise
    
    async def list_orchestrations(
        self,
        owner_id: Optional[UUID] = None,
        status: Optional[str] = None,
        pattern: Optional[OrchestrationPattern] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[Orchestration]:
        """List orchestrations with filters"""
        try:
            query = select(Orchestration).options(
                selectinload(Orchestration.owner),
                selectinload(Orchestration.members)
            )
            
            if owner_id:
                query = query.where(Orchestration.owner_id == owner_id)
            
            if status:
                query = query.where(Orchestration.status == status)
            
            if pattern:
                query = query.where(Orchestration.pattern == pattern)
            
            query = query.limit(limit).offset(offset)
            
            result = await self.db.execute(query)
            return list(result.scalars().all())
            
        except Exception as e:
            logger.error("Failed to list orchestrations", error=str(e))
            raise
    
    async def update_orchestration(
        self,
        orchestration_id: UUID,
        **updates
    ) -> Optional[Orchestration]:
        """Update orchestration"""
        try:
            result = await self.db.execute(
                update(Orchestration)
                .where(Orchestration.id == orchestration_id)
                .values(**updates)
                .returning(Orchestration)
            )
            
            orchestration = result.scalar_one_or_none()
            if orchestration:
                await self.db.commit()
                logger.info(
                    "Orchestration updated",
                    orchestration_id=str(orchestration_id)
                )
            
            return orchestration
            
        except Exception as e:
            await self.db.rollback()
            logger.error(
                "Failed to update orchestration",
                orchestration_id=str(orchestration_id),
                error=str(e)
            )
            raise
    
    async def start_orchestration(self, orchestration_id: UUID) -> bool:
        """Start an orchestration"""
        try:
            # Get orchestration with members
            orchestration = await self.get_orchestration(orchestration_id)
            if not orchestration:
                logger.error("Orchestration not found", orchestration_id=str(orchestration_id))
                return False
            
            if orchestration.status != "created":
                logger.warning(
                    "Cannot start orchestration - invalid status",
                    orchestration_id=str(orchestration_id),
                    current_status=orchestration.status
                )
                return False
            
            # Get agent IDs from members
            agent_ids = [member.agent_id for member in orchestration.members]
            
            if not agent_ids:
                logger.error(
                    "No agents assigned to orchestration",
                    orchestration_id=str(orchestration_id)
                )
                return False
            
            # Create orchestration engine
            engine = await orchestration_manager.create_orchestration(
                orchestration,
                agent_ids,
                orchestration.execution_plan
            )
            
            # Start orchestration
            success = await orchestration_manager.start_orchestration(orchestration_id)
            
            if success:
                await self.update_orchestration(
                    orchestration_id,
                    status="running",
                    started_at=engine.start_time
                )
                
                logger.info(
                    "Orchestration started successfully",
                    orchestration_id=str(orchestration_id)
                )
            else:
                await self.update_orchestration(
                    orchestration_id,
                    status="failed"
                )
            
            return success
            
        except Exception as e:
            logger.error(
                "Failed to start orchestration",
                orchestration_id=str(orchestration_id),
                error=str(e)
            )
            return False
    
    async def stop_orchestration(
        self,
        orchestration_id: UUID,
        graceful: bool = True
    ) -> bool:
        """Stop an orchestration"""
        try:
            # Get orchestration
            orchestration = await self.get_orchestration(orchestration_id)
            if not orchestration:
                return False
            
            if orchestration.status not in ["running", "paused"]:
                logger.warning(
                    "Cannot stop orchestration - invalid status",
                    orchestration_id=str(orchestration_id),
                    current_status=orchestration.status
                )
                return False
            
            # Stop orchestration engine
            success = await orchestration_manager.stop_orchestration(
                orchestration_id,
                graceful
            )
            
            if success:
                await self.update_orchestration(
                    orchestration_id,
                    status="stopped",
                    completed_at=datetime.utcnow()
                )
                
                logger.info(
                    "Orchestration stopped successfully",
                    orchestration_id=str(orchestration_id)
                )
            
            return success
            
        except Exception as e:
            logger.error(
                "Failed to stop orchestration",
                orchestration_id=str(orchestration_id),
                error=str(e)
            )
            return False
    
    async def pause_orchestration(self, orchestration_id: UUID) -> bool:
        """Pause an orchestration"""
        try:
            success = await orchestration_manager.pause_orchestration(orchestration_id)
            
            if success:
                await self.update_orchestration(
                    orchestration_id,
                    status="paused"
                )
            
            return success
            
        except Exception as e:
            logger.error(
                "Failed to pause orchestration",
                orchestration_id=str(orchestration_id),
                error=str(e)
            )
            return False
    
    async def resume_orchestration(self, orchestration_id: UUID) -> bool:
        """Resume an orchestration"""
        try:
            success = await orchestration_manager.resume_orchestration(orchestration_id)
            
            if success:
                await self.update_orchestration(
                    orchestration_id,
                    status="running"
                )
            
            return success
            
        except Exception as e:
            logger.error(
                "Failed to resume orchestration",
                orchestration_id=str(orchestration_id),
                error=str(e)
            )
            return False
    
    async def get_orchestration_progress(
        self,
        orchestration_id: UUID
    ) -> Optional[Dict[str, Any]]:
        """Get orchestration progress"""
        try:
            progress = await orchestration_manager.get_orchestration_progress(
                orchestration_id
            )
            
            if progress:
                # Update database with progress
                await self.update_orchestration(
                    orchestration_id,
                    progress_percentage=int(progress['progress_percentage'])
                )
            
            return progress
            
        except Exception as e:
            logger.error(
                "Failed to get orchestration progress",
                orchestration_id=str(orchestration_id),
                error=str(e)
            )
            return None
    
    async def add_agent_to_orchestration(
        self,
        orchestration_id: UUID,
        agent_id: UUID,
        role: str = "worker"
    ) -> bool:
        """Add an agent to orchestration"""
        try:
            # Check if agent is already in orchestration
            result = await self.db.execute(
                select(OrchestrationMember).where(
                    OrchestrationMember.orchestration_id == orchestration_id,
                    OrchestrationMember.agent_id == agent_id
                )
            )
            
            if result.scalar_one_or_none():
                logger.warning(
                    "Agent already in orchestration",
                    orchestration_id=str(orchestration_id),
                    agent_id=str(agent_id)
                )
                return False
            
            # Get current member count for join order
            count_result = await self.db.execute(
                select(func.count(OrchestrationMember.id)).where(
                    OrchestrationMember.orchestration_id == orchestration_id
                )
            )
            member_count = count_result.scalar()
            
            # Add member
            member = OrchestrationMember(
                orchestration_id=orchestration_id,
                agent_id=agent_id,
                role=role,
                join_order=member_count
            )
            
            self.db.add(member)
            await self.db.commit()
            
            logger.info(
                "Agent added to orchestration",
                orchestration_id=str(orchestration_id),
                agent_id=str(agent_id)
            )
            
            return True
            
        except Exception as e:
            await self.db.rollback()
            logger.error(
                "Failed to add agent to orchestration",
                orchestration_id=str(orchestration_id),
                agent_id=str(agent_id),
                error=str(e)
            )
            return False
    
    async def remove_agent_from_orchestration(
        self,
        orchestration_id: UUID,
        agent_id: UUID
    ) -> bool:
        """Remove an agent from orchestration"""
        try:
            result = await self.db.execute(
                delete(OrchestrationMember).where(
                    OrchestrationMember.orchestration_id == orchestration_id,
                    OrchestrationMember.agent_id == agent_id
                )
            )
            
            if result.rowcount > 0:
                await self.db.commit()
                logger.info(
                    "Agent removed from orchestration",
                    orchestration_id=str(orchestration_id),
                    agent_id=str(agent_id)
                )
                return True
            
            return False
            
        except Exception as e:
            await self.db.rollback()
            logger.error(
                "Failed to remove agent from orchestration",
                orchestration_id=str(orchestration_id),
                agent_id=str(agent_id),
                error=str(e)
            )
            return False
    
    async def delete_orchestration(self, orchestration_id: UUID) -> bool:
        """Delete an orchestration"""
        try:
            # Stop orchestration if running
            orchestration = await self.get_orchestration(orchestration_id)
            if orchestration and orchestration.status in ["running", "paused"]:
                await self.stop_orchestration(orchestration_id)
            
            # Delete orchestration
            result = await self.db.execute(
                delete(Orchestration).where(Orchestration.id == orchestration_id)
            )
            
            if result.rowcount > 0:
                await self.db.commit()
                logger.info(
                    "Orchestration deleted successfully",
                    orchestration_id=str(orchestration_id)
                )
                return True
            
            return False
            
        except Exception as e:
            await self.db.rollback()
            logger.error(
                "Failed to delete orchestration",
                orchestration_id=str(orchestration_id),
                error=str(e)
            )
            raise