apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: redis-pvc
  namespace: ai-agent-platform
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
  storageClassName: standard

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: redis-config
  namespace: ai-agent-platform
data:
  redis.conf: |
    # Redis Configuration
    bind 0.0.0.0
    port 6379
    
    # Security
    requirepass ${REDIS_PASSWORD}
    
    # Memory management
    maxmemory 1gb
    maxmemory-policy allkeys-lru
    
    # Persistence
    save 900 1
    save 300 10
    save 60 10000
    
    # Logging
    loglevel notice
    logfile /var/log/redis/redis.log
    
    # Networking
    timeout 300
    tcp-keepalive 300
    
    # Performance
    databases 16
    
    # Append only file
    appendonly yes
    appendfilename "appendonly.aof"
    appendfsync everysec

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: ai-agent-platform
  labels:
    app: redis
    component: cache
spec:
  replicas: 1
  strategy:
    type: Recreate
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
        component: cache
    spec:
      initContainers:
      - name: config
        image: redis:7-alpine
        command:
        - sh
        - -c
        - |
          cp /tmp/redis/redis.conf /etc/redis/redis.conf
          echo "Redis configuration copied"
        volumeMounts:
        - name: redis-config
          mountPath: /tmp/redis/
        - name: config
          mountPath: /etc/redis/
      containers:
      - name: redis
        image: redis:7-alpine
        command:
        - redis-server
        - /etc/redis/redis.conf
        ports:
        - containerPort: 6379
          name: redis
        env:
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: redis-secret
              key: REDIS_PASSWORD
        volumeMounts:
        - name: redis-storage
          mountPath: /data
        - name: config
          mountPath: /etc/redis/
        - name: log
          mountPath: /var/log/redis/
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          exec:
            command:
            - redis-cli
            - --no-auth-warning
            - -a
            - $(REDIS_PASSWORD)
            - ping
          initialDelaySeconds: 20
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - redis-cli
            - --no-auth-warning
            - -a
            - $(REDIS_PASSWORD)
            - ping
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
      volumes:
      - name: redis-storage
        persistentVolumeClaim:
          claimName: redis-pvc
      - name: redis-config
        configMap:
          name: redis-config
          defaultMode: 0644
      - name: config
        emptyDir: {}
      - name: log
        emptyDir: {}

---
apiVersion: v1
kind: Service
metadata:
  name: redis
  namespace: ai-agent-platform
  labels:
    app: redis
    component: cache
spec:
  type: ClusterIP
  ports:
  - port: 6379
    targetPort: 6379
    protocol: TCP
    name: redis
  selector:
    app: redis

---
# Redis monitoring with redis-exporter
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis-exporter
  namespace: ai-agent-platform
  labels:
    app: redis-exporter
    component: monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis-exporter
  template:
    metadata:
      labels:
        app: redis-exporter
        component: monitoring
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9121"
    spec:
      containers:
      - name: redis-exporter
        image: oliver006/redis_exporter:latest
        ports:
        - containerPort: 9121
          name: metrics
        env:
        - name: REDIS_ADDR
          value: "redis://redis:6379"
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: redis-secret
              key: REDIS_PASSWORD
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
        livenessProbe:
          httpGet:
            path: /metrics
            port: 9121
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /metrics
            port: 9121
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: redis-exporter
  namespace: ai-agent-platform
  labels:
    app: redis-exporter
    component: monitoring
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "9121"
spec:
  type: ClusterIP
  ports:
  - port: 9121
    targetPort: 9121
    protocol: TCP
    name: metrics
  selector:
    app: redis-exporter