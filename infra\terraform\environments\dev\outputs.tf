# Development Environment Outputs

# Network outputs
output "vpc_name" {
  description = "Name of the VPC network"
  value       = module.network.vpc_name
}

output "public_subnet_cidr" {
  description = "CIDR block of the public subnet"
  value       = module.network.public_subnet_cidr
}

output "private_subnet_cidr" {
  description = "CIDR block of the private subnet"
  value       = module.network.private_subnet_cidr
}

# Database outputs
output "database_connection_name" {
  description = "Cloud SQL instance connection name"
  value       = module.database.instance_connection_name
}

output "database_private_ip" {
  description = "Private IP address of the database"
  value       = module.database.instance_private_ip
}

output "database_name" {
  description = "Name of the application database"
  value       = module.database.database_name
}

output "database_user" {
  description = "Database username"
  value       = module.database.database_user
}

# Registry outputs
output "registry_url" {
  description = "URL of the Docker registry"
  value       = module.registry.registry_url
}

output "docker_push_commands" {
  description = "Commands to push Docker images"
  value       = module.registry.docker_push_commands
}

# Storage outputs
output "web_bucket_name" {
  description = "Name of the web storage bucket"
  value       = module.storage.bucket_name
}

output "website_url" {
  description = "URL to access the website"
  value       = module.storage.website_url
}

# Compute outputs
output "instance_name" {
  description = "Name of the compute instance"
  value       = module.compute.instance_name
}

output "instance_internal_ip" {
  description = "Internal IP address of the compute instance"
  value       = module.compute.instance_internal_ip
}

output "instance_external_ip" {
  description = "External IP address of the compute instance"
  value       = module.compute.instance_external_ip
}

output "ssh_command" {
  description = "Command to SSH into the instance"
  value       = module.compute.ssh_command
}

# Load balancer outputs
output "load_balancer_ip" {
  description = "IP address of the load balancer"
  value       = module.loadbalancer.load_balancer_ip
}

output "website_urls" {
  description = "URLs to access the website"
  value       = module.loadbalancer.website_urls
}

output "api_endpoints" {
  description = "API endpoint URLs"
  value       = module.loadbalancer.api_endpoints
}

# Service endpoints
output "service_endpoints" {
  description = "Direct service endpoints"
  value       = module.compute.service_endpoints
}

# Development-specific outputs
output "dev_access_info" {
  description = "Information for development access"
  value = {
    environment = "dev"
    project_id  = var.project_id
    region      = var.region
    zone        = var.zone
    
    # Quick access URLs
    website = module.loadbalancer.website_urls.http
    api_base = module.loadbalancer.api_endpoints.base_url
    
    # Development tools
    ssh_access = module.compute.ssh_command
    docker_logs = module.compute.docker_commands
    
    # Database access
    database_connection = module.database.connection_string
    database_secret = "gcloud secrets versions access latest --secret=${module.database.database_password_secret_name}"
    
    # Registry access
    docker_login = "gcloud auth configure-docker ${module.registry.registry_hostname}"
    
    # Useful commands
    commands = {
      ssh_instance = module.compute.ssh_command
      view_logs = "gcloud logging read 'resource.type=gce_instance' --limit=50 --format=json"
      restart_services = "gcloud compute ssh ${module.compute.instance_name} --zone=${var.zone} --command='sudo systemctl restart platform-services'"
      update_images = "gcloud compute ssh ${module.compute.instance_name} --zone=${var.zone} --command='sudo /opt/platform/update-services.sh'"
    }
  }
}

# Resource summary
output "resource_summary" {
  description = "Summary of created resources"
  value = {
    vpc_network = module.network.vpc_name
    database_instance = module.database.instance_name
    compute_instance = module.compute.instance_name
    storage_bucket = module.storage.bucket_name
    registry_repo = module.registry.repository_name
    load_balancer_ip = module.loadbalancer.load_balancer_ip
    
    # Cost information
    estimated_monthly_cost = "Low (dev environment with minimal resources)"
    cost_optimization = "Resources configured for development use with cost-optimized settings"
  }
}

# Health check URLs
output "health_checks" {
  description = "Health check URLs for services"
  value = {
    load_balancer = "${module.loadbalancer.website_urls.http}/health"
    direct_gateway = "${module.compute.service_endpoints.gateway}/health"
    direct_auth = "${module.compute.service_endpoints.auth_service}/health"
    direct_crm = "${module.compute.service_endpoints.crm_backend}/health"
  }
}

# Next steps information
output "next_steps" {
  description = "Next steps for development setup"
  value = {
    "1_configure_dns" = "Point your domain to ${module.loadbalancer.load_balancer_ip} if using custom domain"
    "2_deploy_code" = "Use the provided docker push commands to deploy your services"
    "3_run_migrations" = "Trigger database migrations via Cloud Build: gcloud builds triggers run ${module.database.migration_trigger_name}"
    "4_access_website" = "Visit ${module.loadbalancer.website_urls.http} to see your application"
    "5_monitor_logs" = "Use Cloud Logging to monitor application logs and errors"
    
    documentation = "See README.md in the terraform/ directory for detailed setup instructions"
  }
}