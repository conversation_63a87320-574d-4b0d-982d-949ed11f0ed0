#!/usr/bin/env python3
"""
Direct deployment test using working AI generation
"""

import asyncio
import sys
import os
sys.path.insert(0, '/Users/<USER>/workspaces/git/ai-agent/backend/src')

# Load environment variables from .env file
from dotenv import load_dotenv
load_dotenv('/Users/<USER>/workspaces/git/ai-agent/backend/.env')

from services.code_generator import code_generator
from services.agent_storage import agent_storage
from services.docker_deployment import docker_deployment

async def test_full_pipeline():
    """Test complete pipeline from AI generation to Docker deployment"""
    print("Starting full pipeline test...")
    
    try:
        # 1. Generate code using AI
        config = {
            "name": "test-calculator-direct",
            "type": "api_service",
            "language": "python",
            "framework": "fastapi",
            "capabilities": ["calculation", "mathematics"]
        }
        
        requirements = "Create a simple calculator that can add, subtract, multiply and divide two numbers. Include error handling for division by zero."
        
        print("1. Generating code with AI...")
        generated_code = await asyncio.wait_for(
            code_generator.generate_agent_code(config, requirements),
            timeout=60
        )
        print(f"   Generated components: {list(generated_code.get('components', {}).keys())}")
        
        # 2. Save to agent storage
        agent_id = "test-direct-123"
        print("2. Saving to agent storage...")
        storage_result = agent_storage.save_generated_code(agent_id, generated_code)
        print(f"   Storage result: {storage_result['success']}")
        
        # 3. Deploy to Docker
        print("3. Deploying to Docker...")
        deployment_info = await asyncio.wait_for(
            docker_deployment.deploy_agent(agent_id, generated_code),
            timeout=120
        )
        
        print("✅ DEPLOYMENT SUCCESSFUL!")
        print(f"   Agent URL: {deployment_info.get('url')}")
        print(f"   Container ID: {deployment_info.get('container_id')}")
        print(f"   Port: {deployment_info.get('port')}")
        
        # 4. Test the deployed agent
        print("4. Testing deployed agent...")
        import aiohttp
        
        agent_url = deployment_info.get('url')
        try:
            async with aiohttp.ClientSession() as session:
                # Test health endpoint
                async with session.get(f"{agent_url}/health") as response:
                    if response.status == 200:
                        health_data = await response.json()
                        print(f"   Health check: ✅ {health_data}")
                    else:
                        print(f"   Health check: ❌ Status {response.status}")
                
                # Test root endpoint (A2A info)
                async with session.get(agent_url) as response:
                    if response.status == 200:
                        agent_info = await response.json()
                        print(f"   A2A info: ✅ Agent '{agent_info.get('name')}' with {len(agent_info.get('capabilities', []))} capabilities")
                    else:
                        print(f"   A2A info: ❌ Status {response.status}")
                        
        except Exception as e:
            print(f"   Testing failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Pipeline failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_full_pipeline())
    print(f"\nFinal result: {'SUCCESS' if success else 'FAILED'}")