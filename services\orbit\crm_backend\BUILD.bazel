load("@gazelle//:def.bzl", "gazelle")
load("@rules_go//go:def.bzl", "go_binary", "go_library")
load("@rules_oci//oci:defs.bzl", "oci_image")
load("@rules_pkg//pkg:tar.bzl", "pkg_tar")

# Main library
go_library(
    name = "crm_backend_lib",
    srcs = ["main.go"],
    importpath = "github.com/TwoDotAi/mono/services/orbit/crm_backend",
    visibility = ["//visibility:public"],
    deps = [
        "//shared/go/logging",
        "//services/orbit/crm_backend/config",
        "//services/orbit/crm_backend/database",
        "//services/orbit/crm_backend/handlers",
        "//services/orbit/crm_backend/middleware",
        "@com_github_gin_gonic_gin//:gin",
        "@org_uber_go_zap//:zap",
    ],
)

go_binary(
    name = "crm_backend",
    embed = [":crm_backend_lib"],
    visibility = ["//visibility:public"],
)

# Package binary for container
pkg_tar(
    name = "crm_backend_binary_tar",
    srcs = [":crm_backend"],
    package_dir = "/",
)

# Test for the CRM backend - commented out until test files are created
# go_test(
#     name = "crm_backend_test",
#     srcs = [
#         "handlers/auth_test.go",
#         "handlers/companies_test.go",
#         "handlers/contacts_test.go",
#         "handlers/deals_test.go",
#         "handlers/users_test.go",
#     ],
#     embed = [":crm_backend_lib"],
#     deps = [
#         "@com_github_stretchr_testify//assert",
#         "@com_github_stretchr_testify//require",
#     ],
#     tags = ["unit"],
# )

# Integration test for the CRM backend - commented out until test file is created
# go_test(
#     name = "crm_backend_integration_test",
#     srcs = ["integration_test.go"],
#     embed = [":crm_backend_lib"],
#     deps = [
#         "@com_github_lib_pq//:pq",
#         "@com_github_stretchr_testify//assert",
#         "@com_github_stretchr_testify//require",
#     ],
#     env = {
#         "DATABASE_URL": "postgres://postgres:testpassword@localhost:5433/crm_db_test?sslmode=disable",
#     },
#     tags = ["integration", "requires-database"],
# )

# OCI image for CRM backend
oci_image(
    name = "crm_backend_image",
    base = "@distroless_base",
    entrypoint = ["/crm_backend"],
    env = {
        "PORT": "8003",
        "GIN_MODE": "release",
    },
    exposed_ports = ["8003"],
    tars = [":crm_backend_binary_tar"],
    visibility = ["//visibility:public"],
)

# Gazelle for dependency management
gazelle(name = "gazelle")
