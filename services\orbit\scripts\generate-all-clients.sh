#!/bin/bash
# Generate all API clients for the CRM system

set -e

SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"
WORKSPACE_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

echo "🚀 Generating all CRM API clients..."

# Ensure OpenAPI Generator is available
if [ ! -f "$WORKSPACE_ROOT/services/orbit/tools/openapi-generator-cli.jar" ]; then
    echo "📥 Downloading OpenAPI Generator..."
    "$WORKSPACE_ROOT/services/orbit/tools/download-openapi-generator.sh"
fi

# Generate TypeScript client for CRM web app
echo "🔨 Generating TypeScript client for CRM web app..."
"$WORKSPACE_ROOT/services/orbit/rest-api/generate-simple-client.sh"

# Generate Python client (if needed)
if [ "$1" = "--all" ]; then
    echo "🐍 Generating Python client..."
    mkdir -p "$WORKSPACE_ROOT/services/orbit/api-clients/python"
    # Add Python client generation here when needed
fi

# Generate Go client (if needed)
if [ "$1" = "--all" ]; then
    echo "🐹 Generating Go client..."
    mkdir -p "$WORKSPACE_ROOT/services/orbit/api-clients/go"
    # Add Go client generation here when needed
fi

echo "✅ All API clients generated successfully!"
echo ""
echo "📁 Generated files:"
echo "   • TypeScript client: services/orbit/web/src/api-client/"
echo ""
echo "🚀 Next steps:"
echo "   • Update your components to use the new API client"
echo "   • Configure the API base URL in .env.local"
echo "   • Test the integration with your backend"