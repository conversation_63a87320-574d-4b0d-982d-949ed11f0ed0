#!/bin/bash

# Migration test with database setup for Bazel
# This script checks if test database is available and runs basic tests

set -e

# Find the directory containing this script  
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Configuration
DB_CONTAINER_NAME="crm_test_db"
DB_NAME="crm_db_test"
DB_USER="postgres"
DB_PASSWORD="testpassword"
DB_PORT="5433"

echo "🔧 Running migration tests with setup validation..."

# Check if test database container is running
if ! docker ps | grep -q $DB_CONTAINER_NAME; then
    echo "❌ Test database container not running. Please run: bazel run //db:setup_test_db"
    exit 1
fi

# Wait for database to be ready
until docker exec $DB_CONTAINER_NAME pg_isready -U $DB_USER -d $DB_NAME 2>/dev/null; do
    echo "Waiting for test database..."
    sleep 1
done

# Run SQL integration tests
echo "Running SQL integration tests..."
docker exec -i $DB_CONTAINER_NAME psql -U $DB_USER -d $DB_NAME < "$SCRIPT_DIR/integration_test.sql"

echo "✅ Migration tests with setup completed successfully!"