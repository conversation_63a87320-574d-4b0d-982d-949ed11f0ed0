"""
AI Agent Platform - Agent-to-Agent (A2A) Protocol Handler
"""

import asyncio
import json
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable
from enum import Enum
import structlog
from pydantic import BaseModel, Field

from database.models import Communication, Agent
from services.messaging import message_queue_service
from agents.manager import agent_manager

logger = structlog.get_logger()


class A2AMessageType(str, Enum):
    """A2A message types"""
    TASK_REQUEST = "task_request"
    TASK_RESPONSE = "task_response"
    CAPABILITY_QUERY = "capability_query"
    CAPABILITY_RESPONSE = "capability_response"
    STATUS_UPDATE = "status_update"
    HEARTBEAT = "heartbeat"
    ERROR = "error"
    COLLABORATION_REQUEST = "collaboration_request"
    COLLABORATION_RESPONSE = "collaboration_response"
    RESOURCE_REQUEST = "resource_request"
    RESOURCE_RESPONSE = "resource_response"


class A2AMessage(BaseModel):
    """A2A protocol message structure"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    type: A2AMessageType
    sender_id: str
    receiver_id: Optional[str] = None  # None for broadcast
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    
    # Message content
    payload: Dict[str, Any] = Field(default_factory=dict)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    # Response tracking
    response_required: bool = False
    response_timeout: Optional[int] = None  # seconds
    correlation_id: Optional[str] = None
    
    # Message routing
    priority: int = Field(default=5, ge=1, le=10)  # 1=highest, 10=lowest
    ttl: Optional[int] = None  # time to live in seconds


class A2AProtocolHandler:
    """Agent-to-Agent Protocol Handler"""
    
    def __init__(self):
        self.handlers: Dict[A2AMessageType, Callable] = {}
        self.pending_responses: Dict[str, asyncio.Future] = {}
        self.subscription_handlers: Dict[str, Callable] = {}
        
        # Initialize default handlers
        self._setup_default_handlers()
        
        logger.info("A2A Protocol Handler initialized")
    
    def _setup_default_handlers(self):
        """Setup default message handlers"""
        self.handlers = {
            A2AMessageType.TASK_REQUEST: self._handle_task_request,
            A2AMessageType.TASK_RESPONSE: self._handle_task_response,
            A2AMessageType.CAPABILITY_QUERY: self._handle_capability_query,
            A2AMessageType.CAPABILITY_RESPONSE: self._handle_capability_response,
            A2AMessageType.STATUS_UPDATE: self._handle_status_update,
            A2AMessageType.HEARTBEAT: self._handle_heartbeat,
            A2AMessageType.ERROR: self._handle_error,
            A2AMessageType.COLLABORATION_REQUEST: self._handle_collaboration_request,
            A2AMessageType.COLLABORATION_RESPONSE: self._handle_collaboration_response,
            A2AMessageType.RESOURCE_REQUEST: self._handle_resource_request,
            A2AMessageType.RESOURCE_RESPONSE: self._handle_resource_response,
        }
    
    async def send_message(self, message: A2AMessage) -> Optional[A2AMessage]:
        """Send an A2A message and optionally wait for response"""
        try:
            # Validate message
            await self._validate_message(message)
            
            # Log communication
            await self._log_communication(message)
            
            # Send via message queue
            if message.receiver_id:
                # Direct message
                topic = f"agent.{message.receiver_id}.a2a"
            else:
                # Broadcast message
                topic = "agent.broadcast.a2a"
            
            await message_queue_service.publish_to_topic(
                topic,
                message.model_dump(),
                routing_key=f"a2a.{message.type.value}"
            )
            
            logger.info(
                "A2A message sent",
                message_id=message.id,
                sender=message.sender_id,
                receiver=message.receiver_id or "broadcast",
                type=message.type.value
            )
            
            # Handle response if required
            if message.response_required:
                return await self._wait_for_response(message)
            
            return None
            
        except Exception as e:
            logger.error(
                "Failed to send A2A message",
                message_id=message.id,
                error=str(e)
            )
            raise
    
    async def _wait_for_response(self, original_message: A2AMessage) -> Optional[A2AMessage]:
        """Wait for response to a message"""
        try:
            # Create future for response
            response_future = asyncio.Future()
            self.pending_responses[original_message.id] = response_future
            
            # Wait for response with timeout
            timeout = original_message.response_timeout or 30
            response = await asyncio.wait_for(response_future, timeout=timeout)
            
            return response
            
        except asyncio.TimeoutError:
            logger.warning(
                "A2A message response timeout",
                message_id=original_message.id,
                timeout=timeout
            )
            return None
        finally:
            # Clean up
            self.pending_responses.pop(original_message.id, None)
    
    async def handle_incoming_message(self, message_data: Dict[str, Any]):
        """Handle incoming A2A message"""
        try:
            # Parse message
            message = A2AMessage(**message_data)
            
            # Log incoming message
            logger.info(
                "A2A message received",
                message_id=message.id,
                sender=message.sender_id,
                receiver=message.receiver_id,
                type=message.type.value
            )
            
            # Check if this is a response to pending request
            if message.correlation_id and message.correlation_id in self.pending_responses:
                future = self.pending_responses[message.correlation_id]
                if not future.done():
                    future.set_result(message)
                return
            
            # Handle message based on type
            handler = self.handlers.get(message.type)
            if handler:
                await handler(message)
            else:
                logger.warning(
                    "No handler for A2A message type",
                    message_type=message.type.value,
                    message_id=message.id
                )
                
        except Exception as e:
            logger.error(
                "Failed to handle incoming A2A message",
                error=str(e),
                message_data=message_data
            )
    
    async def register_agent(self, agent_id: str):
        """Register agent for A2A communication"""
        try:
            # Subscribe to agent-specific A2A messages
            topic = f"agent.{agent_id}.a2a"
            await message_queue_service.subscribe_to_topic(
                topic,
                f"a2a_handler_{agent_id}",
                self.handle_incoming_message
            )
            
            # Subscribe to broadcast messages
            broadcast_topic = "agent.broadcast.a2a"
            await message_queue_service.subscribe_to_topic(
                broadcast_topic,
                f"a2a_broadcast_{agent_id}",
                self.handle_incoming_message
            )
            
            logger.info(
                "Agent registered for A2A communication",
                agent_id=agent_id
            )
            
        except Exception as e:
            logger.error(
                "Failed to register agent for A2A communication",
                agent_id=agent_id,
                error=str(e)
            )
            raise
    
    async def unregister_agent(self, agent_id: str):
        """Unregister agent from A2A communication"""
        try:
            # Unsubscribe from topics
            topic = f"agent.{agent_id}.a2a"
            message_queue_service.unsubscribe_from_topic(topic)
            
            broadcast_topic = "agent.broadcast.a2a"
            message_queue_service.unsubscribe_from_topic(broadcast_topic)
            
            logger.info(
                "Agent unregistered from A2A communication",
                agent_id=agent_id
            )
            
        except Exception as e:
            logger.error(
                "Failed to unregister agent from A2A communication",
                agent_id=agent_id,
                error=str(e)
            )
    
    # Default message handlers
    
    async def _handle_task_request(self, message: A2AMessage):
        """Handle task request from another agent"""
        sender_id = message.sender_id
        task_data = message.payload
        
        logger.info(
            "Processing task request",
            from_agent=sender_id,
            task_type=task_data.get('type')
        )
        
        try:
            # Execute task
            result = await agent_manager.execute_task(
                uuid.UUID(message.receiver_id),
                task_data
            )
            
            # Send response
            response = A2AMessage(
                type=A2AMessageType.TASK_RESPONSE,
                sender_id=message.receiver_id,
                receiver_id=sender_id,
                correlation_id=message.id,
                payload={'result': result, 'status': 'success'}
            )
            
            await self.send_message(response)
            
        except Exception as e:
            # Send error response
            error_response = A2AMessage(
                type=A2AMessageType.TASK_RESPONSE,
                sender_id=message.receiver_id,
                receiver_id=sender_id,
                correlation_id=message.id,
                payload={'error': str(e), 'status': 'error'}
            )
            
            await self.send_message(error_response)
    
    async def _handle_task_response(self, message: A2AMessage):
        """Handle task response from another agent"""
        # Response handling is done in handle_incoming_message
        pass
    
    async def _handle_capability_query(self, message: A2AMessage):
        """Handle capability query from another agent"""
        sender_id = message.sender_id
        
        try:
            # Get agent capabilities
            agent_info = await agent_manager.get_runtime_info(
                uuid.UUID(message.receiver_id)
            )
            
            capabilities = agent_info.get('capabilities', []) if agent_info else []
            
            # Send response
            response = A2AMessage(
                type=A2AMessageType.CAPABILITY_RESPONSE,
                sender_id=message.receiver_id,
                receiver_id=sender_id,
                correlation_id=message.id,
                payload={'capabilities': capabilities}
            )
            
            await self.send_message(response)
            
        except Exception as e:
            logger.error(
                "Failed to handle capability query",
                from_agent=sender_id,
                error=str(e)
            )
    
    async def _handle_capability_response(self, message: A2AMessage):
        """Handle capability response from another agent"""
        # Response handling is done in handle_incoming_message
        pass
    
    async def _handle_status_update(self, message: A2AMessage):
        """Handle status update from another agent"""
        sender_id = message.sender_id
        status_data = message.payload
        
        logger.info(
            "Received status update",
            from_agent=sender_id,
            status=status_data.get('status')
        )
        
        # TODO: Update agent status in registry
    
    async def _handle_heartbeat(self, message: A2AMessage):
        """Handle heartbeat from another agent"""
        sender_id = message.sender_id
        
        logger.debug(
            "Received heartbeat",
            from_agent=sender_id
        )
        
        # TODO: Update agent health status
    
    async def _handle_error(self, message: A2AMessage):
        """Handle error message from another agent"""
        sender_id = message.sender_id
        error_data = message.payload
        
        logger.error(
            "Received error from agent",
            from_agent=sender_id,
            error=error_data.get('error'),
            details=error_data.get('details')
        )
    
    async def _handle_collaboration_request(self, message: A2AMessage):
        """Handle collaboration request from another agent"""
        sender_id = message.sender_id
        collaboration_data = message.payload
        
        logger.info(
            "Processing collaboration request",
            from_agent=sender_id,
            type=collaboration_data.get('type')
        )
        
        # TODO: Implement collaboration logic
        
        # Send response
        response = A2AMessage(
            type=A2AMessageType.COLLABORATION_RESPONSE,
            sender_id=message.receiver_id,
            receiver_id=sender_id,
            correlation_id=message.id,
            payload={'accepted': True, 'details': 'Collaboration accepted'}
        )
        
        await self.send_message(response)
    
    async def _handle_collaboration_response(self, message: A2AMessage):
        """Handle collaboration response from another agent"""
        # Response handling is done in handle_incoming_message
        pass
    
    async def _handle_resource_request(self, message: A2AMessage):
        """Handle resource request from another agent"""
        sender_id = message.sender_id
        resource_data = message.payload
        
        logger.info(
            "Processing resource request",
            from_agent=sender_id,
            resource_type=resource_data.get('type')
        )
        
        # TODO: Implement resource sharing logic
        
        # Send response
        response = A2AMessage(
            type=A2AMessageType.RESOURCE_RESPONSE,
            sender_id=message.receiver_id,
            receiver_id=sender_id,
            correlation_id=message.id,
            payload={'available': True, 'details': 'Resource available'}
        )
        
        await self.send_message(response)
    
    async def _handle_resource_response(self, message: A2AMessage):
        """Handle resource response from another agent"""
        # Response handling is done in handle_incoming_message
        pass
    
    # Utility methods
    
    async def _validate_message(self, message: A2AMessage):
        """Validate A2A message"""
        if not message.sender_id:
            raise ValueError("Message must have sender_id")
        
        if message.ttl and message.ttl < 0:
            raise ValueError("TTL must be positive")
        
        # TODO: Add more validation rules
    
    async def _log_communication(self, message: A2AMessage):
        """Log communication to database"""
        try:
            # TODO: Save to Communication model
            communication = Communication(
                protocol='a2a',
                message_type=message.type.value,
                content=message.model_dump(),
                sender_agent_id=uuid.UUID(message.sender_id),
                receiver_agent_id=uuid.UUID(message.receiver_id) if message.receiver_id else None,
                response_required=message.response_required
            )
            
            # TODO: Save to database when session is available
            
        except Exception as e:
            logger.warning(
                "Failed to log A2A communication",
                error=str(e)
            )


# Global A2A protocol handler instance
a2a_protocol = A2AProtocolHandler()


# Convenience functions for common A2A operations

async def send_task_request(
    sender_id: str,
    receiver_id: str,
    task_type: str,
    task_data: Dict[str, Any],
    timeout: int = 30
) -> Optional[Dict[str, Any]]:
    """Send a task request to another agent"""
    message = A2AMessage(
        type=A2AMessageType.TASK_REQUEST,
        sender_id=sender_id,
        receiver_id=receiver_id,
        payload={'type': task_type, 'data': task_data},
        response_required=True,
        response_timeout=timeout
    )
    
    response = await a2a_protocol.send_message(message)
    if response:
        return response.payload
    return None


async def query_agent_capabilities(
    sender_id: str,
    receiver_id: str,
    timeout: int = 15
) -> Optional[List[str]]:
    """Query capabilities of another agent"""
    message = A2AMessage(
        type=A2AMessageType.CAPABILITY_QUERY,
        sender_id=sender_id,
        receiver_id=receiver_id,
        response_required=True,
        response_timeout=timeout
    )
    
    response = await a2a_protocol.send_message(message)
    if response:
        return response.payload.get('capabilities', [])
    return None


async def send_status_update(
    sender_id: str,
    status: str,
    details: Optional[Dict[str, Any]] = None
):
    """Send status update to all agents"""
    message = A2AMessage(
        type=A2AMessageType.STATUS_UPDATE,
        sender_id=sender_id,
        payload={'status': status, 'details': details or {}}
    )
    
    await a2a_protocol.send_message(message)


async def request_collaboration(
    sender_id: str,
    receiver_id: str,
    collaboration_type: str,
    details: Dict[str, Any],
    timeout: int = 30
) -> Optional[Dict[str, Any]]:
    """Request collaboration with another agent"""
    message = A2AMessage(
        type=A2AMessageType.COLLABORATION_REQUEST,
        sender_id=sender_id,
        receiver_id=receiver_id,
        payload={'type': collaboration_type, 'details': details},
        response_required=True,
        response_timeout=timeout
    )
    
    response = await a2a_protocol.send_message(message)
    if response:
        return response.payload
    return None