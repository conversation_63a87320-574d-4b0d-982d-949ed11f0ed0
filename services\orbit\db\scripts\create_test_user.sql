-- Create test user for CRM
-- Password is 'password123' (you should use bcrypt in production)

-- First, create the basic schema if it doesn't exist
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    password_hash VARCHAR(255),
    email_confirmed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_deleted BOOLEAN DEFAULT FALSE
);

-- Insert a test user
-- Note: In production, you'd use proper password hashing
-- This is just for testing
INSERT INTO users (email, password_hash, email_confirmed_at)
VALUES (
    '<EMAIL>', 
    '$2a$10$X4kv7j5ZcG39WgogSl16yuprcEzV6evwQCXVnAq4DSbCKcSrqTX9i', -- bcrypt hash of 'password123'
    NOW()
)
ON CONFLICT (email) DO NOTHING;

-- Create user profile
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID PRIMARY KEY,
    full_name VARCHAR(255),
    avatar_url TEXT,
    timezone VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Insert profile for test user
INSERT INTO user_profiles (id, full_name)
SELECT id, 'Test User'
FROM users
WHERE email = '<EMAIL>'
ON CONFLICT (id) DO NOTHING;

-- Display the created user
SELECT id, email, created_at FROM users WHERE email = '<EMAIL>';