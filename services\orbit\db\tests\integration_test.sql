-- Integration test for CRM database migrations
-- This SQL script verifies the database state after migrations

-- Test helper function to report results
CREATE OR REPLACE FUNCTION assert_test(test_name TEXT, condition BOOLEAN, message TEXT DEFAULT '') RETURNS VOID AS $$
BEGIN
    IF NOT condition THEN
        RAISE EXCEPTION 'Test % failed: %', test_name, message;
    ELSE
        RAISE NOTICE '✓ Test % passed', test_name;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Start tests
DO $$
DECLARE
    v_count INTEGER;
    v_exists BOOLEAN;
    v_data_type TEXT;
BEGIN
    RAISE NOTICE '🧪 Starting CRM database migration tests...';
    
    -- Test 1: Verify all tables exist
    RAISE NOTICE '';
    RAISE NOTICE '📋 Testing table existence...';
    
    SELECT COUNT(*) INTO v_count
    FROM information_schema.tables
    WHERE table_schema = 'public'
    AND table_name IN (
        'users', 'user_profiles', 'company_statuses', 'companies',
        'contacts', 'deal_stages', 'deals', 'interaction_types',
        'interactions', 'arli_documents', 'arli_content_blocks'
    );
    PERFORM assert_test('all_tables_exist', v_count = 11, 'Expected 11 tables, found ' || v_count);
    
    -- Test 2: Verify users table structure
    RAISE NOTICE '';
    RAISE NOTICE '👤 Testing users table structure...';
    
    SELECT EXISTS(
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'users' AND column_name = 'password_hash'
    ) INTO v_exists;
    PERFORM assert_test('users_password_hash_exists', v_exists, 'password_hash column missing');
    
    SELECT EXISTS(
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'users' AND column_name = 'is_deleted'
        AND column_default = 'false'
    ) INTO v_exists;
    PERFORM assert_test('users_is_deleted_default', v_exists, 'is_deleted should default to false');
    
    -- Test 3: Verify foreign key constraints
    RAISE NOTICE '';
    RAISE NOTICE '🔗 Testing foreign key constraints...';
    
    SELECT COUNT(*) INTO v_count
    FROM information_schema.key_column_usage kcu
    JOIN information_schema.table_constraints tc 
        ON tc.constraint_name = kcu.constraint_name
    WHERE tc.constraint_type = 'FOREIGN KEY'
    AND kcu.table_name = 'companies'
    AND kcu.column_name = 'company_status_id';
    PERFORM assert_test('companies_status_fk', v_count > 0, 'Companies should have FK to company_statuses');
    
    SELECT COUNT(*) INTO v_count
    FROM information_schema.key_column_usage kcu
    JOIN information_schema.table_constraints tc 
        ON tc.constraint_name = kcu.constraint_name
    WHERE tc.constraint_type = 'FOREIGN KEY'
    AND kcu.table_name = 'contacts'
    AND kcu.column_name = 'company_id';
    PERFORM assert_test('contacts_company_fk', v_count > 0, 'Contacts should have FK to companies');
    
    -- Test 4: Verify indexes
    RAISE NOTICE '';
    RAISE NOTICE '🔍 Testing indexes...';
    
    SELECT EXISTS(
        SELECT 1 FROM pg_indexes
        WHERE tablename = 'users' AND indexname = 'idx_users_email'
    ) INTO v_exists;
    PERFORM assert_test('users_email_index', v_exists, 'Email index on users table missing');
    
    SELECT EXISTS(
        SELECT 1 FROM pg_indexes
        WHERE tablename = 'companies' AND indexname = 'idx_companies_name'
    ) INTO v_exists;
    PERFORM assert_test('companies_name_index', v_exists, 'Name index on companies table missing');
    
    SELECT EXISTS(
        SELECT 1 FROM pg_indexes
        WHERE tablename = 'deals' AND indexname = 'idx_deals_company'
    ) INTO v_exists;
    PERFORM assert_test('deals_company_index', v_exists, 'Company index on deals table missing');
    
    -- Test 5: Verify default data
    RAISE NOTICE '';
    RAISE NOTICE '📊 Testing default data...';
    
    SELECT COUNT(*) INTO v_count FROM company_statuses;
    PERFORM assert_test('company_statuses_count', v_count = 4, 'Should have 4 company statuses, found ' || v_count);
    
    SELECT COUNT(*) INTO v_count FROM deal_stages;
    PERFORM assert_test('deal_stages_count', v_count = 6, 'Should have 6 deal stages, found ' || v_count);
    
    SELECT COUNT(*) INTO v_count FROM interaction_types;
    PERFORM assert_test('interaction_types_count', v_count = 5, 'Should have 5 interaction types, found ' || v_count);
    
    -- Test 6: Verify specific default values
    SELECT EXISTS(
        SELECT 1 FROM company_statuses 
        WHERE name = 'Customer' AND pipeline_order = 3
    ) INTO v_exists;
    PERFORM assert_test('customer_status_exists', v_exists, 'Customer status with correct order missing');
    
    SELECT EXISTS(
        SELECT 1 FROM deal_stages 
        WHERE name = 'Closed Won' AND is_closed_won = true AND is_closed_lost = false
    ) INTO v_exists;
    PERFORM assert_test('closed_won_stage', v_exists, 'Closed Won stage with correct flags missing');
    
    -- Test 7: Verify triggers exist
    RAISE NOTICE '';
    RAISE NOTICE '⚡ Testing triggers...';
    
    SELECT COUNT(*) INTO v_count
    FROM information_schema.triggers
    WHERE trigger_name LIKE 'update_%_updated_at'
    AND event_object_schema = 'public';
    PERFORM assert_test('updated_at_triggers', v_count >= 8, 'Should have at least 8 updated_at triggers, found ' || v_count);
    
    -- Test 8: Test trigger functionality
    RAISE NOTICE '';
    RAISE NOTICE '🔧 Testing trigger functionality...';
    
    -- Insert test user
    INSERT INTO users (email, username) VALUES ('<EMAIL>', 'trigger_test');
    
    -- Update and check if updated_at changed
    UPDATE users SET full_name = 'Trigger Test' WHERE email = '<EMAIL>';
    
    SELECT EXISTS(
        SELECT 1 FROM users 
        WHERE email = '<EMAIL>' 
        AND updated_at > created_at
    ) INTO v_exists;
    PERFORM assert_test('updated_at_trigger_works', v_exists, 'updated_at should be greater than created_at after update');
    
    -- Cleanup
    DELETE FROM users WHERE email = '<EMAIL>';
    
    -- Test 9: Verify JSONB columns
    RAISE NOTICE '';
    RAISE NOTICE '📄 Testing JSONB columns...';
    
    SELECT data_type INTO v_data_type
    FROM information_schema.columns
    WHERE table_name = 'arli_documents' AND column_name = 'metadata';
    PERFORM assert_test('arli_documents_metadata_jsonb', v_data_type = 'jsonb', 'metadata column should be jsonb type');
    
    SELECT data_type INTO v_data_type
    FROM information_schema.columns
    WHERE table_name = 'arli_content_blocks' AND column_name = 'metadata';
    PERFORM assert_test('arli_content_blocks_metadata_jsonb', v_data_type = 'jsonb', 'metadata column should be jsonb type');
    
    -- Test 10: Verify CASCADE deletes
    RAISE NOTICE '';
    RAISE NOTICE '🗑️  Testing CASCADE delete constraints...';
    
    SELECT EXISTS(
        SELECT 1
        FROM information_schema.referential_constraints
        WHERE constraint_name IN (
            SELECT constraint_name
            FROM information_schema.key_column_usage
            WHERE table_name = 'user_profiles' AND column_name = 'id'
        )
        AND delete_rule = 'CASCADE'
    ) INTO v_exists;
    PERFORM assert_test('user_profiles_cascade_delete', v_exists, 'user_profiles should CASCADE delete with users');
    
    RAISE NOTICE '';
    RAISE NOTICE '✅ All database migration tests passed!';
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '❌ Test failed: %', SQLERRM;
        RAISE;
END;
$$;

-- Clean up test helper function
DROP FUNCTION IF EXISTS assert_test(TEXT, BOOLEAN, TEXT);