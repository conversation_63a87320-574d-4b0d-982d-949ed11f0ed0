#!/bin/bash
# Migration watcher service that monitors schema changes and auto-applies migrations
set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_error() {
    echo -e "${RED}❌ [$(date '+%H:%M:%S')] Error: $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ [$(date '+%H:%M:%S')] $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  [$(date '+%H:%M:%S')] $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  [$(date '+%H:%M:%S')] $1${NC}"
}

# Get the workspace root
if [ -n "$BUILD_WORKSPACE_DIRECTORY" ]; then
    cd "$BUILD_WORKSPACE_DIRECTORY"
elif [ -n "$TEST_WORKSPACE" ]; then
    cd "$TEST_WORKSPACE/.."
else
    # Find the workspace root by looking for WORKSPACE file
    CURRENT_DIR="$PWD"
    while [ "$CURRENT_DIR" != "/" ]; do
        if [ -f "$CURRENT_DIR/WORKSPACE" ]; then
            cd "$CURRENT_DIR"
            break
        fi
        CURRENT_DIR="$(dirname "$CURRENT_DIR")"
    done
fi

cd platform/db

# Configuration
SCHEMA_DIR="schema"
MIGRATIONS_DIR="migrations"
PIDFILE="/tmp/migration_watcher.pid"
LOGFILE="migration_watcher.log"
ENVIRONMENT="${1:-local}"

# Create schema directory if it doesn't exist
mkdir -p "$SCHEMA_DIR"

# Function to cleanup on exit
cleanup() {
    print_info "Stopping migration watcher..."
    if [ -f "$PIDFILE" ]; then
        rm -f "$PIDFILE"
    fi
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Function to check if watcher is already running
is_running() {
    if [ -f "$PIDFILE" ]; then
        local pid=$(cat "$PIDFILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            return 0
        else
            rm -f "$PIDFILE"
            return 1
        fi
    fi
    return 1
}

# Function to start the watcher
start_watcher() {
    if is_running; then
        print_warning "Migration watcher is already running (PID: $(cat $PIDFILE))"
        return 1
    fi
    
    # Store PID
    echo $$ > "$PIDFILE"
    
    print_info "Starting migration watcher for $ENVIRONMENT environment"
    print_info "Watching directory: $(pwd)/$SCHEMA_DIR"
    print_info "Log file: $(pwd)/$LOGFILE"
    print_info "PID file: $PIDFILE"
    
    # Log start
    echo "$(date): Migration watcher started for $ENVIRONMENT environment" >> "$LOGFILE"
    
    # Initial check
    check_and_apply_migrations
    
    # Start watching
    if command -v fswatch >/dev/null 2>&1; then
        print_info "Using fswatch for file monitoring"
        fswatch -o "$SCHEMA_DIR" | while read; do
            check_and_apply_migrations
        done
    elif command -v inotifywait >/dev/null 2>&1; then
        print_info "Using inotifywait for file monitoring"
        while inotifywait -e modify,create,delete "$SCHEMA_DIR" 2>/dev/null; do
            check_and_apply_migrations
        done
    else
        print_warning "File watching tools not available. Using polling mode."
        print_info "Install fswatch (macOS: brew install fswatch) or inotify-tools for better performance"
        
        local last_check=$(date +%s)
        while true; do
            sleep 5
            local current_time=$(date +%s)
            
            # Check if any files have been modified since last check
            if [ -n "$(find "$SCHEMA_DIR" -name "*.sql" -newer "/tmp/migration_watcher_last_check" 2>/dev/null)" ]; then
                check_and_apply_migrations
            fi
            
            # Update timestamp file
            touch "/tmp/migration_watcher_last_check"
        done
    fi
}

# Function to check and apply migrations
check_and_apply_migrations() {
    print_info "Checking for schema changes..."
    
    # Run the auto-migration system
    if bazel run //platform/db:auto_migrate -- run "$ENVIRONMENT" >> "$LOGFILE" 2>&1; then
        print_success "Auto-migration check completed"
    else
        print_error "Auto-migration failed - check $LOGFILE for details"
    fi
}

# Function to stop the watcher
stop_watcher() {
    if is_running; then
        local pid=$(cat "$PIDFILE")
        print_info "Stopping migration watcher (PID: $pid)"
        kill "$pid" 2>/dev/null || true
        rm -f "$PIDFILE"
        print_success "Migration watcher stopped"
    else
        print_warning "Migration watcher is not running"
    fi
}

# Function to show status
show_status() {
    if is_running; then
        local pid=$(cat "$PIDFILE")
        print_info "Migration watcher is running (PID: $pid)"
        print_info "Environment: $ENVIRONMENT"
        print_info "Watching: $(pwd)/$SCHEMA_DIR"
        print_info "Log file: $(pwd)/$LOGFILE"
        
        # Show recent log entries
        if [ -f "$LOGFILE" ]; then
            echo ""
            print_info "Recent log entries:"
            tail -5 "$LOGFILE"
        fi
    else
        print_info "Migration watcher is not running"
    fi
}

# Function to show logs
show_logs() {
    if [ -f "$LOGFILE" ]; then
        print_info "Migration watcher logs:"
        cat "$LOGFILE"
    else
        print_warning "No log file found"
    fi
}

# Function to restart the watcher
restart_watcher() {
    stop_watcher
    sleep 2
    start_watcher
}

# Help function
show_help() {
    echo "Migration Watcher - Automatically applies database migrations when schema files change"
    echo ""
    echo "Usage: $0 [command] [environment]"
    echo ""
    echo "Commands:"
    echo "  start [env]   - Start the migration watcher (default: local)"
    echo "  stop          - Stop the migration watcher"
    echo "  restart [env] - Restart the migration watcher"
    echo "  status        - Show watcher status"
    echo "  logs          - Show watcher logs"
    echo "  help          - Show this help message"
    echo ""
    echo "Environments:"
    echo "  local         - Local development database (default)"
    echo "  dev           - GCP dev environment (requires AUTO_MIGRATE_DEV=true)"
    echo ""
    echo "Examples:"
    echo "  $0 start local              # Start watching for local development"
    echo "  $0 start dev                # Start watching for GCP dev (with AUTO_MIGRATE_DEV=true)"
    echo "  $0 stop                     # Stop the watcher"
    echo "  $0 status                   # Check if watcher is running"
    echo ""
    echo "Schema files should be placed in: $SCHEMA_DIR/"
    echo "The watcher will automatically generate migrations and apply them when files change."
}

# Main script logic
case "${1:-start}" in
    "start")
        ENVIRONMENT="${2:-local}"
        start_watcher
        ;;
    "stop")
        stop_watcher
        ;;
    "restart")
        ENVIRONMENT="${2:-local}"
        restart_watcher
        ;;
    "status")
        show_status
        ;;
    "logs")
        show_logs
        ;;
    "help"|"--help"|"-h")
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        show_help
        exit 1
        ;;
esac