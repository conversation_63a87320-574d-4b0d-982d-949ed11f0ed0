templates:
  - name: "python_assistant"
    description: "General-purpose assistant agent with conversational capabilities"
    agent_type: "assistant"
    language: "python"
    capabilities:
      - "conversation"
      - "task_planning"
      - "information_retrieval"
      - "api_integration"
    dependencies:
      - "fastapi"
      - "pydantic"
      - "httpx"
      - "openai"
      - "asyncio"
    template_path: "python/assistant"
    config_schema:
      type: "object"
      properties:
        max_response_length:
          type: "integer"
          default: 1000
        personality:
          type: "string"
          default: "helpful"
        language_model:
          type: "string"
          default: "gpt-3.5-turbo"
    examples:
      - name: "Customer Support Assistant"
        description: "Handles customer inquiries and support requests"
        config:
          personality: "professional"
          max_response_length: 500

  - name: "python_analyst"
    description: "Data analysis agent specialized in processing and analyzing datasets"
    agent_type: "analyst"
    language: "python"
    capabilities:
      - "data_analysis"
      - "visualization"
      - "statistical_analysis"
      - "report_generation"
    dependencies:
      - "pandas"
      - "numpy"
      - "matplotlib"
      - "seaborn"
      - "scipy"
      - "scikit-learn"
    template_path: "python/analyst"
    config_schema:
      type: "object"
      properties:
        data_sources:
          type: "array"
          items:
            type: "string"
        output_format:
          type: "string"
          enum: ["json", "csv", "html", "pdf"]
          default: "json"
        analysis_depth:
          type: "string"
          enum: ["basic", "intermediate", "advanced"]
          default: "intermediate"
    examples:
      - name: "Sales Data Analyst"
        description: "Analyzes sales performance and generates insights"
        config:
          data_sources: ["sales_db", "crm_api"]
          output_format: "html"
          analysis_depth: "advanced"

  - name: "python_processor"
    description: "Data processing agent for ETL operations and data transformation"
    agent_type: "processor"
    language: "python"
    capabilities:
      - "data_processing"
      - "etl_operations"
      - "data_validation"
      - "format_conversion"
    dependencies:
      - "pandas"
      - "sqlalchemy"
      - "requests"
      - "jsonschema"
      - "pydantic"
    template_path: "python/processor"
    config_schema:
      type: "object"
      properties:
        batch_size:
          type: "integer"
          default: 1000
        retry_attempts:
          type: "integer"
          default: 3
        validation_schema:
          type: "object"
    examples:
      - name: "Log Processor"
        description: "Processes and transforms log files"
        config:
          batch_size: 5000
          retry_attempts: 5

  - name: "python_monitor"
    description: "Monitoring agent for system health and performance tracking"
    agent_type: "monitor"
    language: "python"
    capabilities:
      - "system_monitoring"
      - "health_checks"
      - "alerting"
      - "metrics_collection"
    dependencies:
      - "psutil"
      - "prometheus_client"
      - "requests"
      - "schedule"
    template_path: "python/monitor"
    config_schema:
      type: "object"
      properties:
        check_interval:
          type: "integer"
          default: 60
        alert_thresholds:
          type: "object"
          properties:
            cpu_usage:
              type: "number"
              default: 80.0
            memory_usage:
              type: "number"
              default: 85.0
        notification_channels:
          type: "array"
          items:
            type: "string"
    examples:
      - name: "Server Monitor"
        description: "Monitors server health and performance"
        config:
          check_interval: 30
          alert_thresholds:
            cpu_usage: 75.0
            memory_usage: 90.0

  - name: "typescript_assistant"
    description: "TypeScript-based assistant agent for web applications"
    agent_type: "assistant"
    language: "typescript"
    capabilities:
      - "conversation"
      - "web_integration"
      - "api_handling"
      - "ui_interaction"
    dependencies:
      - "express"
      - "@types/node"
      - "axios"
      - "typescript"
    template_path: "typescript/assistant"
    config_schema:
      type: "object"
      properties:
        port:
          type: "integer"
          default: 3000
        cors_enabled:
          type: "boolean"
          default: true
        api_timeout:
          type: "integer"
          default: 5000
    examples:
      - name: "Web Chat Assistant"
        description: "Provides chat functionality for web applications"
        config:
          port: 3001
          cors_enabled: true

  - name: "python_specialist"
    description: "Domain-specific specialist agent with custom expertise"
    agent_type: "specialist"
    language: "python"
    capabilities:
      - "domain_expertise"
      - "custom_logic"
      - "specialized_processing"
      - "knowledge_retrieval"
    dependencies:
      - "fastapi"
      - "pydantic"
      - "httpx"
    template_path: "python/specialist"
    config_schema:
      type: "object"
      properties:
        domain:
          type: "string"
          description: "Area of specialization"
        knowledge_base:
          type: "string"
          description: "Path to knowledge base"
        confidence_threshold:
          type: "number"
          default: 0.8
    examples:
      - name: "Medical Specialist"
        description: "Provides medical information and guidance"
        config:
          domain: "medical"
          confidence_threshold: 0.9

  - name: "python_coordinator"
    description: "Coordination agent for managing multi-agent workflows"
    agent_type: "coordinator"
    language: "python"
    capabilities:
      - "workflow_management"
      - "agent_coordination"
      - "task_distribution"
      - "result_aggregation"
    dependencies:
      - "celery"
      - "redis"
      - "pydantic"
      - "asyncio"
    template_path: "python/coordinator"
    config_schema:
      type: "object"
      properties:
        max_concurrent_tasks:
          type: "integer"
          default: 10
        coordination_strategy:
          type: "string"
          enum: ["sequential", "parallel", "conditional"]
          default: "parallel"
        timeout_minutes:
          type: "integer"
          default: 30
    examples:
      - name: "Data Pipeline Coordinator"
        description: "Coordinates data processing pipeline agents"
        config:
          max_concurrent_tasks: 20
          coordination_strategy: "sequential"

  - name: "python_fullstack"
    description: "Full-stack agent with both backend API and frontend interface"
    agent_type: "fullstack"
    language: "python"
    capabilities:
      - "conversation"
      - "api_integration"
      - "web_integration"
      - "database"
      - "agent_coordination"
    dependencies:
      - "fastapi"
      - "uvicorn"
      - "pydantic"
      - "httpx"
      - "sqlalchemy"
      - "psycopg2-binary"
      - "redis"
      - "asyncio"
      - "websockets"
    template_path: "python/fullstack"
    config_schema:
      type: "object"
      properties:
        frontend_port:
          type: "integer"
          default: 3000
        backend_port:
          type: "integer"
          default: 8000
        database_enabled:
          type: "boolean"
          default: true
        websocket_enabled:
          type: "boolean"
          default: true
        a2a_enabled:
          type: "boolean"
          default: true
    examples:
      - name: "Customer Portal Agent"
        description: "Full-stack customer service portal with chat interface"
        config:
          frontend_port: 3001
          backend_port: 8001
          database_enabled: true
          websocket_enabled: true

  - name: "typescript_fullstack"
    description: "Full-stack TypeScript agent with React frontend and Node.js backend"
    agent_type: "fullstack"
    language: "typescript"
    capabilities:
      - "conversation"
      - "web_integration"
      - "api_integration"
      - "ui_interaction"
    dependencies:
      - "express"
      - "@types/node"
      - "react"
      - "@types/react"
      - "axios"
      - "socket.io"
      - "typescript"
    template_path: "typescript/fullstack"
    config_schema:
      type: "object"
      properties:
        frontend_framework:
          type: "string"
          enum: ["react", "vue", "angular"]
          default: "react"
        backend_framework:
          type: "string"
          enum: ["express", "fastify", "koa"]
          default: "express"
        real_time_enabled:
          type: "boolean"
          default: true
    examples:
      - name: "Interactive Dashboard Agent"
        description: "Real-time dashboard with TypeScript backend and React frontend"
        config:
          frontend_framework: "react"
          backend_framework: "express"
          real_time_enabled: true