/* tslint:disable */
/* eslint-disable */
/**
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { InteractionDetailsAllOfCompany } from './InteractionDetailsAllOfCompany';
import {
    InteractionDetailsAllOfCompanyFromJSON,
    InteractionDetailsAllOfCompanyFromJSONTyped,
    InteractionDetailsAllOfCompanyToJSON,
    InteractionDetailsAllOfCompanyToJSONTyped,
} from './InteractionDetailsAllOfCompany';
import type { InteractionDetailsAllOfContact } from './InteractionDetailsAllOfContact';
import {
    InteractionDetailsAllOfContactFromJSON,
    InteractionDetailsAllOfContactFromJSONTyped,
    InteractionDetailsAllOfContactToJSON,
    InteractionDetailsAllOfContactToJSONTyped,
} from './InteractionDetailsAllOfContact';

/**
 * 
 * @export
 * @interface InteractionDetails
 */
export interface InteractionDetails {
    /**
     * 
     * @type {string}
     * @memberof InteractionDetails
     */
    id?: string;
    /**
     * 
     * @type {string}
     * @memberof InteractionDetails
     */
    companyId?: string | null;
    /**
     * 
     * @type {string}
     * @memberof InteractionDetails
     */
    contactId?: string | null;
    /**
     * 
     * @type {string}
     * @memberof InteractionDetails
     */
    interactionType?: InteractionDetailsInteractionTypeEnum;
    /**
     * 
     * @type {string}
     * @memberof InteractionDetails
     */
    notes?: string;
    /**
     * 
     * @type {Date}
     * @memberof InteractionDetails
     */
    interactionDatetime?: Date;
    /**
     * 
     * @type {string}
     * @memberof InteractionDetails
     */
    createdBy?: string;
    /**
     * 
     * @type {Date}
     * @memberof InteractionDetails
     */
    createdAt?: Date;
    /**
     * 
     * @type {Date}
     * @memberof InteractionDetails
     */
    updatedAt?: Date;
    /**
     * 
     * @type {InteractionDetailsAllOfCompany}
     * @memberof InteractionDetails
     */
    company?: InteractionDetailsAllOfCompany | null;
    /**
     * 
     * @type {InteractionDetailsAllOfContact}
     * @memberof InteractionDetails
     */
    contact?: InteractionDetailsAllOfContact | null;
}

/**
* @export
* @enum {string}
*/
export enum InteractionDetailsInteractionTypeEnum {
    Email = 'email',
    Phone = 'phone',
    Meeting = 'meeting',
    Demo = 'demo',
    Proposal = 'proposal',
    FollowUp = 'follow-up',
    Other = 'other'
}


/**
 * Check if a given object implements the InteractionDetails interface.
 */
export function instanceOfInteractionDetails(value: object): value is InteractionDetails {
    return true;
}

export function InteractionDetailsFromJSON(json: any): InteractionDetails {
    return InteractionDetailsFromJSONTyped(json, false);
}

export function InteractionDetailsFromJSONTyped(json: any, ignoreDiscriminator: boolean): InteractionDetails {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'] == null ? undefined : json['id'],
        'companyId': json['company_id'] == null ? undefined : json['company_id'],
        'contactId': json['contact_id'] == null ? undefined : json['contact_id'],
        'interactionType': json['interaction_type'] == null ? undefined : json['interaction_type'],
        'notes': json['notes'] == null ? undefined : json['notes'],
        'interactionDatetime': json['interaction_datetime'] == null ? undefined : (new Date(json['interaction_datetime'])),
        'createdBy': json['created_by'] == null ? undefined : json['created_by'],
        'createdAt': json['created_at'] == null ? undefined : (new Date(json['created_at'])),
        'updatedAt': json['updated_at'] == null ? undefined : (new Date(json['updated_at'])),
        'company': json['company'] == null ? undefined : InteractionDetailsAllOfCompanyFromJSON(json['company']),
        'contact': json['contact'] == null ? undefined : InteractionDetailsAllOfContactFromJSON(json['contact']),
    };
}

  export function InteractionDetailsToJSON(json: any): InteractionDetails {
      return InteractionDetailsToJSONTyped(json, false);
  }

  export function InteractionDetailsToJSONTyped(value?: InteractionDetails | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'company_id': value['companyId'],
        'contact_id': value['contactId'],
        'interaction_type': value['interactionType'],
        'notes': value['notes'],
        'interaction_datetime': value['interactionDatetime'] == null ? undefined : ((value['interactionDatetime']).toISOString()),
        'created_by': value['createdBy'],
        'created_at': value['createdAt'] == null ? undefined : ((value['createdAt']).toISOString()),
        'updated_at': value['updatedAt'] == null ? undefined : ((value['updatedAt']).toISOString()),
        'company': InteractionDetailsAllOfCompanyToJSON(value['company']),
        'contact': InteractionDetailsAllOfContactToJSON(value['contact']),
    };
}

