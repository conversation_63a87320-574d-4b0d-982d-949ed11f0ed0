/* tslint:disable */
/* eslint-disable */
/**
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface DealCreate
 */
export interface DealCreate {
    /**
     * 
     * @type {string}
     * @memberof DealCreate
     */
    title: string;
    /**
     * 
     * @type {string}
     * @memberof DealCreate
     */
    description?: string;
    /**
     * 
     * @type {number}
     * @memberof DealCreate
     */
    estimatedValue?: number;
    /**
     * 
     * @type {string}
     * @memberof DealCreate
     */
    companyId: string;
    /**
     * 
     * @type {string}
     * @memberof DealCreate
     */
    dealStageId: string;
    /**
     * 
     * @type {Date}
     * @memberof DealCreate
     */
    expectedCloseDate?: Date;
}

/**
 * Check if a given object implements the DealCreate interface.
 */
export function instanceOfDealCreate(value: object): value is DealCreate {
    if (!('title' in value) || value['title'] === undefined) return false;
    if (!('companyId' in value) || value['companyId'] === undefined) return false;
    if (!('dealStageId' in value) || value['dealStageId'] === undefined) return false;
    return true;
}

export function DealCreateFromJSON(json: any): DealCreate {
    return DealCreateFromJSONTyped(json, false);
}

export function DealCreateFromJSONTyped(json: any, ignoreDiscriminator: boolean): DealCreate {
    if (json == null) {
        return json;
    }
    return {
        
        'title': json['title'],
        'description': json['description'] == null ? undefined : json['description'],
        'estimatedValue': json['estimated_value'] == null ? undefined : json['estimated_value'],
        'companyId': json['company_id'],
        'dealStageId': json['deal_stage_id'],
        'expectedCloseDate': json['expected_close_date'] == null ? undefined : (new Date(json['expected_close_date'])),
    };
}

  export function DealCreateToJSON(json: any): DealCreate {
      return DealCreateToJSONTyped(json, false);
  }

  export function DealCreateToJSONTyped(value?: DealCreate | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'title': value['title'],
        'description': value['description'],
        'estimated_value': value['estimatedValue'],
        'company_id': value['companyId'],
        'deal_stage_id': value['dealStageId'],
        'expected_close_date': value['expectedCloseDate'] == null ? undefined : ((value['expectedCloseDate']).toISOString().substring(0,10)),
    };
}

