#!/bin/bash
set -e

echo "Loading test data into the CRM database..."

# Check if postgres container is running (try both Docker Compose and individual container)
if docker ps | grep -q platform_postgres; then
    POSTGRES_CONTAINER="platform_postgres"
else
    echo "ERROR: PostgreSQL container is not running!"
    echo "Please run 'docker-compose up -d' (from platform directory) or 'bazel run //db:start' first."
    exit 1
fi

# Load the test data
# Find the test_data.sql file
if [ -f "test_data.sql" ]; then
    SQL_FILE="test_data.sql"
elif [ -f "db/test_data.sql" ]; then
    SQL_FILE="db/test_data.sql"
else
    # Look in the current directory and subdirectories
    SQL_FILE=$(find . -name "test_data.sql" -type f 2>/dev/null | head -1)
fi

if [ -z "$SQL_FILE" ]; then
    echo "ERROR: test_data.sql not found"
    exit 1
fi

docker exec -i "$POSTGRES_CONTAINER" psql -U postgres -d appdb < "$SQL_FILE"

echo "Test data loaded successfully!"
echo ""
echo "You can now log in with:"
echo "  Email: <EMAIL>"
echo "  Password: TestPassword"
echo ""
echo "  OR"
echo ""  
echo "  Email: <EMAIL>"
echo "  Password: password123"