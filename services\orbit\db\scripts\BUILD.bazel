load("@rules_go//go:def.bzl", "go_binary", "go_library")

# Export shell scripts so they can be referenced by parent package
exports_files([
    "start.sh",
    "stop.sh",
    "migrate.sh",
    "clean.sh",
    "clear_data.sh",
    "status.sh",
    "simple_test_data.sh",
    "getting_started.sh",
    "nuke.sh",
    "setup_test_db.sh",
    "cleanup_test_db.sh",
    "fix_flyway_state.sh",
    "migrate_gcp_dev.sh",
    "status_gcp_dev.sh",
    "migrate_gcp_dev_remote.sh",
    "migrate_gcp_dev_simple.sh",
    "migrate_gcp_simple.sh",
    "test_data_gcp_dev.sh",
    "auto_migrate.sh",
    "migration_watcher.sh",
    "pre_commit_migration.sh",
])

go_library(
    name = "scripts_lib",
    srcs = ["hash_password.go"],
    importpath = "github.com/TwoDotAi/mono/platform/db/scripts",
    visibility = ["//visibility:private"],
    deps = ["@org_golang_x_crypto//bcrypt"],
)

go_binary(
    name = "scripts",
    embed = [":scripts_lib"],
    visibility = ["//visibility:public"],
)
