package main

import (
	"fmt"
	"os"

	"golang.org/x/crypto/bcrypt"
)

func main() {
	if len(os.Args) != 2 {
		fmt.Println("Usage: go run hash_password.go <password>")
		fmt.Println("Example: go run hash_password.go 'TestPassword'")
		os.Exit(1)
	}

	password := os.Args[1]
	
	// Generate bcrypt hash with cost 12 (same as backend might use)
	hash, err := bcrypt.GenerateFromPassword([]byte(password), 12)
	if err != nil {
		fmt.Printf("Error generating hash: %v\n", err)
		os.Exit(1)
	}

	fmt.Print(string(hash))
}