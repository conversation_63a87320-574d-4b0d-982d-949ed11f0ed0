#!/bin/bash
# Development server script for CRM web app

# Get the absolute path to the services/orbit/web directory
SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"

# When run via <PERSON>zel, we need to use the workspace directory
if [ -n "$BUILD_WORKSPACE_DIRECTORY" ]; then
    # Running via Bazel - use the actual source location
    PLATFORM_WEB_DIR="$BUILD_WORKSPACE_DIRECTORY/services/orbit/web"
else
    # Running directly
    PLATFORM_WEB_DIR="$SCRIPT_DIR"
fi

# Change to the services/orbit/web directory
cd "$PLATFORM_WEB_DIR"

# Verify we're in the right place
if [ ! -f "package.json" ]; then
    echo "Error: package.json not found in $PLATFORM_WEB_DIR"
    exit 1
fi

if [ ! -f "src/main.tsx" ]; then
    echo "Error: src/main.tsx not found in $PLATFORM_WEB_DIR"
    exit 1
fi

# Ensure we're using localhost for development
echo "Setting up development environment with localhost configuration..."
echo "VITE_API_BASE_URL=http://localhost:8085/api/v1" > .env.local
echo "✅ Development environment configured for localhost"
echo "API will connect to: http://localhost:8085/api/v1"

npm install
npm run dev