{"name": "ai-agent-platform-frontend", "version": "2.0.0", "private": true, "description": "AI Agent Platform - Frontend Application with Tailwind & ShadCN", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "add": "npx shadcn-ui@latest add", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "jest --config=jest.config.integration.js", "test:integration:watch": "jest --config=jest.config.integration.js --watch", "test:e2e": "playwright test", "test:e2e:headed": "playwright test --headed", "test:e2e:ui": "playwright test --ui", "test:e2e:critical": "playwright test agent-generation.critical.spec.ts", "test:browser": "playwright test browser-test-suite.spec.ts", "test:all": "npm run test && npm run test:e2e", "playwright:install": "playwright install", "playwright:report": "playwright show-report", "dev:all": "concurrently \"npm run dev\" \"cd ../backend && make dev\""}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@playwright/test": "^1.54.1", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-aspect-ratio": "^1.0.3", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-tooltip": "^1.0.7", "@tanstack/react-query": "^5.8.4", "@types/js-cookie": "^3.0.6", "@types/node": "^20.10.0", "@types/react": "^18.2.39", "@types/react-dom": "^18.2.17", "axios": "^1.6.2", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cmdk": "^0.2.0", "date-fns": "^2.30.0", "js-cookie": "^3.0.5", "lucide-react": "^0.294.0", "next": "^14.0.4", "react": "^18.2.0", "react-day-picker": "^8.9.1", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-resizable-panels": "^0.0.55", "recharts": "^2.8.0", "tailwind-merge": "^2.0.0", "typescript": "^5.3.2", "vaul": "^0.7.9", "zod": "^3.22.4"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/react-resizable": "^3.0.7", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "autoprefixer": "^10.4.16", "concurrently": "^9.2.0", "eslint": "^8.54.0", "eslint-config-next": "^14.0.4", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "nodemon": "^3.1.10", "postcss": "^8.4.32", "tailwindcss": "^3.3.6"}}