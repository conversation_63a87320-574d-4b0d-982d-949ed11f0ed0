import { FunctionComponent, h } from 'preact';
import { useState } from 'preact/hooks';
import { api } from '@/services/api';

interface ScheduleConfiguratorProps {
  onSubmit?: (data: ScheduleData) => void;
}

interface ScheduleData {
  dateTime: string;
  recurrence: 'daily' | 'weekly' | 'monthly';
}

interface FormErrors {
  dateTime?: string;
  recurrence?: string;
}

const ScheduleConfigurator: FunctionComponent<ScheduleConfiguratorProps> = ({ onSubmit }) => {
  const [formData, setFormData] = useState<ScheduleData>({
    dateTime: '',
    recurrence: 'monthly',
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const validateForm = (data: ScheduleData): boolean => {
    const newErrors: FormErrors = {};
    
    if (!data.dateTime) {
      newErrors.dateTime = 'Date and time are required';
    }
    if (!data.recurrence) {
      newErrors.recurrence = 'Recurrence is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: Event) => {
    e.preventDefault();
    if (!validateForm(formData)) return;

    const data = formData;
    setIsLoading(true);
    setError(null);
    try {
      // Example API call (replace with actual logic)
      // const response = await api.post('/schedule', data);
      if (onSubmit) {
        onSubmit(data);
      }
    } catch (err) {
      setError('Failed to save schedule.');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} class="space-y-4">
      <div>
        <label for="dateTime" class="block mb-1">Date/Time</label>
        <input 
          type="datetime-local" 
          id="dateTime" 
          value={formData.dateTime}
          onInput={(e) => setFormData(prev => ({ ...prev, dateTime: (e.target as HTMLInputElement).value }))}
          class="border rounded px-2 py-1 w-full" 
        />
        {errors.dateTime && <p class="text-red-500 text-sm">{errors.dateTime.message}</p>}
      </div>

      <div>
        <label for="recurrence" class="block mb-1">Recurrence</label>
        <select 
          id="recurrence" 
          value={formData.recurrence}
          onChange={(e) => setFormData(prev => ({ ...prev, recurrence: (e.target as HTMLSelectElement).value as 'daily' | 'weekly' | 'monthly' }))}
          class="border rounded px-2 py-1 w-full"
        >
          <option value="daily">Daily</option>
          <option value="weekly">Weekly</option>
          <option value="monthly">Monthly</option>
        </select>
        {errors.recurrence && <p class="text-red-500 text-sm">{errors.recurrence.message}</p>}
      </div>

      {error && <p class="text-red-500 text-sm">{error}</p>}

      <button type="submit" disabled={isLoading} class="bg-blue-500 text-white px-4 py-2 rounded disabled:opacity-50">
        {isLoading ? 'Saving...' : 'Save Schedule'}
      </button>
    </form>
  );
};

export default ScheduleConfigurator;
