"""
Multi-Factor Authentication (MFA) Service
Support for TOTP, SMS, Email, and WebAuthn
"""

import secrets
import base64
import json
import qrcode
import io
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple
import pyotp
import httpx
from cryptography.fernet import <PERSON>rnet
from sqlalchemy.orm import Session
from fastapi import HTT<PERSON>Ex<PERSON>, status
import structlog

from config.settings import settings
from database.models import User, MFADevice, LoginAttempt
from database.connection import get_db

logger = structlog.get_logger(__name__)

# Encryption for sensitive data
encryption_key = settings.mfa_encryption_key.encode() if settings.mfa_encryption_key else Fernet.generate_key()
cipher = Fernet(encryption_key)


class MFAService:
    """Multi-Factor Authentication service"""
    
    def __init__(self):
        self.backup_code_length = 8
        self.backup_code_count = 10
        logger.info("MFA service initialized")
    
    async def setup_totp(self, user: User, device_name: str, db: Session) -> Dict[str, Any]:
        """Setup TOTP (Time-based One-Time Password) device"""
        try:
            # Generate secret key
            secret_key = pyotp.random_base32()
            
            # Create TOTP instance
            totp = pyotp.TOTP(secret_key)
            
            # Generate provisioning URI for QR code
            provisioning_uri = totp.provisioning_uri(
                user.email,
                issuer_name=settings.app_name
            )
            
            # Generate QR code
            qr = qrcode.QRCode(version=1, box_size=10, border=5)
            qr.add_data(provisioning_uri)
            qr.make(fit=True)
            
            qr_image = qr.make_image(fill_color="black", back_color="white")
            qr_buffer = io.BytesIO()
            qr_image.save(qr_buffer, format='PNG')
            qr_code_base64 = base64.b64encode(qr_buffer.getvalue()).decode()
            
            # Encrypt and store secret key
            encrypted_secret = cipher.encrypt(secret_key.encode()).decode()
            
            # Create MFA device (unverified)
            mfa_device = MFADevice(
                user_id=user.id,
                device_type="totp",
                device_name=device_name,
                secret_key=encrypted_secret,
                is_verified=False,
                is_primary=False,
            )
            
            db.add(mfa_device)
            db.commit()
            db.refresh(mfa_device)
            
            logger.info(f"TOTP device created for user {user.id}", device_id=str(mfa_device.id))
            
            return {
                "device_id": str(mfa_device.id),
                "secret_key": secret_key,  # Only returned during setup
                "qr_code": f"data:image/png;base64,{qr_code_base64}",
                "provisioning_uri": provisioning_uri,
                "backup_codes": None,  # Will be generated after verification
            }
            
        except Exception as e:
            logger.error(f"Failed to setup TOTP for user {user.id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to setup TOTP device"
            )
    
    async def verify_totp_setup(self, user: User, device_id: str, token: str, db: Session) -> Dict[str, Any]:
        """Verify TOTP device setup"""
        try:
            # Get MFA device
            mfa_device = db.query(MFADevice).filter(
                MFADevice.id == device_id,
                MFADevice.user_id == user.id,
                MFADevice.device_type == "totp",
                MFADevice.is_verified == False
            ).first()
            
            if not mfa_device:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="TOTP device not found or already verified"
                )
            
            # Decrypt secret key
            decrypted_secret = cipher.decrypt(mfa_device.secret_key.encode()).decode()
            
            # Verify token
            totp = pyotp.TOTP(decrypted_secret)
            if not totp.verify(token, valid_window=1):  # Allow 1 window of tolerance
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid TOTP code"
                )
            
            # Generate backup codes
            backup_codes = self._generate_backup_codes()
            encrypted_backup_codes = [
                cipher.encrypt(code.encode()).decode()
                for code in backup_codes
            ]
            
            # Mark device as verified and set as primary if it's the first MFA device
            is_first_device = db.query(MFADevice).filter(
                MFADevice.user_id == user.id,
                MFADevice.is_verified == True
            ).count() == 0
            
            mfa_device.is_verified = True
            mfa_device.is_primary = is_first_device
            mfa_device.backup_codes = encrypted_backup_codes
            mfa_device.last_used_at = datetime.utcnow()
            
            db.commit()
            db.refresh(mfa_device)
            
            logger.info(f"TOTP device verified for user {user.id}", device_id=device_id)
            
            return {
                "device_id": device_id,
                "is_primary": is_first_device,
                "backup_codes": backup_codes,  # Show backup codes only once
                "message": "TOTP device successfully verified"
            }
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to verify TOTP setup for user {user.id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to verify TOTP device"
            )
    
    async def verify_totp_token(self, user: User, token: str, db: Session) -> bool:
        """Verify TOTP token for authentication"""
        try:
            # Get user's TOTP devices
            totp_devices = db.query(MFADevice).filter(
                MFADevice.user_id == user.id,
                MFADevice.device_type == "totp",
                MFADevice.is_verified == True
            ).all()
            
            if not totp_devices:
                return False
            
            # Try each TOTP device
            for device in totp_devices:
                try:
                    # Decrypt secret key
                    decrypted_secret = cipher.decrypt(device.secret_key.encode()).decode()
                    
                    # Verify token
                    totp = pyotp.TOTP(decrypted_secret)
                    if totp.verify(token, valid_window=1):
                        # Update last used timestamp
                        device.last_used_at = datetime.utcnow()
                        db.commit()
                        
                        logger.info(f"TOTP token verified for user {user.id}", device_id=str(device.id))
                        return True
                        
                except Exception as e:
                    logger.warning(f"Failed to verify TOTP token for device {device.id}: {e}")
                    continue
            
            # Try backup codes if TOTP failed
            return await self._verify_backup_code(user, token, db)
            
        except Exception as e:
            logger.error(f"Failed to verify TOTP token for user {user.id}: {e}")
            return False
    
    async def setup_sms(self, user: User, device_name: str, phone_number: str, db: Session) -> Dict[str, Any]:
        """Setup SMS MFA device"""
        try:
            # Validate phone number format
            if not self._validate_phone_number(phone_number):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid phone number format"
                )
            
            # Create MFA device (unverified)
            mfa_device = MFADevice(
                user_id=user.id,
                device_type="sms",
                device_name=device_name,
                phone_number=phone_number,
                is_verified=False,
                is_primary=False,
            )
            
            db.add(mfa_device)
            db.commit()
            db.refresh(mfa_device)
            
            # Send verification SMS
            verification_code = self._generate_sms_code()
            await self._send_sms_verification(phone_number, verification_code, user)
            
            # Store verification code temporarily (in production, use Redis)
            self._store_verification_code(str(mfa_device.id), verification_code)
            
            logger.info(f"SMS device created for user {user.id}", device_id=str(mfa_device.id))
            
            return {
                "device_id": str(mfa_device.id),
                "phone_number": self._mask_phone_number(phone_number),
                "message": "Verification code sent to your phone"
            }
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to setup SMS for user {user.id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to setup SMS device"
            )
    
    async def verify_sms_setup(self, user: User, device_id: str, code: str, db: Session) -> Dict[str, Any]:
        """Verify SMS device setup"""
        try:
            # Get MFA device
            mfa_device = db.query(MFADevice).filter(
                MFADevice.id == device_id,
                MFADevice.user_id == user.id,
                MFADevice.device_type == "sms",
                MFADevice.is_verified == False
            ).first()
            
            if not mfa_device:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="SMS device not found or already verified"
                )
            
            # Verify code
            stored_code = self._get_verification_code(device_id)
            if not stored_code or stored_code != code:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid verification code"
                )
            
            # Generate backup codes
            backup_codes = self._generate_backup_codes()
            encrypted_backup_codes = [
                cipher.encrypt(code.encode()).decode()
                for code in backup_codes
            ]
            
            # Mark device as verified
            is_first_device = db.query(MFADevice).filter(
                MFADevice.user_id == user.id,
                MFADevice.is_verified == True
            ).count() == 0
            
            mfa_device.is_verified = True
            mfa_device.is_primary = is_first_device
            mfa_device.backup_codes = encrypted_backup_codes
            
            db.commit()
            db.refresh(mfa_device)
            
            # Clean up verification code
            self._remove_verification_code(device_id)
            
            logger.info(f"SMS device verified for user {user.id}", device_id=device_id)
            
            return {
                "device_id": device_id,
                "is_primary": is_first_device,
                "backup_codes": backup_codes,
                "message": "SMS device successfully verified"
            }
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to verify SMS setup for user {user.id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to verify SMS device"
            )
    
    async def send_sms_token(self, user: User, db: Session) -> Dict[str, Any]:
        """Send SMS token for authentication"""
        try:
            # Get user's SMS devices
            sms_devices = db.query(MFADevice).filter(
                MFADevice.user_id == user.id,
                MFADevice.device_type == "sms",
                MFADevice.is_verified == True
            ).all()
            
            if not sms_devices:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="No verified SMS devices found"
                )
            
            # Use primary device or first available
            primary_device = next((d for d in sms_devices if d.is_primary), sms_devices[0])
            
            # Generate and send SMS code
            sms_code = self._generate_sms_code()
            await self._send_sms_token(primary_device.phone_number, sms_code, user)
            
            # Store code temporarily
            self._store_verification_code(f"auth_{user.id}", sms_code, expires_in=300)  # 5 minutes
            
            logger.info(f"SMS token sent to user {user.id}", device_id=str(primary_device.id))
            
            return {
                "phone_number": self._mask_phone_number(primary_device.phone_number),
                "message": "Authentication code sent to your phone"
            }
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to send SMS token to user {user.id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to send SMS token"
            )
    
    async def verify_sms_token(self, user: User, token: str, db: Session) -> bool:
        """Verify SMS token for authentication"""
        try:
            # Check stored verification code
            stored_code = self._get_verification_code(f"auth_{user.id}")
            if stored_code and stored_code == token:
                # Update last used timestamp
                sms_device = db.query(MFADevice).filter(
                    MFADevice.user_id == user.id,
                    MFADevice.device_type == "sms",
                    MFADevice.is_verified == True,
                    MFADevice.is_primary == True
                ).first()
                
                if sms_device:
                    sms_device.last_used_at = datetime.utcnow()
                    db.commit()
                
                # Remove used code
                self._remove_verification_code(f"auth_{user.id}")
                
                logger.info(f"SMS token verified for user {user.id}")
                return True
            
            # Try backup codes if SMS failed
            return await self._verify_backup_code(user, token, db)
            
        except Exception as e:
            logger.error(f"Failed to verify SMS token for user {user.id}: {e}")
            return False
    
    async def get_user_mfa_devices(self, user: User, db: Session) -> List[Dict[str, Any]]:
        """Get user's MFA devices"""
        devices = db.query(MFADevice).filter(
            MFADevice.user_id == user.id,
            MFADevice.is_verified == True
        ).all()
        
        return [
            {
                "id": str(device.id),
                "device_type": device.device_type,
                "device_name": device.device_name,
                "is_primary": device.is_primary,
                "last_used_at": device.last_used_at.isoformat() if device.last_used_at else None,
                "phone_number": self._mask_phone_number(device.phone_number) if device.phone_number else None,
                "created_at": device.created_at.isoformat(),
            }
            for device in devices
        ]
    
    async def remove_mfa_device(self, user: User, device_id: str, db: Session) -> bool:
        """Remove MFA device"""
        try:
            device = db.query(MFADevice).filter(
                MFADevice.id == device_id,
                MFADevice.user_id == user.id
            ).first()
            
            if not device:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="MFA device not found"
                )
            
            # Don't allow removing the last MFA device if MFA is required
            remaining_devices = db.query(MFADevice).filter(
                MFADevice.user_id == user.id,
                MFADevice.is_verified == True,
                MFADevice.id != device_id
            ).count()
            
            if remaining_devices == 0 and self._is_mfa_required_for_user(user):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Cannot remove the last MFA device when MFA is required"
                )
            
            db.delete(device)
            db.commit()
            
            logger.info(f"MFA device removed for user {user.id}", device_id=device_id)
            return True
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to remove MFA device for user {user.id}: {e}")
            return False
    
    def _generate_backup_codes(self) -> List[str]:
        """Generate backup codes"""
        codes = []
        for _ in range(self.backup_code_count):
            code = ''.join([str(secrets.randbelow(10)) for _ in range(self.backup_code_length)])
            codes.append(f"{code[:4]}-{code[4:]}")  # Format: 1234-5678
        return codes
    
    async def _verify_backup_code(self, user: User, code: str, db: Session) -> bool:
        """Verify backup code"""
        try:
            devices_with_backup_codes = db.query(MFADevice).filter(
                MFADevice.user_id == user.id,
                MFADevice.is_verified == True,
                MFADevice.backup_codes.isnot(None)
            ).all()
            
            for device in devices_with_backup_codes:
                if not device.backup_codes:
                    continue
                
                # Decrypt backup codes
                try:
                    decrypted_codes = [
                        cipher.decrypt(encrypted_code.encode()).decode()
                        for encrypted_code in device.backup_codes
                    ]
                    
                    if code in decrypted_codes:
                        # Mark code as used
                        used_codes = device.backup_codes_used or []
                        used_codes.append(code)
                        device.backup_codes_used = used_codes
                        device.last_used_at = datetime.utcnow()
                        
                        db.commit()
                        
                        logger.info(f"Backup code verified for user {user.id}", device_id=str(device.id))
                        return True
                        
                except Exception as e:
                    logger.warning(f"Failed to decrypt backup codes for device {device.id}: {e}")
                    continue
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to verify backup code for user {user.id}: {e}")
            return False
    
    def _generate_sms_code(self) -> str:
        """Generate 6-digit SMS code"""
        return ''.join([str(secrets.randbelow(10)) for _ in range(6)])
    
    def _validate_phone_number(self, phone_number: str) -> bool:
        """Basic phone number validation"""
        # Remove all non-digit characters
        digits_only = ''.join(filter(str.isdigit, phone_number))
        
        # Basic validation: 10-15 digits
        return 10 <= len(digits_only) <= 15
    
    def _mask_phone_number(self, phone_number: str) -> str:
        """Mask phone number for display"""
        if not phone_number:
            return ""
        
        if len(phone_number) <= 4:
            return "*" * len(phone_number)
        
        return phone_number[:2] + "*" * (len(phone_number) - 4) + phone_number[-2:]
    
    async def _send_sms_verification(self, phone_number: str, code: str, user: User):
        """Send SMS verification code"""
        message = f"Your {settings.app_name} verification code is: {code}. This code will expire in 10 minutes."
        await self._send_sms(phone_number, message)
    
    async def _send_sms_token(self, phone_number: str, code: str, user: User):
        """Send SMS authentication token"""
        message = f"Your {settings.app_name} login code is: {code}. This code will expire in 5 minutes."
        await self._send_sms(phone_number, message)
    
    async def _send_sms(self, phone_number: str, message: str):
        """Send SMS using configured provider"""
        # This is a placeholder implementation
        # In production, integrate with SMS providers like Twilio, AWS SNS, etc.
        if settings.sms_provider == "twilio":
            await self._send_sms_twilio(phone_number, message)
        elif settings.sms_provider == "aws_sns":
            await self._send_sms_aws_sns(phone_number, message)
        else:
            # Mock SMS for development
            logger.info(f"Mock SMS sent to {phone_number}: {message}")
    
    async def _send_sms_twilio(self, phone_number: str, message: str):
        """Send SMS using Twilio"""
        # Implement Twilio SMS integration
        logger.info(f"Twilio SMS to {phone_number}: {message}")
    
    async def _send_sms_aws_sns(self, phone_number: str, message: str):
        """Send SMS using AWS SNS"""
        # Implement AWS SNS SMS integration
        logger.info(f"AWS SNS SMS to {phone_number}: {message}")
    
    def _store_verification_code(self, key: str, code: str, expires_in: int = 600):
        """Store verification code temporarily"""
        # In production, use Redis or similar for temporary storage
        # For now, use in-memory storage (not suitable for production)
        if not hasattr(self, '_verification_codes'):
            self._verification_codes = {}
        
        self._verification_codes[key] = {
            'code': code,
            'expires_at': datetime.utcnow() + timedelta(seconds=expires_in)
        }
    
    def _get_verification_code(self, key: str) -> Optional[str]:
        """Get stored verification code"""
        if not hasattr(self, '_verification_codes'):
            return None
        
        if key not in self._verification_codes:
            return None
        
        data = self._verification_codes[key]
        if datetime.utcnow() > data['expires_at']:
            del self._verification_codes[key]
            return None
        
        return data['code']
    
    def _remove_verification_code(self, key: str):
        """Remove stored verification code"""
        if hasattr(self, '_verification_codes') and key in self._verification_codes:
            del self._verification_codes[key]
    
    def _is_mfa_required_for_user(self, user: User) -> bool:
        """Check if MFA is required for user"""
        # In production, implement policy-based MFA requirements
        # For now, MFA is optional unless user has superuser role
        return user.is_superuser


# Global MFA service instance
mfa_service = MFAService()