"""
AI Agent Platform - Orchestration API Routes
"""

from typing import List, Optional
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
import structlog

from database.connection import get_db
from database.models import OrchestrationPattern
from services.orchestration import OrchestrationService
from auth.dependencies import get_current_active_user
from .schemas import (
    OrchestrationCreate, OrchestrationResponse, OrchestrationUpdate,
    OrchestrationListResponse, OrchestrationStartResponse,
    OrchestrationProgressResponse, AddAgentRequest
)

logger = structlog.get_logger()

router = APIRouter(prefix="/orchestrations", tags=["orchestrations"])


@router.post("/", response_model=OrchestrationResponse, status_code=status.HTTP_201_CREATED)
async def create_orchestration(
    orchestration_data: OrchestrationCreate,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Create a new orchestration"""
    try:
        orchestration_service = OrchestrationService(db)
        
        orchestration = await orchestration_service.create_orchestration(
            owner_id=current_user.id,
            name=orchestration_data.name,
            description=orchestration_data.description,
            pattern=orchestration_data.pattern,
            config=orchestration_data.config,
            execution_plan=orchestration_data.execution_plan,
            agent_ids=orchestration_data.agent_ids
        )
        
        return OrchestrationResponse.from_orm(orchestration)
        
    except Exception as e:
        logger.error("Failed to create orchestration via API", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create orchestration"
        )


@router.get("/{orchestration_id}", response_model=OrchestrationResponse)
async def get_orchestration(
    orchestration_id: UUID,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get orchestration by ID"""
    try:
        orchestration_service = OrchestrationService(db)
        orchestration = await orchestration_service.get_orchestration(orchestration_id)
        
        if not orchestration:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Orchestration not found"
            )
        
        # Check ownership
        if orchestration.owner_id != current_user.id and not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        return OrchestrationResponse.from_orm(orchestration)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get orchestration via API", orchestration_id=str(orchestration_id), error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve orchestration"
        )


@router.get("/", response_model=OrchestrationListResponse)
async def list_orchestrations(
    status_filter: Optional[str] = Query(None, alias="status"),
    pattern_filter: Optional[OrchestrationPattern] = Query(None, alias="pattern"),
    limit: int = Query(100, ge=1, le=1000),
    offset: int = Query(0, ge=0),
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """List orchestrations for current user"""
    try:
        orchestration_service = OrchestrationService(db)
        
        # Filter by current user unless superuser
        owner_id = None if current_user.is_superuser else current_user.id
        
        orchestrations = await orchestration_service.list_orchestrations(
            owner_id=owner_id,
            status=status_filter,
            pattern=pattern_filter,
            limit=limit,
            offset=offset
        )
        
        return OrchestrationListResponse(
            orchestrations=[OrchestrationResponse.from_orm(orch) for orch in orchestrations],
            total=len(orchestrations),
            limit=limit,
            offset=offset
        )
        
    except Exception as e:
        logger.error("Failed to list orchestrations via API", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list orchestrations"
        )


@router.put("/{orchestration_id}", response_model=OrchestrationResponse)
async def update_orchestration(
    orchestration_id: UUID,
    orchestration_data: OrchestrationUpdate,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Update an orchestration"""
    try:
        orchestration_service = OrchestrationService(db)
        
        # Get current orchestration to verify ownership
        current_orchestration = await orchestration_service.get_orchestration(orchestration_id)
        if not current_orchestration:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Orchestration not found"
            )
        
        # Check ownership
        if current_orchestration.owner_id != current_user.id and not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        # Prepare update data
        update_data = orchestration_data.dict(exclude_unset=True)
        
        updated_orchestration = await orchestration_service.update_orchestration(
            orchestration_id,
            **update_data
        )
        
        if not updated_orchestration:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Orchestration not found"
            )
        
        return OrchestrationResponse.from_orm(updated_orchestration)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to update orchestration via API", orchestration_id=str(orchestration_id), error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update orchestration"
        )


@router.post("/{orchestration_id}/start", response_model=OrchestrationStartResponse)
async def start_orchestration(
    orchestration_id: UUID,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Start an orchestration"""
    try:
        orchestration_service = OrchestrationService(db)
        
        # Verify ownership
        orchestration = await orchestration_service.get_orchestration(orchestration_id)
        if not orchestration:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Orchestration not found"
            )
        
        if orchestration.owner_id != current_user.id and not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        success = await orchestration_service.start_orchestration(orchestration_id)
        
        return OrchestrationStartResponse(
            orchestration_id=orchestration_id,
            success=success,
            message="Orchestration started successfully" if success else "Failed to start orchestration"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to start orchestration via API", orchestration_id=str(orchestration_id), error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start orchestration"
        )


@router.post("/{orchestration_id}/stop", response_model=OrchestrationStartResponse)
async def stop_orchestration(
    orchestration_id: UUID,
    graceful: bool = Query(True),
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Stop an orchestration"""
    try:
        orchestration_service = OrchestrationService(db)
        
        # Verify ownership
        orchestration = await orchestration_service.get_orchestration(orchestration_id)
        if not orchestration:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Orchestration not found"
            )
        
        if orchestration.owner_id != current_user.id and not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        success = await orchestration_service.stop_orchestration(orchestration_id, graceful)
        
        return OrchestrationStartResponse(
            orchestration_id=orchestration_id,
            success=success,
            message="Orchestration stopped successfully" if success else "Failed to stop orchestration"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to stop orchestration via API", orchestration_id=str(orchestration_id), error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to stop orchestration"
        )


@router.post("/{orchestration_id}/pause")
async def pause_orchestration(
    orchestration_id: UUID,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Pause an orchestration"""
    try:
        orchestration_service = OrchestrationService(db)
        
        # Verify ownership
        orchestration = await orchestration_service.get_orchestration(orchestration_id)
        if not orchestration:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Orchestration not found"
            )
        
        if orchestration.owner_id != current_user.id and not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        success = await orchestration_service.pause_orchestration(orchestration_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to pause orchestration"
            )
        
        return {"message": "Orchestration paused successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to pause orchestration via API", orchestration_id=str(orchestration_id), error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to pause orchestration"
        )


@router.post("/{orchestration_id}/resume")
async def resume_orchestration(
    orchestration_id: UUID,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Resume an orchestration"""
    try:
        orchestration_service = OrchestrationService(db)
        
        # Verify ownership
        orchestration = await orchestration_service.get_orchestration(orchestration_id)
        if not orchestration:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Orchestration not found"
            )
        
        if orchestration.owner_id != current_user.id and not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        success = await orchestration_service.resume_orchestration(orchestration_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to resume orchestration"
            )
        
        return {"message": "Orchestration resumed successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to resume orchestration via API", orchestration_id=str(orchestration_id), error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to resume orchestration"
        )


@router.get("/{orchestration_id}/progress", response_model=OrchestrationProgressResponse)
async def get_orchestration_progress(
    orchestration_id: UUID,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get orchestration progress"""
    try:
        orchestration_service = OrchestrationService(db)
        
        # Verify ownership
        orchestration = await orchestration_service.get_orchestration(orchestration_id)
        if not orchestration:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Orchestration not found"
            )
        
        if orchestration.owner_id != current_user.id and not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        progress = await orchestration_service.get_orchestration_progress(orchestration_id)
        
        if not progress:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Orchestration progress not available"
            )
        
        return OrchestrationProgressResponse(**progress)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get orchestration progress via API", orchestration_id=str(orchestration_id), error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get orchestration progress"
        )


@router.post("/{orchestration_id}/agents", status_code=status.HTTP_201_CREATED)
async def add_agent_to_orchestration(
    orchestration_id: UUID,
    agent_data: AddAgentRequest,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Add an agent to orchestration"""
    try:
        orchestration_service = OrchestrationService(db)
        
        # Verify ownership
        orchestration = await orchestration_service.get_orchestration(orchestration_id)
        if not orchestration:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Orchestration not found"
            )
        
        if orchestration.owner_id != current_user.id and not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        success = await orchestration_service.add_agent_to_orchestration(
            orchestration_id,
            agent_data.agent_id,
            agent_data.role
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to add agent to orchestration"
            )
        
        return {"message": "Agent added to orchestration successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to add agent to orchestration via API", orchestration_id=str(orchestration_id), error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to add agent to orchestration"
        )


@router.delete("/{orchestration_id}/agents/{agent_id}", status_code=status.HTTP_204_NO_CONTENT)
async def remove_agent_from_orchestration(
    orchestration_id: UUID,
    agent_id: UUID,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Remove an agent from orchestration"""
    try:
        orchestration_service = OrchestrationService(db)
        
        # Verify ownership
        orchestration = await orchestration_service.get_orchestration(orchestration_id)
        if not orchestration:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Orchestration not found"
            )
        
        if orchestration.owner_id != current_user.id and not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        success = await orchestration_service.remove_agent_from_orchestration(
            orchestration_id,
            agent_id
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent not found in orchestration"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to remove agent from orchestration via API", orchestration_id=str(orchestration_id), error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to remove agent from orchestration"
        )


@router.delete("/{orchestration_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_orchestration(
    orchestration_id: UUID,
    current_user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Delete an orchestration"""
    try:
        orchestration_service = OrchestrationService(db)
        
        # Verify ownership
        orchestration = await orchestration_service.get_orchestration(orchestration_id)
        if not orchestration:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Orchestration not found"
            )
        
        if orchestration.owner_id != current_user.id and not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        success = await orchestration_service.delete_orchestration(orchestration_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Orchestration not found"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to delete orchestration via API", orchestration_id=str(orchestration_id), error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete orchestration"
        )