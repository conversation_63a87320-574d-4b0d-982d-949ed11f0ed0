# Cloud Storage Module Outputs

output "bucket_name" {
  description = "Name of the Cloud Storage bucket"
  value       = google_storage_bucket.web_bucket.name
}

output "bucket_url" {
  description = "URL of the Cloud Storage bucket"
  value       = google_storage_bucket.web_bucket.url
}

output "bucket_self_link" {
  description = "Self link of the Cloud Storage bucket"
  value       = google_storage_bucket.web_bucket.self_link
}

output "bucket_location" {
  description = "Location of the Cloud Storage bucket"
  value       = google_storage_bucket.web_bucket.location
}

output "bucket_storage_class" {
  description = "Storage class of the bucket"
  value       = google_storage_bucket.web_bucket.storage_class
}

# Website hosting URLs
output "website_url" {
  description = "Website URL for the bucket"
  value       = "https://storage.googleapis.com/${google_storage_bucket.web_bucket.name}/index.html"
}

output "website_domain" {
  description = "Website domain for direct bucket access"
  value       = "${google_storage_bucket.web_bucket.name}.storage.googleapis.com"
}

# Access logs bucket (if created)
output "access_logs_bucket_name" {
  description = "Name of the access logs bucket (if created)"
  value       = var.enable_access_logs && var.access_log_bucket == "" ? google_storage_bucket.access_logs[0].name : var.access_log_bucket
}

# Deployment automation
output "deploy_trigger_name" {
  description = "Name of the Cloud Build trigger for deployment"
  value       = var.enable_automated_deployment ? google_cloudbuild_trigger.web_deploy[0].name : ""
}

output "deploy_trigger_id" {
  description = "ID of the Cloud Build trigger for deployment"
  value       = var.enable_automated_deployment ? google_cloudbuild_trigger.web_deploy[0].trigger_id : ""
}

# Upload commands
output "upload_commands" {
  description = "Commands to upload files to the bucket"
  value = {
    sync_directory = "gsutil -m rsync -r -d ./dist gs://${google_storage_bucket.web_bucket.name}"
    upload_file    = "gsutil cp ./file.html gs://${google_storage_bucket.web_bucket.name}/"
    upload_recursive = "gsutil -m cp -r ./build/* gs://${google_storage_bucket.web_bucket.name}/"
    set_cache_control = "gsutil -m setmeta -h 'Cache-Control:public, max-age=3600' gs://${google_storage_bucket.web_bucket.name}/**"
  }
}

# Management commands
output "management_commands" {
  description = "Useful commands for bucket management"
  value = {
    list_objects    = "gsutil ls gs://${google_storage_bucket.web_bucket.name}/"
    bucket_info     = "gsutil du -sh gs://${google_storage_bucket.web_bucket.name}"
    clear_bucket    = "gsutil -m rm gs://${google_storage_bucket.web_bucket.name}/**"
    enable_cors     = "gsutil cors set cors.json gs://${google_storage_bucket.web_bucket.name}"
    bucket_policy   = "gsutil iam get gs://${google_storage_bucket.web_bucket.name}"
  }
}

# CDN integration
output "cdn_backend_config" {
  description = "Configuration for CDN backend service integration"
  value = {
    bucket_name = google_storage_bucket.web_bucket.name
    bucket_url  = google_storage_bucket.web_bucket.url
    enable_cdn  = true
    cache_mode  = "CACHE_ALL_STATIC"
  }
}

# React app specific outputs
output "react_deployment_info" {
  description = "Information for React app deployment"
  value = {
    build_command     = "npm run build"
    build_directory   = "./dist"
    upload_command    = "gsutil -m rsync -r -d ./dist gs://${google_storage_bucket.web_bucket.name}"
    index_url         = "https://storage.googleapis.com/${google_storage_bucket.web_bucket.name}/index.html"
    spa_configuration = "Configure your load balancer to serve index.html for all routes"
  }
}

# Security and access information
output "security_info" {
  description = "Security and access configuration"
  value = {
    public_access_enabled = var.enable_public_access
    uniform_bucket_access = true
    cors_enabled         = true
    versioning_enabled   = var.enable_versioning
    lifecycle_rules      = length(var.lifecycle_rules) > 0
  }
}