import { FunctionComponent } from 'preact';
import { useState } from 'preact/hooks';
import { api } from '@/services/api';

interface ThresholdSettingsProps {
  customerId: string;
}

interface Thresholds {
  warningThreshold: number;
  criticalThreshold: number;
}

/**
 * Allows users to set thresholds for escalation.
 */
const ThresholdSettings: FunctionComponent<ThresholdSettingsProps> = ({ customerId }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [formData, setFormData] = useState<Thresholds>({
    warningThreshold: 0,
    criticalThreshold: 0,
  });

  const [errors, setErrors] = useState<Partial<Record<keyof Thresholds, string>>>({});

  const validateForm = (data: Thresholds): boolean => {
    const newErrors: Partial<Record<keyof Thresholds, string>> = {};
    
    if (data.warningThreshold < 0) {
      newErrors.warningThreshold = 'Must be non-negative';
    }
    if (data.criticalThreshold < 0) {
      newErrors.criticalThreshold = 'Must be non-negative';
    }
    if (data.criticalThreshold <= data.warningThreshold) {
      newErrors.criticalThreshold = 'Must be greater than warning threshold';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: Event) => {
    e.preventDefault();
    if (!validateForm(formData)) return;

    const data = formData;
    setIsLoading(true);
    setError(null);
    try {
      // Example API call (replace with actual logic)
      const response = await api.post(`/customers/${customerId}/thresholds`, data);
      console.log(response); // Handle success
    } catch (err) {
      setError('Failed to save thresholds.');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} class="p-4">
      <div class="mb-4">
        <label for="warningThreshold" class="block text-sm font-medium text-gray-700">Warning Threshold</label>
        <input 
          type="number" 
          id="warningThreshold" 
          value={formData.warningThreshold}
          onInput={(e) => setFormData(prev => ({ ...prev, warningThreshold: Number((e.target as HTMLInputElement).value) }))}
          class="mt-1 p-2 border rounded w-full" 
        />
        {errors.warningThreshold && <p class="text-red-500 text-xs mt-1">{errors.warningThreshold.message}</p>}
      </div>
      <div class="mb-4">
        <label for="criticalThreshold" class="block text-sm font-medium text-gray-700">Critical Threshold</label>
        <input 
          type="number" 
          id="criticalThreshold" 
          value={formData.criticalThreshold}
          onInput={(e) => setFormData(prev => ({ ...prev, criticalThreshold: Number((e.target as HTMLInputElement).value) }))}
          class="mt-1 p-2 border rounded w-full" 
        />
        {errors.criticalThreshold && <p class="text-red-500 text-xs mt-1">{errors.criticalThreshold.message}</p>}
      </div>
      <button type="submit" disabled={isLoading} class="bg-blue-500 text-white p-2 rounded disabled:opacity-50">
        {isLoading ? 'Saving...' : 'Save'}
      </button>
      {error && <p class="text-red-500 text-xs mt-2">{error}</p>}
    </form>
  );
};

export default ThresholdSettings;
