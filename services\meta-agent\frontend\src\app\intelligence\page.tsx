/**
 * AI Agent Platform - Intelligence Page
 * AI gateway and models management
 */

'use client';

import React, { useState } from 'react';
import { AppLayout } from '@/components/Layout/AppLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Zap,
  Brain,
  Settings,
  Activity,
  TrendingUp,
  Clock,
  CheckCircle2,
  AlertCircle,
  Eye,
  Edit,
  Trash2,
  Play,
  Pause
} from 'lucide-react';
import Link from 'next/link';

// Mock data for AI models and providers
const mockProviders = [
  {
    id: '1',
    name: 'OpenAI',
    status: 'active',
    models: ['gpt-4', 'gpt-3.5-turbo', 'text-embedding-ada-002'],
    apiKey: 'sk-...abc123',
    usage: { requests: 15420, tokens: 2340000 },
    cost: 234.56,
    latency: 1.2
  },
  {
    id: '2',
    name: 'Anthropic',
    status: 'active',
    models: ['claude-3-opus', 'claude-3-sonnet', 'claude-3-haiku'],
    apiKey: 'sk-...def456',
    usage: { requests: 8930, tokens: 1560000 },
    cost: 156.78,
    latency: 0.9
  },
  {
    id: '3',
    name: 'Google AI',
    status: 'inactive',
    models: ['gemini-pro', 'gemini-pro-vision'],
    apiKey: 'AIza...ghi789',
    usage: { requests: 0, tokens: 0 },
    cost: 0,
    latency: 0
  }
];

const mockModels = [
  {
    id: '1',
    name: 'GPT-4',
    provider: 'OpenAI',
    type: 'text-generation',
    status: 'active',
    usage: 12450,
    cost: 189.23,
    avgLatency: 1.2,
    successRate: 99.2
  },
  {
    id: '2',
    name: 'Claude-3-Opus',
    provider: 'Anthropic',
    type: 'text-generation',
    status: 'active',
    usage: 8930,
    cost: 156.78,
    avgLatency: 0.9,
    successRate: 99.8
  },
  {
    id: '3',
    name: 'Text-Embedding-Ada-002',
    provider: 'OpenAI',
    type: 'embedding',
    status: 'active',
    usage: 45600,
    cost: 12.34,
    avgLatency: 0.3,
    successRate: 99.9
  }
];

export default function IntelligencePage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [loading, setLoading] = useState(false);

  const filteredProviders = mockProviders.filter(provider => {
    const matchesSearch = provider.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || provider.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const filteredModels = mockModels.filter(model => {
    const matchesSearch = model.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         model.provider.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || model.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  return (
    <AppLayout>
      <div className="container mx-auto py-8 px-4 max-w-7xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-3">
              <Zap className="h-8 w-8 text-primary" />
              Intelligence
            </h1>
            <p className="text-muted-foreground mt-2">
              Manage AI models, providers, and gateway configuration
            </p>
          </div>
          <div className="flex gap-3">
            <Button variant="outline">
              <Settings className="h-4 w-4 mr-2" />
              Gateway Settings
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Provider
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Active Providers</p>
                  <p className="text-2xl font-bold">
                    {mockProviders.filter(p => p.status === 'active').length}
                  </p>
                </div>
                <Brain className="h-8 w-8 text-muted-foreground" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Requests</p>
                  <p className="text-2xl font-bold">
                    {mockProviders.reduce((sum, p) => sum + p.usage.requests, 0).toLocaleString()}
                  </p>
                </div>
                <Activity className="h-8 w-8 text-muted-foreground" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Monthly Cost</p>
                  <p className="text-2xl font-bold">
                    ${mockProviders.reduce((sum, p) => sum + p.cost, 0).toFixed(2)}
                  </p>
                </div>
                <TrendingUp className="h-8 w-8 text-muted-foreground" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Avg Latency</p>
                  <p className="text-2xl font-bold">
                    {(mockProviders.reduce((sum, p) => sum + p.latency, 0) / mockProviders.length).toFixed(1)}s
                  </p>
                </div>
                <Clock className="h-8 w-8 text-muted-foreground" />
              </div>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="providers" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="providers">Providers</TabsTrigger>
            <TabsTrigger value="models">Models</TabsTrigger>
            <TabsTrigger value="gateway">Gateway</TabsTrigger>
          </TabsList>

          {/* Providers Tab */}
          <TabsContent value="providers" className="space-y-6">
            {/* Filters */}
            <Card>
              <CardContent className="pt-6">
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Search providers..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                  >
                    <option value="all">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                  </select>
                </div>
              </CardContent>
            </Card>

            {/* Providers List */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {filteredProviders.map((provider) => (
                <Card key={provider.id} className="hover:shadow-md transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="flex items-center gap-2">
                          {provider.name}
                          <Badge className={provider.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                            {provider.status === 'active' ? (
                              <CheckCircle2 className="h-3 w-3 mr-1" />
                            ) : (
                              <AlertCircle className="h-3 w-3 mr-1" />
                            )}
                            {provider.status}
                          </Badge>
                        </CardTitle>
                        <CardDescription>
                          {provider.models.length} models available
                        </CardDescription>
                      </div>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {/* Usage Stats */}
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <p className="text-muted-foreground">Requests</p>
                          <p className="font-medium">{provider.usage.requests.toLocaleString()}</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Tokens</p>
                          <p className="font-medium">{provider.usage.tokens.toLocaleString()}</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Cost</p>
                          <p className="font-medium">${provider.cost.toFixed(2)}</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Latency</p>
                          <p className="font-medium">{provider.latency}s</p>
                        </div>
                      </div>

                      {/* Models */}
                      <div>
                        <p className="text-sm font-medium mb-2">Available Models:</p>
                        <div className="flex flex-wrap gap-1">
                          {provider.models.map((model, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {model}
                            </Badge>
                          ))}
                        </div>
                      </div>

                      {/* API Key */}
                      <div>
                        <p className="text-sm font-medium mb-1">API Key:</p>
                        <code className="text-xs bg-gray-100 px-2 py-1 rounded">
                          {provider.apiKey}
                        </code>
                      </div>

                      {/* Actions */}
                      <div className="flex gap-2 pt-2 border-t">
                        {provider.status === 'active' ? (
                          <Button size="sm" variant="outline">
                            <Pause className="h-4 w-4 mr-1" />
                            Disable
                          </Button>
                        ) : (
                          <Button size="sm">
                            <Play className="h-4 w-4 mr-1" />
                            Enable
                          </Button>
                        )}
                        <Button size="sm" variant="outline">
                          <Edit className="h-4 w-4 mr-1" />
                          Configure
                        </Button>
                        <Button size="sm" variant="outline">
                          <Eye className="h-4 w-4 mr-1" />
                          View Usage
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Models Tab */}
          <TabsContent value="models" className="space-y-6">
            <div className="space-y-4">
              {filteredModels.map((model) => (
                <Card key={model.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="pt-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="font-semibold text-lg">{model.name}</h3>
                          <Badge className={model.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                            {model.status}
                          </Badge>
                          <Badge variant="outline">
                            {model.type}
                          </Badge>
                        </div>
                        <p className="text-muted-foreground">
                          Provider: {model.provider}
                        </p>
                      </div>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <p className="text-muted-foreground">Usage</p>
                        <p className="font-medium">{model.usage.toLocaleString()}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Cost</p>
                        <p className="font-medium">${model.cost.toFixed(2)}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Avg Latency</p>
                        <p className="font-medium">{model.avgLatency}s</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Success Rate</p>
                        <p className="font-medium text-green-600">{model.successRate}%</p>
                      </div>
                    </div>

                    <div className="flex gap-2 pt-4 border-t mt-4">
                      <Button size="sm" variant="outline">
                        <Eye className="h-4 w-4 mr-1" />
                        View Details
                      </Button>
                      <Button size="sm" variant="outline">
                        <Settings className="h-4 w-4 mr-1" />
                        Configure
                      </Button>
                      <Button size="sm" variant="outline">
                        <Activity className="h-4 w-4 mr-1" />
                        Usage Analytics
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Gateway Tab */}
          <TabsContent value="gateway" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>AI Gateway Configuration</CardTitle>
                <CardDescription>
                  Configure routing, rate limiting, and caching for AI requests
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <Settings className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">Gateway Settings</h3>
                  <p className="text-muted-foreground mb-4">
                    Advanced gateway configuration options will be available here
                  </p>
                  <Button>
                    <Settings className="h-4 w-4 mr-2" />
                    Configure Gateway
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AppLayout>
  );
}
