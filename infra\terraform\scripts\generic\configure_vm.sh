#!/bin/bash
# Generic VM configuration script
# Sets up docker-compose and services for any environment

# Source common utilities
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/../common/utils.sh"
source "$SCRIPT_DIR/../common/environment.sh"

# Script configuration
SCRIPT_NAME="configure_vm.sh"
SCRIPT_DESCRIPTION="Configure VM with docker-compose for specified environment"

# Required tools
REQUIRED_TOOLS=("terraform" "gcloud" "gsutil")

# VM configuration function
configure_vm() {
    local env="$1"
    
    print_step "Step 1: Environment setup and validation"
    if ! setup_environment "$env"; then
        return 1
    fi
    
    print_environment_summary "$env"
    production_safety_check
    
    print_step "Step 2: Get VM configuration"
    cd "$TERRAFORM_DIR"
    
    # Get VM details from Terraform outputs
    local instance_name
    local database_ip
    local database_name
    local database_user
    
    if ! instance_name=$(terraform output -raw instance_name 2>/dev/null); then
        print_error "Could not get instance name from Terraform outputs"
        return 1
    fi
    
    if ! database_ip=$(terraform output -raw database_private_ip 2>/dev/null); then
        print_error "Could not get database IP from Terraform outputs"
        return 1
    fi
    
    if ! database_name=$(terraform output -raw database_name 2>/dev/null); then
        print_error "Could not get database name from Terraform outputs"
        return 1
    fi
    
    if ! database_user=$(terraform output -raw database_user 2>/dev/null); then
        print_error "Could not get database user from Terraform outputs"
        return 1
    fi
    
    print_info "VM configuration details:"
    echo "  🖥️  Instance: $instance_name"
    echo "  🏠 Database IP: $database_ip"
    echo "  🗄️  Database Name: $database_name"
    echo "  👤 Database User: $database_user"
    
    # Go back to workspace root for file operations
    cd - > /dev/null
    
    print_step "Step 3: Prepare configuration files"
    
    # Get database password from Secret Manager
    print_info "Retrieving database password from Secret Manager..."
    local database_password
    if ! database_password=$(gcloud secrets versions access latest --secret="$DB_PASSWORD_SECRET" --project="$PROJECT_ID" 2>/dev/null); then
        print_error "Could not retrieve database password from Secret Manager"
        return 1
    fi
    
    # URL encode the password for connection string
    local encoded_password
    encoded_password=$(url_encode "$database_password")
    
    # Create environment file
    local env_file_content=$(cat <<EOF
# Registry Configuration
REGISTRY_URL=$REGISTRY_URL

# Database Configuration
DATABASE_URL=***************************************************************/$database_name

# Service Configuration
JWT_SECRET=$env-jwt-secret-change-in-production
GIN_MODE=release
EOF
)
    
    # Create temporary files for upload
    local temp_env_file
    temp_env_file=$(create_temp_file "env")
    echo "$env_file_content" > "$temp_env_file"
    
    print_success "Environment file created"
    
    print_step "Step 4: Clean and prepare VM"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_info "[DRY RUN] Would clean and configure VM: $instance_name"
        return 0
    fi
    
    # Create cleanup script
    local cleanup_script=$(cat <<'EOF'
#!/bin/bash
set -e

echo "🧹 Cleaning up existing containers and images..."

# Stop and remove all platform containers
echo "🛑 Stopping all platform containers..."
sudo docker stop $(sudo docker ps -aq --filter="name=platform-") 2>/dev/null || true
sudo docker rm $(sudo docker ps -aq --filter="name=platform-") 2>/dev/null || true

# Stop and remove docker-compose services if they exist
echo "🛑 Stopping docker-compose services..."
cd ~/platform 2>/dev/null || true
sudo docker compose down --remove-orphans 2>/dev/null || true
cd ~

# Remove platform network if it exists
echo "🌐 Removing platform network..."
sudo docker network rm platform-network 2>/dev/null || true

# Clean up unused images, containers, and volumes
echo "🗑️  Cleaning up unused Docker resources..."
sudo docker system prune -af --volumes

# Remove platform directory to start fresh
echo "📂 Removing platform directory..."
rm -rf ~/platform

# Create fresh platform directory
echo "📁 Creating fresh platform directory..."
mkdir -p ~/platform

echo "✅ VM cleanup completed!"
EOF
)
    
    print_info "Running cleanup script on VM..."
    echo "$cleanup_script" | gcloud compute ssh "$instance_name" --zone="$ZONE" --command="bash"
    
    print_success "VM cleanup completed"
    
    print_step "Step 5: Upload configuration files"
    
    # Upload environment file
    print_info "Uploading environment file..."
    gcloud compute scp "$temp_env_file" "$instance_name:~/platform/.env" --zone="$ZONE"
    
    # Upload docker-compose.yml
    print_info "Uploading docker-compose.yml..."
    gcloud compute scp terraform/docker-compose.yml "$instance_name:~/platform/" --zone="$ZONE"
    
    # Upload nginx.conf
    print_info "Uploading nginx.conf..."
    gcloud compute scp terraform/nginx.conf "$instance_name:~/platform/" --zone="$ZONE"
    
    print_success "Configuration files uploaded"
    
    print_step "Step 6: Create management scripts on VM"
    
    # Create Docker authentication script
    local docker_auth_script=$(cat <<EOF
#!/bin/bash
# Get an access token from the metadata server
TOKEN=\$(curl -s -H "Metadata-Flavor: Google" "http://metadata.google.internal/computeMetadata/v1/instance/service-accounts/default/token" | cut -d'"' -f4)

# Use the token to authenticate with Docker using writable config directory
echo \$TOKEN | sudo DOCKER_CONFIG=/tmp/docker docker login -u oauth2accesstoken --password-stdin $REGISTRY_URL

echo "✅ Docker authenticated with registry"
EOF
)
    
    # Create update images script
    local update_images_script=$(cat <<EOF
#!/bin/bash
set -euo pipefail

echo "🔄 Updating Docker images..."

cd ~/platform

# Setup Docker authentication using writable config directory
echo "🔑 Setting up Docker authentication..."
sudo mkdir -p /tmp/docker
sudo chmod 777 /tmp/docker

# Use the authentication script we created
if [ -f "~/docker-auth.sh" ]; then
    bash ~/docker-auth.sh
else
    echo "⚠️  Docker authentication script not found, creating one..."
    cat << 'INNER_AUTH_SCRIPT' > ~/docker-auth.sh
#!/bin/bash
# Get an access token from the metadata server
TOKEN=\\\$(curl -s -H "Metadata-Flavor: Google" "http://metadata.google.internal/computeMetadata/v1/instance/service-accounts/default/token" | cut -d'"' -f4)

# Use the token to authenticate with Docker using writable config directory
echo \\\$TOKEN | sudo DOCKER_CONFIG=/tmp/docker docker login -u oauth2accesstoken --password-stdin $REGISTRY_URL

echo "✅ Docker authenticated with registry"
INNER_AUTH_SCRIPT
    chmod +x ~/docker-auth.sh
    bash ~/docker-auth.sh
fi

# Source environment variables
source .env

# Stop and remove existing containers
echo "🛑 Stopping existing containers..."
sudo docker stop platform-crm-backend platform-auth platform-gateway platform-nginx 2>/dev/null || true
sudo docker container remove platform-crm-backend platform-auth platform-gateway platform-nginx 2>/dev/null || true

# Create network
sudo docker network create platform-network 2>/dev/null || true

# Pull and start services with proper authentication
echo "📦 Pulling and starting services..."
sudo DOCKER_CONFIG=/tmp/docker docker run -d --name platform-crm-backend --network platform-network --restart unless-stopped -p 8003:8003 -e PORT=8003 -e GIN_MODE=release -e DATABASE_URL="\$DATABASE_URL" -e AUTH_SERVICE_URL=http://platform-auth:8004 \$REGISTRY_URL/crm-backend:latest
sudo DOCKER_CONFIG=/tmp/docker docker run -d --name platform-auth --network platform-network --restart unless-stopped -p 8004:8004 -e PORT=8004 -e GIN_MODE=release -e DATABASE_URL="\$DATABASE_URL" -e JWT_SECRET="\$JWT_SECRET" \$REGISTRY_URL/auth:latest
sudo DOCKER_CONFIG=/tmp/docker docker run -d --name platform-gateway --network platform-network --restart unless-stopped -p 8085:8085 -e PORT=8085 -e GIN_MODE=release -e AUTH_SERVICE_URL=http://platform-auth:8004 -e CRM_BACKEND_URL=http://platform-crm-backend:8003 \$REGISTRY_URL/gateway:latest
sudo docker run -d --name platform-nginx --network platform-network --restart unless-stopped -p 80:80 -v \$PWD/nginx.conf:/etc/nginx/conf.d/default.conf:ro nginx:alpine

# Clean up old images
echo "🗑️  Cleaning up old images..."
sudo docker image prune -f

echo "✅ Docker images updated successfully!"
echo ""
echo "📊 Current service status:"
sudo docker ps --filter name=platform-
EOF
)
    
    # Create update config script
    local update_config_script=$(cat <<'EOF'
#!/bin/bash
set -euo pipefail

echo "🔧 Updating docker-compose configuration..."

cd ~/platform

# Stop services
echo "🛑 Stopping services..."
docker compose down

# The new configuration files should be uploaded before running this script
# This script just restarts services with the new configuration

echo "🚀 Starting services with new configuration..."
docker compose up -d

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 10

echo "✅ Configuration updated successfully!"
echo ""
echo "📊 Current service status:"
docker compose ps
EOF
)
    
    # Create logs script
    local logs_script=$(cat <<'EOF'
#!/bin/bash
set -euo pipefail

cd ~/platform

if [ $# -eq 0 ]; then
    echo "📋 Showing logs for all services..."
    docker compose logs -f
else
    echo "📋 Showing logs for service: $1"
    docker compose logs -f "$1"
fi
EOF
)
    
    # Upload management scripts
    print_info "Creating management scripts on VM..."
    echo "$docker_auth_script" | gcloud compute ssh "$instance_name" --zone="$ZONE" --command="tee ~/docker-auth.sh > /dev/null && chmod +x ~/docker-auth.sh"
    
    echo "$update_images_script" | gcloud compute ssh "$instance_name" --zone="$ZONE" --command="tee ~/platform/update-images.sh > /dev/null && chmod +x ~/platform/update-images.sh"
    
    echo "$update_config_script" | gcloud compute ssh "$instance_name" --zone="$ZONE" --command="tee ~/platform/update-config.sh > /dev/null && chmod +x ~/platform/update-config.sh"
    
    echo "$logs_script" | gcloud compute ssh "$instance_name" --zone="$ZONE" --command="tee ~/platform/logs.sh > /dev/null && chmod +x ~/platform/logs.sh"
    
    print_success "Management scripts created"
    
    print_step "Step 7: Setup Docker authentication"
    
    # Setup Docker authentication on VM
    local setup_auth_script=$(cat <<EOF
#!/bin/bash
set -e

echo "🔑 Setting up Docker authentication..."

# For Container-Optimized OS, we'll use a token-based approach with writable docker config
echo "📝 Setting up Docker authentication for COS..."

# Create writable docker config directory in /tmp
sudo mkdir -p /tmp/docker
sudo chmod 777 /tmp/docker

# Run the authentication script
echo "🔑 Authenticating with Docker registry..."
bash ~/docker-auth.sh

echo "✅ Docker authentication setup completed!"
EOF
)
    
    print_info "Setting up Docker authentication on VM..."
    echo "$setup_auth_script" | gcloud compute ssh "$instance_name" --zone="$ZONE" --command="bash"
    
    print_success "Docker authentication setup completed"
    
    print_step "Step 8: Verification"
    
    # Verify platform directory structure
    print_info "Verifying platform directory structure..."
    gcloud compute ssh "$instance_name" --zone="$ZONE" --command="ls -la ~/platform/"
    
    print_success "VM configuration completed successfully!"
    
    echo ""
    print_info "🚀 Next steps:"
    echo "  1. Build and push Docker images: bazel run //terraform:build_and_push_backend -- $env"
    echo "  2. Start services: bash ~/platform/update-images.sh"
    echo "  3. Check status: docker compose ps"
    echo ""
    print_info "🛠️  VM Management commands:"
    echo "  • SSH to VM: gcloud compute ssh $instance_name --zone=$ZONE"
    echo "  • View logs: ~/platform/logs.sh [service-name]"
    echo "  • Update images: ~/platform/update-images.sh"
    echo "  • Update config: ~/platform/update-config.sh"
    echo "  • Check status: cd ~/platform && docker compose ps"
    echo ""
    
    return 0
}

# Update configuration only (no cleanup)
update_config() {
    local env="$1"
    
    print_step "Updating VM configuration for: $env"
    
    if ! setup_environment "$env"; then
        return 1
    fi
    
    cd "$TERRAFORM_DIR"
    
    # Get VM details
    local instance_name
    if ! instance_name=$(terraform output -raw instance_name 2>/dev/null); then
        print_error "Could not get instance name from Terraform outputs"
        return 1
    fi
    
    cd - > /dev/null
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_info "[DRY RUN] Would update configuration on VM: $instance_name"
        return 0
    fi
    
    # Upload updated configuration files
    print_info "Uploading updated configuration files..."
    gcloud compute scp terraform/docker-compose.yml "$instance_name:~/platform/" --zone="$ZONE"
    gcloud compute scp terraform/nginx.conf "$instance_name:~/platform/" --zone="$ZONE"
    
    # Run update config script
    print_info "Running configuration update script on VM..."
    gcloud compute ssh "$instance_name" --zone="$ZONE" --command="cd ~/platform && bash update-config.sh"
    
    print_success "Configuration updated successfully"
    return 0
}

# Main script execution
main() {
    setup_error_handling
    
    # Parse command line arguments
    parse_args "$SCRIPT_NAME" "$@"
    
    # Print banner
    print_banner "$SCRIPT_NAME" "$SCRIPT_DESCRIPTION"
    
    # Check required tools
    if ! check_required_tools "${REQUIRED_TOOLS[@]}"; then
        exit 1
    fi
    
    # Change to workspace directory
    if ! change_to_workspace; then
        exit 1
    fi
    
    # Validate environment
    if ! validate_environment "$ENVIRONMENT"; then
        exit 1
    fi
    
    # Handle special commands
    case "$1" in
        "update-config")
            if update_config "$ENVIRONMENT"; then
                print_success "VM configuration update completed! 🎉"
                exit 0
            else
                print_error "VM configuration update failed!"
                exit 1
            fi
            ;;
        *)
            # Full VM configuration
            if configure_vm "$ENVIRONMENT"; then
                print_success "VM configuration completed! 🎉"
                exit 0
            else
                print_error "VM configuration failed!"
                exit 1
            fi
            ;;
    esac
}

# Show usage if no arguments provided
if [[ $# -eq 0 ]]; then
    print_usage "$SCRIPT_NAME"
    echo ""
    echo "Additional commands:"
    echo "  update-config ENVIRONMENT  Update configuration files only"
    exit 1
fi

# Execute main function with all arguments
main "$@"