openapi: 3.0.3
info:
  title: CRM Arli API
  version: 1.0.0
  description: AI-powered document processing endpoints for the CRM system

paths:
  /arli/documents:
    get:
      summary: List documents
      description: Retrieve a list of processed documents
      parameters:
        - name: filter_tags
          in: query
          schema:
            type: array
            items:
              type: string
          description: Filter by tags
        - name: metadata_filter
          in: query
          schema:
            type: object
          description: Filter by metadata properties
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: offset
          in: query
          schema:
            type: integer
            minimum: 0
            default: 0
      responses:
        '200':
          description: List of documents
          content:
            application/json:
              schema:
                type: object
                properties:
                  documents:
                    type: array
                    items:
                      $ref: '#/components/schemas/Document'
                  total_count:
                    type: integer
                  has_more:
                    type: boolean

    post:
      summary: Create document
      description: Create a new document for processing
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DocumentCreate'
      responses:
        '201':
          description: Document created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Document'
        '400':
          description: Invalid input

  /arli/documents/{id}:
    get:
      summary: Get document
      description: Retrieve a specific document by ID
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Document details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentDetails'
        '404':
          description: Document not found

    put:
      summary: Update document
      description: Update an existing document
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DocumentUpdate'
      responses:
        '200':
          description: Document updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Document'
        '404':
          description: Document not found

    delete:
      summary: Delete document
      description: Delete a document
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '204':
          description: Document deleted successfully
        '404':
          description: Document not found

  /arli/documents/vectorize:
    post:
      summary: Vectorize document
      description: Process a document through the AI vectorization pipeline
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                document_id:
                  type: string
                  format: uuid
                content:
                  type: string
                metadata:
                  type: object
              required:
                - document_id
                - content
      responses:
        '200':
          description: Document vectorized successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  vector_id:
                    type: string
        '400':
          description: Invalid input
        '500':
          description: Vectorization failed

  /arli/documents/import:
    post:
      summary: Import documents
      description: Import multiple documents from various sources
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
                metadata:
                  type: object
                filter_tags:
                  type: array
                  items:
                    type: string
                source_id:
                  type: string
      responses:
        '200':
          description: Documents imported successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  imported_count:
                    type: integer
                  failed_count:
                    type: integer
                  duplicates_count:
                    type: integer
                  errors:
                    type: array
                    items:
                      type: string

  /arli/documents/import/review:
    post:
      summary: Review import
      description: Review and approve imported documents
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                documents:
                  type: array
                  items:
                    $ref: '#/components/schemas/ImportReviewDocument'
                metadata_mapping:
                  type: object
              required:
                - documents
      responses:
        '200':
          description: Import review completed
          content:
            application/json:
              schema:
                type: object
                properties:
                  processed_count:
                    type: integer
                  success_count:
                    type: integer
                  error_count:
                    type: integer

  /arli/prompts:
    get:
      summary: Get current prompt
      description: Retrieve the current AI prompt configuration
      responses:
        '200':
          description: Current prompt configuration
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ArliPrompt'
        '404':
          description: No prompt configured

    post:
      summary: Create prompt
      description: Create a new AI prompt configuration
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ArliPromptCreate'
      responses:
        '201':
          description: Prompt created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ArliPrompt'

    put:
      summary: Update prompt
      description: Update the current AI prompt configuration
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ArliPromptUpdate'
      responses:
        '200':
          description: Prompt updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ArliPrompt'

  /arli/metadata-options:
    get:
      summary: List metadata options
      description: Retrieve all available metadata options
      responses:
        '200':
          description: List of metadata options
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/MetadataOption'

    post:
      summary: Create metadata options
      description: Create new metadata options
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/MetadataOptionCreate'
      responses:
        '201':
          description: Metadata options created successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/MetadataOption'

  /arli/filter-tags:
    get:
      summary: List filter tags
      description: Retrieve all available filter tags
      responses:
        '200':
          description: List of filter tags
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/FilterTag'

    post:
      summary: Create filter tag
      description: Create a new filter tag
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FilterTagCreate'
      responses:
        '201':
          description: Filter tag created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FilterTag'

  /arli/filter-tags/{id}:
    delete:
      summary: Delete filter tag
      description: Delete a filter tag
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '204':
          description: Filter tag deleted successfully
        '404':
          description: Filter tag not found

components:
  schemas:
    Document:
      type: object
      properties:
        id:
          type: string
          format: uuid
        content:
          type: string
        metadata:
          type: object
        filter_tags:
          type: array
          items:
            type: string
        source_id:
          type: string
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    DocumentDetails:
      allOf:
        - $ref: '#/components/schemas/Document'
        - type: object
          properties:
            vector_embeddings:
              type: array
              items:
                type: number
            processing_status:
              type: string
              enum: [pending, processing, completed, failed]

    DocumentCreate:
      type: object
      properties:
        content:
          type: string
          minLength: 1
        metadata:
          type: object
        filter_tags:
          type: array
          items:
            type: string
        source_id:
          type: string
      required:
        - content

    DocumentUpdate:
      type: object
      properties:
        content:
          type: string
          minLength: 1
        metadata:
          type: object
        filter_tags:
          type: array
          items:
            type: string
        source_id:
          type: string

    ImportReviewDocument:
      type: object
      properties:
        id:
          type: string
          format: uuid
        content:
          type: string
        metadata:
          type: object
        filter_tags:
          type: array
          items:
            type: string
        approved:
          type: boolean
        action:
          type: string
          enum: [import, skip, merge]

    ArliPrompt:
      type: object
      properties:
        id:
          type: string
          format: uuid
        prompt_text:
          type: string
        model_name:
          type: string
        temperature:
          type: number
          minimum: 0
          maximum: 2
        max_tokens:
          type: integer
          minimum: 1
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    ArliPromptCreate:
      type: object
      properties:
        prompt_text:
          type: string
          minLength: 1
        model_name:
          type: string
          default: "gpt-4"
        temperature:
          type: number
          minimum: 0
          maximum: 2
          default: 0.7
        max_tokens:
          type: integer
          minimum: 1
          default: 1000
      required:
        - prompt_text

    ArliPromptUpdate:
      type: object
      properties:
        prompt_text:
          type: string
          minLength: 1
        model_name:
          type: string
        temperature:
          type: number
          minimum: 0
          maximum: 2
        max_tokens:
          type: integer
          minimum: 1

    MetadataOption:
      type: object
      properties:
        id:
          type: string
          format: uuid
        option_name:
          type: string
        option_type:
          type: string
          enum: [text, number, boolean, date, select]
        option_values:
          type: array
          items:
            type: string
        created_at:
          type: string
          format: date-time

    MetadataOptionCreate:
      type: object
      properties:
        option_name:
          type: string
          minLength: 1
        option_type:
          type: string
          enum: [text, number, boolean, date, select]
        option_values:
          type: array
          items:
            type: string
      required:
        - option_name
        - option_type

    FilterTag:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        color:
          type: string
          pattern: "^#[0-9a-fA-F]{6}$"
        created_at:
          type: string
          format: date-time

    FilterTagCreate:
      type: object
      properties:
        name:
          type: string
          minLength: 1
        color:
          type: string
          pattern: "^#[0-9a-fA-F]{6}$"
          default: "#3b82f6"
      required:
        - name