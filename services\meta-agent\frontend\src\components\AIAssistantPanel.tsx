/**
 * AI Assistant Panel Component
 * Interactive AI assistant for code analysis, documentation, and content generation
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Brain,
  MessageSquare,
  Code,
  FileText,
  Zap,
  Send,
  Loader2,
  Copy,
  CheckCircle2,
  AlertCircle,
  Sparkles,
  Settings,
  RefreshCw
} from 'lucide-react';
import { ApiClient } from '@/lib/api/client';

const apiClient = new ApiClient();

interface AICapability {
  name: string;
  description: string;
}

interface AIProvider {
  name: string;
  status: 'healthy' | 'error';
}

export function AIAssistantPanel() {
  const [capabilities, setCapabilities] = useState<AICapability[]>([]);
  const [providers, setProviders] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('chat');
  
  // Chat state
  const [chatPrompt, setChatPrompt] = useState('');
  const [chatResponse, setChatResponse] = useState('');
  const [chatLoading, setChatLoading] = useState(false);
  
  // Code analysis state
  const [codeInput, setCodeInput] = useState('');
  const [codeLanguage, setCodeLanguage] = useState('javascript');
  const [codeAnalysis, setCodeAnalysis] = useState<any>(null);
  const [codeLoading, setCodeLoading] = useState(false);
  
  // Requirements parsing state
  const [requirements, setRequirements] = useState('');
  const [parsedRequirements, setParsedRequirements] = useState<any>(null);
  const [requirementsLoading, setRequirementsLoading] = useState(false);
  
  // Documentation generation state
  const [docCode, setDocCode] = useState('');
  const [docLanguage, setDocLanguage] = useState('javascript');
  const [generatedDoc, setGeneratedDoc] = useState('');
  const [docLoading, setDocLoading] = useState(false);

  useEffect(() => {
    fetchAIData();
  }, []);

  const fetchAIData = async () => {
    try {
      setLoading(true);
      const [capabilitiesRes, providersRes] = await Promise.all([
        apiClient.getAICapabilities().catch(() => ({ capabilities: [] })),
        apiClient.getAIProviders().catch(() => ({ providers: [] }))
      ]);
      
      setCapabilities(capabilitiesRes.capabilities || []);
      setProviders(providersRes.providers || []);
    } catch (error) {
      console.error('Failed to fetch AI data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleChatSubmit = async () => {
    if (!chatPrompt.trim()) return;
    
    setChatLoading(true);
    try {
      const response = await apiClient.generateAIContent({
        prompt: chatPrompt,
        capability: 'chat',
        max_tokens: 500,
        temperature: 0.7
      });
      
      setChatResponse(response.content);
    } catch (error) {
      console.error('Chat failed:', error);
      setChatResponse('Sorry, I encountered an error. Please try again.');
    } finally {
      setChatLoading(false);
    }
  };

  const handleCodeAnalysis = async () => {
    if (!codeInput.trim()) return;
    
    setCodeLoading(true);
    try {
      const analysis = await apiClient.analyzeCode({
        code: codeInput,
        language: codeLanguage,
        analysis_type: 'comprehensive'
      });
      
      setCodeAnalysis(analysis);
    } catch (error) {
      console.error('Code analysis failed:', error);
      setCodeAnalysis({ error: 'Analysis failed. Please try again.' });
    } finally {
      setCodeLoading(false);
    }
  };

  const handleRequirementsParsing = async () => {
    if (!requirements.trim()) return;
    
    setRequirementsLoading(true);
    try {
      const parsed = await apiClient.parseRequirements({
        requirements: requirements,
        context: {}
      });
      
      setParsedRequirements(parsed);
    } catch (error) {
      console.error('Requirements parsing failed:', error);
      setParsedRequirements({ error: 'Parsing failed. Please try again.' });
    } finally {
      setRequirementsLoading(false);
    }
  };

  const handleDocGeneration = async () => {
    if (!docCode.trim()) return;
    
    setDocLoading(true);
    try {
      const doc = await apiClient.generateDocumentation({
        code: docCode,
        language: docLanguage,
        doc_type: 'comprehensive'
      });
      
      setGeneratedDoc(doc.documentation);
    } catch (error) {
      console.error('Documentation generation failed:', error);
      setGeneratedDoc('Documentation generation failed. Please try again.');
    } finally {
      setDocLoading(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Brain className="h-5 w-5" />
              AI Assistant
              <Badge variant="secondary" className="ml-2">
                <Sparkles className="h-3 w-3 mr-1" />
                {providers.length} providers
              </Badge>
            </CardTitle>
            <CardDescription>
              AI-powered code analysis, documentation, and content generation
            </CardDescription>
          </div>
          <Button variant="outline" size="sm" onClick={fetchAIData} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="chat">Chat</TabsTrigger>
            <TabsTrigger value="code">Code Analysis</TabsTrigger>
            <TabsTrigger value="requirements">Requirements</TabsTrigger>
            <TabsTrigger value="docs">Documentation</TabsTrigger>
          </TabsList>

          {/* Chat Tab */}
          <TabsContent value="chat" className="space-y-4">
            <div className="space-y-3">
              <Textarea
                placeholder="Ask me anything about your agents, code, or platform..."
                value={chatPrompt}
                onChange={(e) => setChatPrompt(e.target.value)}
                rows={3}
              />
              <Button onClick={handleChatSubmit} disabled={chatLoading || !chatPrompt.trim()}>
                {chatLoading ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Send className="h-4 w-4 mr-2" />
                )}
                Send
              </Button>
            </div>
            
            {chatResponse && (
              <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium flex items-center gap-2">
                    <Brain className="h-4 w-4" />
                    AI Response
                  </h4>
                  <Button variant="ghost" size="sm" onClick={() => copyToClipboard(chatResponse)}>
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
                <p className="text-sm whitespace-pre-wrap">{chatResponse}</p>
              </div>
            )}
          </TabsContent>

          {/* Code Analysis Tab */}
          <TabsContent value="code" className="space-y-4">
            <div className="space-y-3">
              <div className="flex gap-2">
                <select
                  value={codeLanguage}
                  onChange={(e) => setCodeLanguage(e.target.value)}
                  className="flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm"
                >
                  <option value="javascript">JavaScript</option>
                  <option value="python">Python</option>
                  <option value="typescript">TypeScript</option>
                  <option value="java">Java</option>
                  <option value="go">Go</option>
                  <option value="rust">Rust</option>
                </select>
                <Button onClick={handleCodeAnalysis} disabled={codeLoading || !codeInput.trim()}>
                  {codeLoading ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Code className="h-4 w-4 mr-2" />
                  )}
                  Analyze
                </Button>
              </div>
              <Textarea
                placeholder="Paste your code here for analysis..."
                value={codeInput}
                onChange={(e) => setCodeInput(e.target.value)}
                rows={6}
                className="font-mono text-sm"
              />
            </div>
            
            {codeAnalysis && (
              <div className="mt-4 space-y-3">
                {codeAnalysis.error ? (
                  <Alert className="border-red-200 bg-red-50">
                    <AlertCircle className="h-4 w-4 text-red-600" />
                    <AlertDescription className="text-red-800">
                      {codeAnalysis.error}
                    </AlertDescription>
                  </Alert>
                ) : (
                  <div className="space-y-3">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="p-3 bg-blue-50 rounded-lg">
                        <p className="text-sm text-blue-800">Quality Score</p>
                        <p className="text-2xl font-bold text-blue-600">{codeAnalysis.quality_score}/100</p>
                      </div>
                      <div className="p-3 bg-green-50 rounded-lg">
                        <p className="text-sm text-green-800">Complexity</p>
                        <p className="text-lg font-semibold text-green-600">{codeAnalysis.complexity}</p>
                      </div>
                    </div>
                    
                    {codeAnalysis.issues?.length > 0 && (
                      <div className="p-3 bg-red-50 rounded-lg">
                        <h4 className="font-medium text-red-800 mb-2">Issues Found</h4>
                        <ul className="text-sm text-red-700 space-y-1">
                          {codeAnalysis.issues.map((issue: string, index: number) => (
                            <li key={index}>• {issue}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                    
                    {codeAnalysis.suggestions?.length > 0 && (
                      <div className="p-3 bg-yellow-50 rounded-lg">
                        <h4 className="font-medium text-yellow-800 mb-2">Suggestions</h4>
                        <ul className="text-sm text-yellow-700 space-y-1">
                          {codeAnalysis.suggestions.map((suggestion: string, index: number) => (
                            <li key={index}>• {suggestion}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}
          </TabsContent>

          {/* Requirements Tab */}
          <TabsContent value="requirements" className="space-y-4">
            <div className="space-y-3">
              <Textarea
                placeholder="Describe what kind of agent you want to create..."
                value={requirements}
                onChange={(e) => setRequirements(e.target.value)}
                rows={4}
              />
              <Button onClick={handleRequirementsParsing} disabled={requirementsLoading || !requirements.trim()}>
                {requirementsLoading ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Zap className="h-4 w-4 mr-2" />
                )}
                Parse Requirements
              </Button>
            </div>
            
            {parsedRequirements && (
              <div className="mt-4">
                {parsedRequirements.error ? (
                  <Alert className="border-red-200 bg-red-50">
                    <AlertCircle className="h-4 w-4 text-red-600" />
                    <AlertDescription className="text-red-800">
                      {parsedRequirements.error}
                    </AlertDescription>
                  </Alert>
                ) : (
                  <div className="p-4 bg-gray-50 rounded-lg space-y-3">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm font-medium">Agent Name</p>
                        <p className="text-lg">{parsedRequirements.name}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Type</p>
                        <p className="text-lg capitalize">{parsedRequirements.type}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Language</p>
                        <p className="text-lg">{parsedRequirements.language}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Framework</p>
                        <p className="text-lg">{parsedRequirements.framework}</p>
                      </div>
                    </div>
                    
                    <div>
                      <p className="text-sm font-medium mb-2">Capabilities</p>
                      <div className="flex flex-wrap gap-2">
                        {parsedRequirements.capabilities?.map((cap: string, index: number) => (
                          <Badge key={index} variant="outline">{cap}</Badge>
                        ))}
                      </div>
                    </div>
                    
                    <div>
                      <p className="text-sm font-medium mb-2">AI Reasoning</p>
                      <p className="text-sm text-gray-600">{parsedRequirements.reasoning}</p>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <p className="text-sm font-medium">Confidence</p>
                      <Badge variant={parsedRequirements.confidence > 80 ? 'default' : 'secondary'}>
                        {parsedRequirements.confidence}%
                      </Badge>
                    </div>
                  </div>
                )}
              </div>
            )}
          </TabsContent>

          {/* Documentation Tab */}
          <TabsContent value="docs" className="space-y-4">
            <div className="space-y-3">
              <div className="flex gap-2">
                <select
                  value={docLanguage}
                  onChange={(e) => setDocLanguage(e.target.value)}
                  className="flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm"
                >
                  <option value="javascript">JavaScript</option>
                  <option value="python">Python</option>
                  <option value="typescript">TypeScript</option>
                  <option value="java">Java</option>
                </select>
                <Button onClick={handleDocGeneration} disabled={docLoading || !docCode.trim()}>
                  {docLoading ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <FileText className="h-4 w-4 mr-2" />
                  )}
                  Generate Docs
                </Button>
              </div>
              <Textarea
                placeholder="Paste your code here to generate documentation..."
                value={docCode}
                onChange={(e) => setDocCode(e.target.value)}
                rows={6}
                className="font-mono text-sm"
              />
            </div>
            
            {generatedDoc && (
              <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    Generated Documentation
                  </h4>
                  <Button variant="ghost" size="sm" onClick={() => copyToClipboard(generatedDoc)}>
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
                <pre className="text-sm whitespace-pre-wrap overflow-x-auto">{generatedDoc}</pre>
              </div>
            )}
          </TabsContent>
        </Tabs>

        {/* Available Capabilities */}
        {capabilities.length > 0 && (
          <div className="mt-6 pt-4 border-t">
            <h4 className="text-sm font-medium mb-3">Available AI Capabilities</h4>
            <div className="flex flex-wrap gap-2">
              {capabilities.map((capability, index) => (
                <Badge key={index} variant="outline" title={capability.description}>
                  {capability.name}
                </Badge>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
