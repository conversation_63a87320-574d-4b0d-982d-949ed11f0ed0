---
# Global variables for all hosts

# Ansible Configuration
ansible_python_interpreter: /usr/bin/python3

# Common Paths
microservices_root: /etc/microservices
docker_data_root: /var/lib/docker
backup_root: /var/backups/microservices

# Common Docker Settings
docker_compose_version: "2.24.0"
docker_prune_schedule: "0 2 * * 0"  # Weekly at 2 AM Sunday

# Security Settings
enable_firewall: true
allowed_ports:
  - 22    # SSH
  - 80    # HTTP
  - 443   # HTTPS
  - 8080  # Alternative HTTP

# Monitoring
enable_monitoring: true
metrics_port: 9090
enable_health_checks: true

# Backup Settings
enable_backups: true
backup_retention_days: 7

# Update Settings
auto_update_images: false
update_window_start: "02:00"
update_window_end: "04:00"

# Deployment Settings
deployment_strategy: blue-green
zero_downtime_deployment: true
rollback_on_failure: true

# Common Labels
common_labels:
  managed-by: ansible
  platform: microservices
  
# Avoid using 'environment' as it's a reserved variable name
deployment_env: "{{ deployment_environment | default('dev') }}"
  
# GCP Metadata Service
gcp_metadata_url: http://metadata.google.internal/computeMetadata/v1
gcp_metadata_headers:
  Metadata-Flavor: Google