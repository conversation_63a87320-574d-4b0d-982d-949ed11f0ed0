#!/bin/bash
# Download OpenAPI Generator CLI if it doesn't exist

JAR_FILE="$(dirname "$0")/openapi-generator-cli.jar"
DOWNLOAD_URL="https://repo1.maven.org/maven2/org/openapitools/openapi-generator-cli/7.9.0/openapi-generator-cli-7.9.0.jar"

if [ ! -f "$JAR_FILE" ]; then
    echo "Downloading OpenAPI Generator CLI..."
    curl -L -o "$JAR_FILE" "$DOWNLOAD_URL"
    echo "Download complete: $JAR_FILE"
else
    echo "OpenAPI Generator CLI already exists: $JAR_FILE"
fi