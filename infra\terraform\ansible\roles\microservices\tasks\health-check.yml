---
- name: "Health check for {{ item.key }}"
  uri:
    url: "http://localhost:{{ item.value.port }}{{ item.value.health_check.path }}"
    method: GET
    status_code: 200
    timeout: "{{ item.value.health_check.timeout | default(30) }}"
  retries: "{{ item.value.health_check.retries | default(30) }}"
  delay: "{{ item.value.health_check.interval | default(10) }}"
  register: health_check_result
  until: health_check_result.status == 200
  tags: ['health-check']

- name: "Log health check result for {{ item.key }}"
  debug:
    msg: "Service {{ item.key }} is healthy - Status: {{ health_check_result.status }}"
  when: health_check_result.status == 200
  tags: ['health-check']