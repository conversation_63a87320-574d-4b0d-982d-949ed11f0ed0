/* tslint:disable */
/* eslint-disable */
/**
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { InteractionWithDetailsAllOfContact } from './InteractionWithDetailsAllOfContact';
import {
    InteractionWithDetailsAllOfContactFromJSON,
    InteractionWithDetailsAllOfContactFromJSONTyped,
    InteractionWithDetailsAllOfContactToJSON,
    InteractionWithDetailsAllOfContactToJSONTyped,
} from './InteractionWithDetailsAllOfContact';
import type { InteractionWithDetailsAllOfCompany } from './InteractionWithDetailsAllOfCompany';
import {
    InteractionWithDetailsAllOfCompanyFromJSON,
    InteractionWithDetailsAllOfCompanyFromJSONTyped,
    InteractionWithDetailsAllOfCompanyToJSON,
    InteractionWithDetailsAllOfCompanyToJSONTyped,
} from './InteractionWithDetailsAllOfCompany';

/**
 * 
 * @export
 * @interface InteractionWithDetails
 */
export interface InteractionWithDetails {
    /**
     * 
     * @type {string}
     * @memberof InteractionWithDetails
     */
    id?: string;
    /**
     * 
     * @type {string}
     * @memberof InteractionWithDetails
     */
    companyId?: string | null;
    /**
     * 
     * @type {string}
     * @memberof InteractionWithDetails
     */
    contactId?: string | null;
    /**
     * 
     * @type {string}
     * @memberof InteractionWithDetails
     */
    interactionType?: InteractionWithDetailsInteractionTypeEnum;
    /**
     * 
     * @type {string}
     * @memberof InteractionWithDetails
     */
    notes?: string;
    /**
     * 
     * @type {Date}
     * @memberof InteractionWithDetails
     */
    interactionDatetime?: Date;
    /**
     * 
     * @type {string}
     * @memberof InteractionWithDetails
     */
    createdBy?: string;
    /**
     * 
     * @type {Date}
     * @memberof InteractionWithDetails
     */
    createdAt?: Date;
    /**
     * 
     * @type {Date}
     * @memberof InteractionWithDetails
     */
    updatedAt?: Date;
    /**
     * 
     * @type {InteractionWithDetailsAllOfCompany}
     * @memberof InteractionWithDetails
     */
    company?: InteractionWithDetailsAllOfCompany | null;
    /**
     * 
     * @type {InteractionWithDetailsAllOfContact}
     * @memberof InteractionWithDetails
     */
    contact?: InteractionWithDetailsAllOfContact | null;
}

/**
* @export
* @enum {string}
*/
export enum InteractionWithDetailsInteractionTypeEnum {
    Email = 'email',
    Phone = 'phone',
    Meeting = 'meeting',
    Demo = 'demo',
    Proposal = 'proposal',
    FollowUp = 'follow-up',
    Other = 'other'
}


/**
 * Check if a given object implements the InteractionWithDetails interface.
 */
export function instanceOfInteractionWithDetails(value: object): value is InteractionWithDetails {
    return true;
}

export function InteractionWithDetailsFromJSON(json: any): InteractionWithDetails {
    return InteractionWithDetailsFromJSONTyped(json, false);
}

export function InteractionWithDetailsFromJSONTyped(json: any, ignoreDiscriminator: boolean): InteractionWithDetails {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'] == null ? undefined : json['id'],
        'companyId': json['company_id'] == null ? undefined : json['company_id'],
        'contactId': json['contact_id'] == null ? undefined : json['contact_id'],
        'interactionType': json['interaction_type'] == null ? undefined : json['interaction_type'],
        'notes': json['notes'] == null ? undefined : json['notes'],
        'interactionDatetime': json['interaction_datetime'] == null ? undefined : (new Date(json['interaction_datetime'])),
        'createdBy': json['created_by'] == null ? undefined : json['created_by'],
        'createdAt': json['created_at'] == null ? undefined : (new Date(json['created_at'])),
        'updatedAt': json['updated_at'] == null ? undefined : (new Date(json['updated_at'])),
        'company': json['company'] == null ? undefined : InteractionWithDetailsAllOfCompanyFromJSON(json['company']),
        'contact': json['contact'] == null ? undefined : InteractionWithDetailsAllOfContactFromJSON(json['contact']),
    };
}

  export function InteractionWithDetailsToJSON(json: any): InteractionWithDetails {
      return InteractionWithDetailsToJSONTyped(json, false);
  }

  export function InteractionWithDetailsToJSONTyped(value?: InteractionWithDetails | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'company_id': value['companyId'],
        'contact_id': value['contactId'],
        'interaction_type': value['interactionType'],
        'notes': value['notes'],
        'interaction_datetime': value['interactionDatetime'] == null ? undefined : ((value['interactionDatetime']).toISOString()),
        'created_by': value['createdBy'],
        'created_at': value['createdAt'] == null ? undefined : ((value['createdAt']).toISOString()),
        'updated_at': value['updatedAt'] == null ? undefined : ((value['updatedAt']).toISOString()),
        'company': InteractionWithDetailsAllOfCompanyToJSON(value['company']),
        'contact': InteractionWithDetailsAllOfContactToJSON(value['contact']),
    };
}

