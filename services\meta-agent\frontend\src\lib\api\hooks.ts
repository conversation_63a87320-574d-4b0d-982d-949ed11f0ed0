// React Query hooks for AI Agent Platform API
// Provides typed hooks for all API endpoints

import { useQuery, useMutation, useQueryClient, UseQueryOptions, UseMutationOptions } from '@tanstack/react-query';
import { api } from './index';
import type { components } from './types';

// Query keys factory for consistent caching
export const queryKeys = {
  agents: {
    all: ['agents'] as const,
    lists: () => [...queryKeys.agents.all, 'list'] as const,
    list: (filters: Record<string, any>) => [...queryKeys.agents.lists(), filters] as const,
    details: () => [...queryKeys.agents.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.agents.details(), id] as const,
  },
  tasks: {
    all: ['tasks'] as const,
    lists: () => [...queryKeys.tasks.all, 'list'] as const,
    list: (filters: Record<string, any>) => [...queryKeys.tasks.lists(), filters] as const,
    details: () => [...queryKeys.tasks.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.tasks.details(), id] as const,
  },
  migration: {
    all: ['migration'] as const,
    analyses: () => [...queryKeys.migration.all, 'analyses'] as const,
    analysis: (id: string) => [...queryKeys.migration.analyses(), id] as const,
    plans: () => [...queryKeys.migration.all, 'plans'] as const,
    plan: (id: string) => [...queryKeys.migration.plans(), id] as const,
    projects: () => [...queryKeys.migration.all, 'projects'] as const,
    project: (id: string) => [...queryKeys.migration.projects(), id] as const,
    summary: () => [...queryKeys.migration.all, 'summary'] as const,
  },
  system: {
    all: ['system'] as const,
    stats: () => [...queryKeys.system.all, 'stats'] as const,
    health: () => [...queryKeys.system.all, 'health'] as const,
  },
} as const;

// Agent Hooks
export function useAgents(
  params?: Parameters<typeof api.agents.list>[0],
  options?: UseQueryOptions<Types.PaginatedResponse<Types.Agent>>
) {
  return useQuery({
    queryKey: queryKeys.agents.list(params || {}),
    queryFn: () => api.agents.list(params),
    ...options,
  });
}

export function useAgent(
  id: string,
  options?: UseQueryOptions<Types.Agent>
) {
  return useQuery({
    queryKey: queryKeys.agents.detail(id),
    queryFn: () => api.agents.get(id),
    enabled: !!id,
    ...options,
  });
}

export function useCreateAgent(
  options?: UseMutationOptions<
    { agent: Types.Agent; task_id: string },
    Error,
    Types.GenerationRequest
  >
) {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: api.agents.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.agents.lists() });
    },
    ...options,
  });
}

export function useUpdateAgent(
  options?: UseMutationOptions<
    Types.Agent,
    Error,
    { id: string; data: Parameters<typeof api.agents.update>[1] }
  >
) {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }) => api.agents.update(id, data),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.agents.detail(variables.id) });
      queryClient.invalidateQueries({ queryKey: queryKeys.agents.lists() });
    },
    ...options,
  });
}

export function useDeleteAgent(
  options?: UseMutationOptions<void, Error, string>
) {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: api.agents.delete,
    onSuccess: (_, id) => {
      queryClient.removeQueries({ queryKey: queryKeys.agents.detail(id) });
      queryClient.invalidateQueries({ queryKey: queryKeys.agents.lists() });
    },
    ...options,
  });
}

export function useStartAgent(
  options?: UseMutationOptions<
    { status: string; port: number; process_id: number },
    Error,
    string
  >
) {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: api.agents.start,
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.agents.detail(id) });
      queryClient.invalidateQueries({ queryKey: queryKeys.agents.lists() });
    },
    ...options,
  });
}

export function useStopAgent(
  options?: UseMutationOptions<
    { status: string; message: string },
    Error,
    string
  >
) {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: api.agents.stop,
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.agents.detail(id) });
      queryClient.invalidateQueries({ queryKey: queryKeys.agents.lists() });
    },
    ...options,
  });
}

// Task Hooks
export function useTasks(
  params?: Parameters<typeof api.tasks.list>[0],
  options?: UseQueryOptions<Types.PaginatedResponse<Types.Task>>
) {
  return useQuery({
    queryKey: queryKeys.tasks.list(params || {}),
    queryFn: () => api.tasks.list(params),
    ...options,
  });
}

export function useTask(
  id: string,
  options?: UseQueryOptions<Types.Task>
) {
  return useQuery({
    queryKey: queryKeys.tasks.detail(id),
    queryFn: () => api.tasks.get(id),
    enabled: !!id,
    // refetchInterval: disabled for now
    ...options,
  });
}

// Migration Hooks
export function useMigrationAnalyses(
  options?: UseQueryOptions<Types.ApplicationAnalysis[]>
) {
  return useQuery({
    queryKey: queryKeys.migration.analyses(),
    queryFn: api.migration.listAnalyses,
    ...options,
  });
}

export function useMigrationAnalysis(
  id: string,
  options?: UseQueryOptions<Types.ApplicationAnalysis>
) {
  return useQuery({
    queryKey: queryKeys.migration.analysis(id),
    queryFn: () => api.migration.getAnalysis(id),
    enabled: !!id,
    ...options,
  });
}

export function useAnalyzeMigration(
  options?: UseMutationOptions<
    Types.ApplicationAnalysis,
    Error,
    Parameters<typeof api.migration.analyze>[0]
  >
) {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: api.migration.analyze,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.migration.analyses() });
    },
    ...options,
  });
}

export function useMigrationPlans(
  options?: UseQueryOptions<Types.MigrationPlan[]>
) {
  return useQuery({
    queryKey: queryKeys.migration.plans(),
    queryFn: api.migration.listPlans,
    ...options,
  });
}

export function useMigrationPlan(
  id: string,
  options?: UseQueryOptions<Types.MigrationPlan>
) {
  return useQuery({
    queryKey: queryKeys.migration.plan(id),
    queryFn: () => api.migration.getPlan(id),
    enabled: !!id,
    ...options,
  });
}

export function useCreateMigrationPlan(
  options?: UseMutationOptions<
    Types.MigrationPlan,
    Error,
    Parameters<typeof api.migration.createPlan>[0]
  >
) {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: api.migration.createPlan,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.migration.plans() });
    },
    ...options,
  });
}

export function useMigrationProjects(
  options?: UseQueryOptions<Types.MigrationProject[]>
) {
  return useQuery({
    queryKey: queryKeys.migration.projects(),
    queryFn: api.migration.listProjects,
    ...options,
  });
}

export function useMigrationProject(
  id: string,
  options?: UseQueryOptions<Types.MigrationProject>
) {
  return useQuery({
    queryKey: queryKeys.migration.project(id),
    queryFn: () => api.migration.getStatus(id),
    enabled: !!id,
    // refetchInterval: disabled for now
    ...options,
  });
}

export function useStartMigration(
  options?: UseMutationOptions<
    { project_id: string; status: string; estimated_timeline_days: number },
    Error,
    Parameters<typeof api.migration.start>[0]
  >
) {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: api.migration.start,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.migration.projects() });
    },
    ...options,
  });
}

export function useMigrationSummary(
  options?: UseQueryOptions<Awaited<ReturnType<typeof api.migration.getSummary>>>
) {
  return useQuery({
    queryKey: queryKeys.migration.summary(),
    queryFn: api.migration.getSummary,
    ...options,
  });
}

// System Hooks
export function useSystemStats(
  options?: UseQueryOptions<Types.SystemStats>
) {
  return useQuery({
    queryKey: queryKeys.system.stats(),
    queryFn: api.system.getStats,
    refetchInterval: 30000, // 30 seconds
    ...options,
  });
}

export function useSystemHealth(
  options?: UseQueryOptions<Types.SystemHealth>
) {
  return useQuery({
    queryKey: queryKeys.system.health(),
    queryFn: api.system.getHealth,
    refetchInterval: 60000, // 1 minute
    ...options,
  });
}

// Utility hooks
export function useInvalidateQueries() {
  const queryClient = useQueryClient();
  
  return {
    agents: () => queryClient.invalidateQueries({ queryKey: queryKeys.agents.all }),
    tasks: () => queryClient.invalidateQueries({ queryKey: queryKeys.tasks.all }),
    migration: () => queryClient.invalidateQueries({ queryKey: queryKeys.migration.all }),
    system: () => queryClient.invalidateQueries({ queryKey: queryKeys.system.all }),
    all: () => queryClient.invalidateQueries(),
  };
}

export function usePrefetchAgent(id: string) {
  const queryClient = useQueryClient();
  
  return () => {
    queryClient.prefetchQuery({
      queryKey: queryKeys.agents.detail(id),
      queryFn: () => api.agents.get(id),
      staleTime: 10000, // 10 seconds
    });
  };
}