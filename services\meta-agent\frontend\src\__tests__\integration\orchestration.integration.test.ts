/**
 * Orchestration Integration Tests
 * Tests real API calls to the backend orchestration endpoints
 */

import { agentService } from '@/services/agent.service';
import { orchestrationService } from '@/services/orchestration.service';
import { apiService } from '../../services/api.test';
import {
  User,
  LoginRequest,
  RegisterRequest,
  TokenResponse,
  OrchestrationPattern,
} from '@/types/api';

class AuthServiceTest {
  async login(credentials: LoginRequest): Promise<User> {
    const params = new URLSearchParams();
    params.append('username', credentials.username);
    params.append('password', credentials.password);

    const response = await apiService.post<TokenResponse>(
      '/auth/login',
      params
    );
    apiService.setTokens(response.data);

    // Get user data after login
    const userResponse = await apiService.get<User>('/auth/me');
    return userResponse.data;
  }

  async register(data: RegisterRequest): Promise<User> {
    const response = await apiService.post<User>('/auth/register', data);
    return response.data;
  }

  logout() {
    apiService.logout();
  }

  isAuthenticated(): boolean {
    return !!apiService.getToken();
  }
}

const authService = new AuthServiceTest();

describe('Orchestration Integration Tests', () => {
  let testUser: any;
  let testAgents: any[] = [];
  let authToken: string | null = null;
  let createdOrchestrationIds: string[] = [];
  let createdAgentIds: string[] = [];

  beforeAll(async () => {
    // Setup authenticated user for all orchestration tests
    testUser = (global as any).testUtils.generateTestUser();
    
    // Register user
    await authService.register({
      username: testUser.username,
      email: testUser.email,
      password: testUser.password,
      full_name: testUser.full_name
    });

    // Login user
    await authService.login({
      username: testUser.username,
      password: testUser.password
    });

    authToken = apiService.getToken();

    // Create test agents for orchestration testing
    for (let i = 0; i < 3; i++) {
      const agent = await agentService.createAgent({
        name: `Orchestration Test Agent ${i + 1}`,
        description: `Agent ${i + 1} for orchestration integration testing`,
        type: 'assistant',
        config: { max_concurrent_tasks: 5, timeout_seconds: 300 },
        capabilities: ['orchestration', 'task_execution', 'data_processing']
      });
      testAgents.push(agent);
      createdAgentIds.push(agent.id);
    }
  });

  afterEach(async () => {
    // Cleanup created orchestrations
    for (const orchestrationId of createdOrchestrationIds) {
      try {
        await orchestrationService.deleteOrchestration(orchestrationId);
      } catch (error) {
        console.warn(`Failed to cleanup orchestration ${orchestrationId}:`, error);
      }
    }
    createdOrchestrationIds = [];
  });

  afterAll(async () => {
    // Cleanup created agents
    for (const agentId of createdAgentIds) {
      try {
        await agentService.deleteAgent(agentId);
      } catch (error) {
        console.warn(`Failed to cleanup agent ${agentId}:`, error);
      }
    }
    
    // Cleanup authentication
    if (authToken) {
      authService.logout();
    }
  });

  const generateTestOrchestration = () => ({
    name: `Test Orchestration ${Date.now()}`,
    description: 'A test orchestration for integration testing',
    pattern: OrchestrationPattern.SEQUENTIAL,
    config: {
      max_retries: 3,
      timeout_minutes: 30,
      parallel_execution: false
    },
    execution_plan: {
      steps: [
        {
          id: 'step1',
          name: 'Data Collection',
          type: 'data_processing',
          agent_role: 'collector'
        },
        {
          id: 'step2',
          name: 'Data Analysis',
          type: 'analysis',
          agent_role: 'analyzer'
        },
        {
          id: 'step3',
          name: 'Result Compilation',
          type: 'compilation',
          agent_role: 'compiler'
        }
      ]
    },
    agent_ids: testAgents.slice(0, 2).map(agent => agent.id)
  });

  describe('Orchestration CRUD Operations', () => {
    test('should create a new orchestration', async () => {
      const orchestrationData = generateTestOrchestration();
      
      const orchestration = await orchestrationService.createOrchestration(orchestrationData);

      expect(orchestration).toBeDefined();
      expect(orchestration.id).toBeDefined();
      expect(orchestration.name).toBe(orchestrationData.name);
      expect(orchestration.description).toBe(orchestrationData.description);
      expect(orchestration.pattern).toBe(orchestrationData.pattern);
      expect(orchestration.config).toEqual(orchestrationData.config);
      expect(orchestration.execution_plan).toEqual(orchestrationData.execution_plan);
      expect(orchestration.agent_ids).toEqual(orchestrationData.agent_ids);
      expect(orchestration.status).toBeDefined();
      expect(orchestration.created_at).toBeDefined();
      expect(orchestration.updated_at).toBeDefined();

      createdOrchestrationIds.push(orchestration.id);
    });

    test('should list orchestrations', async () => {
      // Create a few orchestrations first
      const orch1 = await orchestrationService.createOrchestration({
        ...generateTestOrchestration(),
        name: 'Orchestration 1'
      });
      createdOrchestrationIds.push(orch1.id);

      const orch2 = await orchestrationService.createOrchestration({
        ...generateTestOrchestration(),
        name: 'Orchestration 2',
        pattern: OrchestrationPattern.PARALLEL
      });
      createdOrchestrationIds.push(orch2.id);

      const response = await orchestrationService.listOrchestrations();

      expect(response).toBeDefined();
      expect(response.items).toBeDefined();
      expect(Array.isArray(response.items)).toBe(true);
      expect(response.items.length).toBeGreaterThanOrEqual(2);
      expect(response.total).toBeGreaterThanOrEqual(2);
      expect(response.limit).toBeDefined();
      expect(response.offset).toBeDefined();

      // Check that our created orchestrations are in the list
      const orchestrationIds = response.items.map(orch => orch.id);
      expect(orchestrationIds).toContain(orch1.id);
      expect(orchestrationIds).toContain(orch2.id);
    });

    test('should list orchestrations with pagination', async () => {
      // Create multiple orchestrations
      for (let i = 0; i < 5; i++) {
        const orchestration = await orchestrationService.createOrchestration({
          ...generateTestOrchestration(),
          name: `Pagination Orchestration ${i}`
        });
        createdOrchestrationIds.push(orchestration.id);
      }

      // Test pagination
      const page1 = await orchestrationService.listOrchestrations({ limit: 2, offset: 0 });
      expect(page1.items.length).toBeLessThanOrEqual(2);
      expect(page1.limit).toBe(2);
      expect(page1.offset).toBe(0);

      const page2 = await orchestrationService.listOrchestrations({ limit: 2, offset: 2 });
      expect(page2.items.length).toBeLessThanOrEqual(2);
      expect(page2.limit).toBe(2);
      expect(page2.offset).toBe(2);

      // Ensure different pages have different items
      const page1Ids = page1.items.map(orch => orch.id);
      const page2Ids = page2.items.map(orch => orch.id);
      const commonIds = page1Ids.filter(id => page2Ids.includes(id));
      expect(commonIds.length).toBe(0);
    });

    test('should filter orchestrations by pattern', async () => {
      const sequentialOrch = await orchestrationService.createOrchestration({
        ...generateTestOrchestration(),
        pattern: OrchestrationPattern.SEQUENTIAL
      });
      createdOrchestrationIds.push(sequentialOrch.id);

      const parallelOrch = await orchestrationService.createOrchestration({
        ...generateTestOrchestration(),
        pattern: OrchestrationPattern.PARALLEL
      });
      createdOrchestrationIds.push(parallelOrch.id);

      // Filter by pattern
      const sequentialOrchs = await orchestrationService.listOrchestrations({ pattern: OrchestrationPattern.SEQUENTIAL });
      const sequentialIds = sequentialOrchs.items.map(orch => orch.id);
      expect(sequentialIds).toContain(sequentialOrch.id);

      const parallelOrchs = await orchestrationService.listOrchestrations({ pattern: OrchestrationPattern.PARALLEL });
      const parallelIds = parallelOrchs.items.map(orch => orch.id);
      expect(parallelIds).toContain(parallelOrch.id);
    });

    test('should get orchestration by id', async () => {
      const createdOrchestration = await orchestrationService.createOrchestration(generateTestOrchestration());
      createdOrchestrationIds.push(createdOrchestration.id);

      const retrievedOrchestration = await orchestrationService.getOrchestration(createdOrchestration.id);

      expect(retrievedOrchestration).toBeDefined();
      expect(retrievedOrchestration.id).toBe(createdOrchestration.id);
      expect(retrievedOrchestration.name).toBe(createdOrchestration.name);
      expect(retrievedOrchestration.description).toBe(createdOrchestration.description);
      expect(retrievedOrchestration.pattern).toBe(createdOrchestration.pattern);
      expect(retrievedOrchestration.config).toEqual(createdOrchestration.config);
    });

    test('should update orchestration', async () => {
      const createdOrchestration = await orchestrationService.createOrchestration(generateTestOrchestration());
      createdOrchestrationIds.push(createdOrchestration.id);

      const updateData = {
        name: 'Updated Orchestration Name',
        description: 'Updated description',
        config: {
          ...createdOrchestration.config,
          max_retries: 5,
          timeout_minutes: 60
        }
      };

      const updatedOrchestration = await orchestrationService.updateOrchestration(createdOrchestration.id, updateData);

      expect(updatedOrchestration.id).toBe(createdOrchestration.id);
      expect(updatedOrchestration.name).toBe(updateData.name);
      expect(updatedOrchestration.description).toBe(updateData.description);
      expect(updatedOrchestration.config.max_retries).toBe(5);
      expect(updatedOrchestration.config.timeout_minutes).toBe(60);
      expect(updatedOrchestration.updated_at).not.toBe(createdOrchestration.updated_at);
    });

    test('should delete orchestration', async () => {
      const createdOrchestration = await orchestrationService.createOrchestration(generateTestOrchestration());

      await orchestrationService.deleteOrchestration(createdOrchestration.id);

      await expect(orchestrationService.getOrchestration(createdOrchestration.id)).rejects.toThrow();

      // Remove from cleanup list since it's already deleted
      createdOrchestrationIds = createdOrchestrationIds.filter(id => id !== createdOrchestration.id);
    });

    test('should fail to get non-existent orchestration', async () => {
      const nonExistentId = 'non-existent-orchestration-id-12345';
      
      await expect(orchestrationService.getOrchestration(nonExistentId)).rejects.toThrow();
    });

    test('should fail to update non-existent orchestration', async () => {
      const nonExistentId = 'non-existent-orchestration-id-12345';
      
      await expect(orchestrationService.updateOrchestration(nonExistentId, {
        name: 'Updated Name'
      })).rejects.toThrow();
    });

    test('should fail to delete non-existent orchestration', async () => {
      const nonExistentId = 'non-existent-orchestration-id-12345';
      
      await expect(orchestrationService.deleteOrchestration(nonExistentId)).rejects.toThrow();
    });
  });

  describe('Orchestration Lifecycle Management', () => {
    let orchestration: any;

    beforeEach(async () => {
      orchestration = await orchestrationService.createOrchestration(generateTestOrchestration());
      createdOrchestrationIds.push(orchestration.id);
    });

    test('should start orchestration', async () => {
      const result = await orchestrationService.startOrchestration(orchestration.id);

      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      expect(result.message).toBeDefined();
    });

    test('should stop orchestration', async () => {
      // Start orchestration first
      await orchestrationService.startOrchestration(orchestration.id);

      const result = await orchestrationService.stopOrchestration(orchestration.id);

      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      expect(result.message).toBeDefined();
    });

    test('should stop orchestration gracefully', async () => {
      // Start orchestration first
      await orchestrationService.startOrchestration(orchestration.id);

      const result = await orchestrationService.stopOrchestration(orchestration.id, true);

      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      expect(result.message).toBeDefined();
    });

    test('should force stop orchestration', async () => {
      // Start orchestration first
      await orchestrationService.startOrchestration(orchestration.id);

      const result = await orchestrationService.stopOrchestration(orchestration.id, false);

      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      expect(result.message).toBeDefined();
    });

    test('should pause orchestration', async () => {
      // Start orchestration first
      await orchestrationService.startOrchestration(orchestration.id);

      const result = await orchestrationService.pauseOrchestration(orchestration.id);

      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      expect(result.message).toBeDefined();
    });

    test('should resume orchestration', async () => {
      // Start and pause orchestration first
      await orchestrationService.startOrchestration(orchestration.id);
      await orchestrationService.pauseOrchestration(orchestration.id);

      const result = await orchestrationService.resumeOrchestration(orchestration.id);

      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      expect(result.message).toBeDefined();
    });

    test('should get orchestration progress', async () => {
      const progress = await orchestrationService.getOrchestrationProgress(orchestration.id);

      expect(progress).toBeDefined();
      // Progress structure may vary based on implementation
      expect(typeof progress).toBe('object');
    });

    test('should fail to start non-existent orchestration', async () => {
      const nonExistentId = 'non-existent-orchestration-id-12345';
      
      await expect(orchestrationService.startOrchestration(nonExistentId)).rejects.toThrow();
    });

    test('should fail to stop non-existent orchestration', async () => {
      const nonExistentId = 'non-existent-orchestration-id-12345';
      
      await expect(orchestrationService.stopOrchestration(nonExistentId)).rejects.toThrow();
    });
  });

  describe('Agent Management in Orchestrations', () => {
    let orchestration: any;

    beforeEach(async () => {
      orchestration = await orchestrationService.createOrchestration({
        ...generateTestOrchestration(),
        agent_ids: [] // Start with no agents
      });
      createdOrchestrationIds.push(orchestration.id);
    });

    test('should add agent to orchestration', async () => {
      const agentId = testAgents[0].id;
      const role = 'coordinator';

      const result = await orchestrationService.addAgentToOrchestration(
        orchestration.id,
        agentId,
        role
      );

      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      expect(result.message).toBeDefined();

      // Verify agent was added
      const updatedOrchestration = await orchestrationService.getOrchestration(orchestration.id);
      expect(updatedOrchestration.agent_ids).toContain(agentId);
    });

    test('should add agent to orchestration with default role', async () => {
      const agentId = testAgents[1].id;

      const result = await orchestrationService.addAgentToOrchestration(
        orchestration.id,
        agentId
      );

      expect(result).toBeDefined();
      expect(result.success).toBe(true);

      // Verify agent was added
      const updatedOrchestration = await orchestrationService.getOrchestration(orchestration.id);
      expect(updatedOrchestration.agent_ids).toContain(agentId);
    });

    test('should remove agent from orchestration', async () => {
      const agentId = testAgents[0].id;

      // Add agent first
      await orchestrationService.addAgentToOrchestration(orchestration.id, agentId);

      // Then remove it
      await orchestrationService.removeAgentFromOrchestration(orchestration.id, agentId);

      // Verify agent was removed
      const updatedOrchestration = await orchestrationService.getOrchestration(orchestration.id);
      expect(updatedOrchestration.agent_ids).not.toContain(agentId);
    });

    test('should fail to add non-existent agent to orchestration', async () => {
      const nonExistentAgentId = 'non-existent-agent-id-12345';
      
      await expect(orchestrationService.addAgentToOrchestration(
        orchestration.id,
        nonExistentAgentId
      )).rejects.toThrow();
    });

    test('should fail to add agent to non-existent orchestration', async () => {
      const nonExistentOrchestrationId = 'non-existent-orchestration-id-12345';
      const agentId = testAgents[0].id;
      
      await expect(orchestrationService.addAgentToOrchestration(
        nonExistentOrchestrationId,
        agentId
      )).rejects.toThrow();
    });

    test('should fail to remove agent from non-existent orchestration', async () => {
      const nonExistentOrchestrationId = 'non-existent-orchestration-id-12345';
      const agentId = testAgents[0].id;
      
      await expect(orchestrationService.removeAgentFromOrchestration(
        nonExistentOrchestrationId,
        agentId
      )).rejects.toThrow();
    });
  });

  describe('Orchestration Validation', () => {
    test('should fail to create orchestration with invalid data', async () => {
      const invalidOrchestrationData = {
        name: '', // Empty name
        description: '',
        pattern: 'invalid_pattern' as any,
        config: {
          max_retries: -1, // Invalid negative value
          timeout_minutes: 0 // Invalid zero timeout
        },
        execution_plan: {}, // Invalid execution plan
        agent_ids: ['non-existent-agent-id'] // Non-existent agent
      };

      await expect(orchestrationService.createOrchestration(invalidOrchestrationData)).rejects.toThrow();
    });

    test('should fail to create orchestration with missing required fields', async () => {
      const incompleteOrchestrationData = {
        name: 'Incomplete Orchestration'
        // Missing required fields
      };

      await expect(orchestrationService.createOrchestration(incompleteOrchestrationData as any)).rejects.toThrow();
    });

    test('should fail to create orchestration without authentication', async () => {
      // Logout to remove authentication
      authService.logout();

      const orchestrationData = generateTestOrchestration();

      await expect(orchestrationService.createOrchestration(orchestrationData)).rejects.toThrow();

      // Re-login for other tests
      await authService.login({
        username: testUser.username,
        password: testUser.password
      });
    });
  });

  describe('Complex Orchestration Scenarios', () => {
    test('should create orchestration with multiple agents and complex execution plan', async () => {
      const complexOrchestrationData = {
        name: 'Complex Multi-Agent Orchestration',
        description: 'A complex orchestration with multiple agents and parallel execution',
        pattern: OrchestrationPattern.PARALLEL,
        config: {
          max_retries: 5,
          timeout_minutes: 120,
          parallel_execution: true,
          failure_tolerance: 0.8
        },
        execution_plan: {
          steps: [
            {
              id: 'parallel-group-1',
              name: 'Parallel Data Processing',
              type: 'parallel_group',
              parallel_steps: [
                {
                  id: 'data-collection-1',
                  name: 'Data Collection Stream 1',
                  type: 'data_collection',
                  agent_role: 'collector'
                },
                {
                  id: 'data-collection-2',
                  name: 'Data Collection Stream 2',
                  type: 'data_collection',
                  agent_role: 'collector'
                }
              ]
            },
            {
              id: 'aggregation',
              name: 'Data Aggregation',
              type: 'aggregation',
              agent_role: 'aggregator',
              depends_on: ['parallel-group-1']
            },
            {
              id: 'analysis',
              name: 'Final Analysis',
              type: 'analysis',
              agent_role: 'analyzer',
              depends_on: ['aggregation']
            }
          ]
        },
        agent_ids: testAgents.map(agent => agent.id)
      };

      const orchestration = await orchestrationService.createOrchestration(complexOrchestrationData);

      expect(orchestration).toBeDefined();
      expect(orchestration.name).toBe(complexOrchestrationData.name);
      expect(orchestration.pattern).toBe(complexOrchestrationData.pattern);
      expect(orchestration.config).toEqual(complexOrchestrationData.config);
      expect(orchestration.execution_plan).toEqual(complexOrchestrationData.execution_plan);
      expect(orchestration.agent_ids).toEqual(complexOrchestrationData.agent_ids);

      createdOrchestrationIds.push(orchestration.id);

      // Test lifecycle operations on complex orchestration
      const startResult = await orchestrationService.startOrchestration(orchestration.id);
      expect(startResult.success).toBe(true);

      const progress = await orchestrationService.getOrchestrationProgress(orchestration.id);
      expect(progress).toBeDefined();

      const stopResult = await orchestrationService.stopOrchestration(orchestration.id);
      expect(stopResult.success).toBe(true);
    });

    test('should handle orchestration with conditional execution paths', async () => {
      const conditionalOrchestrationData = {
        name: 'Conditional Execution Orchestration',
        description: 'Orchestration with conditional branching logic',
        pattern: OrchestrationPattern.EVENT_DRIVEN,
        config: {
          max_retries: 3,
          timeout_minutes: 60,
          conditional_execution: true
        },
        execution_plan: {
          steps: [
            {
              id: 'validation',
              name: 'Input Validation',
              type: 'validation',
              agent_role: 'validator'
            },
            {
              id: 'branch-decision',
              name: 'Processing Branch Decision',
              type: 'decision',
              agent_role: 'coordinator',
              depends_on: ['validation'],
              conditions: {
                success_path: 'heavy-processing',
                failure_path: 'light-processing'
              }
            },
            {
              id: 'heavy-processing',
              name: 'Heavy Data Processing',
              type: 'heavy_processing',
              agent_role: 'processor',
              condition: 'validation.result === "complex"'
            },
            {
              id: 'light-processing',
              name: 'Light Data Processing',
              type: 'light_processing',
              agent_role: 'processor',
              condition: 'validation.result === "simple"'
            },
            {
              id: 'finalization',
              name: 'Result Finalization',
              type: 'finalization',
              agent_role: 'finalizer',
              depends_on: ['heavy-processing', 'light-processing']
            }
          ]
        },
        agent_ids: testAgents.slice(0, 2).map(agent => agent.id)
      };

      const orchestration = await orchestrationService.createOrchestration(conditionalOrchestrationData);

      expect(orchestration).toBeDefined();
      expect(orchestration.pattern).toBe(conditionalOrchestrationData.pattern);
      expect(orchestration.execution_plan.steps).toHaveLength(5);

      createdOrchestrationIds.push(orchestration.id);
    });
  });
});