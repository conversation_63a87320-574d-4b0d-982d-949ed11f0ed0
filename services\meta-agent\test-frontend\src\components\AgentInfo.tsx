import { h, FunctionComponent } from 'preact';
import { useState, useEffect } from 'preact/hooks';
import { api } from '@/services/api';

interface AgentInfoProps {
  agentVersion: string;
  a2aProtocolVersion: string;
}

/**
 * Displays agent information and A2A protocol details.
 * @param {AgentInfoProps} props - Component properties.
 * @returns {JSX.Element} The rendered AgentInfo component.
 */
const AgentInfo: FunctionComponent<AgentInfoProps> = ({ agentVersion, a2aProtocolVersion }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Placeholder for future API calls if needed
    // Example:
    // const fetchAgentData = async () => {
    //   setLoading(true);
    //   try {
    //     const data = await api.get('/agent-info');
    //     // Update state with fetched data
    //   } catch (err) {
    //     setError('Failed to fetch agent information.');
    //   } finally {
    //     setLoading(false);
    //   }
    // };
    // fetchAgentData();
  }, []);


  if (loading) {
    return (
      <div class="flex justify-center items-center h-16">
        <p class="text-gray-500">Loading...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div class="text-red-500">
        <p>{error}</p>
      </div>
    );
  }

  return (
    <div class="p-4 bg-white rounded-lg shadow-sm">
      <div class="mb-4">
        <h3 class="text-lg font-medium">Agent Information</h3>
      </div>
      <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div>
          <p class="text-gray-600">Agent Version:</p>
          <p class="font-medium">{agentVersion || 'N/A'}</p>
        </div>
        <div>
          <p class="text-gray-600">A2A Protocol Version:</p>
          <p class="font-medium">{a2aProtocolVersion || 'N/A'}</p>
        </div>
      </div>
    </div>
  );
};

export default AgentInfo;
