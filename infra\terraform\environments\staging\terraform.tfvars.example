# Staging Environment Configuration Example
# Copy this file to terraform.tfvars and customize the values

# GCP Project Configuration (using dev project for now)
project_id   = "agent-dev-459718"
project_name = "platform"
region       = "australia-southeast1"
zone         = "australia-southeast1-a"

# GitHub Repository (for CI/CD automation)
github_repo_owner = "your-github-username"
github_repo_name  = "your-repo-name"

# SSL/TLS Configuration (for staging environment)
ssl_domains = [
  "internal.stg.twodot.ai",
  "api.stg.twodot.ai"
]

# Security Configuration (more relaxed for staging)
admin_ip_ranges = [
  "0.0.0.0/0"  # Allow from anywhere - restrict as needed
  # "***********/24",    # Example: Your office IP range
]

# Cloud Armor Security Settings
rate_limit_requests_per_minute = 500  # Lower limit for staging
blocked_regions = []
blocked_ip_ranges = []

# Database Migration Settings
enable_scheduled_migrations = true   # Test automated migrations
migration_schedule         = "0 2 * * 0"  # Sundays at 2 AM AEST
migration_timezone         = "Australia/Sydney"

# Notification Settings
enable_migration_notifications = true
notification_email = "<EMAIL>"
slack_webhook_url  = ""  # Optional

# Staging Settings
enable_debug_mode      = true
enable_external_access = false  # Keep instances private

# Resource Scaling
min_instance_count = 1
max_instance_count = 3

# Feature Flags
enable_monitoring        = true
enable_backup_automation = true
auto_scaling_enabled     = false

# Staging Notes:
# - Uses the same dev project (agent-dev-459718)
# - Located in Australia Southeast 1
# - Moderate security settings
# - SSL enabled with twodot.ai domains
# - Automated migrations enabled for testing
# - Monitoring enabled to test alerts
# - Resources kept minimal for cost control