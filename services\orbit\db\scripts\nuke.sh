#!/bin/bash
set -e

echo "🧨 NUCLEAR OPTION: Destroying all database containers and data..."
echo ""

# Stop and remove all containers related to the platform
echo "Stopping and removing containers..."
docker-compose -f platform/db/docker-compose.yml down -v --remove-orphans 2>/dev/null || true
docker stop platform_postgres 2>/dev/null || true
docker rm platform_postgres 2>/dev/null || true

# Remove volumes
echo "Removing database volumes..."
docker volume rm platform_db_postgres_data 2>/dev/null || true
docker volume rm db_postgres_data 2>/dev/null || true

# Clean up any dangling volumes related to postgres
echo "Cleaning up any postgres-related volumes..."
docker volume ls -q | grep -E "(postgres|platform)" | xargs -r docker volume rm 2>/dev/null || true

# Optional: clean up networks (if they exist)
docker network rm platform_default 2>/dev/null || true
docker network rm db_default 2>/dev/null || true

echo ""
echo "💥 NUKED! All database containers, volumes, and data have been removed."
echo ""
echo "To start fresh, run:"
echo "  bazel run //platform/db:getting_started"
echo ""
echo "Or manually:"
echo "  bazel run //platform/db:start"
echo "  bazel run //platform/db:migrate" 
echo "  bazel run //platform/db:test_data"