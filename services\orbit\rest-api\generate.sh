#!/bin/bash
set -e

# Get the directory where this script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

echo "Generating Go server code with oapi-codegen..."
cd "$SCRIPT_DIR"

# Create output directory
mkdir -p generated/go

# Bundle the OpenAPI spec to resolve external references using Redocly
echo "Bundling OpenAPI spec with Redocly..."
redocly bundle openapi.yaml --output openapi-bundled.yaml

# Generate the Go server code using oapi-codegen
echo "Running oapi-codegen..."
~/go/bin/oapi-codegen --config=oapi-codegen.yaml openapi-bundled.yaml

echo "Go server code generation complete!"