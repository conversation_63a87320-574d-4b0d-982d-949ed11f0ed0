#!/bin/bash
set -euo pipefail

# Terraform security scanning script using checkov

echo "Running Terraform security scan..."

# Check if checkov is installed
if ! command -v checkov &> /dev/null; then
    echo "WARNING: checkov not found. Installing..."
    # Install checkov if not present
    if command -v pip3 &> /dev/null; then
        pip3 install checkov
    elif command -v pip &> /dev/null; then
        pip install checkov
    else
        echo "ERROR: Cannot install checkov. Please install it manually."
        exit 1
    fi
fi

# Find all Terraform directories
terraform_dirs=$(find . -name "*.tf" -exec dirname {} \; | sort -u)

exit_code=0

for dir in $terraform_dirs; do
    echo "Scanning Terraform configuration in: $dir"
    
    # Run checkov security scan
    if ! checkov -d "$dir" --framework terraform --quiet --compact; then
        echo "WARNING: Security issues found in $dir"
        # Don't fail on security warnings, just report them
        # exit_code=1
    else
        echo "SUCCESS: No security issues found in $dir"
    fi
done

if [ $exit_code -eq 0 ]; then
    echo "Terraform security scan completed!"
else
    echo "Some security issues were found in Terraform configurations!"
fi

exit $exit_code