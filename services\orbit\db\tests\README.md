# Database Migration Tests

This directory contains comprehensive tests to verify that the CRM database migrations are applied correctly and the database is in the expected state.

## Test Coverage

### 1. Table Structure Tests
- ✅ All required tables exist
- ✅ Column data types are correct
- ✅ Not-null constraints are applied
- ✅ Default values are set correctly

### 2. Relationship Tests
- ✅ Foreign key constraints exist
- ✅ Cascade delete behavior
- ✅ Index creation for performance

### 3. Default Data Tests
- ✅ Company statuses (Lead, Prospect, Customer, Former Customer)
- ✅ Deal stages (Prospecting → Closed Won/Lost)
- ✅ Interaction types (Email, Phone Call, Meeting, Note, Task)

### 4. Trigger Tests
- ✅ Updated_at triggers exist and function correctly
- ✅ Automatic timestamp updates on record changes

### 5. Data Integrity Tests
- ✅ Unique constraints on email/username
- ✅ Proper JSONB column types
- ✅ Index performance optimizations

## Running Tests (Bazel-Centric)

### Quick Start with Make (Recommended)
```bash
# Run all database tests
make db-test

# Run specific test types
make db-test-sql      # SQL integration tests only
make db-test-go       # Go migration tests only
make db-test-with-setup # Full test with database setup
```

### Direct Bazel Commands
```bash
# Run all database tests
bazel test //db/tests:database_test_suite

# Run specific tests
bazel test //db/tests:sql_integration_test
bazel test //db/tests:migration_test
bazel test //db/tests:migration_with_setup

# Run with test output
bazel test //db/tests:database_test_suite --test_output=all
```

### Database Setup Commands
```bash
# Setup test database
make db-setup-test
# or
bazel run //db:setup_test_db

# Cleanup test database
make db-cleanup-test
# or
bazel run //db:cleanup_test_db
```

## Test Files

### `migration_test.go`
Comprehensive Go test suite covering:
- Table existence and structure
- Column types and constraints
- Index verification
- Foreign key relationships
- Trigger functionality
- Default data validation

### `integration_test.sql`
SQL-based integration tests that:
- Verify database schema state
- Test trigger functionality
- Validate default data
- Check constraint behavior

### Test Runners
- `test_with_docker.sh` - Docker-based isolated testing
- `run_tests.sh` - Local PostgreSQL testing
- `Makefile` - Convenient test execution

## Prerequisites

### For Docker Tests (Recommended)
- Docker installed and running
- No other dependencies required

### For Local Tests
- PostgreSQL server running
- Database: `crm_db_test`
- User: `postgres` with password `password`
- Port: `5432`

### For Go Tests
- Go 1.21+ installed
- Dependencies: `go mod download`

## Test Database Setup

The tests automatically:
1. Create a clean test database
2. Apply both migration files:
   - `V001__initial_schema.sql`
   - `V002__crm_schema.sql`
3. Run verification tests
4. Clean up resources

## Expected Test Results

All tests should pass, verifying:

### Tables Created (11 total)
- `users` - User authentication and profiles
- `user_profiles` - Extended user information
- `company_statuses` - Company pipeline stages
- `companies` - Company entities
- `contacts` - Contact management
- `deal_stages` - Deal pipeline stages
- `deals` - Sales opportunities
- `interaction_types` - Communication types
- `interactions` - Communication history
- `arli_documents` - AI document processing
- `arli_content_blocks` - AI content blocks

### Indexes Created (13+ total)
Performance indexes on commonly queried columns:
- User email lookups
- Company name searches
- Contact company relationships
- Deal company and stage filters
- Interaction filtering

### Default Data Inserted
- 4 Company statuses with pipeline order
- 6 Deal stages with win/loss flags
- 5 Interaction types for communication tracking

### Triggers Active (8 total)
Automatic `updated_at` timestamp maintenance on all core tables.

## Troubleshooting

### Docker Issues
```bash
# Clean up containers
make clean

# Check Docker status
docker ps -a | grep crm_test_db
```

### Local PostgreSQL Issues
```bash
# Check PostgreSQL is running
pg_isready -h localhost -p 5432

# Verify database exists
psql -h localhost -U postgres -l | grep crm_db_test
```

### Test Failures
Tests are designed to fail fast with descriptive error messages. Common issues:
- Missing migration files
- Incorrect database connection string
- PostgreSQL version compatibility
- Permission issues

## Continuous Integration

These tests can be integrated into CI/CD pipelines:

```yaml
# GitHub Actions example
- name: Run Database Tests
  run: |
    cd db/tests
    make test-docker
```

## Development Workflow

1. Make changes to migration files
2. Run tests to verify changes
3. Fix any failing tests
4. Commit changes with passing tests

The test suite ensures database migrations are always applied correctly and the schema remains consistent across environments.