#!/bin/bash
set -euo pipefail

# Terraform format checking script

echo "Checking Terraform formatting..."

# Find all Terraform files
terraform_files=$(find . -name "*.tf" -o -name "*.tfvars")

if [ -z "$terraform_files" ]; then
    echo "No Terraform files found."
    exit 0
fi

exit_code=0

# Check if files are properly formatted
if ! terraform fmt -check=true -diff=true $terraform_files; then
    echo "ERROR: Some Terraform files are not properly formatted!"
    echo "Run 'terraform fmt -recursive' to fix formatting issues."
    exit_code=1
else
    echo "SUCCESS: All Terraform files are properly formatted!"
fi

exit $exit_code