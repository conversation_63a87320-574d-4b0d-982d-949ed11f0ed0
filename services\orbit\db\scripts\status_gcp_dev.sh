#!/bin/bash
set -e

echo "Checking GCP dev database status..."

# Get the workspace root - when running via <PERSON><PERSON>, we need to navigate to the workspace
if [ -n "$BUILD_WORKSPACE_DIRECTORY" ]; then
    cd "$BUILD_WORKSPACE_DIRECTORY"
elif [ -n "$TEST_WORKSPACE" ]; then
    cd "$TEST_WORKSPACE/.."
else
    # Find the workspace root by looking for WORKSPACE file
    CURRENT_DIR="$PWD"
    while [ "$CURRENT_DIR" != "/" ]; do
        if [ -f "$CURRENT_DIR/WORKSPACE" ]; then
            cd "$CURRENT_DIR"
            break
        fi
        CURRENT_DIR="$(dirname "$CURRENT_DIR")"
    done
fi

# Navigate to terraform dev environment to get current values
cd terraform/environments/dev

# Get database connection details from terraform outputs
echo "Getting database connection details from Terraform..."
DATABASE_CONNECTION_NAME=$(terraform output -raw database_connection_name)
DATABASE_NAME=$(terraform output -raw database_name)
DATABASE_USER=$(terraform output -raw database_user)
DATABASE_PRIVATE_IP=$(terraform output -raw database_private_ip)
PROJECT_ID=$(terraform output -json dev_access_info | jq -r '.project_id')

echo "Database connection details:"
echo "  Connection name: $DATABASE_CONNECTION_NAME"
echo "  Database name: $DATABASE_NAME"
echo "  Database user: $DATABASE_USER"
echo "  Private IP: $DATABASE_PRIVATE_IP"
echo "  Project ID: $PROJECT_ID"

# Get database password from Secret Manager
echo "Getting database password from Secret Manager..."
DATABASE_PASSWORD=$(gcloud secrets versions access latest --secret=platform-db-password)

if [ -z "$DATABASE_PASSWORD" ]; then
    echo "ERROR: Could not retrieve database password from Secret Manager"
    exit 1
fi

# Return to workspace root
cd ../../..

# Navigate to platform/db directory
cd platform/db

# Create GCP-specific flyway configuration
echo "Creating GCP-specific flyway configuration..."
cat > flyway-gcp.conf << EOF
# Flyway Configuration for GCP Cloud SQL
flyway.url=jdbc:postgresql://${DATABASE_PRIVATE_IP}:5432/${DATABASE_NAME}
flyway.user=${DATABASE_USER}
flyway.password=${DATABASE_PASSWORD}
flyway.schemas=public
flyway.locations=filesystem:./migrations
flyway.baselineOnMigrate=true
flyway.baselineVersion=0
flyway.validateOnMigrate=true
flyway.cleanDisabled=true
flyway.connectRetries=3
flyway.connectRetriesInterval=10
EOF

# Check if Cloud SQL Proxy is installed
if ! command -v cloud-sql-proxy &> /dev/null; then
    echo "Cloud SQL Proxy is not installed. Using docker-based approach..."
    USE_DOCKER=true
else
    echo "Using local Cloud SQL Proxy..."
    USE_DOCKER=false
fi

if [ "$USE_DOCKER" = "true" ]; then
    echo "Checking status via Docker with Cloud SQL Proxy..."
    
    # Stop any existing proxy containers
    docker stop gcp-sql-proxy 2>/dev/null || true
    docker rm gcp-sql-proxy 2>/dev/null || true
    
    # Create a Docker network for the proxy and flyway
    docker network create gcp-migration-network 2>/dev/null || true
    
    # Start Cloud SQL Proxy in a container
    echo "Starting Cloud SQL Proxy..."
    docker run -d \
        --name gcp-sql-proxy \
        --network gcp-migration-network \
        -v "$HOME/.config/gcloud:/config" \
        -e GOOGLE_CONFIG_DIR=/config \
        gcr.io/cloudsql-docker/gce-proxy:1.33.2 \
        /cloud_sql_proxy \
        -instances=${DATABASE_CONNECTION_NAME}=tcp:0.0.0.0:5432 \
        -credential_file=/config/application_default_credentials.json
    
    # Wait for proxy to be ready
    echo "Waiting for Cloud SQL Proxy to be ready..."
    sleep 10
    
    # Run Flyway info using Docker image
    echo "Getting migration status..."
    docker run --rm \
        --network gcp-migration-network \
        -v "$(pwd)/migrations:/flyway/sql" \
        -v "$(pwd)/flyway-gcp.conf:/flyway/conf/flyway.conf" \
        -e FLYWAY_URL="************************************/${DATABASE_NAME}" \
        -e FLYWAY_USER="${DATABASE_USER}" \
        -e FLYWAY_PASSWORD="${DATABASE_PASSWORD}" \
        -e FLYWAY_LOCATIONS="filesystem:/flyway/sql" \
        -e FLYWAY_BASELINE_ON_MIGRATE="true" \
        flyway/flyway:10-alpine \
        info
    
    # Cleanup
    echo "Cleaning up..."
    docker stop gcp-sql-proxy
    docker rm gcp-sql-proxy
    docker network rm gcp-migration-network 2>/dev/null || true
    
else
    echo "Starting Cloud SQL Proxy..."
    # Start Cloud SQL Proxy in background
    cloud-sql-proxy ${DATABASE_CONNECTION_NAME} &
    PROXY_PID=$!
    
    # Wait for proxy to be ready
    echo "Waiting for Cloud SQL Proxy to be ready..."
    sleep 10
    
    # Test connection
    if ! pg_isready -h 127.0.0.1 -p 5432 -U ${DATABASE_USER} -d ${DATABASE_NAME}; then
        echo "ERROR: Cannot connect to database through Cloud SQL Proxy"
        kill $PROXY_PID 2>/dev/null || true
        exit 1
    fi
    
    # Run Flyway info
    echo "Getting migration status..."
    if command -v flyway &> /dev/null; then
        flyway -configFiles=flyway-gcp.conf info
    else
        echo "Flyway not found locally, using Docker..."
        docker run --rm \
            --network="host" \
            -v "$(pwd)/migrations:/flyway/sql" \
            -v "$(pwd)/flyway-gcp.conf:/flyway/conf/flyway.conf" \
            flyway/flyway:10-alpine \
            info
    fi
    
    # Stop Cloud SQL Proxy
    kill $PROXY_PID 2>/dev/null || true
fi

# Clean up temporary config file
rm -f flyway-gcp.conf

echo ""
echo "GCP database status check completed!"
echo ""
echo "Database connection details:"
echo "  Instance: $DATABASE_CONNECTION_NAME"
echo "  Database: $DATABASE_NAME"
echo "  Private IP: $DATABASE_PRIVATE_IP"