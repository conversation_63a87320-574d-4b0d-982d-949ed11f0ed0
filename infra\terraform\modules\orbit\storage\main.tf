# Cloud Storage Module for Static Web Hosting
# Creates Cloud Storage bucket for hosting React application static files

terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
  }
}

# Enable Storage API
resource "google_project_service" "storage" {
  service = "storage.googleapis.com"
  
  disable_dependent_services = true
}

# Cloud Storage bucket for static web files
resource "google_storage_bucket" "web_bucket" {
  name     = "${var.project_name}-web-${var.environment}-${random_string.bucket_suffix.result}"
  location = var.bucket_location
  
  # Bucket configuration
  storage_class                 = var.storage_class
  uniform_bucket_level_access   = true
  public_access_prevention      = var.enable_public_access ? "inherited" : "enforced"
  
  
  # Versioning configuration
  versioning {
    enabled = var.enable_versioning
  }

  # Lifecycle management
  dynamic "lifecycle_rule" {
    for_each = var.lifecycle_rules
    content {
      action {
        type = lifecycle_rule.value.action
        storage_class = lifecycle_rule.value.action == "SetStorageClass" ? lifecycle_rule.value.target_storage_class : null
      }
      
      condition {
        age                   = lifecycle_rule.value.age_days
        matches_storage_class = lifecycle_rule.value.matches_storage_class
      }
    }
  }

  # CORS configuration for web hosting
  cors {
    origin          = var.cors_origins
    method          = ["GET", "HEAD", "OPTIONS"]
    response_header = ["*"]
    max_age_seconds = 3600
  }

  # Website configuration
  website {
    main_page_suffix = var.main_page_suffix
    not_found_page   = var.not_found_page
  }

  # Logging configuration
  dynamic "logging" {
    for_each = var.enable_access_logs ? [1] : []
    content {
      log_bucket        = var.access_log_bucket != "" ? var.access_log_bucket : google_storage_bucket.access_logs[0].name
      log_object_prefix = "web-access-logs/"
    }
  }

  depends_on = [google_project_service.storage]
}

# Random suffix for bucket name uniqueness
resource "random_string" "bucket_suffix" {
  length  = 8
  special = false
  upper   = false
}

# Access logs bucket (optional)
resource "google_storage_bucket" "access_logs" {
  count = var.enable_access_logs && var.access_log_bucket == "" ? 1 : 0
  
  name     = "${var.project_name}-web-logs-${var.environment}-${random_string.bucket_suffix.result}"
  location = var.bucket_location
  
  storage_class               = "COLDLINE"
  uniform_bucket_level_access = true
  public_access_prevention    = "enforced"

  lifecycle_rule {
    action {
      type = "Delete"
    }
    condition {
      age = 90  # Delete access logs after 90 days
    }
  }
}

# IAM binding for public read access (only if explicitly enabled)
# For production, consider using signed URLs or IAM conditions
resource "google_storage_bucket_iam_binding" "public_read" {
  count = var.enable_public_access ? 1 : 0
  
  bucket = google_storage_bucket.web_bucket.name
  role   = "roles/storage.objectViewer"
  
  members = ["allUsers"]
  
}

# IAM binding for Cloud Build to upload files
resource "google_storage_bucket_iam_binding" "cloud_build_writer" {
  count = var.enable_cloud_build_access ? 1 : 0
  
  bucket = google_storage_bucket.web_bucket.name
  role   = "roles/storage.objectAdmin"
  
  members = [
    "serviceAccount:${data.google_project.current.number}@cloudbuild.gserviceaccount.com",
  ]
}

# Custom IAM bindings
resource "google_storage_bucket_iam_binding" "custom_readers" {
  for_each = var.additional_readers
  
  bucket = google_storage_bucket.web_bucket.name
  role   = "roles/storage.objectViewer"
  
  members = each.value
}

resource "google_storage_bucket_iam_binding" "custom_writers" {
  for_each = var.additional_writers
  
  bucket = google_storage_bucket.web_bucket.name
  role   = "roles/storage.objectAdmin"
  
  members = each.value
}

# Cloud Build trigger for automated deployment (optional)
resource "google_cloudbuild_trigger" "web_deploy" {
  count = var.enable_automated_deployment ? 1 : 0
  
  name        = "${var.project_name}-web-deploy"
  description = "Automated deployment of web application to Cloud Storage"

  github {
    owner = var.github_repo_owner
    name  = var.github_repo_name
    push {
      branch = var.deploy_branch
    }
  }

  substitutions = {
    _BUCKET_NAME = google_storage_bucket.web_bucket.name
    _PROJECT_ID  = data.google_project.current.project_id
  }

  filename = var.cloudbuild_file_path

  depends_on = [
    google_project_service.cloudbuild,
    google_storage_bucket.web_bucket
  ]
}

# Enable Cloud Build API for automated deployment
resource "google_project_service" "cloudbuild" {
  count   = var.enable_automated_deployment ? 1 : 0
  service = "cloudbuild.googleapis.com"
  
  disable_dependent_services = true
}

# Data source to get current project information
data "google_project" "current" {}

# Upload default index.html if specified
resource "google_storage_bucket_object" "default_index" {
  count = var.upload_default_files ? 1 : 0
  
  name   = "index.html"
  bucket = google_storage_bucket.web_bucket.name
  
  content = templatefile("${path.module}/templates/index.html", {
    project_name = var.project_name
    environment  = var.environment
  })
  
  content_type = "text/html"
}

# Upload default 404.html if specified
resource "google_storage_bucket_object" "default_404" {
  count = var.upload_default_files ? 1 : 0
  
  name   = "404.html"
  bucket = google_storage_bucket.web_bucket.name
  
  content = templatefile("${path.module}/templates/404.html", {
    project_name = var.project_name
    environment  = var.environment
  })
  
  content_type = "text/html"
}