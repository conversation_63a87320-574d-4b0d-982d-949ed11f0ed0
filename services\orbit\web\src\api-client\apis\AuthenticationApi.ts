/* tslint:disable */
/* eslint-disable */
/**
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  AuthResponse,
  OAuthRedirectResponse,
  OAuthSignInRequest,
  Session,
  SignInRequest,
  User,
} from '../models/index';
import {
    AuthResponseFromJSON,
    AuthResponseToJSON,
    OAuthRedirectResponseFromJSON,
    OAuthRedirectResponseToJSON,
    OAuthSignInRequestFromJSON,
    OAuthSignInRequestToJSON,
    SessionFromJSON,
    SessionToJSON,
    SignInRequestFromJSON,
    SignInRequestToJSON,
    UserFromJ<PERSON><PERSON>,
    UserToJSO<PERSON>,
} from '../models/index';

export interface AuthSigninOauthPostRequest {
    oAuthSignInRequest: OAuthSignInRequest;
}

export interface AuthSigninPostRequest {
    signInRequest: SignInRequest;
}

/**
 * 
 */
export class AuthenticationApi extends runtime.BaseAPI {

    /**
     * Retrieve the current user session
     * Get current session
     */
    async authSessionGetRaw(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Session>> {
        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        const response = await this.request({
            path: `/auth/session`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => SessionFromJSON(jsonValue));
    }

    /**
     * Retrieve the current user session
     * Get current session
     */
    async authSessionGet(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Session> {
        const response = await this.authSessionGetRaw(initOverrides);
        return await response.value();
    }

    /**
     * Authenticate user with OAuth provider (Google)
     * Sign in with OAuth
     */
    async authSigninOauthPostRaw(requestParameters: AuthSigninOauthPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<OAuthRedirectResponse>> {
        if (requestParameters['oAuthSignInRequest'] == null) {
            throw new runtime.RequiredError(
                'oAuthSignInRequest',
                'Required parameter "oAuthSignInRequest" was null or undefined when calling authSigninOauthPost().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        const response = await this.request({
            path: `/auth/signin/oauth`,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: OAuthSignInRequestToJSON(requestParameters['oAuthSignInRequest']),
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => OAuthRedirectResponseFromJSON(jsonValue));
    }

    /**
     * Authenticate user with OAuth provider (Google)
     * Sign in with OAuth
     */
    async authSigninOauthPost(requestParameters: AuthSigninOauthPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<OAuthRedirectResponse> {
        const response = await this.authSigninOauthPostRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Authenticate user with email and password
     * Sign in with email/password
     */
    async authSigninPostRaw(requestParameters: AuthSigninPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<AuthResponse>> {
        if (requestParameters['signInRequest'] == null) {
            throw new runtime.RequiredError(
                'signInRequest',
                'Required parameter "signInRequest" was null or undefined when calling authSigninPost().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        const response = await this.request({
            path: `/auth/signin`,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: SignInRequestToJSON(requestParameters['signInRequest']),
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => AuthResponseFromJSON(jsonValue));
    }

    /**
     * Authenticate user with email and password
     * Sign in with email/password
     */
    async authSigninPost(requestParameters: AuthSigninPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<AuthResponse> {
        const response = await this.authSigninPostRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * End the current user session
     * Sign out
     */
    async authSignoutPostRaw(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<void>> {
        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("bearerAuth", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/auth/signout`,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.VoidApiResponse(response);
    }

    /**
     * End the current user session
     * Sign out
     */
    async authSignoutPost(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void> {
        await this.authSignoutPostRaw(initOverrides);
    }

    /**
     * Retrieve current authenticated user information
     * Get current user
     */
    async authUserGetRaw(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<User>> {
        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("bearerAuth", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/auth/user`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => UserFromJSON(jsonValue));
    }

    /**
     * Retrieve current authenticated user information
     * Get current user
     */
    async authUserGet(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<User> {
        const response = await this.authUserGetRaw(initOverrides);
        return await response.value();
    }

}
