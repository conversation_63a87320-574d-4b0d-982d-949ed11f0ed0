#!/bin/bash
set -euo pipefail

# Complete setup script for new environment deployment
# This script handles all OAuth and deployment configuration from scratch

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_error() {
    echo -e "${RED}❌ Error: $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_step() {
    echo -e "${BLUE}🚀 $1${NC}"
}

print_banner() {
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "🔧 $1"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
}

# Check if running from workspace root
if [ ! -f "WORKSPACE" ]; then
    print_error "This script must be run from the workspace root directory"
    exit 1
fi

print_banner "NEW ENVIRONMENT SETUP"
echo ""
print_info "This script will set up a complete development environment with OAuth functionality"
echo ""

# Step 1: Check prerequisites
print_step "Step 1: Checking prerequisites"
echo ""

# Check if gcloud is installed and authenticated
if ! command -v gcloud &> /dev/null; then
    print_error "gcloud CLI is not installed. Please install it first:"
    echo "  https://cloud.google.com/sdk/docs/install"
    exit 1
fi

# Check if authenticated
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q "@"; then
    print_error "Please authenticate with gcloud first:"
    echo "  gcloud auth login"
    exit 1
fi

# Check if Bazel is installed
if ! command -v bazel &> /dev/null; then
    print_error "Bazel is not installed. Please install it first:"
    echo "  https://bazel.build/install"
    exit 1
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install it first:"
    echo "  https://docs.docker.com/get-docker/"
    exit 1
fi

# Check if Terraform is installed
if ! command -v terraform &> /dev/null; then
    print_error "Terraform is not installed. Please install it first:"
    echo "  https://learn.hashicorp.com/tutorials/terraform/install-cli"
    exit 1
fi

print_success "All prerequisites are installed"
echo ""

# Step 2: Project setup
print_step "Step 2: Setting up Google Cloud project"
echo ""

# Get current project
CURRENT_PROJECT=$(gcloud config get-value project 2>/dev/null || echo "")
if [ -z "$CURRENT_PROJECT" ]; then
    print_error "No Google Cloud project is set. Please set your project:"
    echo "  gcloud config set project YOUR_PROJECT_ID"
    exit 1
fi

print_info "Using Google Cloud project: $CURRENT_PROJECT"

# Enable required APIs
print_info "Enabling required Google Cloud APIs..."
gcloud services enable \
    compute.googleapis.com \
    container.googleapis.com \
    artifactregistry.googleapis.com \
    secretmanager.googleapis.com \
    sql-component.googleapis.com \
    sqladmin.googleapis.com \
    storage.googleapis.com \
    iam.googleapis.com \
    cloudresourcemanager.googleapis.com

print_success "Google Cloud APIs enabled"
echo ""

# Step 3: OAuth configuration
print_step "Step 3: Setting up OAuth configuration"
echo ""

# Check if OAuth credentials are provided
if [[ -n "${GOOGLE_OAUTH_CLIENT_ID:-}" && -n "${GOOGLE_OAUTH_CLIENT_SECRET:-}" ]]; then
    print_info "Using OAuth credentials from environment variables"
    export GOOGLE_OAUTH_CLIENT_ID
    export GOOGLE_OAUTH_CLIENT_SECRET
    
    # Run OAuth setup script
    if bazel run //terraform:setup_oauth_secrets; then
        print_success "OAuth secrets configured in Secret Manager"
    else
        print_error "Failed to configure OAuth secrets"
        exit 1
    fi
else
    print_warning "OAuth credentials not provided in environment variables"
    print_info "Creating placeholder OAuth secrets that you'll need to update"
    
    # Run OAuth setup script without credentials
    if bazel run //terraform:setup_oauth_secrets; then
        print_success "Placeholder OAuth secrets created"
        echo ""
        print_warning "IMPORTANT: You need to update OAuth secrets with real values:"
        echo "1. Go to Google Cloud Console → APIs & Services → Credentials"
        echo "2. Create OAuth 2.0 Client ID (Web application)"
        echo "3. Set authorized redirect URIs to: https://your-domain.com/auth/callback/google"
        echo "4. Update the secrets with real values:"
        echo "   echo 'real-client-id' | gcloud secrets versions add oauth-client-id --data-file=-"
        echo "   echo 'real-client-secret' | gcloud secrets versions add oauth-client-secret --data-file=-"
        echo ""
        
        # Wait for user confirmation
        read -p "Press Enter after you've updated the OAuth secrets, or 'c' to continue with placeholders: " -n 1 -r
        echo ""
        if [[ ! $REPLY =~ ^[Cc]$ ]]; then
            print_info "Continuing with placeholder secrets (you can update them later)"
        fi
    else
        print_error "Failed to create OAuth secrets"
        exit 1
    fi
fi

echo ""

# Step 4: Terraform infrastructure
print_step "Step 4: Setting up Terraform infrastructure"
echo ""

cd terraform/environments/dev

# Initialize Terraform
print_info "Initializing Terraform..."
if terraform init; then
    print_success "Terraform initialized"
else
    print_error "Failed to initialize Terraform"
    exit 1
fi

# Check if terraform.tfvars exists
if [ ! -f "terraform.tfvars" ]; then
    print_info "Creating terraform.tfvars file..."
    
    # Create a basic tfvars file
    cat > terraform.tfvars << EOF
# Project configuration
project_id = "$CURRENT_PROJECT"
region     = "us-central1"
zone       = "us-central1-a"

# Environment
environment = "dev"

# Instance configuration
instance_name = "platform-dev"
machine_type  = "e2-medium"

# Database configuration
database_name = "platform_dev"
database_user = "platform_user"

# Domain configuration (update this with your actual domain)
domain = "internal.dev.twodot.ai"

# OAuth configuration
oauth_redirect_url = "https://internal.dev.twodot.ai/auth/callback/google"

# Enable features
enable_https = true
enable_monitoring = true
EOF

    print_success "terraform.tfvars created"
    print_warning "Please review and update terraform.tfvars with your specific configuration"
fi

# Plan and apply Terraform
print_info "Planning Terraform deployment..."
if terraform plan -out=tfplan; then
    print_success "Terraform plan created"
    
    echo ""
    print_info "Applying Terraform configuration..."
    if terraform apply tfplan; then
        print_success "Terraform infrastructure deployed"
    else
        print_error "Failed to apply Terraform configuration"
        exit 1
    fi
else
    print_error "Failed to create Terraform plan"
    exit 1
fi

# Return to workspace root
cd ../../..

print_success "Infrastructure deployment completed"
echo ""

# Step 5: Build and deploy backend services
print_step "Step 5: Building and deploying backend services"
echo ""

print_info "Building and pushing backend Docker images..."
if bazel run //terraform:build_and_push_backend_dev; then
    print_success "Backend services built and pushed"
else
    print_error "Failed to build and push backend services"
    exit 1
fi

echo ""

# Step 6: Deploy to compute instance
print_step "Step 6: Deploying to compute instance"
echo ""

print_info "Deploying services to compute instance..."
if bazel run //terraform:deploy_to_instance_dev; then
    print_success "Services deployed to compute instance"
else
    print_error "Failed to deploy to compute instance"
    exit 1
fi

echo ""

# Step 7: Run database migrations
print_step "Step 7: Running database migrations"
echo ""

print_info "Running database migrations..."
if bazel run //terraform:migrate_db_dev; then
    print_success "Database migrations completed"
else
    print_error "Failed to run database migrations"
    exit 1
fi

echo ""

# Step 8: Deploy web application
print_step "Step 8: Deploying web application"
echo ""

print_info "Building and deploying web application..."
if bazel run //terraform:deploy_web_dev; then
    print_success "Web application deployed"
else
    print_error "Failed to deploy web application"
    exit 1
fi

echo ""

# Step 9: Final summary
print_banner "DEPLOYMENT COMPLETED SUCCESSFULLY!"
echo ""

# Get deployment information
cd terraform/environments/dev
INSTANCE_NAME=$(terraform output -raw instance_name 2>/dev/null || echo "platform-dev")
EXTERNAL_IP=$(terraform output -raw external_ip 2>/dev/null || echo "N/A")
DOMAIN=$(terraform output -raw domain 2>/dev/null || echo "N/A")
cd ../../..

print_success "Your platform is now deployed and ready to use!"
echo ""
echo "🌐 Deployment Information:"
echo "  • Instance Name: $INSTANCE_NAME"
echo "  • External IP: $EXTERNAL_IP"
echo "  • Domain: $DOMAIN"
echo "  • Project: $CURRENT_PROJECT"
echo ""
echo "🔗 Access URLs:"
echo "  • Web Application: https://$DOMAIN"
echo "  • API Gateway: https://$DOMAIN/api/v1"
echo "  • Health Check: https://$DOMAIN/health"
echo ""
echo "🔐 OAuth Configuration:"
echo "  • OAuth secrets have been set up in Secret Manager"
echo "  • Update redirect URLs in Google Cloud Console to: https://$DOMAIN/auth/callback/google"
echo ""
echo "🚀 Next Steps:"
echo "  1. Update OAuth client redirect URIs in Google Cloud Console"
echo "  2. Test the OAuth flow at https://$DOMAIN"
echo "  3. Monitor logs: gcloud compute ssh $INSTANCE_NAME --command='docker logs platform-auth'"
echo ""
echo "📝 Common Commands:"
echo "  • Update backend: bazel run //terraform:build_and_push_backend_dev"
echo "  • Deploy web: bazel run //terraform:deploy_web_dev"
echo "  • Run migrations: bazel run //terraform:migrate_db_dev"
echo "  • SSH to instance: gcloud compute ssh $INSTANCE_NAME"
echo ""
print_success "Setup completed! Your platform is ready to use. 🎉"