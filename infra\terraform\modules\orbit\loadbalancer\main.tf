# Load Balancer Module for GCP Platform
# Creates HTTP(S) Load Balancer with SSL termination and traffic routing

terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
  }
}

# Enable required APIs
resource "google_project_service" "compute" {
  service = "compute.googleapis.com"
  
  disable_dependent_services = true
}

# Global static IP address for the load balancer
resource "google_compute_global_address" "lb_ip" {
  name = "${var.project_name}-lb-ip-${var.environment}"
  
  description = "Static IP address for ${var.project_name} load balancer"
}

# SSL certificate (managed or self-managed)
resource "google_compute_managed_ssl_certificate" "ssl_cert" {
  count = var.enable_ssl && var.use_managed_ssl ? 1 : 0
  
  name = "${var.project_name}-ssl-cert-${var.environment}"

  managed {
    domains = var.ssl_domains
  }

  lifecycle {
    create_before_destroy = true
  }
}

# Backend bucket for static files (Cloud Storage)
resource "google_compute_backend_bucket" "static_backend" {
  name        = "${var.project_name}-static-backend-${var.environment}"
  bucket_name = var.storage_bucket_name
  enable_cdn  = var.enable_cdn

  dynamic "cdn_policy" {
    for_each = var.enable_cdn ? [1] : []
    content {
      cache_mode                   = "CACHE_ALL_STATIC"
      default_ttl                  = var.cdn_default_ttl
      max_ttl                      = var.cdn_max_ttl
      client_ttl                   = var.cdn_client_ttl
      negative_caching             = true
      serve_while_stale            = var.cdn_serve_while_stale

      negative_caching_policy {
        code = 404
        ttl  = 120
      }

      negative_caching_policy {
        code = 410
        ttl  = 120
      }
    }
  }
}

# URL map for routing traffic
resource "google_compute_url_map" "lb_url_map" {
  name            = "${var.project_name}-url-map-${var.environment}"
  default_service = google_compute_backend_bucket.static_backend.id

  # Host rule (use wildcard for dev, specific domains for prod)
  host_rule {
    hosts        = length(var.ssl_domains) > 0 ? var.ssl_domains : ["*"]
    path_matcher = "main"
  }

  path_matcher {
    name            = "main"  
    default_service = google_compute_backend_bucket.static_backend.id

    # API routes go to compute backend
    path_rule {
      paths   = ["/api/*"]
      service = var.api_backend_service
    }

    # Health check route for backend services
    path_rule {
      paths   = ["/health"]
      service = var.api_backend_service
    }

    # All other routes (/, /companies, /deals, etc.) go to storage bucket for React SPA
    # No need for /* path rule - default_service handles all unmatched paths
  }
}

# HTTP to HTTPS redirect
resource "google_compute_url_map" "http_redirect" {
  count = var.enable_ssl && var.enable_http_redirect ? 1 : 0
  
  name = "${var.project_name}-http-redirect-${var.environment}"

  default_url_redirect {
    https_redirect         = true
    redirect_response_code = "MOVED_PERMANENTLY_DEFAULT"
    strip_query            = false
  }
}

# HTTP target proxy (for redirect)
resource "google_compute_target_http_proxy" "http_proxy_redirect" {
  count = var.enable_ssl && var.enable_http_redirect ? 1 : 0
  
  name    = "${var.project_name}-http-proxy-redirect-${var.environment}"
  url_map = google_compute_url_map.http_redirect[0].id
}

# HTTP target proxy (for direct traffic when redirect is disabled)
resource "google_compute_target_http_proxy" "http_proxy_direct" {
  count = var.enable_ssl && !var.enable_http_redirect ? 1 : 0
  
  name    = "${var.project_name}-http-proxy-direct-${var.environment}"
  url_map = google_compute_url_map.lb_url_map.id
}

# HTTPS target proxy
resource "google_compute_target_https_proxy" "https_proxy" {
  count = var.enable_ssl ? 1 : 0
  
  name             = "${var.project_name}-https-proxy-${var.environment}"
  url_map          = google_compute_url_map.lb_url_map.id
  ssl_certificates = var.use_managed_ssl ? [google_compute_managed_ssl_certificate.ssl_cert[0].id] : var.ssl_certificate_ids

  ssl_policy = var.ssl_policy_name != "" ? var.ssl_policy_name : null
}

# HTTP target proxy (for non-SSL deployments)
resource "google_compute_target_http_proxy" "http_proxy_main" {
  count = !var.enable_ssl ? 1 : 0
  
  name    = "${var.project_name}-http-proxy-${var.environment}"
  url_map = google_compute_url_map.lb_url_map.id
}

# Global forwarding rule for HTTPS
resource "google_compute_global_forwarding_rule" "https_rule" {
  count = var.enable_ssl ? 1 : 0
  
  name       = "${var.project_name}-https-rule-${var.environment}"
  target     = google_compute_target_https_proxy.https_proxy[0].id
  port_range = "443"
  ip_address = google_compute_global_address.lb_ip.id
}

# Global forwarding rule for HTTP
resource "google_compute_global_forwarding_rule" "http_rule" {
  name       = "${var.project_name}-http-rule-${var.environment}"
  target     = var.enable_ssl && var.enable_http_redirect ? google_compute_target_http_proxy.http_proxy_redirect[0].id : (var.enable_ssl ? google_compute_target_http_proxy.http_proxy_direct[0].id : google_compute_target_http_proxy.http_proxy_main[0].id)
  port_range = "80"
  ip_address = google_compute_global_address.lb_ip.id
  
  depends_on = [
    google_compute_target_http_proxy.http_proxy_redirect,
    google_compute_target_http_proxy.http_proxy_direct,
    google_compute_target_http_proxy.http_proxy_main
  ]
}

# SSL policy for security
resource "google_compute_ssl_policy" "ssl_policy" {
  count = var.enable_ssl && var.create_ssl_policy ? 1 : 0
  
  name            = "${var.project_name}-ssl-policy-${var.environment}"
  profile         = var.ssl_policy_profile
  min_tls_version = var.ssl_min_tls_version

  description = "SSL policy for ${var.project_name} load balancer"
}

# Cloud Armor security policy (optional)
resource "google_compute_security_policy" "security_policy" {
  count = var.enable_cloud_armor ? 1 : 0
  
  name        = "${var.project_name}-security-policy-${var.environment}"
  description = "Cloud Armor security policy for ${var.project_name}"

  # Default rule - allow all traffic
  rule {
    action   = "allow"
    priority = "2147483647"
    match {
      versioned_expr = "SRC_IPS_V1"
      config {
        src_ip_ranges = ["*"]
      }
    }
    description = "Default allow rule"
  }

  # Rate limiting rule
  dynamic "rule" {
    for_each = var.rate_limit_requests_per_minute > 0 ? [1] : []
    content {
      action   = "rate_based_ban"
      priority = "1000"
      match {
        versioned_expr = "SRC_IPS_V1"
        config {
          src_ip_ranges = ["*"]
        }
      }
      rate_limit_options {
        conform_action = "allow"
        exceed_action  = "deny(429)"
        enforce_on_key = "IP"
        rate_limit_threshold {
          count        = var.rate_limit_requests_per_minute
          interval_sec = 60
        }
        ban_duration_sec = 600  # 10 minutes
      }
      description = "Rate limiting rule"
    }
  }

  # Geographic restriction rules
  dynamic "rule" {
    for_each = var.blocked_regions
    content {
      action   = "deny(403)"
      priority = 500 + rule.key
      match {
        expr {
          expression = "origin.region_code == '${rule.value}'"
        }
      }
      description = "Block traffic from ${rule.value}"
    }
  }

  # IP whitelist/blacklist rules
  dynamic "rule" {
    for_each = var.blocked_ip_ranges
    content {
      action   = "deny(403)"
      priority = 100 + rule.key
      match {
        versioned_expr = "SRC_IPS_V1"
        config {
          src_ip_ranges = [rule.value]
        }
      }
      description = "Block IP range ${rule.value}"
    }
  }
}

# Note: Security policy should be attached to the backend service directly
# in the compute module where the backend service is created