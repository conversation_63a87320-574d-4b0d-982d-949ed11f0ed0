load("@rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "middleware",
    srcs = [
        "auth.go",
        "cors.go",
    ],
    importpath = "github.com/TwoDotAi/mono/services/orbit/auth/middleware",
    visibility = ["//visibility:public"],
    deps = [
        "//services/orbit/auth/config",
        "@com_github_gin_gonic_gin//:gin",
        "@com_github_golang_jwt_jwt_v5//:jwt",
    ],
)

go_test(
    name = "middleware_test",
    srcs = [
        "auth_test.go",
        "cors_test.go",
    ],
    embed = [":middleware"],
    deps = [
        "@com_github_gin_gonic_gin//:gin",
        "@com_github_golang_jwt_jwt_v5//:jwt",
    ],
)
