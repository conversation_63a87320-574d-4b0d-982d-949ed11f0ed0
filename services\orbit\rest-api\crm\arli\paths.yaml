# Arli Document Processing paths with named anchors
arli-documents: &arli-documents
  get:
    tags: [Arli]
    summary: List documents
    description: Retrieve a list of processed documents
    parameters:
      - name: filter_tags
        in: query
        schema:
          type: array
          items:
            type: string
        description: Filter by tags
      - name: metadata_filter
        in: query
        schema:
          type: object
        description: Filter by metadata properties
      - name: limit
        in: query
        schema:
          type: integer
          minimum: 1
          maximum: 100
          default: 20
      - name: offset
        in: query
        schema:
          type: integer
          minimum: 0
          default: 0
    responses:
      '200':
        description: List of documents
        content:
          application/json:
            schema:
              type: object
              properties:
                documents:
                  type: array
                  items:
                    $ref: './schemas.yaml#/Document'
                total_count:
                  type: integer
                has_more:
                  type: boolean
      '400':
        description: Invalid input
        content:
          application/json:
            schema:
              $ref: '../../auth/schemas.yaml#/ValidationError'

  post:
    tags: [Arli]
    summary: Create document
    description: Create a new document for processing
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: './schemas.yaml#/DocumentCreate'
    responses:
      '201':
        description: Document created successfully
        content:
          application/json:
            schema:
              $ref: './schemas.yaml#/Document'
      '400':
        description: Invalid input
        content:
          application/json:
            schema:
              $ref: '../../auth/schemas.yaml#/ValidationError'

arli-documents-by-id: &arli-documents-by-id
  get:
    tags: [Arli]
    summary: Get document
    description: Retrieve a specific document by ID
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: string
          format: uuid
    responses:
      '200':
        description: Document details
        content:
          application/json:
            schema:
              $ref: './schemas.yaml#/DocumentDetails'
      '404':
        description: Document not found
        content:
          application/json:
            schema:
              $ref: '../../auth/schemas.yaml#/ErrorResponse'

  put:
    tags: [Arli]
    summary: Update document
    description: Update an existing document
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: string
          format: uuid
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: './schemas.yaml#/DocumentUpdate'
    responses:
      '200':
        description: Document updated successfully
        content:
          application/json:
            schema:
              $ref: './schemas.yaml#/Document'
      '400':
        description: Invalid input
        content:
          application/json:
            schema:
              $ref: '../../auth/schemas.yaml#/ValidationError'
      '404':
        description: Document not found
        content:
          application/json:
            schema:
              $ref: '../../auth/schemas.yaml#/ErrorResponse'

  delete:
    tags: [Arli]
    summary: Delete document
    description: Delete a document
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: string
          format: uuid
    responses:
      '204':
        description: Document deleted successfully
      '404':
        description: Document not found
        content:
          application/json:
            schema:
              $ref: '../../auth/schemas.yaml#/ErrorResponse'

arli-vectorize: &arli-vectorize
  post:
    tags: [Arli]
    summary: Vectorize document
    description: Process a document through the AI vectorization pipeline
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            title: ArliVectorizeRequest
            properties:
              document_id:
                type: string
                format: uuid
              content:
                type: string
              metadata:
                type: object
            required:
              - document_id
              - content
    responses:
      '200':
        description: Document vectorized successfully
        content:
          application/json:
            schema:
              type: object
              title: ArliVectorizeResponse
              properties:
                success:
                  type: boolean
                message:
                  type: string
                vector_id:
                  type: string
      '400':
        description: Invalid input
        content:
          application/json:
            schema:
              $ref: '../../auth/schemas.yaml#/ValidationError'
      '500':
        description: Vectorization failed
        content:
          application/json:
            schema:
              $ref: '../../auth/schemas.yaml#/ErrorResponse'

filter-tags: &filter-tags
  get:
    tags: [Arli]
    summary: List filter tags
    description: Retrieve all available filter tags
    responses:
      '200':
        description: List of filter tags
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: './schemas.yaml#/FilterTag'

  post:
    tags: [Arli]
    summary: Create filter tag
    description: Create a new filter tag
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: './schemas.yaml#/FilterTagCreate'
    responses:
      '201':
        description: Filter tag created successfully
        content:
          application/json:
            schema:
              $ref: './schemas.yaml#/FilterTag'
      '400':
        description: Invalid input
        content:
          application/json:
            schema:
              $ref: '../../auth/schemas.yaml#/ValidationError'