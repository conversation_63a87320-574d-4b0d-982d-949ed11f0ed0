#!/bin/bash
# <PERSON><PERSON>t to copy generated files to the generated/ directory

set -e

# Get the workspace root
WORKSPACE_ROOT="/Users/<USER>/Projects/multi/mono"

# Create directories if they don't exist
mkdir -p "$WORKSPACE_ROOT/generated/orbit/go/server"
mkdir -p "$WORKSPACE_ROOT/generated/orbit/go/client"
mkdir -p "$WORKSPACE_ROOT/generated/orbit/typescript"

# Copy generated Go server code
if [ -f bazel-bin/services/orbit/rest-api/server.go ]; then
    cp bazel-bin/services/orbit/rest-api/server.go "$WORKSPACE_ROOT/generated/orbit/go/server/"
    echo "Copied Go server code to generated/orbit/go/server/"
fi

# Copy generated Go client code
if [ -f bazel-bin/services/orbit/rest-api/client.go ]; then
    cp bazel-bin/services/orbit/rest-api/client.go "$WORKSPACE_ROOT/generated/orbit/go/client/"
    echo "Copied Go client code to generated/orbit/go/client/"
fi

# Copy generated TypeScript client code
if [ -f bazel-bin/services/orbit/rest-api/client.ts ]; then
    cp bazel-bin/services/orbit/rest-api/client.ts "$WORKSPACE_ROOT/generated/orbit/typescript/"
    echo "Copied TypeScript client code to generated/orbit/typescript/"
fi

echo "All generated files have been copied to the generated/ directory"