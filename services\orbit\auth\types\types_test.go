package types

import (
	"encoding/json"
	"testing"
	"time"
)

func TestSignInRequestSerialization(t *testing.T) {
	req := SignInRequest{
		Email:    "<EMAIL>",
		Password: "password123",
	}

	jsonData, err := json.Marshal(req)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("SignInRequest marshal error: %v", err)
	}

	var unmarshaled SignInRequest
	err = json.Unmarshal(jsonData, &unmarshaled)
	if err != nil {
		t.Errorf("SignInRequest unmarshal error: %v", err)
	}

	if unmarshaled.Email != req.Email {
		t.Errorf("Email = %v, want %v", unmarshaled.Email, req.Email)
	}
	if unmarshaled.Password != req.Password {
		t.Errorf("Password = %v, want %v", unmarshaled.Password, req.Password)
	}
}

func TestOAuthSignInRequestSerialization(t *testing.T) {
	req := OAuthSignInRequest{
		Provider:   "google",
		RedirectTo: "http://localhost:3000",
	}

	jsonData, err := json.Marshal(req)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("OAuthSignInRequest marshal error: %v", err)
	}

	var unmarshaled OAuthSignInRequest
	err = json.Unmarshal(jsonData, &unmarshaled)
	if err != nil {
		t.Errorf("OAuthSignInRequest unmarshal error: %v", err)
	}

	if unmarshaled.Provider != req.Provider {
		t.Errorf("Provider = %v, want %v", unmarshaled.Provider, req.Provider)
	}
	if unmarshaled.RedirectTo != req.RedirectTo {
		t.Errorf("RedirectTo = %v, want %v", unmarshaled.RedirectTo, req.RedirectTo)
	}
}

func TestAuthResponseSerialization(t *testing.T) {
	now := time.Now()
	user := User{
		ID:               "user123",
		Email:            "<EMAIL>",
		EmailConfirmedAt: now,
		CreatedAt:        now,
		UpdatedAt:        now,
	}

	response := AuthResponse{
		AccessToken:  "access-token",
		RefreshToken: "refresh-token",
		ExpiresIn:    3600,
		TokenType:    "Bearer",
		User:         user,
	}

	jsonData, err := json.Marshal(response)
	if err != nil {
		t.Errorf("AuthResponse marshal error: %v", err)
	}

	var unmarshaled AuthResponse
	err = json.Unmarshal(jsonData, &unmarshaled)
	if err != nil {
		t.Errorf("AuthResponse unmarshal error: %v", err)
	}

	if unmarshaled.AccessToken != response.AccessToken {
		t.Errorf("AccessToken = %v, want %v", unmarshaled.AccessToken, response.AccessToken)
	}
	if unmarshaled.User.ID != response.User.ID {
		t.Errorf("User.ID = %v, want %v", unmarshaled.User.ID, response.User.ID)
	}
}

func TestUserSerialization(t *testing.T) {
	now := time.Now()
	user := User{
		ID:               "user123",
		Email:            "<EMAIL>",
		EmailConfirmedAt: now,
		CreatedAt:        now,
		UpdatedAt:        now,
	}

	jsonData, err := json.Marshal(user)
	if err != nil {
		t.Errorf("User marshal error: %v", err)
	}

	var unmarshaled User
	err = json.Unmarshal(jsonData, &unmarshaled)
	if err != nil {
		t.Errorf("User unmarshal error: %v", err)
	}

	if unmarshaled.ID != user.ID {
		t.Errorf("ID = %v, want %v", unmarshaled.ID, user.ID)
	}
	if unmarshaled.Email != user.Email {
		t.Errorf("Email = %v, want %v", unmarshaled.Email, user.Email)
	}
}

func TestSessionSerialization(t *testing.T) {
	now := time.Now()
	session := Session{
		ID:        "session123",
		UserID:    "user123",
		ExpiresAt: now.Add(24 * time.Hour),
		CreatedAt: now,
	}

	jsonData, err := json.Marshal(session)
	if err != nil {
		t.Errorf("Session marshal error: %v", err)
	}

	var unmarshaled Session
	err = json.Unmarshal(jsonData, &unmarshaled)
	if err != nil {
		t.Errorf("Session unmarshal error: %v", err)
	}

	if unmarshaled.ID != session.ID {
		t.Errorf("ID = %v, want %v", unmarshaled.ID, session.ID)
	}
	if unmarshaled.UserID != session.UserID {
		t.Errorf("UserID = %v, want %v", unmarshaled.UserID, session.UserID)
	}
}

func TestValidateTokenRequestSerialization(t *testing.T) {
	req := ValidateTokenRequest{
		Token: "valid.jwt.token",
	}

	jsonData, err := json.Marshal(req)
	if err != nil {
		t.Errorf("ValidateTokenRequest marshal error: %v", err)
	}

	var unmarshaled ValidateTokenRequest
	err = json.Unmarshal(jsonData, &unmarshaled)
	if err != nil {
		t.Errorf("ValidateTokenRequest unmarshal error: %v", err)
	}

	if unmarshaled.Token != req.Token {
		t.Errorf("Token = %v, want %v", unmarshaled.Token, req.Token)
	}
}

func TestValidateTokenResponseSerialization(t *testing.T) {
	tests := []struct {
		name     string
		response ValidateTokenResponse
	}{
		{
			name: "valid token response",
			response: ValidateTokenResponse{
				Valid:  true,
				UserID: "user123",
				Email:  "<EMAIL>",
			},
		},
		{
			name: "invalid token response",
			response: ValidateTokenResponse{
				Valid: false,
				Error: "Token expired",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			jsonData, err := json.Marshal(tt.response)
			if err != nil {
				t.Errorf("ValidateTokenResponse marshal error: %v", err)
			}

			var unmarshaled ValidateTokenResponse
			err = json.Unmarshal(jsonData, &unmarshaled)
			if err != nil {
				t.Errorf("ValidateTokenResponse unmarshal error: %v", err)
			}

			if unmarshaled.Valid != tt.response.Valid {
				t.Errorf("Valid = %v, want %v", unmarshaled.Valid, tt.response.Valid)
			}
			if unmarshaled.UserID != tt.response.UserID {
				t.Errorf("UserID = %v, want %v", unmarshaled.UserID, tt.response.UserID)
			}
			if unmarshaled.Error != tt.response.Error {
				t.Errorf("Error = %v, want %v", unmarshaled.Error, tt.response.Error)
			}
		})
	}
}