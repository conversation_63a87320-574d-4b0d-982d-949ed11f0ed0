/* tslint:disable */
/* eslint-disable */
/**
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface InteractionDetailsAllOfContact
 */
export interface InteractionDetailsAllOfContact {
    /**
     * 
     * @type {string}
     * @memberof InteractionDetailsAllOfContact
     */
    id?: string;
    /**
     * 
     * @type {string}
     * @memberof InteractionDetailsAllOfContact
     */
    firstName?: string;
    /**
     * 
     * @type {string}
     * @memberof InteractionDetailsAllOfContact
     */
    lastName?: string;
    /**
     * 
     * @type {string}
     * @memberof InteractionDetailsAllOfContact
     */
    email?: string;
    /**
     * 
     * @type {string}
     * @memberof InteractionDetailsAllOfContact
     */
    phone?: string;
    /**
     * 
     * @type {string}
     * @memberof InteractionDetailsAllOfContact
     */
    jobTitle?: string;
}

/**
 * Check if a given object implements the InteractionDetailsAllOfContact interface.
 */
export function instanceOfInteractionDetailsAllOfContact(value: object): value is InteractionDetailsAllOfContact {
    return true;
}

export function InteractionDetailsAllOfContactFromJSON(json: any): InteractionDetailsAllOfContact {
    return InteractionDetailsAllOfContactFromJSONTyped(json, false);
}

export function InteractionDetailsAllOfContactFromJSONTyped(json: any, ignoreDiscriminator: boolean): InteractionDetailsAllOfContact {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'] == null ? undefined : json['id'],
        'firstName': json['first_name'] == null ? undefined : json['first_name'],
        'lastName': json['last_name'] == null ? undefined : json['last_name'],
        'email': json['email'] == null ? undefined : json['email'],
        'phone': json['phone'] == null ? undefined : json['phone'],
        'jobTitle': json['job_title'] == null ? undefined : json['job_title'],
    };
}

  export function InteractionDetailsAllOfContactToJSON(json: any): InteractionDetailsAllOfContact {
      return InteractionDetailsAllOfContactToJSONTyped(json, false);
  }

  export function InteractionDetailsAllOfContactToJSONTyped(value?: InteractionDetailsAllOfContact | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'first_name': value['firstName'],
        'last_name': value['lastName'],
        'email': value['email'],
        'phone': value['phone'],
        'job_title': value['jobTitle'],
    };
}

