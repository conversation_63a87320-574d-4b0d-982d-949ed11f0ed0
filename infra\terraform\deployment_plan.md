# Modern Microservices Deployment Plan for GCP with Terraform + Ansible

Based on analysis of the current infrastructure and modern best practices, this document outlines a comprehensive plan to modernize microservices deployment with full Terraform + Ansible automation.

## Executive Summary

**Goal**: Transform the current manual deployment process into a fully automated, GitOps-driven system where `terraform apply` handles everything from infrastructure provisioning to service deployment and configuration updates.

**Key Benefits**:
- 100% declarative infrastructure and configuration management
- Zero manual SSH access required
- Automated rollbacks and health checks
- Blue-green deployments for zero downtime
- Configuration-as-code with version control
- Scalable architecture supporting multiple environments

## Current State vs. Target State

### Current (Manual Process)
```
Code Change → Build Images → Push to Registry → SSH to VM → Run Scripts → Manual Verification
```

### Target (Automated Process)
```
Code Change → Git Push → Terraform Apply → Automated Deployment → Health Validation → Done
```

## Architecture Overview

### Infrastructure Components

1. **Terraform Layer** (Infrastructure Provisioning)
   - VM instances with auto-scaling capability
   - Load balancers with health checks
   - Artifact Registry for container images
   - Cloud SQL for database
   - Secret Manager for configuration
   - Cloud Storage for static assets

2. **Ansible Layer** (Configuration Management)
   - Service configuration templates
   - Container orchestration with Docker Compose
   - Health monitoring and auto-restart
   - Blue-green deployment automation
   - Log aggregation setup

3. **GitOps Integration**
   - Branch-based environment management
   - Automated CI/CD with Cloud Build
   - Configuration drift detection
   - Automated rollback mechanisms

## Current Architecture Analysis

### Infrastructure Components
- **Single VM Instance**: e2-standard-2 hosting all microservices
- **Services**: Gateway (8085), Auth (8004), CRM Backend (8003), Nginx (80)
- **Managed Services**: Cloud SQL, Artifact Registry, Cloud Storage, Secret Manager
- **Deployment**: Docker Compose on VM with manual image updates

### Limitations
- No auto-scaling: Single VM bottleneck
- Manual deployment: Requires SSH access
- Configuration rigidity: Environment changes require VM recreation
- No zero-downtime deployments: Services restart causes outage
- Limited monitoring and no automated rollback

## Implementation Plan

### Phase 1: Ansible Integration Foundation

#### 1.1 Terraform + Ansible Integration
```hcl
# terraform/modules/compute/main.tf
resource "google_compute_instance" "microservices" {
  # ... existing configuration ...
  
  # Remove startup script, replace with Ansible provisioning
  metadata = {
    ansible-managed = "true"
    services-config = jsonencode(var.services_config)
  }
  
  # Trigger Ansible after VM creation
  provisioner "local-exec" {
    command = "ansible-playbook -i inventory/gcp.yml playbooks/deploy-services.yml --extra-vars 'target_host=${self.name}'"
  }
}
```

#### 1.2 Ansible Directory Structure
```
terraform/ansible/
├── playbooks/
│   ├── deploy-services.yml      # Main deployment playbook
│   ├── update-config.yml        # Configuration updates
│   ├── update-images.yml        # Image updates
│   └── rollback.yml             # Rollback playbook
├── roles/
│   ├── docker/                  # Docker setup and management
│   ├── nginx/                   # Nginx configuration
│   ├── monitoring/              # Health checks and logging
│   └── microservices/           # Service-specific configuration
├── inventory/
│   ├── gcp.yml                  # Dynamic GCP inventory
│   └── group_vars/              # Environment-specific variables
├── templates/
│   ├── docker-compose.yml.j2    # Docker Compose template
│   ├── nginx.conf.j2            # Nginx configuration template
│   └── env-files/               # Service environment templates
└── vars/
    ├── dev.yml                  # Development configuration
    ├── staging.yml              # Staging configuration
    └── prod.yml                 # Production configuration
```

### Phase 2: Service Configuration Management

#### 2.1 Dynamic Service Configuration
```yaml
# terraform/ansible/vars/dev.yml
services:
  gateway:
    image: "gcr.io/{{ project_id }}/gateway:{{ gateway_version | default('latest') }}"
    port: 8085
    environment:
      LOG_LEVEL: "debug"
      AUTH_SERVICE_URL: "http://auth:8004"
    health_check:
      path: "/health"
      interval: 30s
      
  auth:
    image: "gcr.io/{{ project_id }}/auth:{{ auth_version | default('latest') }}"
    port: 8004
    environment:
      JWT_SECRET: "{{ vault_jwt_secret }}"
      DATABASE_URL: "{{ database_connection_string }}"
    health_check:
      path: "/health"
      interval: 30s
      
  crm_backend:
    image: "gcr.io/{{ project_id }}/crm-backend:{{ backend_version | default('latest') }}"
    port: 8003
    environment:
      DATABASE_URL: "{{ database_connection_string }}"
      LOG_LEVEL: "info"
    health_check:
      path: "/api/v1/health"
      interval: 30s
```

#### 2.2 Terraform Variables Integration
```hcl
# terraform/environments/dev/variables.tf
variable "service_versions" {
  description = "Docker image versions for each service"
  type = object({
    gateway_version     = string
    auth_version       = string
    backend_version    = string
  })
  default = {
    gateway_version  = "latest"
    auth_version     = "latest"
    backend_version  = "latest"
  }
}

variable "service_config" {
  description = "Service configuration overrides"
  type = map(object({
    replicas    = number
    cpu_limit   = string
    memory_limit = string
    environment = map(string)
  }))
  default = {}
}
```

### Phase 3: Deployment Automation

#### 3.1 Blue-Green Deployment Playbook
```yaml
# terraform/ansible/playbooks/deploy-services.yml
---
- name: Deploy Microservices with Blue-Green Strategy
  hosts: microservices
  become: yes
  vars:
    deployment_color: "{{ 'green' if current_color == 'blue' else 'blue' }}"
    
  tasks:
    - name: Pull new container images
      docker_image:
        name: "{{ item.value.image }}"
        source: pull
        force_source: yes
      loop: "{{ services | dict2items }}"
      
    - name: Deploy services to {{ deployment_color }} environment
      docker_compose:
        project_name: "microservices-{{ deployment_color }}"
        definition:
          version: '3.8'
          services: "{{ services | create_compose_services(deployment_color) }}"
        state: present
        
    - name: Wait for services to be healthy
      uri:
        url: "http://localhost:{{ item.value.port }}{{ item.value.health_check.path }}"
        method: GET
        status_code: 200
      retries: 30
      delay: 10
      loop: "{{ services | dict2items }}"
      
    - name: Update Nginx to point to {{ deployment_color }}
      template:
        src: nginx.conf.j2
        dest: /etc/nginx/nginx.conf
      vars:
        active_color: "{{ deployment_color }}"
      notify: reload nginx
      
    - name: Stop old {{ current_color }} environment
      docker_compose:
        project_name: "microservices-{{ current_color }}"
        state: absent
      when: current_color is defined
```

#### 3.2 Terraform Automation Integration
```hcl
# terraform/modules/deployment/main.tf
resource "null_resource" "deploy_services" {
  triggers = {
    service_versions = jsonencode(var.service_versions)
    service_config   = jsonencode(var.service_config)
    instance_id      = var.instance_id
  }
  
  provisioner "local-exec" {
    command = <<-EOT
      cd ${path.module}/../ansible
      ansible-playbook playbooks/deploy-services.yml \
        -i inventory/gcp.yml \
        --extra-vars '${jsonencode({
          services = var.services_config
          project_id = var.project_id
          environment = var.environment
        })}'
    EOT
  }
  
  depends_on = [var.instance_ready]
}
```

### Phase 4: GitOps Integration

#### 4.1 Cloud Build Configuration
```yaml
# cloudbuild.yaml
steps:
  # Build and push Docker images
  - name: 'gcr.io/cloud-builders/bazel'
    id: 'build-images'
    args: ['run', '//terraform:build_and_push_backend_${_ENV}']
    
  # Apply Terraform with new image versions
  - name: 'hashicorp/terraform:1.6'
    id: 'terraform-plan'
    dir: 'terraform/environments/${_ENV}'
    env:
      - 'TF_VAR_gateway_version=${_GATEWAY_VERSION}'
      - 'TF_VAR_auth_version=${_AUTH_VERSION}'
      - 'TF_VAR_backend_version=${_BACKEND_VERSION}'
    args: ['plan', '-out=tfplan']
    
  - name: 'hashicorp/terraform:1.6'
    id: 'terraform-apply'
    dir: 'terraform/environments/${_ENV}'
    args: ['apply', 'tfplan']
    
  # Validate deployment
  - name: 'gcr.io/cloud-builders/curl'
    id: 'health-check'
    args: ['--fail', '${_LOAD_BALANCER_URL}/api/v1/health']

substitutions:
  _ENV: 'dev'
  _GATEWAY_VERSION: '${SHORT_SHA}'
  _AUTH_VERSION: '${SHORT_SHA}'
  _BACKEND_VERSION: '${SHORT_SHA}'
```

#### 4.2 Branch-based Deployments
```hcl
# terraform/environments/dev/cloudbuild-trigger.tf
resource "google_cloudbuild_trigger" "deploy_dev" {
  name = "deploy-microservices-dev"
  
  github {
    owner = var.github_owner
    name  = var.github_repo
    push {
      branch = "^main$"
    }
  }
  
  build {
    step {
      name = "gcr.io/cloud-builders/gcloud"
      script = "gcloud builds submit --config=cloudbuild-dev.yaml"
    }
  }
  
  substitutions = {
    _ENV = "dev"
  }
}
```

### Phase 5: Advanced Features

#### 5.1 Configuration Drift Detection
```yaml
# terraform/ansible/playbooks/drift-detection.yml
- name: Detect Configuration Drift
  hosts: microservices
  tasks:
    - name: Check service configuration
      docker_compose:
        project_name: microservices
        state: present
        definition: "{{ expected_compose_config }}"
      check_mode: yes
      register: drift_check
      
    - name: Report drift
      fail:
        msg: "Configuration drift detected: {{ drift_check.diff }}"
      when: drift_check.changed
```

#### 5.2 Automated Rollback
```yaml
# terraform/ansible/playbooks/rollback.yml
- name: Automatic Rollback
  hosts: microservices
  vars:
    rollback_version: "{{ ansible_date_time.epoch | int - 3600 }}"
    
  tasks:
    - name: Get previous successful deployment
      uri:
        url: "{{ artifact_registry_api }}/tags"
      register: previous_tags
      
    - name: Deploy previous version
      include_tasks: deploy-services.yml
      vars:
        services: "{{ services | rollback_versions(previous_tags.json) }}"
```

## Implementation Timeline

### Week 1-2: Foundation
- [ ] Set up Ansible directory structure
- [ ] Create basic playbooks for service deployment
- [ ] Implement dynamic inventory for GCP
- [ ] Test Ansible integration with existing Terraform

### Week 3-4: Service Management
- [ ] Convert current deployment scripts to Ansible roles
- [ ] Implement blue-green deployment strategy
- [ ] Add health checking and monitoring
- [ ] Create configuration templates

### Week 5-6: GitOps Integration
- [ ] Set up Cloud Build triggers
- [ ] Implement branch-based deployments
- [ ] Add automated testing and validation
- [ ] Create rollback mechanisms

### Week 7-8: Advanced Features
- [ ] Implement configuration drift detection
- [ ] Add automated scaling capabilities
- [ ] Set up comprehensive monitoring
- [ ] Performance optimization and testing

## Key Technologies and Patterns

### Modern Best Practices Applied
1. **Infrastructure as Code**: All infrastructure defined in Terraform
2. **Configuration Management**: Ansible for service configuration
3. **GitOps**: Git as single source of truth for deployments
4. **Immutable Infrastructure**: New deployments create new resources
5. **Blue-Green Deployments**: Zero-downtime updates
6. **Health-First**: Automated health checks before traffic switching
7. **Declarative Everything**: No imperative scripts or manual steps

### Security Considerations
- Secrets managed via GCP Secret Manager
- Service accounts with minimal permissions
- Network segmentation between services
- Automated security scanning in CI/CD
- Audit logging for all deployments

## Benefits Realized

1. **Zero Manual Intervention**: All deployments happen via `terraform apply`
2. **Configuration as Code**: All service configs versioned and reviewable
3. **Atomic Deployments**: Services deploy together with rollback capability
4. **Environment Parity**: Identical deployment process across dev/staging/prod
5. **Audit Trail**: Complete history of all configuration changes
6. **Scalability**: Easy to add new services or environments
7. **Disaster Recovery**: Infrastructure and configuration fully reproducible

## Migration Strategy

### Step 1: Parallel Testing
- Deploy new Ansible-managed VM alongside existing VM
- Test deployment processes without affecting production

### Step 2: Traffic Shifting
- Use load balancer to gradually shift traffic
- Monitor metrics and rollback if needed

### Step 3: Full Migration
- Switch all traffic to Ansible-managed infrastructure
- Decommission old VM and scripts

## Monitoring and Operations

### Key Metrics
- Deployment success rate
- Service health status
- Configuration drift detection
- Resource utilization
- Response times and error rates

### Operational Procedures
- Automated alerts for failed deployments
- Daily configuration drift reports
- Weekly deployment metrics review
- Monthly infrastructure optimization

## Future Enhancements

1. **Kubernetes Migration**: Move from Docker Compose to K8s for better orchestration
2. **Multi-Region Support**: Deploy across multiple GCP regions
3. **Service Mesh**: Implement Istio for advanced traffic management
4. **Progressive Delivery**: Canary deployments with automatic rollback
5. **Cost Optimization**: Automated instance sizing based on load

## Conclusion

This modernization plan transforms manual, error-prone deployments into a fully automated, reliable system. By combining Terraform's infrastructure management with Ansible's configuration capabilities, we achieve a best-of-breed solution that scales with your needs while maintaining simplicity and operational excellence.