"""
AI Agent Platform - Authentication Tests
"""

import pytest
from unittest.mock import patch
from jose import jwt
from datetime import datetime, timedelta

from config.settings import settings


class TestAuthAPI:
    """Test authentication API endpoints"""
    
    def test_register_user(self, client, test_user_data):
        """Test user registration"""
        response = client.post("/api/v1/auth/register", json=test_user_data)
        
        # Debug: Print response details if not 201
        if response.status_code != 201:
            print(f"Response status: {response.status_code}")
            print(f"Response text: {response.text}")
            try:
                print(f"Response JSON: {response.json()}")
            except:
                pass
        
        assert response.status_code == 201
        data = response.json()
        
        assert data["username"] == test_user_data["username"]
        assert data["email"] == test_user_data["email"]
        assert data["full_name"] == test_user_data["full_name"]
        assert data["is_active"] == True
        assert data["is_superuser"] == False
        assert "id" in data
        assert "password" not in data  # Password should not be returned
    
    def test_register_duplicate_user(self, client, test_user_data):
        """Test registering duplicate user"""
        # Register first time
        response = client.post("/api/v1/auth/register", json=test_user_data)
        assert response.status_code == 201
        
        # Try to register again
        response = client.post("/api/v1/auth/register", json=test_user_data)
        assert response.status_code == 400
    
    def test_register_invalid_data(self, client):
        """Test registration with invalid data"""
        invalid_data = {
            "username": "",  # Empty username
            "email": "invalid-email",  # Invalid email
            "password": "123"  # Too short password
        }
        
        response = client.post("/api/v1/auth/register", json=invalid_data)
        assert response.status_code == 422  # Validation error
    
    def test_login_success(self, client, authenticated_user):
        """Test successful login"""
        token_data = authenticated_user["token_data"]
        
        assert "access_token" in token_data
        assert "refresh_token" in token_data
        assert token_data["token_type"] == "bearer"
        
        # Verify token is valid JWT
        decoded = jwt.decode(
            token_data["access_token"],
            settings.security.secret_key,
            algorithms=[settings.security.algorithm]
        )
        assert "sub" in decoded
        assert "exp" in decoded
    
    def test_login_invalid_credentials(self, client, test_user_data):
        """Test login with invalid credentials"""
        # Register user first
        client.post("/api/v1/auth/register", json=test_user_data)
        
        # Try login with wrong password
        login_data = {
            "username": test_user_data["username"],
            "password": "wrongpassword"
        }
        
        response = client.post("/api/v1/auth/login", data=login_data)
        assert response.status_code == 401
    
    def test_login_nonexistent_user(self, client):
        """Test login with nonexistent user"""
        login_data = {
            "username": "nonexistent",
            "password": "password"
        }
        
        response = client.post("/api/v1/auth/login", data=login_data)
        assert response.status_code == 401
    
    def test_get_current_user(self, client, authenticated_user):
        """Test getting current user info"""
        response = client.get(
            "/api/v1/auth/me",
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["username"] == authenticated_user["user_data"]["username"]
        assert data["email"] == authenticated_user["user_data"]["email"]
    
    def test_get_current_user_without_auth(self, client):
        """Test getting current user without authentication"""
        response = client.get("/api/v1/auth/me")
        assert response.status_code == 401
    
    def test_update_profile(self, client, authenticated_user):
        """Test updating user profile"""
        update_data = {
            "full_name": "Updated Full Name"
        }
        
        response = client.put(
            "/api/v1/auth/me",
            json=update_data,
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["full_name"] == "Updated Full Name"
    
    def test_change_password(self, client, authenticated_user, test_user_data):
        """Test changing password"""
        password_data = {
            "current_password": test_user_data["password"],
            "new_password": "newpassword123"
        }
        
        response = client.post(
            "/api/v1/auth/change-password",
            json=password_data,
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Password changed successfully"
        
        # Verify can login with new password
        login_data = {
            "username": test_user_data["username"],
            "password": "newpassword123"
        }
        
        response = client.post("/api/v1/auth/login", data=login_data)
        assert response.status_code == 200
    
    def test_change_password_wrong_current(self, client, authenticated_user):
        """Test changing password with wrong current password"""
        password_data = {
            "current_password": "wrongpassword",
            "new_password": "newpassword123"
        }
        
        response = client.post(
            "/api/v1/auth/change-password",
            json=password_data,
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 400


class TestSecurityService:
    """Test security service layer"""
    
    @pytest.mark.asyncio
    async def test_create_user(self, test_db, test_user_data):
        """Test user creation via security service"""
        from auth.security import SecurityService
        
        security_service = SecurityService(test_db)
        user = await security_service.create_user(
            username=test_user_data["username"],
            email=test_user_data["email"],
            password=test_user_data["password"],
            full_name=test_user_data["full_name"]
        )
        
        assert user.username == test_user_data["username"]
        assert user.email == test_user_data["email"]
        assert user.full_name == test_user_data["full_name"]
        assert user.is_active == True
        assert user.is_superuser == False
        
        # Password should be hashed
        assert user.hashed_password != test_user_data["password"]
        
        # Should be able to verify password
        assert security_service.verify_password(
            test_user_data["password"],
            user.hashed_password
        )
    
    @pytest.mark.asyncio
    async def test_create_duplicate_user(self, test_db, test_user_data):
        """Test creating duplicate user"""
        from auth.security import SecurityService
        
        security_service = SecurityService(test_db)
        
        # Create first user
        await security_service.create_user(
            username=test_user_data["username"],
            email=test_user_data["email"],
            password=test_user_data["password"]
        )
        
        # Try to create duplicate
        with pytest.raises(ValueError, match="already exists"):
            await security_service.create_user(
                username=test_user_data["username"],
                email="<EMAIL>",
                password=test_user_data["password"]
            )
    
    @pytest.mark.asyncio
    async def test_authenticate_user(self, test_db, test_user_data):
        """Test user authentication"""
        from auth.security import SecurityService
        
        security_service = SecurityService(test_db)
        
        # Create user
        created_user = await security_service.create_user(
            username=test_user_data["username"],
            email=test_user_data["email"],
            password=test_user_data["password"]
        )
        
        # Authenticate with username
        authenticated_user = await security_service.authenticate_user(
            test_user_data["username"],
            test_user_data["password"]
        )
        
        assert authenticated_user is not None
        assert authenticated_user.id == created_user.id
        
        # Authenticate with email
        authenticated_user = await security_service.authenticate_user(
            test_user_data["email"],
            test_user_data["password"]
        )
        
        assert authenticated_user is not None
        assert authenticated_user.id == created_user.id
    
    @pytest.mark.asyncio
    async def test_authenticate_invalid_user(self, test_db):
        """Test authentication with invalid credentials"""
        from auth.security import SecurityService
        
        security_service = SecurityService(test_db)
        
        # Try to authenticate nonexistent user
        result = await security_service.authenticate_user("nonexistent", "password")
        assert result is None
    
    def test_create_access_token(self, test_db):
        """Test creating access token"""
        from auth.security import SecurityService
        
        security_service = SecurityService(test_db)
        
        data = {"sub": "user123", "username": "testuser"}
        token = security_service.create_access_token(data)
        
        assert isinstance(token, str)
        
        # Verify token can be decoded
        decoded = jwt.decode(
            token,
            settings.security.secret_key,
            algorithms=[settings.security.algorithm]
        )
        
        assert decoded["sub"] == "user123"
        assert decoded["username"] == "testuser"
        assert "exp" in decoded
    
    def test_verify_token(self, test_db):
        """Test token verification"""
        from auth.security import SecurityService
        
        security_service = SecurityService(test_db)
        
        # Create token
        data = {"sub": "user123", "username": "testuser"}
        token = security_service.create_access_token(data)
        
        # Verify token
        payload = security_service.verify_token(token)
        
        assert payload is not None
        assert payload["sub"] == "user123"
        assert payload["username"] == "testuser"
    
    def test_verify_invalid_token(self, test_db):
        """Test verification of invalid token"""
        from auth.security import SecurityService
        
        security_service = SecurityService(test_db)
        
        # Test with invalid token
        payload = security_service.verify_token("invalid.token.here")
        assert payload is None
        
        # Test with expired token
        expired_data = {
            "sub": "user123",
            "exp": datetime.utcnow() - timedelta(minutes=1)  # Expired
        }
        expired_token = jwt.encode(
            expired_data,
            settings.security.secret_key,
            algorithm=settings.security.algorithm
        )
        
        payload = security_service.verify_token(expired_token)
        assert payload is None