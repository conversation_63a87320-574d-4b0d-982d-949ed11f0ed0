/* tslint:disable */
/* eslint-disable */
/**
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { InteractionWithDetails } from './InteractionWithDetails';
import {
    InteractionWithDetailsFromJSON,
    InteractionWithDetailsFromJSONTyped,
    InteractionWithDetailsToJSON,
    InteractionWithDetailsToJSONTyped,
} from './InteractionWithDetails';

/**
 * 
 * @export
 * @interface InteractionsGet200Response
 */
export interface InteractionsGet200Response {
    /**
     * 
     * @type {Array<InteractionWithDetails>}
     * @memberof InteractionsGet200Response
     */
    interactions?: Array<InteractionWithDetails>;
    /**
     * 
     * @type {number}
     * @memberof InteractionsGet200Response
     */
    totalCount?: number;
    /**
     * 
     * @type {boolean}
     * @memberof InteractionsGet200Response
     */
    hasMore?: boolean;
}

/**
 * Check if a given object implements the InteractionsGet200Response interface.
 */
export function instanceOfInteractionsGet200Response(value: object): value is InteractionsGet200Response {
    return true;
}

export function InteractionsGet200ResponseFromJSON(json: any): InteractionsGet200Response {
    return InteractionsGet200ResponseFromJSONTyped(json, false);
}

export function InteractionsGet200ResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): InteractionsGet200Response {
    if (json == null) {
        return json;
    }
    return {
        
        'interactions': json['interactions'] == null ? undefined : ((json['interactions'] as Array<any>).map(InteractionWithDetailsFromJSON)),
        'totalCount': json['total_count'] == null ? undefined : json['total_count'],
        'hasMore': json['has_more'] == null ? undefined : json['has_more'],
    };
}

  export function InteractionsGet200ResponseToJSON(json: any): InteractionsGet200Response {
      return InteractionsGet200ResponseToJSONTyped(json, false);
  }

  export function InteractionsGet200ResponseToJSONTyped(value?: InteractionsGet200Response | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'interactions': value['interactions'] == null ? undefined : ((value['interactions'] as Array<any>).map(InteractionWithDetailsToJSON)),
        'total_count': value['totalCount'],
        'has_more': value['hasMore'],
    };
}

