/* tslint:disable */
/* eslint-disable */
/**
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  ArliDocumentsGet200Response,
  ArliVectorizeRequest,
  ArliVectorizeResponse,
  Document,
  DocumentCreate,
  DocumentDetails,
  DocumentUpdate,
  ErrorResponse,
  FilterTag,
  FilterTagCreate,
  ValidationError,
} from '../models/index';
import {
    ArliDocumentsGet200ResponseFromJSON,
    ArliDocumentsGet200ResponseToJSON,
    ArliVectorizeRequestFromJSON,
    ArliVectorizeRequestToJSON,
    ArliVectorizeResponseFromJSON,
    ArliVectorizeResponseToJSON,
    DocumentFromJSON,
    DocumentToJSON,
    DocumentCreateFromJSON,
    DocumentCreateToJSON,
    DocumentDetailsFromJSON,
    DocumentDetailsToJSON,
    DocumentUpdateFromJSON,
    DocumentUpdateToJSON,
    ErrorResponseFromJSON,
    ErrorResponseToJSON,
    FilterTagFromJSON,
    FilterTagToJSON,
    FilterTagCreateFromJSON,
    FilterTagCreateToJSON,
    ValidationErrorFromJSON,
    ValidationErrorToJSON,
} from '../models/index';

export interface ArliDocumentsGetRequest {
    filterTags?: Array<string>;
    metadataFilter?: object;
    limit?: number;
    offset?: number;
}

export interface ArliDocumentsIdDeleteRequest {
    id: string;
}

export interface ArliDocumentsIdGetRequest {
    id: string;
}

export interface ArliDocumentsIdPutRequest {
    id: string;
    documentUpdate: DocumentUpdate;
}

export interface ArliDocumentsPostRequest {
    documentCreate: DocumentCreate;
}

export interface ArliDocumentsVectorizePostRequest {
    arliVectorizeRequest: ArliVectorizeRequest;
}

export interface ArliFilterTagsPostRequest {
    filterTagCreate: FilterTagCreate;
}

/**
 * 
 */
export class ArliApi extends runtime.BaseAPI {

    /**
     * Retrieve a list of processed documents
     * List documents
     */
    async arliDocumentsGetRaw(requestParameters: ArliDocumentsGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<ArliDocumentsGet200Response>> {
        const queryParameters: any = {};

        if (requestParameters['filterTags'] != null) {
            queryParameters['filter_tags'] = requestParameters['filterTags']!.join(runtime.COLLECTION_FORMATS["csv"]);
        }

        if (requestParameters['metadataFilter'] != null) {
            queryParameters['metadata_filter'] = requestParameters['metadataFilter'];
        }

        if (requestParameters['limit'] != null) {
            queryParameters['limit'] = requestParameters['limit'];
        }

        if (requestParameters['offset'] != null) {
            queryParameters['offset'] = requestParameters['offset'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("bearerAuth", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/arli/documents`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => ArliDocumentsGet200ResponseFromJSON(jsonValue));
    }

    /**
     * Retrieve a list of processed documents
     * List documents
     */
    async arliDocumentsGet(requestParameters: ArliDocumentsGetRequest = {}, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<ArliDocumentsGet200Response> {
        const response = await this.arliDocumentsGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Delete a document
     * Delete document
     */
    async arliDocumentsIdDeleteRaw(requestParameters: ArliDocumentsIdDeleteRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<void>> {
        if (requestParameters['id'] == null) {
            throw new runtime.RequiredError(
                'id',
                'Required parameter "id" was null or undefined when calling arliDocumentsIdDelete().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("bearerAuth", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/arli/documents/{id}`.replace(`{${"id"}}`, encodeURIComponent(String(requestParameters['id']))),
            method: 'DELETE',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.VoidApiResponse(response);
    }

    /**
     * Delete a document
     * Delete document
     */
    async arliDocumentsIdDelete(requestParameters: ArliDocumentsIdDeleteRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void> {
        await this.arliDocumentsIdDeleteRaw(requestParameters, initOverrides);
    }

    /**
     * Retrieve a specific document by ID
     * Get document
     */
    async arliDocumentsIdGetRaw(requestParameters: ArliDocumentsIdGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<DocumentDetails>> {
        if (requestParameters['id'] == null) {
            throw new runtime.RequiredError(
                'id',
                'Required parameter "id" was null or undefined when calling arliDocumentsIdGet().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("bearerAuth", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/arli/documents/{id}`.replace(`{${"id"}}`, encodeURIComponent(String(requestParameters['id']))),
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => DocumentDetailsFromJSON(jsonValue));
    }

    /**
     * Retrieve a specific document by ID
     * Get document
     */
    async arliDocumentsIdGet(requestParameters: ArliDocumentsIdGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<DocumentDetails> {
        const response = await this.arliDocumentsIdGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Update an existing document
     * Update document
     */
    async arliDocumentsIdPutRaw(requestParameters: ArliDocumentsIdPutRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Document>> {
        if (requestParameters['id'] == null) {
            throw new runtime.RequiredError(
                'id',
                'Required parameter "id" was null or undefined when calling arliDocumentsIdPut().'
            );
        }

        if (requestParameters['documentUpdate'] == null) {
            throw new runtime.RequiredError(
                'documentUpdate',
                'Required parameter "documentUpdate" was null or undefined when calling arliDocumentsIdPut().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("bearerAuth", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/arli/documents/{id}`.replace(`{${"id"}}`, encodeURIComponent(String(requestParameters['id']))),
            method: 'PUT',
            headers: headerParameters,
            query: queryParameters,
            body: DocumentUpdateToJSON(requestParameters['documentUpdate']),
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => DocumentFromJSON(jsonValue));
    }

    /**
     * Update an existing document
     * Update document
     */
    async arliDocumentsIdPut(requestParameters: ArliDocumentsIdPutRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Document> {
        const response = await this.arliDocumentsIdPutRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Create a new document for processing
     * Create document
     */
    async arliDocumentsPostRaw(requestParameters: ArliDocumentsPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Document>> {
        if (requestParameters['documentCreate'] == null) {
            throw new runtime.RequiredError(
                'documentCreate',
                'Required parameter "documentCreate" was null or undefined when calling arliDocumentsPost().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("bearerAuth", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/arli/documents`,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: DocumentCreateToJSON(requestParameters['documentCreate']),
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => DocumentFromJSON(jsonValue));
    }

    /**
     * Create a new document for processing
     * Create document
     */
    async arliDocumentsPost(requestParameters: ArliDocumentsPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Document> {
        const response = await this.arliDocumentsPostRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Process a document through the AI vectorization pipeline
     * Vectorize document
     */
    async arliDocumentsVectorizePostRaw(requestParameters: ArliDocumentsVectorizePostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<ArliVectorizeResponse>> {
        if (requestParameters['arliVectorizeRequest'] == null) {
            throw new runtime.RequiredError(
                'arliVectorizeRequest',
                'Required parameter "arliVectorizeRequest" was null or undefined when calling arliDocumentsVectorizePost().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("bearerAuth", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/arli/documents/vectorize`,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: ArliVectorizeRequestToJSON(requestParameters['arliVectorizeRequest']),
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => ArliVectorizeResponseFromJSON(jsonValue));
    }

    /**
     * Process a document through the AI vectorization pipeline
     * Vectorize document
     */
    async arliDocumentsVectorizePost(requestParameters: ArliDocumentsVectorizePostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<ArliVectorizeResponse> {
        const response = await this.arliDocumentsVectorizePostRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Retrieve all available filter tags
     * List filter tags
     */
    async arliFilterTagsGetRaw(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Array<FilterTag>>> {
        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("bearerAuth", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/arli/filter-tags`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => jsonValue.map(FilterTagFromJSON));
    }

    /**
     * Retrieve all available filter tags
     * List filter tags
     */
    async arliFilterTagsGet(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Array<FilterTag>> {
        const response = await this.arliFilterTagsGetRaw(initOverrides);
        return await response.value();
    }

    /**
     * Create a new filter tag
     * Create filter tag
     */
    async arliFilterTagsPostRaw(requestParameters: ArliFilterTagsPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<FilterTag>> {
        if (requestParameters['filterTagCreate'] == null) {
            throw new runtime.RequiredError(
                'filterTagCreate',
                'Required parameter "filterTagCreate" was null or undefined when calling arliFilterTagsPost().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("bearerAuth", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/arli/filter-tags`,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: FilterTagCreateToJSON(requestParameters['filterTagCreate']),
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => FilterTagFromJSON(jsonValue));
    }

    /**
     * Create a new filter tag
     * Create filter tag
     */
    async arliFilterTagsPost(requestParameters: ArliFilterTagsPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<FilterTag> {
        const response = await this.arliFilterTagsPostRaw(requestParameters, initOverrides);
        return await response.value();
    }

}
