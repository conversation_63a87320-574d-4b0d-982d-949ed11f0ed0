package middleware

import (
	"github.com/gin-gonic/gin"
)

func CORS() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		// Get origin from request
		origin := c.Request.Header.Get("Origin")
		
		// List of allowed origins
		allowedOrigins := []string{
			"https://internal.dev.twodot.ai",
			"http://localhost:8080",
			"http://localhost:3000",
		}
		
		// Check if origin is allowed
		allowed := false
		for _, allowedOrigin := range allowedOrigins {
			if origin == allowedOrigin {
				allowed = true
				break
			}
		}
		
		if allowed {
			c.<PERSON><PERSON>("Access-Control-Allow-Origin", origin)
			c.<PERSON><PERSON>("Access-Control-Allow-Credentials", "true")
		} else {
			c.<PERSON>("Access-Control-Allow-Origin", "*")
		}
		
		c.<PERSON>("Access-Control-Allow-Headers", "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, accept, origin, Cache-Control, X-Requested-With")
		c.<PERSON><PERSON>("Access-Control-Allow-Methods", "POST, OPTIONS, GET, PUT, DELETE, PATCH")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})
}