"""
Google AI Development Kit (ADK) Integration Service
Provides advanced AI capabilities using Google's Vertex AI and Gemini models
"""

import json
import logging
from typing import Dict, Any, List, Optional, Union, AsyncGenerator
from datetime import datetime
import base64
from io import BytesIO

from google.cloud import aiplatform
from google.cloud.aiplatform import gapic
from google.oauth2 import service_account
import vertexai
from vertexai.generative_models import (
    GenerativeModel, 
    Part, 
    Content, 
    GenerationConfig,
    HarmCategory,
    HarmBlockThreshold,
    SafetySetting
)
from vertexai.language_models import TextGenerationModel, CodeGenerationModel
from vertexai.vision_models import ImageTextModel, MultiModalEmbeddingModel
import google.generativeai as genai

from config.settings import settings
from database.models import Agent
from services.vector_db import get_vector_db

logger = logging.getLogger(__name__)


class GoogleADKService:
    """Google AI Development Kit integration service"""
    
    def __init__(self):
        self.project_id = settings.ai_services.google_ai_project_id
        self.location = settings.ai_services.google_ai_location
        self.credentials = None
        self.vertex_initialized = False
        self.genai_initialized = False
        
        # Model configurations
        self.model_configs = {
            "gemini-pro": {
                "temperature": 0.7,
                "max_output_tokens": 2048,
                "top_p": 0.95,
                "top_k": 40
            },
            "gemini-pro-vision": {
                "temperature": 0.4,
                "max_output_tokens": 2048,
                "top_p": 0.95,
                "top_k": 40
            },
            "code-bison": {
                "temperature": 0.2,
                "max_output_tokens": 2048,
                "candidate_count": 1
            },
            "text-bison": {
                "temperature": 0.7,
                "max_output_tokens": 1024,
                "top_p": 0.95,
                "top_k": 40
            }
        }
        
        # Safety settings
        self.safety_settings = [
            SafetySetting(
                category=HarmCategory.HARM_CATEGORY_HATE_SPEECH,
                threshold=HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE
            ),
            SafetySetting(
                category=HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
                threshold=HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE
            ),
            SafetySetting(
                category=HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
                threshold=HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE
            ),
            SafetySetting(
                category=HarmCategory.HARM_CATEGORY_HARASSMENT,
                threshold=HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE
            )
        ]
    
    async def initialize(self):
        """Initialize Google ADK connections"""
        try:
            # Initialize credentials if provided
            if hasattr(settings.ai_services, 'google_service_account_json'):
                credentials_info = json.loads(settings.ai_services.google_service_account_json)
                self.credentials = service_account.Credentials.from_service_account_info(
                    credentials_info
                )
            
            # Initialize Vertex AI
            if self.project_id:
                vertexai.init(
                    project=self.project_id,
                    location=self.location,
                    credentials=self.credentials
                )
                self.vertex_initialized = True
                logger.info("Vertex AI initialized successfully")
            
            # Initialize Generative AI (for Gemini)
            if hasattr(settings.ai_services, 'google_api_key') and settings.ai_services.google_api_key:
                genai.configure(api_key=settings.ai_services.google_api_key)
                self.genai_initialized = True
                logger.info("Google Generative AI initialized successfully")
                
        except Exception as e:
            logger.error(f"Failed to initialize Google ADK: {e}")
            raise
    
    async def generate_text(
        self,
        prompt: str,
        model: str = "gemini-pro",
        agent_context: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> str:
        """Generate text using Google's generative models"""
        try:
            if not self.vertex_initialized and not self.genai_initialized:
                await self.initialize()
            
            # Merge configurations
            config = self.model_configs.get(model, {}).copy()
            config.update(kwargs)
            
            # Add agent context if provided
            if agent_context:
                context_prompt = f"Agent Context:\n"
                context_prompt += f"- Type: {agent_context.get('type', 'unknown')}\n"
                context_prompt += f"- Capabilities: {', '.join(agent_context.get('capabilities', []))}\n"
                context_prompt += f"- Task: {agent_context.get('task', 'general')}\n\n"
                prompt = context_prompt + prompt
            
            # Use Gemini through Generative AI API
            if model.startswith("gemini") and self.genai_initialized:
                gemini_model = genai.GenerativeModel(model)
                response = gemini_model.generate_content(
                    prompt,
                    generation_config=genai.GenerationConfig(**config),
                    safety_settings=self.safety_settings
                )
                return response.text
            
            # Use Vertex AI for other models
            elif self.vertex_initialized:
                if model == "text-bison":
                    vertex_model = TextGenerationModel.from_pretrained(model)
                    response = vertex_model.predict(
                        prompt,
                        temperature=config.get("temperature", 0.7),
                        max_output_tokens=config.get("max_output_tokens", 1024),
                        top_p=config.get("top_p", 0.95),
                        top_k=config.get("top_k", 40)
                    )
                    return response.text
                else:
                    # Use GenerativeModel for Gemini models in Vertex AI
                    vertex_model = GenerativeModel(model)
                    response = vertex_model.generate_content(
                        prompt,
                        generation_config=GenerationConfig(**config),
                        safety_settings=self.safety_settings
                    )
                    return response.text
            
            else:
                raise RuntimeError("Google ADK not properly initialized")
                
        except Exception as e:
            logger.error(f"Failed to generate text: {e}")
            raise
    
    async def generate_code(
        self,
        prompt: str,
        language: str = "python",
        model: str = "code-bison",
        **kwargs
    ) -> str:
        """Generate code using Google's code generation models"""
        try:
            if not self.vertex_initialized:
                await self.initialize()
            
            # Prepare code-specific prompt
            code_prompt = f"Generate {language} code for the following:\n{prompt}"
            
            if model == "code-bison" and self.vertex_initialized:
                code_model = CodeGenerationModel.from_pretrained(model)
                response = code_model.predict(
                    prefix=code_prompt,
                    temperature=kwargs.get("temperature", 0.2),
                    max_output_tokens=kwargs.get("max_output_tokens", 2048),
                    candidate_count=kwargs.get("candidate_count", 1)
                )
                return response.text
            else:
                # Fallback to Gemini for code generation
                return await self.generate_text(
                    code_prompt,
                    model="gemini-pro",
                    temperature=0.2,
                    **kwargs
                )
                
        except Exception as e:
            logger.error(f"Failed to generate code: {e}")
            raise
    
    async def analyze_image(
        self,
        image_data: Union[str, bytes],
        prompt: str,
        model: str = "gemini-pro-vision",
        **kwargs
    ) -> str:
        """Analyze images using multimodal models"""
        try:
            if not self.genai_initialized:
                await self.initialize()
            
            # Convert image data to Part
            if isinstance(image_data, str):
                # Assume base64 encoded
                image_bytes = base64.b64decode(image_data)
            else:
                image_bytes = image_data
            
            # Use Gemini Vision
            if self.genai_initialized:
                vision_model = genai.GenerativeModel(model)
                
                # Create image part
                image_part = Part.from_data(
                    data=image_bytes,
                    mime_type="image/jpeg"  # Adjust based on actual image type
                )
                
                # Generate response
                response = vision_model.generate_content(
                    [prompt, image_part],
                    generation_config=genai.GenerationConfig(
                        **self.model_configs.get(model, {})
                    ),
                    safety_settings=self.safety_settings
                )
                return response.text
            else:
                raise RuntimeError("Google Generative AI not initialized for vision models")
                
        except Exception as e:
            logger.error(f"Failed to analyze image: {e}")
            raise
    
    async def generate_embeddings(
        self,
        texts: List[str],
        model: str = "textembedding-gecko",
        task_type: str = "RETRIEVAL_DOCUMENT"
    ) -> List[List[float]]:
        """Generate embeddings for text using Google's embedding models"""
        try:
            if not self.vertex_initialized:
                await self.initialize()
            
            from vertexai.language_models import TextEmbeddingModel
            
            embedding_model = TextEmbeddingModel.from_pretrained(model)
            embeddings = embedding_model.get_embeddings(
                texts,
                task_type=task_type
            )
            
            return [emb.values for emb in embeddings]
            
        except Exception as e:
            logger.error(f"Failed to generate embeddings: {e}")
            raise
    
    async def chat_stream(
        self,
        messages: List[Dict[str, str]],
        model: str = "gemini-pro",
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """Stream chat responses using Google's chat models"""
        try:
            if not self.genai_initialized:
                await self.initialize()
            
            # Convert messages to Gemini format
            chat_model = genai.GenerativeModel(model)
            chat = chat_model.start_chat()
            
            # Send previous messages to establish context
            for msg in messages[:-1]:
                if msg["role"] == "user":
                    chat.send_message(msg["content"])
            
            # Stream the response for the last message
            response = chat.send_message(
                messages[-1]["content"],
                generation_config=genai.GenerationConfig(
                    **self.model_configs.get(model, {})
                ),
                safety_settings=self.safety_settings,
                stream=True
            )
            
            for chunk in response:
                if chunk.text:
                    yield chunk.text
                    
        except Exception as e:
            logger.error(f"Failed to stream chat: {e}")
            raise
    
    async def create_agent_with_adk(
        self,
        agent_type: str,
        capabilities: List[str],
        model_preferences: Dict[str, str],
        **kwargs
    ) -> Dict[str, Any]:
        """Create an AI agent configuration optimized for Google ADK"""
        try:
            # Define model mappings for different capabilities
            capability_models = {
                "text_generation": "gemini-pro",
                "code_generation": "code-bison",
                "image_analysis": "gemini-pro-vision",
                "conversation": "gemini-pro",
                "reasoning": "gemini-pro",
                "data_analysis": "text-bison"
            }
            
            # Build agent configuration
            agent_config = {
                "type": agent_type,
                "capabilities": capabilities,
                "models": {},
                "parameters": {},
                "metadata": kwargs.get("metadata", {})
            }
            
            # Assign appropriate models for each capability
            for capability in capabilities:
                if capability in capability_models:
                    model = model_preferences.get(
                        capability,
                        capability_models[capability]
                    )
                    agent_config["models"][capability] = model
                    agent_config["parameters"][capability] = self.model_configs.get(
                        model, {}
                    ).copy()
            
            # Add ADK-specific features
            agent_config["features"] = {
                "streaming": True,
                "multimodal": "image_analysis" in capabilities,
                "code_execution": "code_generation" in capabilities,
                "embeddings": True,
                "safety_settings": True
            }
            
            return agent_config
            
        except Exception as e:
            logger.error(f"Failed to create agent with ADK: {e}")
            raise
    
    async def enhance_agent_with_knowledge(
        self,
        agent_id: str,
        knowledge_sources: List[Dict[str, Any]]
    ) -> bool:
        """Enhance agent with knowledge using embeddings and vector storage"""
        try:
            vector_db = await get_vector_db()
            
            for source in knowledge_sources:
                content = source.get("content", "")
                metadata = source.get("metadata", {})
                category = source.get("category", "general")
                
                # Generate embeddings using Google's model
                embeddings = await self.generate_embeddings(
                    [content],
                    task_type="RETRIEVAL_DOCUMENT"
                )
                
                # Store in vector database
                # Note: This would need to be adapted to use Google embeddings
                await vector_db.store_agent_knowledge(
                    agent_id=agent_id,
                    content=content,
                    metadata=metadata,
                    category=category
                )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to enhance agent with knowledge: {e}")
            return False
    
    async def analyze_agent_performance(
        self,
        agent_id: str,
        metrics: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Analyze agent performance using Google's AI capabilities"""
        try:
            # Prepare analysis prompt
            prompt = f"""Analyze the following AI agent performance metrics and provide insights:
            
Agent ID: {agent_id}
Metrics:
{json.dumps(metrics, indent=2)}

Provide:
1. Performance summary
2. Strengths and weaknesses
3. Optimization recommendations
4. Predicted performance trends
"""
            
            # Use Gemini for analysis
            analysis = await self.generate_text(
                prompt,
                model="gemini-pro",
                temperature=0.5
            )
            
            # Parse and structure the response
            return {
                "agent_id": agent_id,
                "analysis": analysis,
                "metrics": metrics,
                "timestamp": datetime.utcnow().isoformat(),
                "recommendations": self._extract_recommendations(analysis)
            }
            
        except Exception as e:
            logger.error(f"Failed to analyze agent performance: {e}")
            raise
    
    def _extract_recommendations(self, analysis: str) -> List[str]:
        """Extract recommendations from analysis text"""
        # Simple extraction - in production, use more sophisticated parsing
        recommendations = []
        lines = analysis.split('\n')
        
        in_recommendations = False
        for line in lines:
            if "recommendation" in line.lower():
                in_recommendations = True
            elif in_recommendations and line.strip():
                if line.strip().startswith(('-', '*', '•', '1', '2', '3')):
                    recommendations.append(line.strip().lstrip('-*•123. '))
        
        return recommendations[:5]  # Limit to top 5
    
    async def get_model_info(self, model: str) -> Dict[str, Any]:
        """Get information about available Google AI models"""
        try:
            model_info = {
                "gemini-pro": {
                    "name": "Gemini Pro",
                    "description": "Advanced text generation and reasoning",
                    "capabilities": ["text_generation", "conversation", "reasoning"],
                    "max_tokens": 32768,
                    "supports_streaming": True
                },
                "gemini-pro-vision": {
                    "name": "Gemini Pro Vision",
                    "description": "Multimodal model for image and text understanding",
                    "capabilities": ["image_analysis", "text_generation", "multimodal"],
                    "max_tokens": 16384,
                    "supports_streaming": True
                },
                "code-bison": {
                    "name": "Code Bison",
                    "description": "Specialized model for code generation",
                    "capabilities": ["code_generation", "code_completion"],
                    "max_tokens": 2048,
                    "supports_streaming": False
                },
                "text-bison": {
                    "name": "Text Bison",
                    "description": "General text generation model",
                    "capabilities": ["text_generation", "summarization"],
                    "max_tokens": 1024,
                    "supports_streaming": False
                },
                "textembedding-gecko": {
                    "name": "Text Embedding Gecko",
                    "description": "Generate embeddings for semantic search",
                    "capabilities": ["embeddings"],
                    "embedding_size": 768,
                    "supports_streaming": False
                }
            }
            
            return model_info.get(model, {
                "name": model,
                "description": "Unknown model",
                "capabilities": [],
                "error": "Model information not available"
            })
            
        except Exception as e:
            logger.error(f"Failed to get model info: {e}")
            raise
    
    async def health_check(self) -> Dict[str, Any]:
        """Check Google ADK service health"""
        try:
            health_status = {
                "status": "healthy",
                "vertex_ai": {
                    "initialized": self.vertex_initialized,
                    "project_id": self.project_id,
                    "location": self.location
                },
                "generative_ai": {
                    "initialized": self.genai_initialized,
                    "api_key_configured": bool(
                        hasattr(settings.ai_services, 'google_api_key') and 
                        settings.ai_services.google_api_key
                    )
                },
                "available_models": [],
                "timestamp": datetime.utcnow().isoformat()
            }
            
            # Check available models
            if self.vertex_initialized or self.genai_initialized:
                health_status["available_models"] = list(self.model_configs.keys())
            
            # Test basic functionality
            if self.genai_initialized:
                try:
                    test_response = await self.generate_text(
                        "Hello, this is a health check. Respond with 'OK'.",
                        model="gemini-pro",
                        max_output_tokens=10
                    )
                    health_status["test_response"] = "OK" in test_response
                except:
                    health_status["test_response"] = False
            
            return health_status
            
        except Exception as e:
            logger.error(f"Google ADK health check failed: {e}")
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }


# Global Google ADK service instance
google_adk_service = GoogleADKService()


async def get_google_adk_service() -> GoogleADKService:
    """Get Google ADK service instance"""
    if not google_adk_service.vertex_initialized and not google_adk_service.genai_initialized:
        await google_adk_service.initialize()
    return google_adk_service