package database

import (
	"database/sql"
	"testing"
)

func TestInitDB(t *testing.T) {
	tests := []struct {
		name    string
		dbURL   string
		wantErr bool
	}{
		{
			name:    "empty database URL returns error",
			dbURL:   "",
			wantErr: true,
		},
		{
			name:    "invalid database URL returns error",
			dbURL:   "invalid://url",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := InitDB(tt.dbURL)
			if (err != nil) != tt.wantErr {
				t.Errorf("InitDB() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestGetDB(t *testing.T) {
	// Save original db
	originalDB := db
	defer func() { db = originalDB }()

	// Test that GetDB returns the global db
	got := GetDB()
	if got != db {
		t.Errorf("GetDB() should return the global db variable")
	}
}

func TestCloseDB(t *testing.T) {
	tests := []struct {
		name    string
		setupDB func() *sql.DB
		wantErr bool
	}{
		{
			name: "handles nil database",
			setupDB: func() *sql.DB {
				return nil
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			originalDB := db
			db = tt.setupDB()
			defer func() { db = originalDB }()

			err := CloseDB()
			if (err != nil) != tt.wantErr {
				t.Errorf("CloseDB() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}