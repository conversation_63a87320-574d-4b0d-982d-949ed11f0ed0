// Package client provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.5.0 DO NOT EDIT.
package client

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/oapi-codegen/runtime"
	openapi_types "github.com/oapi-codegen/runtime/types"
)

const (
	BearerAuthScopes = "bearerAuth.Scopes"
)

// Defines values for DocumentDetailsProcessingStatus.
const (
	Completed  DocumentDetailsProcessingStatus = "completed"
	Failed     DocumentDetailsProcessingStatus = "failed"
	Pending    DocumentDetailsProcessingStatus = "pending"
	Processing DocumentDetailsProcessingStatus = "processing"
)

// Defines values for InteractionInteractionType.
const (
	InteractionInteractionTypeDemo     InteractionInteractionType = "demo"
	InteractionInteractionTypeEmail    InteractionInteractionType = "email"
	InteractionInteractionTypeFollowUp InteractionInteractionType = "follow-up"
	InteractionInteractionTypeMeeting  InteractionInteractionType = "meeting"
	InteractionInteractionTypeOther    InteractionInteractionType = "other"
	InteractionInteractionTypePhone    InteractionInteractionType = "phone"
	InteractionInteractionTypeProposal InteractionInteractionType = "proposal"
)

// Defines values for InteractionCreateInteractionType.
const (
	InteractionCreateInteractionTypeDemo     InteractionCreateInteractionType = "demo"
	InteractionCreateInteractionTypeEmail    InteractionCreateInteractionType = "email"
	InteractionCreateInteractionTypeFollowUp InteractionCreateInteractionType = "follow-up"
	InteractionCreateInteractionTypeMeeting  InteractionCreateInteractionType = "meeting"
	InteractionCreateInteractionTypeOther    InteractionCreateInteractionType = "other"
	InteractionCreateInteractionTypePhone    InteractionCreateInteractionType = "phone"
	InteractionCreateInteractionTypeProposal InteractionCreateInteractionType = "proposal"
)

// Defines values for InteractionDetailsInteractionType.
const (
	InteractionDetailsInteractionTypeDemo     InteractionDetailsInteractionType = "demo"
	InteractionDetailsInteractionTypeEmail    InteractionDetailsInteractionType = "email"
	InteractionDetailsInteractionTypeFollowUp InteractionDetailsInteractionType = "follow-up"
	InteractionDetailsInteractionTypeMeeting  InteractionDetailsInteractionType = "meeting"
	InteractionDetailsInteractionTypeOther    InteractionDetailsInteractionType = "other"
	InteractionDetailsInteractionTypePhone    InteractionDetailsInteractionType = "phone"
	InteractionDetailsInteractionTypeProposal InteractionDetailsInteractionType = "proposal"
)

// Defines values for InteractionUpdateInteractionType.
const (
	InteractionUpdateInteractionTypeDemo     InteractionUpdateInteractionType = "demo"
	InteractionUpdateInteractionTypeEmail    InteractionUpdateInteractionType = "email"
	InteractionUpdateInteractionTypeFollowUp InteractionUpdateInteractionType = "follow-up"
	InteractionUpdateInteractionTypeMeeting  InteractionUpdateInteractionType = "meeting"
	InteractionUpdateInteractionTypeOther    InteractionUpdateInteractionType = "other"
	InteractionUpdateInteractionTypePhone    InteractionUpdateInteractionType = "phone"
	InteractionUpdateInteractionTypeProposal InteractionUpdateInteractionType = "proposal"
)

// Defines values for InteractionWithDetailsInteractionType.
const (
	Demo     InteractionWithDetailsInteractionType = "demo"
	Email    InteractionWithDetailsInteractionType = "email"
	FollowUp InteractionWithDetailsInteractionType = "follow-up"
	Meeting  InteractionWithDetailsInteractionType = "meeting"
	Other    InteractionWithDetailsInteractionType = "other"
	Phone    InteractionWithDetailsInteractionType = "phone"
	Proposal InteractionWithDetailsInteractionType = "proposal"
)

// Defines values for GetAuthCallbackProviderParamsProvider.
const (
	GetAuthCallbackProviderParamsProviderGoogle GetAuthCallbackProviderParamsProvider = "google"
)

// Defines values for PostAuthSigninOauthJSONBodyProvider.
const (
	PostAuthSigninOauthJSONBodyProviderGoogle PostAuthSigninOauthJSONBodyProvider = "google"
)

// AuthResponse defines model for AuthResponse.
type AuthResponse struct {
	AccessToken  *string `json:"access_token,omitempty"`
	ExpiresIn    *int    `json:"expires_in,omitempty"`
	RefreshToken *string `json:"refresh_token,omitempty"`
	TokenType    *string `json:"token_type,omitempty"`
	User         *User   `json:"user,omitempty"`
}

// Company defines model for Company.
type Company struct {
	Address         *string             `json:"address,omitempty"`
	CompanyStatusId *openapi_types.UUID `json:"company_status_id,omitempty"`
	CreatedAt       *time.Time          `json:"created_at,omitempty"`
	Id              *openapi_types.UUID `json:"id,omitempty"`
	IsDeleted       *bool               `json:"is_deleted,omitempty"`
	Name            *string             `json:"name,omitempty"`
	Notes           *string             `json:"notes,omitempty"`
	Phone           *string             `json:"phone,omitempty"`
	UpdatedAt       *time.Time          `json:"updated_at,omitempty"`
	Website         *string             `json:"website,omitempty"`
}

// CompanyBasicInfo defines model for CompanyBasicInfo.
type CompanyBasicInfo struct {
	Id   *openapi_types.UUID `json:"id,omitempty"`
	Name *string             `json:"name,omitempty"`
}

// CompanyCreate defines model for CompanyCreate.
type CompanyCreate struct {
	Address         *string             `json:"address,omitempty"`
	CompanyStatusId *openapi_types.UUID `json:"company_status_id,omitempty"`
	Name            string              `json:"name"`
	Notes           *string             `json:"notes,omitempty"`
	Phone           *string             `json:"phone,omitempty"`
	Website         *string             `json:"website,omitempty"`
}

// CompanyDetails defines model for CompanyDetails.
type CompanyDetails struct {
	Address         *string             `json:"address,omitempty"`
	CompanyStatusId *openapi_types.UUID `json:"company_status_id,omitempty"`
	Contacts        *[]Contact          `json:"contacts,omitempty"`
	CreatedAt       *time.Time          `json:"created_at,omitempty"`
	Id              *openapi_types.UUID `json:"id,omitempty"`
	IsDeleted       *bool               `json:"is_deleted,omitempty"`
	Name            *string             `json:"name,omitempty"`
	Notes           *string             `json:"notes,omitempty"`
	Phone           *string             `json:"phone,omitempty"`
	UpdatedAt       *time.Time          `json:"updated_at,omitempty"`
	Website         *string             `json:"website,omitempty"`
}

// CompanyInfo defines model for CompanyInfo.
type CompanyInfo struct {
	Id        *openapi_types.UUID `json:"id,omitempty"`
	IsDeleted *bool               `json:"is_deleted,omitempty"`
	Name      *string             `json:"name,omitempty"`
}

// CompanyStatus defines model for CompanyStatus.
type CompanyStatus struct {
	CreatedAt     *time.Time          `json:"created_at,omitempty"`
	Id            *openapi_types.UUID `json:"id,omitempty"`
	Name          *string             `json:"name,omitempty"`
	PipelineOrder *int                `json:"pipeline_order,omitempty"`
}

// CompanyUpdate defines model for CompanyUpdate.
type CompanyUpdate struct {
	Address         *string             `json:"address,omitempty"`
	CompanyStatusId *openapi_types.UUID `json:"company_status_id,omitempty"`
	Name            *string             `json:"name,omitempty"`
	Notes           *string             `json:"notes,omitempty"`
	Phone           *string             `json:"phone,omitempty"`
	Website         *string             `json:"website,omitempty"`
}

// Contact defines model for Contact.
type Contact struct {
	CompanyId *openapi_types.UUID  `json:"company_id"`
	CreatedAt *time.Time           `json:"created_at,omitempty"`
	CreatedBy *openapi_types.UUID  `json:"created_by,omitempty"`
	Email     *openapi_types.Email `json:"email,omitempty"`
	FirstName *string              `json:"first_name,omitempty"`
	Id        *openapi_types.UUID  `json:"id,omitempty"`
	IsDeleted *bool                `json:"is_deleted,omitempty"`
	JobTitle  *string              `json:"job_title,omitempty"`
	LastName  *string              `json:"last_name,omitempty"`
	Phone     *string              `json:"phone,omitempty"`
	UpdatedAt *time.Time           `json:"updated_at,omitempty"`
}

// ContactCreate defines model for ContactCreate.
type ContactCreate struct {
	CompanyId *openapi_types.UUID  `json:"company_id,omitempty"`
	Email     *openapi_types.Email `json:"email,omitempty"`
	FirstName string               `json:"first_name"`
	JobTitle  *string              `json:"job_title,omitempty"`
	LastName  string               `json:"last_name"`
	Phone     *string              `json:"phone,omitempty"`
}

// ContactDetails defines model for ContactDetails.
type ContactDetails struct {
	Company   *CompanyBasicInfo    `json:"company"`
	CompanyId *openapi_types.UUID  `json:"company_id"`
	CreatedAt *time.Time           `json:"created_at,omitempty"`
	CreatedBy *openapi_types.UUID  `json:"created_by,omitempty"`
	Email     *openapi_types.Email `json:"email,omitempty"`
	FirstName *string              `json:"first_name,omitempty"`
	Id        *openapi_types.UUID  `json:"id,omitempty"`
	IsDeleted *bool                `json:"is_deleted,omitempty"`
	JobTitle  *string              `json:"job_title,omitempty"`
	LastName  *string              `json:"last_name,omitempty"`
	Phone     *string              `json:"phone,omitempty"`
	UpdatedAt *time.Time           `json:"updated_at,omitempty"`
}

// ContactUpdate defines model for ContactUpdate.
type ContactUpdate struct {
	CompanyId *openapi_types.UUID  `json:"company_id"`
	Email     *openapi_types.Email `json:"email,omitempty"`
	FirstName *string              `json:"first_name,omitempty"`
	JobTitle  *string              `json:"job_title,omitempty"`
	LastName  *string              `json:"last_name,omitempty"`
	Phone     *string              `json:"phone,omitempty"`
}

// ContactWithCompany defines model for ContactWithCompany.
type ContactWithCompany struct {
	Company   *CompanyInfo         `json:"company"`
	CompanyId *openapi_types.UUID  `json:"company_id"`
	CreatedAt *time.Time           `json:"created_at,omitempty"`
	CreatedBy *openapi_types.UUID  `json:"created_by,omitempty"`
	Email     *openapi_types.Email `json:"email,omitempty"`
	FirstName *string              `json:"first_name,omitempty"`
	Id        *openapi_types.UUID  `json:"id,omitempty"`
	IsDeleted *bool                `json:"is_deleted,omitempty"`
	JobTitle  *string              `json:"job_title,omitempty"`
	LastName  *string              `json:"last_name,omitempty"`
	Phone     *string              `json:"phone,omitempty"`
	UpdatedAt *time.Time           `json:"updated_at,omitempty"`
}

// Deal defines model for Deal.
type Deal struct {
	CompanyId         *openapi_types.UUID `json:"company_id,omitempty"`
	CreatedAt         *time.Time          `json:"created_at,omitempty"`
	CreatedBy         *openapi_types.UUID `json:"created_by,omitempty"`
	DealStageId       *openapi_types.UUID `json:"deal_stage_id,omitempty"`
	Description       *string             `json:"description,omitempty"`
	EstimatedValue    *float32            `json:"estimated_value,omitempty"`
	ExpectedCloseDate *openapi_types.Date `json:"expected_close_date,omitempty"`
	Id                *openapi_types.UUID `json:"id,omitempty"`
	Title             *string             `json:"title,omitempty"`
	UpdatedAt         *time.Time          `json:"updated_at,omitempty"`
}

// DealCompanyInfo defines model for DealCompanyInfo.
type DealCompanyInfo struct {
	Id   *openapi_types.UUID `json:"id,omitempty"`
	Name *string             `json:"name,omitempty"`
}

// DealCreate defines model for DealCreate.
type DealCreate struct {
	CompanyId         openapi_types.UUID  `json:"company_id"`
	DealStageId       openapi_types.UUID  `json:"deal_stage_id"`
	Description       *string             `json:"description,omitempty"`
	EstimatedValue    *float32            `json:"estimated_value,omitempty"`
	ExpectedCloseDate *openapi_types.Date `json:"expected_close_date,omitempty"`
	Title             string              `json:"title"`
}

// DealDetails defines model for DealDetails.
type DealDetails struct {
	Company *struct {
		Id      *openapi_types.UUID `json:"id,omitempty"`
		Name    *string             `json:"name,omitempty"`
		Phone   *string             `json:"phone,omitempty"`
		Website *string             `json:"website,omitempty"`
	} `json:"company,omitempty"`
	CompanyId *openapi_types.UUID `json:"company_id,omitempty"`
	CreatedAt *time.Time          `json:"created_at,omitempty"`
	CreatedBy *openapi_types.UUID `json:"created_by,omitempty"`
	DealStage *struct {
		Id            *openapi_types.UUID `json:"id,omitempty"`
		IsClosedLost  *bool               `json:"is_closed_lost,omitempty"`
		IsClosedWon   *bool               `json:"is_closed_won,omitempty"`
		Name          *string             `json:"name,omitempty"`
		PipelineOrder *int                `json:"pipeline_order,omitempty"`
	} `json:"deal_stage,omitempty"`
	DealStageId       *openapi_types.UUID `json:"deal_stage_id,omitempty"`
	Description       *string             `json:"description,omitempty"`
	EstimatedValue    *float32            `json:"estimated_value,omitempty"`
	ExpectedCloseDate *openapi_types.Date `json:"expected_close_date,omitempty"`
	Id                *openapi_types.UUID `json:"id,omitempty"`
	Title             *string             `json:"title,omitempty"`
	UpdatedAt         *time.Time          `json:"updated_at,omitempty"`
}

// DealStage defines model for DealStage.
type DealStage struct {
	CreatedAt     *time.Time          `json:"created_at,omitempty"`
	Id            *openapi_types.UUID `json:"id,omitempty"`
	IsClosedLost  *bool               `json:"is_closed_lost,omitempty"`
	IsClosedWon   *bool               `json:"is_closed_won,omitempty"`
	Name          *string             `json:"name,omitempty"`
	PipelineOrder *int                `json:"pipeline_order,omitempty"`
}

// DealStageInfo defines model for DealStageInfo.
type DealStageInfo struct {
	Id            *openapi_types.UUID `json:"id,omitempty"`
	Name          *string             `json:"name,omitempty"`
	PipelineOrder *int                `json:"pipeline_order,omitempty"`
}

// DealUpdate defines model for DealUpdate.
type DealUpdate struct {
	CompanyId         *openapi_types.UUID `json:"company_id,omitempty"`
	DealStageId       *openapi_types.UUID `json:"deal_stage_id,omitempty"`
	Description       *string             `json:"description,omitempty"`
	EstimatedValue    *float32            `json:"estimated_value,omitempty"`
	ExpectedCloseDate *openapi_types.Date `json:"expected_close_date,omitempty"`
	Title             *string             `json:"title,omitempty"`
}

// DealWithDetails defines model for DealWithDetails.
type DealWithDetails struct {
	Company           *DealCompanyInfo    `json:"company,omitempty"`
	CompanyId         *openapi_types.UUID `json:"company_id,omitempty"`
	CreatedAt         *time.Time          `json:"created_at,omitempty"`
	CreatedBy         *openapi_types.UUID `json:"created_by,omitempty"`
	DealStage         *DealStageInfo      `json:"deal_stage,omitempty"`
	DealStageId       *openapi_types.UUID `json:"deal_stage_id,omitempty"`
	Description       *string             `json:"description,omitempty"`
	EstimatedValue    *float32            `json:"estimated_value,omitempty"`
	ExpectedCloseDate *openapi_types.Date `json:"expected_close_date,omitempty"`
	Id                *openapi_types.UUID `json:"id,omitempty"`
	Title             *string             `json:"title,omitempty"`
	UpdatedAt         *time.Time          `json:"updated_at,omitempty"`
}

// Document defines model for Document.
type Document struct {
	Content    *string                 `json:"content,omitempty"`
	CreatedAt  *time.Time              `json:"created_at,omitempty"`
	FilterTags *[]string               `json:"filter_tags,omitempty"`
	Id         *openapi_types.UUID     `json:"id,omitempty"`
	Metadata   *map[string]interface{} `json:"metadata,omitempty"`
	SourceId   *string                 `json:"source_id,omitempty"`
	UpdatedAt  *time.Time              `json:"updated_at,omitempty"`
}

// DocumentCreate defines model for DocumentCreate.
type DocumentCreate struct {
	Content    string                  `json:"content"`
	FilterTags *[]string               `json:"filter_tags,omitempty"`
	Metadata   *map[string]interface{} `json:"metadata,omitempty"`
	SourceId   *string                 `json:"source_id,omitempty"`
}

// DocumentDetails defines model for DocumentDetails.
type DocumentDetails struct {
	Content          *string                          `json:"content,omitempty"`
	CreatedAt        *time.Time                       `json:"created_at,omitempty"`
	FilterTags       *[]string                        `json:"filter_tags,omitempty"`
	Id               *openapi_types.UUID              `json:"id,omitempty"`
	Metadata         *map[string]interface{}          `json:"metadata,omitempty"`
	ProcessingStatus *DocumentDetailsProcessingStatus `json:"processing_status,omitempty"`
	SourceId         *string                          `json:"source_id,omitempty"`
	UpdatedAt        *time.Time                       `json:"updated_at,omitempty"`
	VectorEmbeddings *[]float32                       `json:"vector_embeddings,omitempty"`
}

// DocumentDetailsProcessingStatus defines model for DocumentDetails.ProcessingStatus.
type DocumentDetailsProcessingStatus string

// DocumentUpdate defines model for DocumentUpdate.
type DocumentUpdate struct {
	Content    *string                 `json:"content,omitempty"`
	FilterTags *[]string               `json:"filter_tags,omitempty"`
	Metadata   *map[string]interface{} `json:"metadata,omitempty"`
	SourceId   *string                 `json:"source_id,omitempty"`
}

// Error defines model for Error.
type Error struct {
	Code    string                  `json:"code"`
	Details *map[string]interface{} `json:"details,omitempty"`
	Message string                  `json:"message"`
}

// ErrorResponse defines model for ErrorResponse.
type ErrorResponse struct {
	Error   *string `json:"error,omitempty"`
	Message *string `json:"message,omitempty"`
}

// FilterTag defines model for FilterTag.
type FilterTag struct {
	Color     *string             `json:"color,omitempty"`
	CreatedAt *time.Time          `json:"created_at,omitempty"`
	Id        *openapi_types.UUID `json:"id,omitempty"`
	Name      *string             `json:"name,omitempty"`
}

// FilterTagCreate defines model for FilterTagCreate.
type FilterTagCreate struct {
	Color *string `json:"color,omitempty"`
	Name  string  `json:"name"`
}

// Interaction defines model for Interaction.
type Interaction struct {
	CompanyId           *openapi_types.UUID         `json:"company_id"`
	ContactId           *openapi_types.UUID         `json:"contact_id"`
	CreatedAt           *time.Time                  `json:"created_at,omitempty"`
	CreatedBy           *openapi_types.UUID         `json:"created_by,omitempty"`
	Id                  *openapi_types.UUID         `json:"id,omitempty"`
	InteractionDatetime *time.Time                  `json:"interaction_datetime,omitempty"`
	InteractionType     *InteractionInteractionType `json:"interaction_type,omitempty"`
	Notes               *string                     `json:"notes,omitempty"`
	UpdatedAt           *time.Time                  `json:"updated_at,omitempty"`
}

// InteractionInteractionType defines model for Interaction.InteractionType.
type InteractionInteractionType string

// InteractionCreate defines model for InteractionCreate.
type InteractionCreate struct {
	CompanyId           *openapi_types.UUID              `json:"company_id,omitempty"`
	ContactId           *openapi_types.UUID              `json:"contact_id,omitempty"`
	InteractionDatetime time.Time                        `json:"interaction_datetime"`
	InteractionType     InteractionCreateInteractionType `json:"interaction_type"`
	Notes               string                           `json:"notes"`
}

// InteractionCreateInteractionType defines model for InteractionCreate.InteractionType.
type InteractionCreateInteractionType string

// InteractionDetails defines model for InteractionDetails.
type InteractionDetails struct {
	Company *struct {
		Id      *openapi_types.UUID `json:"id,omitempty"`
		Name    *string             `json:"name,omitempty"`
		Phone   *string             `json:"phone,omitempty"`
		Website *string             `json:"website,omitempty"`
	} `json:"company"`
	CompanyId *openapi_types.UUID `json:"company_id"`
	Contact   *struct {
		Email     *string             `json:"email,omitempty"`
		FirstName *string             `json:"first_name,omitempty"`
		Id        *openapi_types.UUID `json:"id,omitempty"`
		JobTitle  *string             `json:"job_title,omitempty"`
		LastName  *string             `json:"last_name,omitempty"`
		Phone     *string             `json:"phone,omitempty"`
	} `json:"contact"`
	ContactId           *openapi_types.UUID                `json:"contact_id"`
	CreatedAt           *time.Time                         `json:"created_at,omitempty"`
	CreatedBy           *openapi_types.UUID                `json:"created_by,omitempty"`
	Id                  *openapi_types.UUID                `json:"id,omitempty"`
	InteractionDatetime *time.Time                         `json:"interaction_datetime,omitempty"`
	InteractionType     *InteractionDetailsInteractionType `json:"interaction_type,omitempty"`
	Notes               *string                            `json:"notes,omitempty"`
	UpdatedAt           *time.Time                         `json:"updated_at,omitempty"`
}

// InteractionDetailsInteractionType defines model for InteractionDetails.InteractionType.
type InteractionDetailsInteractionType string

// InteractionUpdate defines model for InteractionUpdate.
type InteractionUpdate struct {
	CompanyId           *openapi_types.UUID               `json:"company_id,omitempty"`
	ContactId           *openapi_types.UUID               `json:"contact_id,omitempty"`
	InteractionDatetime *time.Time                        `json:"interaction_datetime,omitempty"`
	InteractionType     *InteractionUpdateInteractionType `json:"interaction_type,omitempty"`
	Notes               *string                           `json:"notes,omitempty"`
}

// InteractionUpdateInteractionType defines model for InteractionUpdate.InteractionType.
type InteractionUpdateInteractionType string

// InteractionWithDetails defines model for InteractionWithDetails.
type InteractionWithDetails struct {
	Company *struct {
		Id   *openapi_types.UUID `json:"id,omitempty"`
		Name *string             `json:"name,omitempty"`
	} `json:"company"`
	CompanyId *openapi_types.UUID `json:"company_id"`
	Contact   *struct {
		Email     *string             `json:"email,omitempty"`
		FirstName *string             `json:"first_name,omitempty"`
		Id        *openapi_types.UUID `json:"id,omitempty"`
		LastName  *string             `json:"last_name,omitempty"`
	} `json:"contact"`
	ContactId           *openapi_types.UUID                    `json:"contact_id"`
	CreatedAt           *time.Time                             `json:"created_at,omitempty"`
	CreatedBy           *openapi_types.UUID                    `json:"created_by,omitempty"`
	Id                  *openapi_types.UUID                    `json:"id,omitempty"`
	InteractionDatetime *time.Time                             `json:"interaction_datetime,omitempty"`
	InteractionType     *InteractionWithDetailsInteractionType `json:"interaction_type,omitempty"`
	Notes               *string                                `json:"notes,omitempty"`
	UpdatedAt           *time.Time                             `json:"updated_at,omitempty"`
}

// InteractionWithDetailsInteractionType defines model for InteractionWithDetails.InteractionType.
type InteractionWithDetailsInteractionType string

// Session defines model for Session.
type Session struct {
	AccessToken  *string `json:"access_token,omitempty"`
	ExpiresIn    *int    `json:"expires_in,omitempty"`
	RefreshToken *string `json:"refresh_token,omitempty"`
	TokenType    *string `json:"token_type,omitempty"`
	User         *User   `json:"user,omitempty"`
}

// User defines model for User.
type User struct {
	CreatedAt        *time.Time           `json:"created_at,omitempty"`
	Email            *openapi_types.Email `json:"email,omitempty"`
	EmailConfirmedAt *time.Time           `json:"email_confirmed_at,omitempty"`
	Id               *openapi_types.UUID  `json:"id,omitempty"`
	UpdatedAt        *time.Time           `json:"updated_at,omitempty"`
}

// UserProfile defines model for UserProfile.
type UserProfile struct {
	AvatarUrl *string             `json:"avatar_url,omitempty"`
	CreatedAt *time.Time          `json:"created_at,omitempty"`
	FullName  *string             `json:"full_name,omitempty"`
	Id        *openapi_types.UUID `json:"id,omitempty"`
	Timezone  *string             `json:"timezone,omitempty"`
	UpdatedAt *time.Time          `json:"updated_at,omitempty"`
}

// UserProfileUpdate defines model for UserProfileUpdate.
type UserProfileUpdate struct {
	AvatarUrl *string `json:"avatar_url,omitempty"`
	FullName  *string `json:"full_name,omitempty"`
	Timezone  *string `json:"timezone,omitempty"`
}

// SchemasValidationError defines model for schemas_ValidationError.
type SchemasValidationError struct {
	Code    *string                 `json:"code,omitempty"`
	Details *map[string]interface{} `json:"details,omitempty"`
	Message *string                 `json:"message,omitempty"`
}

// GetArliDocumentsParams defines parameters for GetArliDocuments.
type GetArliDocumentsParams struct {
	// FilterTags Filter by tags
	FilterTags *[]string `form:"filter_tags,omitempty" json:"filter_tags,omitempty"`

	// MetadataFilter Filter by metadata properties
	MetadataFilter *map[string]interface{} `form:"metadata_filter,omitempty" json:"metadata_filter,omitempty"`
	Limit          *int                    `form:"limit,omitempty" json:"limit,omitempty"`
	Offset         *int                    `form:"offset,omitempty" json:"offset,omitempty"`
}

// PostArliDocumentsVectorizeJSONBody defines parameters for PostArliDocumentsVectorize.
type PostArliDocumentsVectorizeJSONBody struct {
	Content    string                  `json:"content"`
	DocumentId openapi_types.UUID      `json:"document_id"`
	Metadata   *map[string]interface{} `json:"metadata,omitempty"`
}

// GetAuthCallbackProviderParams defines parameters for GetAuthCallbackProvider.
type GetAuthCallbackProviderParams struct {
	Code  string  `form:"code" json:"code"`
	State string  `form:"state" json:"state"`
	Error *string `form:"error,omitempty" json:"error,omitempty"`
}

// GetAuthCallbackProviderParamsProvider defines parameters for GetAuthCallbackProvider.
type GetAuthCallbackProviderParamsProvider string

// PostAuthSigninJSONBody defines parameters for PostAuthSignin.
type PostAuthSigninJSONBody struct {
	Email    openapi_types.Email `json:"email"`
	Password string              `json:"password"`
}

// PostAuthSigninOauthJSONBody defines parameters for PostAuthSigninOauth.
type PostAuthSigninOauthJSONBody struct {
	Provider   PostAuthSigninOauthJSONBodyProvider `json:"provider"`
	RedirectTo *string                             `json:"redirectTo,omitempty"`
}

// PostAuthSigninOauthJSONBodyProvider defines parameters for PostAuthSigninOauth.
type PostAuthSigninOauthJSONBodyProvider string

// GetCompaniesParams defines parameters for GetCompanies.
type GetCompaniesParams struct {
	// StatusId Filter by company status
	StatusId *openapi_types.UUID `form:"status_id,omitempty" json:"status_id,omitempty"`

	// IncludeDeleted Include soft-deleted companies
	IncludeDeleted *bool `form:"include_deleted,omitempty" json:"include_deleted,omitempty"`
}

// PutCompaniesIdStatusJSONBody defines parameters for PutCompaniesIdStatus.
type PutCompaniesIdStatusJSONBody struct {
	CompanyStatusId openapi_types.UUID `json:"company_status_id"`
}

// GetContactsParams defines parameters for GetContacts.
type GetContactsParams struct {
	// CompanyId Filter by company ID
	CompanyId *openapi_types.UUID `form:"company_id,omitempty" json:"company_id,omitempty"`

	// Unlinked Only return contacts without a company
	Unlinked *bool `form:"unlinked,omitempty" json:"unlinked,omitempty"`

	// IncludeDeleted Include soft-deleted contacts
	IncludeDeleted *bool `form:"include_deleted,omitempty" json:"include_deleted,omitempty"`
}

// PutContactsIdCompanyJSONBody defines parameters for PutContactsIdCompany.
type PutContactsIdCompanyJSONBody struct {
	CompanyId openapi_types.UUID `json:"company_id"`
}

// GetDealsParams defines parameters for GetDeals.
type GetDealsParams struct {
	// StageId Filter by deal stage ID
	StageId *openapi_types.UUID `form:"stage_id,omitempty" json:"stage_id,omitempty"`

	// CompanyId Filter by company ID
	CompanyId *openapi_types.UUID `form:"company_id,omitempty" json:"company_id,omitempty"`
}

// GetInteractionsParams defines parameters for GetInteractions.
type GetInteractionsParams struct {
	// CompanyId Filter by company ID
	CompanyId *openapi_types.UUID `form:"company_id,omitempty" json:"company_id,omitempty"`

	// ContactId Filter by contact ID
	ContactId *openapi_types.UUID `form:"contact_id,omitempty" json:"contact_id,omitempty"`

	// InteractionType Filter by interaction type
	InteractionType *string `form:"interaction_type,omitempty" json:"interaction_type,omitempty"`

	// FromDate Filter interactions from this date
	FromDate *time.Time `form:"from_date,omitempty" json:"from_date,omitempty"`

	// ToDate Filter interactions to this date
	ToDate *time.Time `form:"to_date,omitempty" json:"to_date,omitempty"`

	// Limit Number of interactions to return
	Limit *int `form:"limit,omitempty" json:"limit,omitempty"`

	// Offset Number of interactions to skip
	Offset *int `form:"offset,omitempty" json:"offset,omitempty"`
}

// PostArliDocumentsJSONRequestBody defines body for PostArliDocuments for application/json ContentType.
type PostArliDocumentsJSONRequestBody = DocumentCreate

// PostArliDocumentsVectorizeJSONRequestBody defines body for PostArliDocumentsVectorize for application/json ContentType.
type PostArliDocumentsVectorizeJSONRequestBody PostArliDocumentsVectorizeJSONBody

// PutArliDocumentsIdJSONRequestBody defines body for PutArliDocumentsId for application/json ContentType.
type PutArliDocumentsIdJSONRequestBody = DocumentUpdate

// PostArliFilterTagsJSONRequestBody defines body for PostArliFilterTags for application/json ContentType.
type PostArliFilterTagsJSONRequestBody = FilterTagCreate

// PostAuthSigninJSONRequestBody defines body for PostAuthSignin for application/json ContentType.
type PostAuthSigninJSONRequestBody PostAuthSigninJSONBody

// PostAuthSigninOauthJSONRequestBody defines body for PostAuthSigninOauth for application/json ContentType.
type PostAuthSigninOauthJSONRequestBody PostAuthSigninOauthJSONBody

// PostCompaniesJSONRequestBody defines body for PostCompanies for application/json ContentType.
type PostCompaniesJSONRequestBody = CompanyCreate

// PutCompaniesIdJSONRequestBody defines body for PutCompaniesId for application/json ContentType.
type PutCompaniesIdJSONRequestBody = CompanyUpdate

// PutCompaniesIdStatusJSONRequestBody defines body for PutCompaniesIdStatus for application/json ContentType.
type PutCompaniesIdStatusJSONRequestBody PutCompaniesIdStatusJSONBody

// PostContactsJSONRequestBody defines body for PostContacts for application/json ContentType.
type PostContactsJSONRequestBody = ContactCreate

// PutContactsIdJSONRequestBody defines body for PutContactsId for application/json ContentType.
type PutContactsIdJSONRequestBody = ContactUpdate

// PutContactsIdCompanyJSONRequestBody defines body for PutContactsIdCompany for application/json ContentType.
type PutContactsIdCompanyJSONRequestBody PutContactsIdCompanyJSONBody

// PostDealsJSONRequestBody defines body for PostDeals for application/json ContentType.
type PostDealsJSONRequestBody = DealCreate

// PutDealsIdJSONRequestBody defines body for PutDealsId for application/json ContentType.
type PutDealsIdJSONRequestBody = DealUpdate

// PostInteractionsJSONRequestBody defines body for PostInteractions for application/json ContentType.
type PostInteractionsJSONRequestBody = InteractionCreate

// PutInteractionsIdJSONRequestBody defines body for PutInteractionsId for application/json ContentType.
type PutInteractionsIdJSONRequestBody = InteractionUpdate

// PutUsersProfileJSONRequestBody defines body for PutUsersProfile for application/json ContentType.
type PutUsersProfileJSONRequestBody = UserProfileUpdate

// RequestEditorFn  is the function signature for the RequestEditor callback function
type RequestEditorFn func(ctx context.Context, req *http.Request) error

// Doer performs HTTP requests.
//
// The standard http.Client implements this interface.
type HttpRequestDoer interface {
	Do(req *http.Request) (*http.Response, error)
}

// Client which conforms to the OpenAPI3 specification for this service.
type Client struct {
	// The endpoint of the server conforming to this interface, with scheme,
	// https://api.deepmap.com for example. This can contain a path relative
	// to the server, such as https://api.deepmap.com/dev-test, and all the
	// paths in the swagger spec will be appended to the server.
	Server string

	// Doer for performing requests, typically a *http.Client with any
	// customized settings, such as certificate chains.
	Client HttpRequestDoer

	// A list of callbacks for modifying requests which are generated before sending over
	// the network.
	RequestEditors []RequestEditorFn
}

// ClientOption allows setting custom parameters during construction
type ClientOption func(*Client) error

// Creates a new Client, with reasonable defaults
func NewClient(server string, opts ...ClientOption) (*Client, error) {
	// create a client with sane default values
	client := Client{
		Server: server,
	}
	// mutate client and add all optional params
	for _, o := range opts {
		if err := o(&client); err != nil {
			return nil, err
		}
	}
	// ensure the server URL always has a trailing slash
	if !strings.HasSuffix(client.Server, "/") {
		client.Server += "/"
	}
	// create httpClient, if not already present
	if client.Client == nil {
		client.Client = &http.Client{}
	}
	return &client, nil
}

// WithHTTPClient allows overriding the default Doer, which is
// automatically created using http.Client. This is useful for tests.
func WithHTTPClient(doer HttpRequestDoer) ClientOption {
	return func(c *Client) error {
		c.Client = doer
		return nil
	}
}

// WithRequestEditorFn allows setting up a callback function, which will be
// called right before sending the request. This can be used to mutate the request.
func WithRequestEditorFn(fn RequestEditorFn) ClientOption {
	return func(c *Client) error {
		c.RequestEditors = append(c.RequestEditors, fn)
		return nil
	}
}

// The interface specification for the client above.
type ClientInterface interface {
	// GetArliDocuments request
	GetArliDocuments(ctx context.Context, params *GetArliDocumentsParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostArliDocumentsWithBody request with any body
	PostArliDocumentsWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostArliDocuments(ctx context.Context, body PostArliDocumentsJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostArliDocumentsVectorizeWithBody request with any body
	PostArliDocumentsVectorizeWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostArliDocumentsVectorize(ctx context.Context, body PostArliDocumentsVectorizeJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// DeleteArliDocumentsId request
	DeleteArliDocumentsId(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetArliDocumentsId request
	GetArliDocumentsId(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PutArliDocumentsIdWithBody request with any body
	PutArliDocumentsIdWithBody(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PutArliDocumentsId(ctx context.Context, id openapi_types.UUID, body PutArliDocumentsIdJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetArliFilterTags request
	GetArliFilterTags(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostArliFilterTagsWithBody request with any body
	PostArliFilterTagsWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostArliFilterTags(ctx context.Context, body PostArliFilterTagsJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetAuthCallbackProvider request
	GetAuthCallbackProvider(ctx context.Context, provider GetAuthCallbackProviderParamsProvider, params *GetAuthCallbackProviderParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetAuthSession request
	GetAuthSession(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostAuthSigninWithBody request with any body
	PostAuthSigninWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostAuthSignin(ctx context.Context, body PostAuthSigninJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostAuthSigninOauthWithBody request with any body
	PostAuthSigninOauthWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostAuthSigninOauth(ctx context.Context, body PostAuthSigninOauthJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostAuthSignout request
	PostAuthSignout(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetAuthUser request
	GetAuthUser(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetCompanies request
	GetCompanies(ctx context.Context, params *GetCompaniesParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostCompaniesWithBody request with any body
	PostCompaniesWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostCompanies(ctx context.Context, body PostCompaniesJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// DeleteCompaniesId request
	DeleteCompaniesId(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetCompaniesId request
	GetCompaniesId(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PutCompaniesIdWithBody request with any body
	PutCompaniesIdWithBody(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PutCompaniesId(ctx context.Context, id openapi_types.UUID, body PutCompaniesIdJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PutCompaniesIdStatusWithBody request with any body
	PutCompaniesIdStatusWithBody(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PutCompaniesIdStatus(ctx context.Context, id openapi_types.UUID, body PutCompaniesIdStatusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetCompanyStatuses request
	GetCompanyStatuses(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetContacts request
	GetContacts(ctx context.Context, params *GetContactsParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostContactsWithBody request with any body
	PostContactsWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostContacts(ctx context.Context, body PostContactsJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// DeleteContactsId request
	DeleteContactsId(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetContactsId request
	GetContactsId(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PutContactsIdWithBody request with any body
	PutContactsIdWithBody(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PutContactsId(ctx context.Context, id openapi_types.UUID, body PutContactsIdJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// DeleteContactsIdCompany request
	DeleteContactsIdCompany(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PutContactsIdCompanyWithBody request with any body
	PutContactsIdCompanyWithBody(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PutContactsIdCompany(ctx context.Context, id openapi_types.UUID, body PutContactsIdCompanyJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetDealStages request
	GetDealStages(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetDeals request
	GetDeals(ctx context.Context, params *GetDealsParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostDealsWithBody request with any body
	PostDealsWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostDeals(ctx context.Context, body PostDealsJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// DeleteDealsId request
	DeleteDealsId(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetDealsId request
	GetDealsId(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PutDealsIdWithBody request with any body
	PutDealsIdWithBody(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PutDealsId(ctx context.Context, id openapi_types.UUID, body PutDealsIdJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetInteractions request
	GetInteractions(ctx context.Context, params *GetInteractionsParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostInteractionsWithBody request with any body
	PostInteractionsWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostInteractions(ctx context.Context, body PostInteractionsJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// DeleteInteractionsId request
	DeleteInteractionsId(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetInteractionsId request
	GetInteractionsId(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PutInteractionsIdWithBody request with any body
	PutInteractionsIdWithBody(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PutInteractionsId(ctx context.Context, id openapi_types.UUID, body PutInteractionsIdJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetUsersProfile request
	GetUsersProfile(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PutUsersProfileWithBody request with any body
	PutUsersProfileWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PutUsersProfile(ctx context.Context, body PutUsersProfileJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)
}

func (c *Client) GetArliDocuments(ctx context.Context, params *GetArliDocumentsParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetArliDocumentsRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostArliDocumentsWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostArliDocumentsRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostArliDocuments(ctx context.Context, body PostArliDocumentsJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostArliDocumentsRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostArliDocumentsVectorizeWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostArliDocumentsVectorizeRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostArliDocumentsVectorize(ctx context.Context, body PostArliDocumentsVectorizeJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostArliDocumentsVectorizeRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeleteArliDocumentsId(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteArliDocumentsIdRequest(c.Server, id)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetArliDocumentsId(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetArliDocumentsIdRequest(c.Server, id)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutArliDocumentsIdWithBody(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutArliDocumentsIdRequestWithBody(c.Server, id, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutArliDocumentsId(ctx context.Context, id openapi_types.UUID, body PutArliDocumentsIdJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutArliDocumentsIdRequest(c.Server, id, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetArliFilterTags(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetArliFilterTagsRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostArliFilterTagsWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostArliFilterTagsRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostArliFilterTags(ctx context.Context, body PostArliFilterTagsJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostArliFilterTagsRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetAuthCallbackProvider(ctx context.Context, provider GetAuthCallbackProviderParamsProvider, params *GetAuthCallbackProviderParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetAuthCallbackProviderRequest(c.Server, provider, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetAuthSession(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetAuthSessionRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostAuthSigninWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostAuthSigninRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostAuthSignin(ctx context.Context, body PostAuthSigninJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostAuthSigninRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostAuthSigninOauthWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostAuthSigninOauthRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostAuthSigninOauth(ctx context.Context, body PostAuthSigninOauthJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostAuthSigninOauthRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostAuthSignout(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostAuthSignoutRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetAuthUser(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetAuthUserRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetCompanies(ctx context.Context, params *GetCompaniesParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetCompaniesRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostCompaniesWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostCompaniesRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostCompanies(ctx context.Context, body PostCompaniesJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostCompaniesRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeleteCompaniesId(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteCompaniesIdRequest(c.Server, id)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetCompaniesId(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetCompaniesIdRequest(c.Server, id)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutCompaniesIdWithBody(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutCompaniesIdRequestWithBody(c.Server, id, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutCompaniesId(ctx context.Context, id openapi_types.UUID, body PutCompaniesIdJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutCompaniesIdRequest(c.Server, id, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutCompaniesIdStatusWithBody(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutCompaniesIdStatusRequestWithBody(c.Server, id, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutCompaniesIdStatus(ctx context.Context, id openapi_types.UUID, body PutCompaniesIdStatusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutCompaniesIdStatusRequest(c.Server, id, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetCompanyStatuses(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetCompanyStatusesRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetContacts(ctx context.Context, params *GetContactsParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetContactsRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostContactsWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostContactsRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostContacts(ctx context.Context, body PostContactsJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostContactsRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeleteContactsId(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteContactsIdRequest(c.Server, id)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetContactsId(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetContactsIdRequest(c.Server, id)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutContactsIdWithBody(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutContactsIdRequestWithBody(c.Server, id, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutContactsId(ctx context.Context, id openapi_types.UUID, body PutContactsIdJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutContactsIdRequest(c.Server, id, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeleteContactsIdCompany(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteContactsIdCompanyRequest(c.Server, id)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutContactsIdCompanyWithBody(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutContactsIdCompanyRequestWithBody(c.Server, id, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutContactsIdCompany(ctx context.Context, id openapi_types.UUID, body PutContactsIdCompanyJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutContactsIdCompanyRequest(c.Server, id, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetDealStages(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetDealStagesRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetDeals(ctx context.Context, params *GetDealsParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetDealsRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostDealsWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostDealsRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostDeals(ctx context.Context, body PostDealsJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostDealsRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeleteDealsId(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteDealsIdRequest(c.Server, id)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetDealsId(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetDealsIdRequest(c.Server, id)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutDealsIdWithBody(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutDealsIdRequestWithBody(c.Server, id, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutDealsId(ctx context.Context, id openapi_types.UUID, body PutDealsIdJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutDealsIdRequest(c.Server, id, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetInteractions(ctx context.Context, params *GetInteractionsParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetInteractionsRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostInteractionsWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostInteractionsRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostInteractions(ctx context.Context, body PostInteractionsJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostInteractionsRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeleteInteractionsId(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteInteractionsIdRequest(c.Server, id)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetInteractionsId(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetInteractionsIdRequest(c.Server, id)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutInteractionsIdWithBody(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutInteractionsIdRequestWithBody(c.Server, id, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutInteractionsId(ctx context.Context, id openapi_types.UUID, body PutInteractionsIdJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutInteractionsIdRequest(c.Server, id, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetUsersProfile(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetUsersProfileRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutUsersProfileWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutUsersProfileRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutUsersProfile(ctx context.Context, body PutUsersProfileJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutUsersProfileRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

// NewGetArliDocumentsRequest generates requests for GetArliDocuments
func NewGetArliDocumentsRequest(server string, params *GetArliDocumentsParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/arli/documents")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.FilterTags != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "filter_tags", runtime.ParamLocationQuery, *params.FilterTags); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.MetadataFilter != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "metadata_filter", runtime.ParamLocationQuery, *params.MetadataFilter); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.Limit != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "limit", runtime.ParamLocationQuery, *params.Limit); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.Offset != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "offset", runtime.ParamLocationQuery, *params.Offset); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostArliDocumentsRequest calls the generic PostArliDocuments builder with application/json body
func NewPostArliDocumentsRequest(server string, body PostArliDocumentsJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostArliDocumentsRequestWithBody(server, "application/json", bodyReader)
}

// NewPostArliDocumentsRequestWithBody generates requests for PostArliDocuments with any type of body
func NewPostArliDocumentsRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/arli/documents")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostArliDocumentsVectorizeRequest calls the generic PostArliDocumentsVectorize builder with application/json body
func NewPostArliDocumentsVectorizeRequest(server string, body PostArliDocumentsVectorizeJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostArliDocumentsVectorizeRequestWithBody(server, "application/json", bodyReader)
}

// NewPostArliDocumentsVectorizeRequestWithBody generates requests for PostArliDocumentsVectorize with any type of body
func NewPostArliDocumentsVectorizeRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/arli/documents/vectorize")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewDeleteArliDocumentsIdRequest generates requests for DeleteArliDocumentsId
func NewDeleteArliDocumentsIdRequest(server string, id openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/arli/documents/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetArliDocumentsIdRequest generates requests for GetArliDocumentsId
func NewGetArliDocumentsIdRequest(server string, id openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/arli/documents/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPutArliDocumentsIdRequest calls the generic PutArliDocumentsId builder with application/json body
func NewPutArliDocumentsIdRequest(server string, id openapi_types.UUID, body PutArliDocumentsIdJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPutArliDocumentsIdRequestWithBody(server, id, "application/json", bodyReader)
}

// NewPutArliDocumentsIdRequestWithBody generates requests for PutArliDocumentsId with any type of body
func NewPutArliDocumentsIdRequestWithBody(server string, id openapi_types.UUID, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/arli/documents/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PUT", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetArliFilterTagsRequest generates requests for GetArliFilterTags
func NewGetArliFilterTagsRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/arli/filter-tags")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostArliFilterTagsRequest calls the generic PostArliFilterTags builder with application/json body
func NewPostArliFilterTagsRequest(server string, body PostArliFilterTagsJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostArliFilterTagsRequestWithBody(server, "application/json", bodyReader)
}

// NewPostArliFilterTagsRequestWithBody generates requests for PostArliFilterTags with any type of body
func NewPostArliFilterTagsRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/arli/filter-tags")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetAuthCallbackProviderRequest generates requests for GetAuthCallbackProvider
func NewGetAuthCallbackProviderRequest(server string, provider GetAuthCallbackProviderParamsProvider, params *GetAuthCallbackProviderParams) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "provider", runtime.ParamLocationPath, provider)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/auth/callback/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "code", runtime.ParamLocationQuery, params.Code); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "state", runtime.ParamLocationQuery, params.State); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

		if params.Error != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "error", runtime.ParamLocationQuery, *params.Error); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetAuthSessionRequest generates requests for GetAuthSession
func NewGetAuthSessionRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/auth/session")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostAuthSigninRequest calls the generic PostAuthSignin builder with application/json body
func NewPostAuthSigninRequest(server string, body PostAuthSigninJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostAuthSigninRequestWithBody(server, "application/json", bodyReader)
}

// NewPostAuthSigninRequestWithBody generates requests for PostAuthSignin with any type of body
func NewPostAuthSigninRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/auth/signin")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostAuthSigninOauthRequest calls the generic PostAuthSigninOauth builder with application/json body
func NewPostAuthSigninOauthRequest(server string, body PostAuthSigninOauthJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostAuthSigninOauthRequestWithBody(server, "application/json", bodyReader)
}

// NewPostAuthSigninOauthRequestWithBody generates requests for PostAuthSigninOauth with any type of body
func NewPostAuthSigninOauthRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/auth/signin/oauth")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostAuthSignoutRequest generates requests for PostAuthSignout
func NewPostAuthSignoutRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/auth/signout")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetAuthUserRequest generates requests for GetAuthUser
func NewGetAuthUserRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/auth/user")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetCompaniesRequest generates requests for GetCompanies
func NewGetCompaniesRequest(server string, params *GetCompaniesParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/companies")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.StatusId != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "status_id", runtime.ParamLocationQuery, *params.StatusId); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.IncludeDeleted != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "include_deleted", runtime.ParamLocationQuery, *params.IncludeDeleted); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostCompaniesRequest calls the generic PostCompanies builder with application/json body
func NewPostCompaniesRequest(server string, body PostCompaniesJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostCompaniesRequestWithBody(server, "application/json", bodyReader)
}

// NewPostCompaniesRequestWithBody generates requests for PostCompanies with any type of body
func NewPostCompaniesRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/companies")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewDeleteCompaniesIdRequest generates requests for DeleteCompaniesId
func NewDeleteCompaniesIdRequest(server string, id openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/companies/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetCompaniesIdRequest generates requests for GetCompaniesId
func NewGetCompaniesIdRequest(server string, id openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/companies/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPutCompaniesIdRequest calls the generic PutCompaniesId builder with application/json body
func NewPutCompaniesIdRequest(server string, id openapi_types.UUID, body PutCompaniesIdJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPutCompaniesIdRequestWithBody(server, id, "application/json", bodyReader)
}

// NewPutCompaniesIdRequestWithBody generates requests for PutCompaniesId with any type of body
func NewPutCompaniesIdRequestWithBody(server string, id openapi_types.UUID, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/companies/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PUT", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPutCompaniesIdStatusRequest calls the generic PutCompaniesIdStatus builder with application/json body
func NewPutCompaniesIdStatusRequest(server string, id openapi_types.UUID, body PutCompaniesIdStatusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPutCompaniesIdStatusRequestWithBody(server, id, "application/json", bodyReader)
}

// NewPutCompaniesIdStatusRequestWithBody generates requests for PutCompaniesIdStatus with any type of body
func NewPutCompaniesIdStatusRequestWithBody(server string, id openapi_types.UUID, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/companies/%s/status", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PUT", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetCompanyStatusesRequest generates requests for GetCompanyStatuses
func NewGetCompanyStatusesRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/company-statuses")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetContactsRequest generates requests for GetContacts
func NewGetContactsRequest(server string, params *GetContactsParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/contacts")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.CompanyId != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "company_id", runtime.ParamLocationQuery, *params.CompanyId); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.Unlinked != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "unlinked", runtime.ParamLocationQuery, *params.Unlinked); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.IncludeDeleted != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "include_deleted", runtime.ParamLocationQuery, *params.IncludeDeleted); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostContactsRequest calls the generic PostContacts builder with application/json body
func NewPostContactsRequest(server string, body PostContactsJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostContactsRequestWithBody(server, "application/json", bodyReader)
}

// NewPostContactsRequestWithBody generates requests for PostContacts with any type of body
func NewPostContactsRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/contacts")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewDeleteContactsIdRequest generates requests for DeleteContactsId
func NewDeleteContactsIdRequest(server string, id openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/contacts/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetContactsIdRequest generates requests for GetContactsId
func NewGetContactsIdRequest(server string, id openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/contacts/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPutContactsIdRequest calls the generic PutContactsId builder with application/json body
func NewPutContactsIdRequest(server string, id openapi_types.UUID, body PutContactsIdJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPutContactsIdRequestWithBody(server, id, "application/json", bodyReader)
}

// NewPutContactsIdRequestWithBody generates requests for PutContactsId with any type of body
func NewPutContactsIdRequestWithBody(server string, id openapi_types.UUID, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/contacts/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PUT", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewDeleteContactsIdCompanyRequest generates requests for DeleteContactsIdCompany
func NewDeleteContactsIdCompanyRequest(server string, id openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/contacts/%s/company", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPutContactsIdCompanyRequest calls the generic PutContactsIdCompany builder with application/json body
func NewPutContactsIdCompanyRequest(server string, id openapi_types.UUID, body PutContactsIdCompanyJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPutContactsIdCompanyRequestWithBody(server, id, "application/json", bodyReader)
}

// NewPutContactsIdCompanyRequestWithBody generates requests for PutContactsIdCompany with any type of body
func NewPutContactsIdCompanyRequestWithBody(server string, id openapi_types.UUID, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/contacts/%s/company", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PUT", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetDealStagesRequest generates requests for GetDealStages
func NewGetDealStagesRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/deal-stages")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetDealsRequest generates requests for GetDeals
func NewGetDealsRequest(server string, params *GetDealsParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/deals")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.StageId != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "stage_id", runtime.ParamLocationQuery, *params.StageId); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.CompanyId != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "company_id", runtime.ParamLocationQuery, *params.CompanyId); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostDealsRequest calls the generic PostDeals builder with application/json body
func NewPostDealsRequest(server string, body PostDealsJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostDealsRequestWithBody(server, "application/json", bodyReader)
}

// NewPostDealsRequestWithBody generates requests for PostDeals with any type of body
func NewPostDealsRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/deals")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewDeleteDealsIdRequest generates requests for DeleteDealsId
func NewDeleteDealsIdRequest(server string, id openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/deals/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetDealsIdRequest generates requests for GetDealsId
func NewGetDealsIdRequest(server string, id openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/deals/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPutDealsIdRequest calls the generic PutDealsId builder with application/json body
func NewPutDealsIdRequest(server string, id openapi_types.UUID, body PutDealsIdJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPutDealsIdRequestWithBody(server, id, "application/json", bodyReader)
}

// NewPutDealsIdRequestWithBody generates requests for PutDealsId with any type of body
func NewPutDealsIdRequestWithBody(server string, id openapi_types.UUID, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/deals/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PUT", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetInteractionsRequest generates requests for GetInteractions
func NewGetInteractionsRequest(server string, params *GetInteractionsParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/interactions")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.CompanyId != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "company_id", runtime.ParamLocationQuery, *params.CompanyId); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.ContactId != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "contact_id", runtime.ParamLocationQuery, *params.ContactId); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.InteractionType != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "interaction_type", runtime.ParamLocationQuery, *params.InteractionType); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.FromDate != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "from_date", runtime.ParamLocationQuery, *params.FromDate); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.ToDate != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "to_date", runtime.ParamLocationQuery, *params.ToDate); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.Limit != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "limit", runtime.ParamLocationQuery, *params.Limit); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.Offset != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "offset", runtime.ParamLocationQuery, *params.Offset); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostInteractionsRequest calls the generic PostInteractions builder with application/json body
func NewPostInteractionsRequest(server string, body PostInteractionsJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostInteractionsRequestWithBody(server, "application/json", bodyReader)
}

// NewPostInteractionsRequestWithBody generates requests for PostInteractions with any type of body
func NewPostInteractionsRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/interactions")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewDeleteInteractionsIdRequest generates requests for DeleteInteractionsId
func NewDeleteInteractionsIdRequest(server string, id openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/interactions/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetInteractionsIdRequest generates requests for GetInteractionsId
func NewGetInteractionsIdRequest(server string, id openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/interactions/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPutInteractionsIdRequest calls the generic PutInteractionsId builder with application/json body
func NewPutInteractionsIdRequest(server string, id openapi_types.UUID, body PutInteractionsIdJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPutInteractionsIdRequestWithBody(server, id, "application/json", bodyReader)
}

// NewPutInteractionsIdRequestWithBody generates requests for PutInteractionsId with any type of body
func NewPutInteractionsIdRequestWithBody(server string, id openapi_types.UUID, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/interactions/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PUT", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetUsersProfileRequest generates requests for GetUsersProfile
func NewGetUsersProfileRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/users/profile")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPutUsersProfileRequest calls the generic PutUsersProfile builder with application/json body
func NewPutUsersProfileRequest(server string, body PutUsersProfileJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPutUsersProfileRequestWithBody(server, "application/json", bodyReader)
}

// NewPutUsersProfileRequestWithBody generates requests for PutUsersProfile with any type of body
func NewPutUsersProfileRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/users/profile")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PUT", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

func (c *Client) applyEditors(ctx context.Context, req *http.Request, additionalEditors []RequestEditorFn) error {
	for _, r := range c.RequestEditors {
		if err := r(ctx, req); err != nil {
			return err
		}
	}
	for _, r := range additionalEditors {
		if err := r(ctx, req); err != nil {
			return err
		}
	}
	return nil
}

// ClientWithResponses builds on ClientInterface to offer response payloads
type ClientWithResponses struct {
	ClientInterface
}

// NewClientWithResponses creates a new ClientWithResponses, which wraps
// Client with return type handling
func NewClientWithResponses(server string, opts ...ClientOption) (*ClientWithResponses, error) {
	client, err := NewClient(server, opts...)
	if err != nil {
		return nil, err
	}
	return &ClientWithResponses{client}, nil
}

// WithBaseURL overrides the baseURL.
func WithBaseURL(baseURL string) ClientOption {
	return func(c *Client) error {
		newBaseURL, err := url.Parse(baseURL)
		if err != nil {
			return err
		}
		c.Server = newBaseURL.String()
		return nil
	}
}

// ClientWithResponsesInterface is the interface specification for the client with responses above.
type ClientWithResponsesInterface interface {
	// GetArliDocumentsWithResponse request
	GetArliDocumentsWithResponse(ctx context.Context, params *GetArliDocumentsParams, reqEditors ...RequestEditorFn) (*GetArliDocumentsResponse, error)

	// PostArliDocumentsWithBodyWithResponse request with any body
	PostArliDocumentsWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostArliDocumentsResponse, error)

	PostArliDocumentsWithResponse(ctx context.Context, body PostArliDocumentsJSONRequestBody, reqEditors ...RequestEditorFn) (*PostArliDocumentsResponse, error)

	// PostArliDocumentsVectorizeWithBodyWithResponse request with any body
	PostArliDocumentsVectorizeWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostArliDocumentsVectorizeResponse, error)

	PostArliDocumentsVectorizeWithResponse(ctx context.Context, body PostArliDocumentsVectorizeJSONRequestBody, reqEditors ...RequestEditorFn) (*PostArliDocumentsVectorizeResponse, error)

	// DeleteArliDocumentsIdWithResponse request
	DeleteArliDocumentsIdWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteArliDocumentsIdResponse, error)

	// GetArliDocumentsIdWithResponse request
	GetArliDocumentsIdWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*GetArliDocumentsIdResponse, error)

	// PutArliDocumentsIdWithBodyWithResponse request with any body
	PutArliDocumentsIdWithBodyWithResponse(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutArliDocumentsIdResponse, error)

	PutArliDocumentsIdWithResponse(ctx context.Context, id openapi_types.UUID, body PutArliDocumentsIdJSONRequestBody, reqEditors ...RequestEditorFn) (*PutArliDocumentsIdResponse, error)

	// GetArliFilterTagsWithResponse request
	GetArliFilterTagsWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetArliFilterTagsResponse, error)

	// PostArliFilterTagsWithBodyWithResponse request with any body
	PostArliFilterTagsWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostArliFilterTagsResponse, error)

	PostArliFilterTagsWithResponse(ctx context.Context, body PostArliFilterTagsJSONRequestBody, reqEditors ...RequestEditorFn) (*PostArliFilterTagsResponse, error)

	// GetAuthCallbackProviderWithResponse request
	GetAuthCallbackProviderWithResponse(ctx context.Context, provider GetAuthCallbackProviderParamsProvider, params *GetAuthCallbackProviderParams, reqEditors ...RequestEditorFn) (*GetAuthCallbackProviderResponse, error)

	// GetAuthSessionWithResponse request
	GetAuthSessionWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetAuthSessionResponse, error)

	// PostAuthSigninWithBodyWithResponse request with any body
	PostAuthSigninWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostAuthSigninResponse, error)

	PostAuthSigninWithResponse(ctx context.Context, body PostAuthSigninJSONRequestBody, reqEditors ...RequestEditorFn) (*PostAuthSigninResponse, error)

	// PostAuthSigninOauthWithBodyWithResponse request with any body
	PostAuthSigninOauthWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostAuthSigninOauthResponse, error)

	PostAuthSigninOauthWithResponse(ctx context.Context, body PostAuthSigninOauthJSONRequestBody, reqEditors ...RequestEditorFn) (*PostAuthSigninOauthResponse, error)

	// PostAuthSignoutWithResponse request
	PostAuthSignoutWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*PostAuthSignoutResponse, error)

	// GetAuthUserWithResponse request
	GetAuthUserWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetAuthUserResponse, error)

	// GetCompaniesWithResponse request
	GetCompaniesWithResponse(ctx context.Context, params *GetCompaniesParams, reqEditors ...RequestEditorFn) (*GetCompaniesResponse, error)

	// PostCompaniesWithBodyWithResponse request with any body
	PostCompaniesWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostCompaniesResponse, error)

	PostCompaniesWithResponse(ctx context.Context, body PostCompaniesJSONRequestBody, reqEditors ...RequestEditorFn) (*PostCompaniesResponse, error)

	// DeleteCompaniesIdWithResponse request
	DeleteCompaniesIdWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteCompaniesIdResponse, error)

	// GetCompaniesIdWithResponse request
	GetCompaniesIdWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*GetCompaniesIdResponse, error)

	// PutCompaniesIdWithBodyWithResponse request with any body
	PutCompaniesIdWithBodyWithResponse(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutCompaniesIdResponse, error)

	PutCompaniesIdWithResponse(ctx context.Context, id openapi_types.UUID, body PutCompaniesIdJSONRequestBody, reqEditors ...RequestEditorFn) (*PutCompaniesIdResponse, error)

	// PutCompaniesIdStatusWithBodyWithResponse request with any body
	PutCompaniesIdStatusWithBodyWithResponse(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutCompaniesIdStatusResponse, error)

	PutCompaniesIdStatusWithResponse(ctx context.Context, id openapi_types.UUID, body PutCompaniesIdStatusJSONRequestBody, reqEditors ...RequestEditorFn) (*PutCompaniesIdStatusResponse, error)

	// GetCompanyStatusesWithResponse request
	GetCompanyStatusesWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetCompanyStatusesResponse, error)

	// GetContactsWithResponse request
	GetContactsWithResponse(ctx context.Context, params *GetContactsParams, reqEditors ...RequestEditorFn) (*GetContactsResponse, error)

	// PostContactsWithBodyWithResponse request with any body
	PostContactsWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostContactsResponse, error)

	PostContactsWithResponse(ctx context.Context, body PostContactsJSONRequestBody, reqEditors ...RequestEditorFn) (*PostContactsResponse, error)

	// DeleteContactsIdWithResponse request
	DeleteContactsIdWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteContactsIdResponse, error)

	// GetContactsIdWithResponse request
	GetContactsIdWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*GetContactsIdResponse, error)

	// PutContactsIdWithBodyWithResponse request with any body
	PutContactsIdWithBodyWithResponse(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutContactsIdResponse, error)

	PutContactsIdWithResponse(ctx context.Context, id openapi_types.UUID, body PutContactsIdJSONRequestBody, reqEditors ...RequestEditorFn) (*PutContactsIdResponse, error)

	// DeleteContactsIdCompanyWithResponse request
	DeleteContactsIdCompanyWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteContactsIdCompanyResponse, error)

	// PutContactsIdCompanyWithBodyWithResponse request with any body
	PutContactsIdCompanyWithBodyWithResponse(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutContactsIdCompanyResponse, error)

	PutContactsIdCompanyWithResponse(ctx context.Context, id openapi_types.UUID, body PutContactsIdCompanyJSONRequestBody, reqEditors ...RequestEditorFn) (*PutContactsIdCompanyResponse, error)

	// GetDealStagesWithResponse request
	GetDealStagesWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetDealStagesResponse, error)

	// GetDealsWithResponse request
	GetDealsWithResponse(ctx context.Context, params *GetDealsParams, reqEditors ...RequestEditorFn) (*GetDealsResponse, error)

	// PostDealsWithBodyWithResponse request with any body
	PostDealsWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostDealsResponse, error)

	PostDealsWithResponse(ctx context.Context, body PostDealsJSONRequestBody, reqEditors ...RequestEditorFn) (*PostDealsResponse, error)

	// DeleteDealsIdWithResponse request
	DeleteDealsIdWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteDealsIdResponse, error)

	// GetDealsIdWithResponse request
	GetDealsIdWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*GetDealsIdResponse, error)

	// PutDealsIdWithBodyWithResponse request with any body
	PutDealsIdWithBodyWithResponse(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutDealsIdResponse, error)

	PutDealsIdWithResponse(ctx context.Context, id openapi_types.UUID, body PutDealsIdJSONRequestBody, reqEditors ...RequestEditorFn) (*PutDealsIdResponse, error)

	// GetInteractionsWithResponse request
	GetInteractionsWithResponse(ctx context.Context, params *GetInteractionsParams, reqEditors ...RequestEditorFn) (*GetInteractionsResponse, error)

	// PostInteractionsWithBodyWithResponse request with any body
	PostInteractionsWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostInteractionsResponse, error)

	PostInteractionsWithResponse(ctx context.Context, body PostInteractionsJSONRequestBody, reqEditors ...RequestEditorFn) (*PostInteractionsResponse, error)

	// DeleteInteractionsIdWithResponse request
	DeleteInteractionsIdWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteInteractionsIdResponse, error)

	// GetInteractionsIdWithResponse request
	GetInteractionsIdWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*GetInteractionsIdResponse, error)

	// PutInteractionsIdWithBodyWithResponse request with any body
	PutInteractionsIdWithBodyWithResponse(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutInteractionsIdResponse, error)

	PutInteractionsIdWithResponse(ctx context.Context, id openapi_types.UUID, body PutInteractionsIdJSONRequestBody, reqEditors ...RequestEditorFn) (*PutInteractionsIdResponse, error)

	// GetUsersProfileWithResponse request
	GetUsersProfileWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetUsersProfileResponse, error)

	// PutUsersProfileWithBodyWithResponse request with any body
	PutUsersProfileWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutUsersProfileResponse, error)

	PutUsersProfileWithResponse(ctx context.Context, body PutUsersProfileJSONRequestBody, reqEditors ...RequestEditorFn) (*PutUsersProfileResponse, error)
}

type GetArliDocumentsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *struct {
		Documents  *[]Document `json:"documents,omitempty"`
		HasMore    *bool       `json:"has_more,omitempty"`
		TotalCount *int        `json:"total_count,omitempty"`
	}
	JSON400 *SchemasValidationError
}

// Status returns HTTPResponse.Status
func (r GetArliDocumentsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetArliDocumentsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostArliDocumentsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON201      *Document
	JSON400      *SchemasValidationError
}

// Status returns HTTPResponse.Status
func (r PostArliDocumentsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostArliDocumentsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostArliDocumentsVectorizeResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *struct {
		Message  *string `json:"message,omitempty"`
		Success  *bool   `json:"success,omitempty"`
		VectorId *string `json:"vector_id,omitempty"`
	}
	JSON400 *SchemasValidationError
	JSON500 *ErrorResponse
}

// Status returns HTTPResponse.Status
func (r PostArliDocumentsVectorizeResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostArliDocumentsVectorizeResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type DeleteArliDocumentsIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON404      *ErrorResponse
}

// Status returns HTTPResponse.Status
func (r DeleteArliDocumentsIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeleteArliDocumentsIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetArliDocumentsIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DocumentDetails
	JSON404      *ErrorResponse
}

// Status returns HTTPResponse.Status
func (r GetArliDocumentsIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetArliDocumentsIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PutArliDocumentsIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *Document
	JSON400      *SchemasValidationError
	JSON404      *ErrorResponse
}

// Status returns HTTPResponse.Status
func (r PutArliDocumentsIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PutArliDocumentsIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetArliFilterTagsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *[]FilterTag
}

// Status returns HTTPResponse.Status
func (r GetArliFilterTagsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetArliFilterTagsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostArliFilterTagsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON201      *FilterTag
	JSON400      *SchemasValidationError
}

// Status returns HTTPResponse.Status
func (r PostArliFilterTagsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostArliFilterTagsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetAuthCallbackProviderResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *AuthResponse
	JSON400      *Error
	JSON401      *Error
}

// Status returns HTTPResponse.Status
func (r GetAuthCallbackProviderResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetAuthCallbackProviderResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetAuthSessionResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *Session
}

// Status returns HTTPResponse.Status
func (r GetAuthSessionResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetAuthSessionResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostAuthSigninResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *AuthResponse
}

// Status returns HTTPResponse.Status
func (r PostAuthSigninResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostAuthSigninResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostAuthSigninOauthResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *struct {
		Url *string `json:"url,omitempty"`
	}
}

// Status returns HTTPResponse.Status
func (r PostAuthSigninOauthResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostAuthSigninOauthResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostAuthSignoutResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r PostAuthSignoutResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostAuthSignoutResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetAuthUserResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *User
}

// Status returns HTTPResponse.Status
func (r GetAuthUserResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetAuthUserResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetCompaniesResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *[]Company
}

// Status returns HTTPResponse.Status
func (r GetCompaniesResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetCompaniesResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostCompaniesResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON201      *Company
}

// Status returns HTTPResponse.Status
func (r PostCompaniesResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostCompaniesResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type DeleteCompaniesIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r DeleteCompaniesIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeleteCompaniesIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetCompaniesIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *CompanyDetails
}

// Status returns HTTPResponse.Status
func (r GetCompaniesIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetCompaniesIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PutCompaniesIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *Company
}

// Status returns HTTPResponse.Status
func (r PutCompaniesIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PutCompaniesIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PutCompaniesIdStatusResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r PutCompaniesIdStatusResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PutCompaniesIdStatusResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetCompanyStatusesResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *[]CompanyStatus
}

// Status returns HTTPResponse.Status
func (r GetCompanyStatusesResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetCompanyStatusesResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetContactsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *[]ContactWithCompany
	JSON400      *SchemasValidationError
}

// Status returns HTTPResponse.Status
func (r GetContactsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetContactsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostContactsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON201      *Contact
	JSON400      *SchemasValidationError
}

// Status returns HTTPResponse.Status
func (r PostContactsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostContactsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type DeleteContactsIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON404      *ErrorResponse
}

// Status returns HTTPResponse.Status
func (r DeleteContactsIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeleteContactsIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetContactsIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *ContactDetails
	JSON404      *ErrorResponse
}

// Status returns HTTPResponse.Status
func (r GetContactsIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetContactsIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PutContactsIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *Contact
	JSON400      *SchemasValidationError
	JSON404      *ErrorResponse
}

// Status returns HTTPResponse.Status
func (r PutContactsIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PutContactsIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type DeleteContactsIdCompanyResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON404      *ErrorResponse
}

// Status returns HTTPResponse.Status
func (r DeleteContactsIdCompanyResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeleteContactsIdCompanyResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PutContactsIdCompanyResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON400      *SchemasValidationError
	JSON404      *ErrorResponse
}

// Status returns HTTPResponse.Status
func (r PutContactsIdCompanyResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PutContactsIdCompanyResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetDealStagesResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *[]DealStage
}

// Status returns HTTPResponse.Status
func (r GetDealStagesResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetDealStagesResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetDealsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *[]DealWithDetails
	JSON400      *SchemasValidationError
}

// Status returns HTTPResponse.Status
func (r GetDealsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetDealsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostDealsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON201      *Deal
	JSON400      *SchemasValidationError
}

// Status returns HTTPResponse.Status
func (r PostDealsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostDealsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type DeleteDealsIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON404      *ErrorResponse
}

// Status returns HTTPResponse.Status
func (r DeleteDealsIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeleteDealsIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetDealsIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DealDetails
	JSON404      *ErrorResponse
}

// Status returns HTTPResponse.Status
func (r GetDealsIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetDealsIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PutDealsIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *Deal
	JSON400      *SchemasValidationError
	JSON404      *ErrorResponse
}

// Status returns HTTPResponse.Status
func (r PutDealsIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PutDealsIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetInteractionsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *struct {
		HasMore      *bool                     `json:"has_more,omitempty"`
		Interactions *[]InteractionWithDetails `json:"interactions,omitempty"`
		TotalCount   *int                      `json:"total_count,omitempty"`
	}
	JSON400 *SchemasValidationError
}

// Status returns HTTPResponse.Status
func (r GetInteractionsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetInteractionsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostInteractionsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON201      *Interaction
	JSON400      *SchemasValidationError
}

// Status returns HTTPResponse.Status
func (r PostInteractionsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostInteractionsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type DeleteInteractionsIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON404      *ErrorResponse
}

// Status returns HTTPResponse.Status
func (r DeleteInteractionsIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeleteInteractionsIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetInteractionsIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *InteractionDetails
	JSON404      *ErrorResponse
}

// Status returns HTTPResponse.Status
func (r GetInteractionsIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetInteractionsIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PutInteractionsIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *Interaction
	JSON400      *SchemasValidationError
	JSON404      *ErrorResponse
}

// Status returns HTTPResponse.Status
func (r PutInteractionsIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PutInteractionsIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetUsersProfileResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *UserProfile
}

// Status returns HTTPResponse.Status
func (r GetUsersProfileResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetUsersProfileResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PutUsersProfileResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *UserProfile
}

// Status returns HTTPResponse.Status
func (r PutUsersProfileResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PutUsersProfileResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

// GetArliDocumentsWithResponse request returning *GetArliDocumentsResponse
func (c *ClientWithResponses) GetArliDocumentsWithResponse(ctx context.Context, params *GetArliDocumentsParams, reqEditors ...RequestEditorFn) (*GetArliDocumentsResponse, error) {
	rsp, err := c.GetArliDocuments(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetArliDocumentsResponse(rsp)
}

// PostArliDocumentsWithBodyWithResponse request with arbitrary body returning *PostArliDocumentsResponse
func (c *ClientWithResponses) PostArliDocumentsWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostArliDocumentsResponse, error) {
	rsp, err := c.PostArliDocumentsWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostArliDocumentsResponse(rsp)
}

func (c *ClientWithResponses) PostArliDocumentsWithResponse(ctx context.Context, body PostArliDocumentsJSONRequestBody, reqEditors ...RequestEditorFn) (*PostArliDocumentsResponse, error) {
	rsp, err := c.PostArliDocuments(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostArliDocumentsResponse(rsp)
}

// PostArliDocumentsVectorizeWithBodyWithResponse request with arbitrary body returning *PostArliDocumentsVectorizeResponse
func (c *ClientWithResponses) PostArliDocumentsVectorizeWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostArliDocumentsVectorizeResponse, error) {
	rsp, err := c.PostArliDocumentsVectorizeWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostArliDocumentsVectorizeResponse(rsp)
}

func (c *ClientWithResponses) PostArliDocumentsVectorizeWithResponse(ctx context.Context, body PostArliDocumentsVectorizeJSONRequestBody, reqEditors ...RequestEditorFn) (*PostArliDocumentsVectorizeResponse, error) {
	rsp, err := c.PostArliDocumentsVectorize(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostArliDocumentsVectorizeResponse(rsp)
}

// DeleteArliDocumentsIdWithResponse request returning *DeleteArliDocumentsIdResponse
func (c *ClientWithResponses) DeleteArliDocumentsIdWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteArliDocumentsIdResponse, error) {
	rsp, err := c.DeleteArliDocumentsId(ctx, id, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteArliDocumentsIdResponse(rsp)
}

// GetArliDocumentsIdWithResponse request returning *GetArliDocumentsIdResponse
func (c *ClientWithResponses) GetArliDocumentsIdWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*GetArliDocumentsIdResponse, error) {
	rsp, err := c.GetArliDocumentsId(ctx, id, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetArliDocumentsIdResponse(rsp)
}

// PutArliDocumentsIdWithBodyWithResponse request with arbitrary body returning *PutArliDocumentsIdResponse
func (c *ClientWithResponses) PutArliDocumentsIdWithBodyWithResponse(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutArliDocumentsIdResponse, error) {
	rsp, err := c.PutArliDocumentsIdWithBody(ctx, id, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutArliDocumentsIdResponse(rsp)
}

func (c *ClientWithResponses) PutArliDocumentsIdWithResponse(ctx context.Context, id openapi_types.UUID, body PutArliDocumentsIdJSONRequestBody, reqEditors ...RequestEditorFn) (*PutArliDocumentsIdResponse, error) {
	rsp, err := c.PutArliDocumentsId(ctx, id, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutArliDocumentsIdResponse(rsp)
}

// GetArliFilterTagsWithResponse request returning *GetArliFilterTagsResponse
func (c *ClientWithResponses) GetArliFilterTagsWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetArliFilterTagsResponse, error) {
	rsp, err := c.GetArliFilterTags(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetArliFilterTagsResponse(rsp)
}

// PostArliFilterTagsWithBodyWithResponse request with arbitrary body returning *PostArliFilterTagsResponse
func (c *ClientWithResponses) PostArliFilterTagsWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostArliFilterTagsResponse, error) {
	rsp, err := c.PostArliFilterTagsWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostArliFilterTagsResponse(rsp)
}

func (c *ClientWithResponses) PostArliFilterTagsWithResponse(ctx context.Context, body PostArliFilterTagsJSONRequestBody, reqEditors ...RequestEditorFn) (*PostArliFilterTagsResponse, error) {
	rsp, err := c.PostArliFilterTags(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostArliFilterTagsResponse(rsp)
}

// GetAuthCallbackProviderWithResponse request returning *GetAuthCallbackProviderResponse
func (c *ClientWithResponses) GetAuthCallbackProviderWithResponse(ctx context.Context, provider GetAuthCallbackProviderParamsProvider, params *GetAuthCallbackProviderParams, reqEditors ...RequestEditorFn) (*GetAuthCallbackProviderResponse, error) {
	rsp, err := c.GetAuthCallbackProvider(ctx, provider, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetAuthCallbackProviderResponse(rsp)
}

// GetAuthSessionWithResponse request returning *GetAuthSessionResponse
func (c *ClientWithResponses) GetAuthSessionWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetAuthSessionResponse, error) {
	rsp, err := c.GetAuthSession(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetAuthSessionResponse(rsp)
}

// PostAuthSigninWithBodyWithResponse request with arbitrary body returning *PostAuthSigninResponse
func (c *ClientWithResponses) PostAuthSigninWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostAuthSigninResponse, error) {
	rsp, err := c.PostAuthSigninWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostAuthSigninResponse(rsp)
}

func (c *ClientWithResponses) PostAuthSigninWithResponse(ctx context.Context, body PostAuthSigninJSONRequestBody, reqEditors ...RequestEditorFn) (*PostAuthSigninResponse, error) {
	rsp, err := c.PostAuthSignin(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostAuthSigninResponse(rsp)
}

// PostAuthSigninOauthWithBodyWithResponse request with arbitrary body returning *PostAuthSigninOauthResponse
func (c *ClientWithResponses) PostAuthSigninOauthWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostAuthSigninOauthResponse, error) {
	rsp, err := c.PostAuthSigninOauthWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostAuthSigninOauthResponse(rsp)
}

func (c *ClientWithResponses) PostAuthSigninOauthWithResponse(ctx context.Context, body PostAuthSigninOauthJSONRequestBody, reqEditors ...RequestEditorFn) (*PostAuthSigninOauthResponse, error) {
	rsp, err := c.PostAuthSigninOauth(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostAuthSigninOauthResponse(rsp)
}

// PostAuthSignoutWithResponse request returning *PostAuthSignoutResponse
func (c *ClientWithResponses) PostAuthSignoutWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*PostAuthSignoutResponse, error) {
	rsp, err := c.PostAuthSignout(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostAuthSignoutResponse(rsp)
}

// GetAuthUserWithResponse request returning *GetAuthUserResponse
func (c *ClientWithResponses) GetAuthUserWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetAuthUserResponse, error) {
	rsp, err := c.GetAuthUser(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetAuthUserResponse(rsp)
}

// GetCompaniesWithResponse request returning *GetCompaniesResponse
func (c *ClientWithResponses) GetCompaniesWithResponse(ctx context.Context, params *GetCompaniesParams, reqEditors ...RequestEditorFn) (*GetCompaniesResponse, error) {
	rsp, err := c.GetCompanies(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetCompaniesResponse(rsp)
}

// PostCompaniesWithBodyWithResponse request with arbitrary body returning *PostCompaniesResponse
func (c *ClientWithResponses) PostCompaniesWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostCompaniesResponse, error) {
	rsp, err := c.PostCompaniesWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostCompaniesResponse(rsp)
}

func (c *ClientWithResponses) PostCompaniesWithResponse(ctx context.Context, body PostCompaniesJSONRequestBody, reqEditors ...RequestEditorFn) (*PostCompaniesResponse, error) {
	rsp, err := c.PostCompanies(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostCompaniesResponse(rsp)
}

// DeleteCompaniesIdWithResponse request returning *DeleteCompaniesIdResponse
func (c *ClientWithResponses) DeleteCompaniesIdWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteCompaniesIdResponse, error) {
	rsp, err := c.DeleteCompaniesId(ctx, id, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteCompaniesIdResponse(rsp)
}

// GetCompaniesIdWithResponse request returning *GetCompaniesIdResponse
func (c *ClientWithResponses) GetCompaniesIdWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*GetCompaniesIdResponse, error) {
	rsp, err := c.GetCompaniesId(ctx, id, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetCompaniesIdResponse(rsp)
}

// PutCompaniesIdWithBodyWithResponse request with arbitrary body returning *PutCompaniesIdResponse
func (c *ClientWithResponses) PutCompaniesIdWithBodyWithResponse(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutCompaniesIdResponse, error) {
	rsp, err := c.PutCompaniesIdWithBody(ctx, id, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutCompaniesIdResponse(rsp)
}

func (c *ClientWithResponses) PutCompaniesIdWithResponse(ctx context.Context, id openapi_types.UUID, body PutCompaniesIdJSONRequestBody, reqEditors ...RequestEditorFn) (*PutCompaniesIdResponse, error) {
	rsp, err := c.PutCompaniesId(ctx, id, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutCompaniesIdResponse(rsp)
}

// PutCompaniesIdStatusWithBodyWithResponse request with arbitrary body returning *PutCompaniesIdStatusResponse
func (c *ClientWithResponses) PutCompaniesIdStatusWithBodyWithResponse(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutCompaniesIdStatusResponse, error) {
	rsp, err := c.PutCompaniesIdStatusWithBody(ctx, id, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutCompaniesIdStatusResponse(rsp)
}

func (c *ClientWithResponses) PutCompaniesIdStatusWithResponse(ctx context.Context, id openapi_types.UUID, body PutCompaniesIdStatusJSONRequestBody, reqEditors ...RequestEditorFn) (*PutCompaniesIdStatusResponse, error) {
	rsp, err := c.PutCompaniesIdStatus(ctx, id, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutCompaniesIdStatusResponse(rsp)
}

// GetCompanyStatusesWithResponse request returning *GetCompanyStatusesResponse
func (c *ClientWithResponses) GetCompanyStatusesWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetCompanyStatusesResponse, error) {
	rsp, err := c.GetCompanyStatuses(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetCompanyStatusesResponse(rsp)
}

// GetContactsWithResponse request returning *GetContactsResponse
func (c *ClientWithResponses) GetContactsWithResponse(ctx context.Context, params *GetContactsParams, reqEditors ...RequestEditorFn) (*GetContactsResponse, error) {
	rsp, err := c.GetContacts(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetContactsResponse(rsp)
}

// PostContactsWithBodyWithResponse request with arbitrary body returning *PostContactsResponse
func (c *ClientWithResponses) PostContactsWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostContactsResponse, error) {
	rsp, err := c.PostContactsWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostContactsResponse(rsp)
}

func (c *ClientWithResponses) PostContactsWithResponse(ctx context.Context, body PostContactsJSONRequestBody, reqEditors ...RequestEditorFn) (*PostContactsResponse, error) {
	rsp, err := c.PostContacts(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostContactsResponse(rsp)
}

// DeleteContactsIdWithResponse request returning *DeleteContactsIdResponse
func (c *ClientWithResponses) DeleteContactsIdWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteContactsIdResponse, error) {
	rsp, err := c.DeleteContactsId(ctx, id, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteContactsIdResponse(rsp)
}

// GetContactsIdWithResponse request returning *GetContactsIdResponse
func (c *ClientWithResponses) GetContactsIdWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*GetContactsIdResponse, error) {
	rsp, err := c.GetContactsId(ctx, id, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetContactsIdResponse(rsp)
}

// PutContactsIdWithBodyWithResponse request with arbitrary body returning *PutContactsIdResponse
func (c *ClientWithResponses) PutContactsIdWithBodyWithResponse(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutContactsIdResponse, error) {
	rsp, err := c.PutContactsIdWithBody(ctx, id, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutContactsIdResponse(rsp)
}

func (c *ClientWithResponses) PutContactsIdWithResponse(ctx context.Context, id openapi_types.UUID, body PutContactsIdJSONRequestBody, reqEditors ...RequestEditorFn) (*PutContactsIdResponse, error) {
	rsp, err := c.PutContactsId(ctx, id, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutContactsIdResponse(rsp)
}

// DeleteContactsIdCompanyWithResponse request returning *DeleteContactsIdCompanyResponse
func (c *ClientWithResponses) DeleteContactsIdCompanyWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteContactsIdCompanyResponse, error) {
	rsp, err := c.DeleteContactsIdCompany(ctx, id, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteContactsIdCompanyResponse(rsp)
}

// PutContactsIdCompanyWithBodyWithResponse request with arbitrary body returning *PutContactsIdCompanyResponse
func (c *ClientWithResponses) PutContactsIdCompanyWithBodyWithResponse(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutContactsIdCompanyResponse, error) {
	rsp, err := c.PutContactsIdCompanyWithBody(ctx, id, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutContactsIdCompanyResponse(rsp)
}

func (c *ClientWithResponses) PutContactsIdCompanyWithResponse(ctx context.Context, id openapi_types.UUID, body PutContactsIdCompanyJSONRequestBody, reqEditors ...RequestEditorFn) (*PutContactsIdCompanyResponse, error) {
	rsp, err := c.PutContactsIdCompany(ctx, id, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutContactsIdCompanyResponse(rsp)
}

// GetDealStagesWithResponse request returning *GetDealStagesResponse
func (c *ClientWithResponses) GetDealStagesWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetDealStagesResponse, error) {
	rsp, err := c.GetDealStages(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetDealStagesResponse(rsp)
}

// GetDealsWithResponse request returning *GetDealsResponse
func (c *ClientWithResponses) GetDealsWithResponse(ctx context.Context, params *GetDealsParams, reqEditors ...RequestEditorFn) (*GetDealsResponse, error) {
	rsp, err := c.GetDeals(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetDealsResponse(rsp)
}

// PostDealsWithBodyWithResponse request with arbitrary body returning *PostDealsResponse
func (c *ClientWithResponses) PostDealsWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostDealsResponse, error) {
	rsp, err := c.PostDealsWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostDealsResponse(rsp)
}

func (c *ClientWithResponses) PostDealsWithResponse(ctx context.Context, body PostDealsJSONRequestBody, reqEditors ...RequestEditorFn) (*PostDealsResponse, error) {
	rsp, err := c.PostDeals(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostDealsResponse(rsp)
}

// DeleteDealsIdWithResponse request returning *DeleteDealsIdResponse
func (c *ClientWithResponses) DeleteDealsIdWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteDealsIdResponse, error) {
	rsp, err := c.DeleteDealsId(ctx, id, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteDealsIdResponse(rsp)
}

// GetDealsIdWithResponse request returning *GetDealsIdResponse
func (c *ClientWithResponses) GetDealsIdWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*GetDealsIdResponse, error) {
	rsp, err := c.GetDealsId(ctx, id, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetDealsIdResponse(rsp)
}

// PutDealsIdWithBodyWithResponse request with arbitrary body returning *PutDealsIdResponse
func (c *ClientWithResponses) PutDealsIdWithBodyWithResponse(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutDealsIdResponse, error) {
	rsp, err := c.PutDealsIdWithBody(ctx, id, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutDealsIdResponse(rsp)
}

func (c *ClientWithResponses) PutDealsIdWithResponse(ctx context.Context, id openapi_types.UUID, body PutDealsIdJSONRequestBody, reqEditors ...RequestEditorFn) (*PutDealsIdResponse, error) {
	rsp, err := c.PutDealsId(ctx, id, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutDealsIdResponse(rsp)
}

// GetInteractionsWithResponse request returning *GetInteractionsResponse
func (c *ClientWithResponses) GetInteractionsWithResponse(ctx context.Context, params *GetInteractionsParams, reqEditors ...RequestEditorFn) (*GetInteractionsResponse, error) {
	rsp, err := c.GetInteractions(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetInteractionsResponse(rsp)
}

// PostInteractionsWithBodyWithResponse request with arbitrary body returning *PostInteractionsResponse
func (c *ClientWithResponses) PostInteractionsWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostInteractionsResponse, error) {
	rsp, err := c.PostInteractionsWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostInteractionsResponse(rsp)
}

func (c *ClientWithResponses) PostInteractionsWithResponse(ctx context.Context, body PostInteractionsJSONRequestBody, reqEditors ...RequestEditorFn) (*PostInteractionsResponse, error) {
	rsp, err := c.PostInteractions(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostInteractionsResponse(rsp)
}

// DeleteInteractionsIdWithResponse request returning *DeleteInteractionsIdResponse
func (c *ClientWithResponses) DeleteInteractionsIdWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteInteractionsIdResponse, error) {
	rsp, err := c.DeleteInteractionsId(ctx, id, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteInteractionsIdResponse(rsp)
}

// GetInteractionsIdWithResponse request returning *GetInteractionsIdResponse
func (c *ClientWithResponses) GetInteractionsIdWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*GetInteractionsIdResponse, error) {
	rsp, err := c.GetInteractionsId(ctx, id, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetInteractionsIdResponse(rsp)
}

// PutInteractionsIdWithBodyWithResponse request with arbitrary body returning *PutInteractionsIdResponse
func (c *ClientWithResponses) PutInteractionsIdWithBodyWithResponse(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutInteractionsIdResponse, error) {
	rsp, err := c.PutInteractionsIdWithBody(ctx, id, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutInteractionsIdResponse(rsp)
}

func (c *ClientWithResponses) PutInteractionsIdWithResponse(ctx context.Context, id openapi_types.UUID, body PutInteractionsIdJSONRequestBody, reqEditors ...RequestEditorFn) (*PutInteractionsIdResponse, error) {
	rsp, err := c.PutInteractionsId(ctx, id, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutInteractionsIdResponse(rsp)
}

// GetUsersProfileWithResponse request returning *GetUsersProfileResponse
func (c *ClientWithResponses) GetUsersProfileWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetUsersProfileResponse, error) {
	rsp, err := c.GetUsersProfile(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetUsersProfileResponse(rsp)
}

// PutUsersProfileWithBodyWithResponse request with arbitrary body returning *PutUsersProfileResponse
func (c *ClientWithResponses) PutUsersProfileWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutUsersProfileResponse, error) {
	rsp, err := c.PutUsersProfileWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutUsersProfileResponse(rsp)
}

func (c *ClientWithResponses) PutUsersProfileWithResponse(ctx context.Context, body PutUsersProfileJSONRequestBody, reqEditors ...RequestEditorFn) (*PutUsersProfileResponse, error) {
	rsp, err := c.PutUsersProfile(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutUsersProfileResponse(rsp)
}

// ParseGetArliDocumentsResponse parses an HTTP response from a GetArliDocumentsWithResponse call
func ParseGetArliDocumentsResponse(rsp *http.Response) (*GetArliDocumentsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetArliDocumentsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest struct {
			Documents  *[]Document `json:"documents,omitempty"`
			HasMore    *bool       `json:"has_more,omitempty"`
			TotalCount *int        `json:"total_count,omitempty"`
		}
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest SchemasValidationError
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	}

	return response, nil
}

// ParsePostArliDocumentsResponse parses an HTTP response from a PostArliDocumentsWithResponse call
func ParsePostArliDocumentsResponse(rsp *http.Response) (*PostArliDocumentsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostArliDocumentsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 201:
		var dest Document
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON201 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest SchemasValidationError
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	}

	return response, nil
}

// ParsePostArliDocumentsVectorizeResponse parses an HTTP response from a PostArliDocumentsVectorizeWithResponse call
func ParsePostArliDocumentsVectorizeResponse(rsp *http.Response) (*PostArliDocumentsVectorizeResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostArliDocumentsVectorizeResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest struct {
			Message  *string `json:"message,omitempty"`
			Success  *bool   `json:"success,omitempty"`
			VectorId *string `json:"vector_id,omitempty"`
		}
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest SchemasValidationError
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest ErrorResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	}

	return response, nil
}

// ParseDeleteArliDocumentsIdResponse parses an HTTP response from a DeleteArliDocumentsIdWithResponse call
func ParseDeleteArliDocumentsIdResponse(rsp *http.Response) (*DeleteArliDocumentsIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeleteArliDocumentsIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest ErrorResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseGetArliDocumentsIdResponse parses an HTTP response from a GetArliDocumentsIdWithResponse call
func ParseGetArliDocumentsIdResponse(rsp *http.Response) (*GetArliDocumentsIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetArliDocumentsIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DocumentDetails
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest ErrorResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePutArliDocumentsIdResponse parses an HTTP response from a PutArliDocumentsIdWithResponse call
func ParsePutArliDocumentsIdResponse(rsp *http.Response) (*PutArliDocumentsIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PutArliDocumentsIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest Document
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest SchemasValidationError
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest ErrorResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseGetArliFilterTagsResponse parses an HTTP response from a GetArliFilterTagsWithResponse call
func ParseGetArliFilterTagsResponse(rsp *http.Response) (*GetArliFilterTagsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetArliFilterTagsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest []FilterTag
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParsePostArliFilterTagsResponse parses an HTTP response from a PostArliFilterTagsWithResponse call
func ParsePostArliFilterTagsResponse(rsp *http.Response) (*PostArliFilterTagsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostArliFilterTagsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 201:
		var dest FilterTag
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON201 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest SchemasValidationError
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	}

	return response, nil
}

// ParseGetAuthCallbackProviderResponse parses an HTTP response from a GetAuthCallbackProviderWithResponse call
func ParseGetAuthCallbackProviderResponse(rsp *http.Response) (*GetAuthCallbackProviderResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetAuthCallbackProviderResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest AuthResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	}

	return response, nil
}

// ParseGetAuthSessionResponse parses an HTTP response from a GetAuthSessionWithResponse call
func ParseGetAuthSessionResponse(rsp *http.Response) (*GetAuthSessionResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetAuthSessionResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest Session
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParsePostAuthSigninResponse parses an HTTP response from a PostAuthSigninWithResponse call
func ParsePostAuthSigninResponse(rsp *http.Response) (*PostAuthSigninResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostAuthSigninResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest AuthResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParsePostAuthSigninOauthResponse parses an HTTP response from a PostAuthSigninOauthWithResponse call
func ParsePostAuthSigninOauthResponse(rsp *http.Response) (*PostAuthSigninOauthResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostAuthSigninOauthResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest struct {
			Url *string `json:"url,omitempty"`
		}
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParsePostAuthSignoutResponse parses an HTTP response from a PostAuthSignoutWithResponse call
func ParsePostAuthSignoutResponse(rsp *http.Response) (*PostAuthSignoutResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostAuthSignoutResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParseGetAuthUserResponse parses an HTTP response from a GetAuthUserWithResponse call
func ParseGetAuthUserResponse(rsp *http.Response) (*GetAuthUserResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetAuthUserResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest User
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseGetCompaniesResponse parses an HTTP response from a GetCompaniesWithResponse call
func ParseGetCompaniesResponse(rsp *http.Response) (*GetCompaniesResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetCompaniesResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest []Company
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParsePostCompaniesResponse parses an HTTP response from a PostCompaniesWithResponse call
func ParsePostCompaniesResponse(rsp *http.Response) (*PostCompaniesResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostCompaniesResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 201:
		var dest Company
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON201 = &dest

	}

	return response, nil
}

// ParseDeleteCompaniesIdResponse parses an HTTP response from a DeleteCompaniesIdWithResponse call
func ParseDeleteCompaniesIdResponse(rsp *http.Response) (*DeleteCompaniesIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeleteCompaniesIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParseGetCompaniesIdResponse parses an HTTP response from a GetCompaniesIdWithResponse call
func ParseGetCompaniesIdResponse(rsp *http.Response) (*GetCompaniesIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetCompaniesIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest CompanyDetails
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParsePutCompaniesIdResponse parses an HTTP response from a PutCompaniesIdWithResponse call
func ParsePutCompaniesIdResponse(rsp *http.Response) (*PutCompaniesIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PutCompaniesIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest Company
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParsePutCompaniesIdStatusResponse parses an HTTP response from a PutCompaniesIdStatusWithResponse call
func ParsePutCompaniesIdStatusResponse(rsp *http.Response) (*PutCompaniesIdStatusResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PutCompaniesIdStatusResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParseGetCompanyStatusesResponse parses an HTTP response from a GetCompanyStatusesWithResponse call
func ParseGetCompanyStatusesResponse(rsp *http.Response) (*GetCompanyStatusesResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetCompanyStatusesResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest []CompanyStatus
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseGetContactsResponse parses an HTTP response from a GetContactsWithResponse call
func ParseGetContactsResponse(rsp *http.Response) (*GetContactsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetContactsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest []ContactWithCompany
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest SchemasValidationError
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	}

	return response, nil
}

// ParsePostContactsResponse parses an HTTP response from a PostContactsWithResponse call
func ParsePostContactsResponse(rsp *http.Response) (*PostContactsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostContactsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 201:
		var dest Contact
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON201 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest SchemasValidationError
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	}

	return response, nil
}

// ParseDeleteContactsIdResponse parses an HTTP response from a DeleteContactsIdWithResponse call
func ParseDeleteContactsIdResponse(rsp *http.Response) (*DeleteContactsIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeleteContactsIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest ErrorResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseGetContactsIdResponse parses an HTTP response from a GetContactsIdWithResponse call
func ParseGetContactsIdResponse(rsp *http.Response) (*GetContactsIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetContactsIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest ContactDetails
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest ErrorResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePutContactsIdResponse parses an HTTP response from a PutContactsIdWithResponse call
func ParsePutContactsIdResponse(rsp *http.Response) (*PutContactsIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PutContactsIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest Contact
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest SchemasValidationError
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest ErrorResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseDeleteContactsIdCompanyResponse parses an HTTP response from a DeleteContactsIdCompanyWithResponse call
func ParseDeleteContactsIdCompanyResponse(rsp *http.Response) (*DeleteContactsIdCompanyResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeleteContactsIdCompanyResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest ErrorResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePutContactsIdCompanyResponse parses an HTTP response from a PutContactsIdCompanyWithResponse call
func ParsePutContactsIdCompanyResponse(rsp *http.Response) (*PutContactsIdCompanyResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PutContactsIdCompanyResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest SchemasValidationError
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest ErrorResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseGetDealStagesResponse parses an HTTP response from a GetDealStagesWithResponse call
func ParseGetDealStagesResponse(rsp *http.Response) (*GetDealStagesResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetDealStagesResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest []DealStage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseGetDealsResponse parses an HTTP response from a GetDealsWithResponse call
func ParseGetDealsResponse(rsp *http.Response) (*GetDealsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetDealsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest []DealWithDetails
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest SchemasValidationError
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	}

	return response, nil
}

// ParsePostDealsResponse parses an HTTP response from a PostDealsWithResponse call
func ParsePostDealsResponse(rsp *http.Response) (*PostDealsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostDealsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 201:
		var dest Deal
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON201 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest SchemasValidationError
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	}

	return response, nil
}

// ParseDeleteDealsIdResponse parses an HTTP response from a DeleteDealsIdWithResponse call
func ParseDeleteDealsIdResponse(rsp *http.Response) (*DeleteDealsIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeleteDealsIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest ErrorResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseGetDealsIdResponse parses an HTTP response from a GetDealsIdWithResponse call
func ParseGetDealsIdResponse(rsp *http.Response) (*GetDealsIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetDealsIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DealDetails
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest ErrorResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePutDealsIdResponse parses an HTTP response from a PutDealsIdWithResponse call
func ParsePutDealsIdResponse(rsp *http.Response) (*PutDealsIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PutDealsIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest Deal
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest SchemasValidationError
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest ErrorResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseGetInteractionsResponse parses an HTTP response from a GetInteractionsWithResponse call
func ParseGetInteractionsResponse(rsp *http.Response) (*GetInteractionsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetInteractionsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest struct {
			HasMore      *bool                     `json:"has_more,omitempty"`
			Interactions *[]InteractionWithDetails `json:"interactions,omitempty"`
			TotalCount   *int                      `json:"total_count,omitempty"`
		}
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest SchemasValidationError
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	}

	return response, nil
}

// ParsePostInteractionsResponse parses an HTTP response from a PostInteractionsWithResponse call
func ParsePostInteractionsResponse(rsp *http.Response) (*PostInteractionsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostInteractionsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 201:
		var dest Interaction
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON201 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest SchemasValidationError
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	}

	return response, nil
}

// ParseDeleteInteractionsIdResponse parses an HTTP response from a DeleteInteractionsIdWithResponse call
func ParseDeleteInteractionsIdResponse(rsp *http.Response) (*DeleteInteractionsIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeleteInteractionsIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest ErrorResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseGetInteractionsIdResponse parses an HTTP response from a GetInteractionsIdWithResponse call
func ParseGetInteractionsIdResponse(rsp *http.Response) (*GetInteractionsIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetInteractionsIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest InteractionDetails
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest ErrorResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePutInteractionsIdResponse parses an HTTP response from a PutInteractionsIdWithResponse call
func ParsePutInteractionsIdResponse(rsp *http.Response) (*PutInteractionsIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PutInteractionsIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest Interaction
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest SchemasValidationError
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest ErrorResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseGetUsersProfileResponse parses an HTTP response from a GetUsersProfileWithResponse call
func ParseGetUsersProfileResponse(rsp *http.Response) (*GetUsersProfileResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetUsersProfileResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest UserProfile
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParsePutUsersProfileResponse parses an HTTP response from a PutUsersProfileWithResponse call
func ParsePutUsersProfileResponse(rsp *http.Response) (*PutUsersProfileResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PutUsersProfileResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest UserProfile
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}
