#!/bin/bash

# Docker-based test runner for database migrations
# This script starts a PostgreSQL container, runs migrations, and executes tests

set -e

# Configuration
DB_CONTAINER_NAME="crm_test_db"
DB_NAME="crm_db_test"
DB_USER="postgres"
DB_PASSWORD="testpassword"
DB_PORT="5433"

echo "🐳 Starting PostgreSQL container..."

# Stop and remove existing container if it exists
docker stop $DB_CONTAINER_NAME 2>/dev/null || true
docker rm $DB_CONTAINER_NAME 2>/dev/null || true

# Start PostgreSQL container
docker run -d \
    --name $DB_CONTAINER_NAME \
    -e POSTGRES_DB=$DB_NAME \
    -e POSTGRES_USER=$DB_USER \
    -e POSTGRES_PASSWORD=$DB_PASSWORD \
    -p $DB_PORT:5432 \
    postgres:17-alpine

echo "⏳ Waiting for PostgreSQL to be ready..."
sleep 10

# Wait for PostgreSQL to be ready
until docker exec $DB_CONTAINER_NAME pg_isready -U $DB_USER -d $DB_NAME; do
    echo "Waiting for PostgreSQL..."
    sleep 2
done

echo "📦 Running migrations..."

# Apply migrations
docker exec -i $DB_CONTAINER_NAME psql -U $DB_USER -d $DB_NAME < ../migrations/V001__initial_schema.sql
docker exec -i $DB_CONTAINER_NAME psql -U $DB_USER -d $DB_NAME < ../migrations/V002__crm_schema.sql

echo "🧪 Running SQL integration tests..."

# Run the SQL integration tests
docker exec -i $DB_CONTAINER_NAME psql -U $DB_USER -d $DB_NAME < integration_test.sql

echo "🧪 Running Go tests..."

# Set environment variables for Go tests
export DATABASE_URL="postgres://$DB_USER:$DB_PASSWORD@localhost:$DB_PORT/$DB_NAME?sslmode=disable"

# Run Go tests if available
if command -v go &> /dev/null; then
    go test -v ./... -tags=integration
else
    echo "⚠️  Go not available, skipping Go tests"
fi

echo "🧹 Cleaning up..."

# Stop and remove container
docker stop $DB_CONTAINER_NAME
docker rm $DB_CONTAINER_NAME

echo "✅ All tests completed successfully!"