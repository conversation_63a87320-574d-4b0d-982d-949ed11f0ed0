/**
 * {{ agent_name | title }} Agent
 * {{ description }}
 *
 * Generated on: {{ "now" | datetime }}
 * Agent Type: {{ agent_type | title }}
 * Capabilities: {{ capabilities | join(", ") }}
 */

import express, { Express, Request, Response } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import { v4 as uuidv4 } from 'uuid';
import axios, { AxiosInstance } from 'axios';
import winston from 'winston';
import EventEmitter from 'events';

// Types and Interfaces
export enum AgentStatus {
    IDLE = 'idle',
    RUNNING = 'running',
    ERROR = 'error',
    COMPLETED = 'completed'
}

export interface TaskRequest {
    taskId: string;
    taskType: string;
    inputData: Record<string, any>;
    priority: 'low' | 'medium' | 'high';
    timeout?: number;
}

export interface TaskResponse {
    taskId: string;
    status: AgentStatus;
    result?: any;
    error?: string;
    executionTime?: number;
    metadata: Record<string, any>;
}

export interface AgentConfig {
    name?: string;
    port?: number;
    corsEnabled?: boolean;
    apiTimeout?: number;
    logLevel?: string;
    {% for key, value in configuration.items() %}
    {{ key }}?: {{ 'number' if value is number else 'string' if value is string else 'boolean' if value is boolean else 'any' }};
    {% endfor %}
    [key: string]: any;
}

export interface AgentStatusInfo {
    agentId: string;
    name: string;
    type: string;
    status: AgentStatus;
    capabilities: string[];
    uptime: string;
    tasksExecuted: number;
    lastTaskTime?: string;
}

/**
 * {{ agent_name | title }} Agent Class
 * 
 * {{ description }}
 * 
 * Capabilities:
 * {% for capability in capabilities %}
 * - {{ capability | title | replace("_", " ") }}
 * {% endfor %}
 */
export class {{ agent_class }}Agent extends EventEmitter {
    private agentId: string;
    private name: string;
    private agentType: string;
    private config: AgentConfig;
    private status: AgentStatus;
    private app: Express;
    private server: any;
    private logger: winston.Logger;
    private capabilities: string[];
    private startTime: Date;
    private tasksExecuted: number = 0;
    private lastTaskTime?: Date;

    {% if 'api_integration' in capabilities %}
    private apiClient: AxiosInstance;
    {% endif %}

    constructor(config: AgentConfig) {
        super();
        this.agentId = uuidv4();
        this.name = config.name || '{{ agent_name }}';
        this.agentType = '{{ agent_type }}';
        this.config = config;
        this.status = AgentStatus.IDLE;
        this.capabilities = {{ capabilities | tojson }};
        this.startTime = new Date();
        
        this.setupLogger();
        this.setupExpress();
        
        {% if 'api_integration' in capabilities %}
        this.setupApiClient();
        {% endif %}
        
        this.logger.info(`Initialized ${this.name} agent with ID: ${this.agentId}`);
    }

    /**
     * Setup Winston logger
     */
    private setupLogger(): void {
        this.logger = winston.createLogger({
            level: this.config.logLevel || 'info',
            format: winston.format.combine(
                winston.format.timestamp(),
                winston.format.errors({ stack: true }),
                winston.format.json()
            ),
            transports: [
                new winston.transports.Console({
                    format: winston.format.combine(
                        winston.format.colorize(),
                        winston.format.simple()
                    )
                }),
                new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
                new winston.transports.File({ filename: 'logs/combined.log' })
            ]
        });
    }

    /**
     * Setup Express application
     */
    private setupExpress(): void {
        this.app = express();
        
        // Security middleware
        this.app.use(helmet());
        
        // CORS
        if (this.config.corsEnabled !== false) {
            this.app.use(cors());
        }
        
        // Rate limiting
        const limiter = rateLimit({
            windowMs: 15 * 60 * 1000, // 15 minutes
            max: 100 // limit each IP to 100 requests per windowMs
        });
        this.app.use(limiter);
        
        // Body parsing
        this.app.use(express.json({ limit: '10mb' }));
        this.app.use(express.urlencoded({ extended: true }));
        
        this.setupRoutes();
    }

    {% if 'api_integration' in capabilities %}
    /**
     * Setup API client for external integrations
     */
    private setupApiClient(): void {
        this.apiClient = axios.create({
            timeout: this.config.apiTimeout || 30000,
            headers: {
                'User-Agent': `${this.name}-agent/${this.agentId}`,
                'Content-Type': 'application/json'
            }
        });
        
        // Request interceptor
        this.apiClient.interceptors.request.use(
            (config) => {
                this.logger.debug(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
                return config;
            },
            (error) => {
                this.logger.error('API Request Error:', error);
                return Promise.reject(error);
            }
        );
        
        // Response interceptor
        this.apiClient.interceptors.response.use(
            (response) => {
                this.logger.debug(`API Response: ${response.status} ${response.config.url}`);
                return response;
            },
            (error) => {
                this.logger.error('API Response Error:', error);
                return Promise.reject(error);
            }
        );
    }
    {% endif %}

    /**
     * Setup Express routes
     */
    private setupRoutes(): void {
        // Health check
        this.app.get('/health', (req: Request, res: Response) => {
            res.json({ status: 'healthy', timestamp: new Date().toISOString() });
        });
        
        // Agent status
        this.app.get('/status', async (req: Request, res: Response) => {
            try {
                const status = await this.getStatus();
                res.json(status);
            } catch (error) {
                res.status(500).json({ error: 'Failed to get status' });
            }
        });
        
        // Execute task
        this.app.post('/tasks', async (req: Request, res: Response) => {
            try {
                const taskRequest: TaskRequest = {
                    taskId: req.body.taskId || uuidv4(),
                    taskType: req.body.taskType,
                    inputData: req.body.inputData || {},
                    priority: req.body.priority || 'medium',
                    timeout: req.body.timeout
                };
                
                const result = await this.executeTask(taskRequest);
                res.json(result);
            } catch (error) {
                this.logger.error('Task execution error:', error);
                res.status(500).json({ 
                    error: 'Task execution failed',
                    message: error instanceof Error ? error.message : 'Unknown error'
                });
            }
        });
        
        // Get task result (for async operations)
        this.app.get('/tasks/:taskId', (req: Request, res: Response) => {
            // Implementation would depend on task storage mechanism
            res.json({ message: 'Task result retrieval not implemented in this template' });
        });

        {% if 'web_integration' in capabilities %}
        // Web integration specific routes
        this.app.get('/api/capabilities', (req: Request, res: Response) => {
            res.json({
                capabilities: this.capabilities,
                agentType: this.agentType,
                version: '1.0.0'
            });
        });
        {% endif %}
        
        // Catch all
        this.app.use('*', (req: Request, res: Response) => {
            res.status(404).json({ error: 'Endpoint not found' });
        });
        
        // Error handler
        this.app.use((err: any, req: Request, res: Response, next: any) => {
            this.logger.error('Express error:', err);
            res.status(500).json({ error: 'Internal server error' });
        });
    }

    /**
     * Initialize the agent and start the server
     */
    public async initialize(): Promise<boolean> {
        try {
            this.logger.info('Initializing agent resources...');
            
            {% if 'database' in capabilities %}
            // Database initialization would go here
            this.logger.info('Database connection initialized');
            {% endif %}
            
            {% if custom_logic %}
            // Custom initialization logic
            {{ custom_logic | indent(12) }}
            {% endif %}
            
            // Start the server
            const port = this.config.port || 3000;
            this.server = this.app.listen(port, () => {
                this.logger.info(`Agent server started on port ${port}`);
            });
            
            this.logger.info('Agent initialization completed successfully');
            return true;
            
        } catch (error) {
            this.logger.error('Agent initialization failed:', error);
            return false;
        }
    }

    /**
     * Execute a task and return the result
     */
    public async executeTask(task: TaskRequest): Promise<TaskResponse> {
        const startTime = Date.now();
        this.status = AgentStatus.RUNNING;
        this.tasksExecuted++;
        this.lastTaskTime = new Date();
        
        try {
            this.logger.info(`Executing task ${task.taskId} of type ${task.taskType}`);
            this.emit('taskStarted', task);
            
            let result: any = null;
            
            // Route task based on type and capabilities
            {% if 'conversation' in capabilities %}
            if (task.taskType === 'conversation') {
                result = await this.handleConversation(task.inputData);
            } else 
            {% endif %}
            {% if 'web_integration' in capabilities %}
            if (task.taskType === 'web_request') {
                result = await this.handleWebRequest(task.inputData);
            } else 
            {% endif %}
            {% if 'api_integration' in capabilities %}
            if (task.taskType === 'api_call') {
                result = await this.handleApiCall(task.inputData);
            } else 
            {% endif %}
            {% if 'ui_interaction' in capabilities %}
            if (task.taskType === 'ui_interaction') {
                result = await this.handleUiInteraction(task.inputData);
            } else 
            {% endif %}
            {
                // Generic task handling
                result = await this.handleGenericTask(task);
            }
            
            const executionTime = (Date.now() - startTime) / 1000;
            this.status = AgentStatus.COMPLETED;
            
            const response: TaskResponse = {
                taskId: task.taskId,
                status: AgentStatus.COMPLETED,
                result,
                executionTime,
                metadata: {
                    agentId: this.agentId,
                    agentName: this.name,
                    completedAt: new Date().toISOString()
                }
            };
            
            this.emit('taskCompleted', response);
            return response;
            
        } catch (error) {
            const executionTime = (Date.now() - startTime) / 1000;
            this.status = AgentStatus.ERROR;
            this.logger.error(`Task execution failed: ${error}`);
            
            const response: TaskResponse = {
                taskId: task.taskId,
                status: AgentStatus.ERROR,
                error: error instanceof Error ? error.message : 'Unknown error',
                executionTime,
                metadata: {
                    agentId: this.agentId,
                    agentName: this.name,
                    failedAt: new Date().toISOString()
                }
            };
            
            this.emit('taskFailed', response);
            return response;
        } finally {
            // Reset status if not in error state
            if (this.status !== AgentStatus.ERROR) {
                this.status = AgentStatus.IDLE;
            }
        }
    }

    {% if 'conversation' in capabilities %}
    /**
     * Handle conversation tasks
     */
    private async handleConversation(inputData: Record<string, any>): Promise<Record<string, any>> {
        const message = inputData.message || '';
        const context = inputData.context || {};
        
        // Simple response generation (replace with actual AI integration)
        const response = `I understand you said: "${message}". How can I help you further?`;
        
        return {
            response,
            context,
            agentType: this.agentType,
            timestamp: new Date().toISOString()
        };
    }
    {% endif %}

    {% if 'web_integration' in capabilities %}
    /**
     * Handle web integration requests
     */
    private async handleWebRequest(inputData: Record<string, any>): Promise<Record<string, any>> {
        const { url, method = 'GET', headers = {}, data } = inputData;
        
        try {
            const response = await axios({
                url,
                method,
                headers,
                data,
                timeout: this.config.apiTimeout || 30000
            });
            
            return {
                status: response.status,
                statusText: response.statusText,
                headers: response.headers,
                data: response.data
            };
        } catch (error) {
            throw new Error(`Web request failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    {% endif %}

    {% if 'api_integration' in capabilities %}
    /**
     * Handle API integration calls
     */
    private async handleApiCall(inputData: Record<string, any>): Promise<Record<string, any>> {
        const { endpoint, method = 'GET', params = {}, data } = inputData;
        
        try {
            const response = await this.apiClient.request({
                url: endpoint,
                method,
                params,
                data
            });
            
            return {
                status: response.status,
                data: response.data,
                headers: response.headers
            };
        } catch (error) {
            throw new Error(`API call failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    {% endif %}

    {% if 'ui_interaction' in capabilities %}
    /**
     * Handle UI interaction tasks
     */
    private async handleUiInteraction(inputData: Record<string, any>): Promise<Record<string, any>> {
        const { action, element, data } = inputData;
        
        // Mock UI interaction (replace with actual implementation)
        return {
            action,
            element,
            data,
            result: `UI interaction '${action}' completed`,
            timestamp: new Date().toISOString()
        };
    }
    {% endif %}

    /**
     * Handle generic tasks that don't match specific capabilities
     */
    private async handleGenericTask(task: TaskRequest): Promise<Record<string, any>> {
        return {
            message: `Task ${task.taskType} completed by ${this.name}`,
            inputReceived: task.inputData,
            agentCapabilities: this.capabilities,
            taskId: task.taskId,
            timestamp: new Date().toISOString()
        };
    }

    /**
     * Get current agent status
     */
    public async getStatus(): Promise<AgentStatusInfo> {
        const uptime = Date.now() - this.startTime.getTime();
        
        return {
            agentId: this.agentId,
            name: this.name,
            type: this.agentType,
            status: this.status,
            capabilities: this.capabilities,
            uptime: `${Math.floor(uptime / 1000)}s`,
            tasksExecuted: this.tasksExecuted,
            lastTaskTime: this.lastTaskTime?.toISOString()
        };
    }

    /**
     * Shutdown the agent gracefully
     */
    public async shutdown(): Promise<boolean> {
        try {
            this.logger.info('Shutting down agent...');
            
            {% if 'api_integration' in capabilities %}
            // Close API client connections if needed
            // this.apiClient cleanup would go here
            {% endif %}
            
            // Close server
            if (this.server) {
                await new Promise<void>((resolve) => {
                    this.server.close(() => {
                        this.logger.info('Server closed');
                        resolve();
                    });
                });
            }
            
            this.status = AgentStatus.IDLE;
            this.logger.info('Agent shutdown completed');
            return true;
            
        } catch (error) {
            this.logger.error(`Error during shutdown: ${error}`);
            return false;
        }
    }
}

/**
 * Factory function for creating agent instances
 */
export function createAgent(config: AgentConfig): {{ agent_class }}Agent {
    return new {{ agent_class }}Agent(config);
}

/**
 * Main function for running the agent standalone
 */
async function main(): Promise<void> {
    // Default configuration
    const config: AgentConfig = {
        name: '{{ agent_name }}',
        port: {{ configuration.get('port', 3000) }},
        corsEnabled: {{ 'true' if configuration.get('cors_enabled', true) else 'false' }},
        apiTimeout: {{ configuration.get('api_timeout', 30000) }},
        {% for key, value in configuration.items() %}
        {{ key }}: {{ value | tojson }},
        {% endfor %}
    };
    
    // Create and initialize agent
    const agent = createAgent(config);
    
    // Handle graceful shutdown
    const gracefulShutdown = async (signal: string) => {
        console.log(`Received ${signal}. Starting graceful shutdown...`);
        await agent.shutdown();
        process.exit(0);
    };
    
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    
    if (await agent.initialize()) {
        console.log(`Agent ${agent.name || '{{ agent_name }}'} initialized successfully`);
        
        // Example task execution (for testing)
        const testTask: TaskRequest = {
            taskId: uuidv4(),
            taskType: 'test',
            inputData: { message: 'Hello, agent!' },
            priority: 'medium'
        };
        
        const result = await agent.executeTask(testTask);
        console.log('Test task result:', result);
        
    } else {
        console.error('Failed to initialize agent');
        process.exit(1);
    }
}

// Run if this file is executed directly
if (require.main === module) {
    main().catch(console.error);
}