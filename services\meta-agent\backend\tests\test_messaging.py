"""
AI Agent Platform - Message Queue Integration Tests
"""

import pytest
import uuid
import json
from unittest.mock import AsyncMock, patch, MagicMock


class TestMessageQueueAPI:
    """Test message queue API endpoints"""
    
    def test_publish_message(self, client, authenticated_user):
        """Test publishing message to queue"""
        message_data = {
            "topic": "agent_events",
            "message": {
                "agent_id": str(uuid.uuid4()),
                "event_type": "task_completed",
                "payload": {"result": "success"}
            }
        }
        
        response = client.post(
            "/api/v1/messaging/publish",
            json=message_data,
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] == True
        assert "message_id" in data
    
    def test_publish_message_without_auth(self, client):
        """Test publishing message without authentication"""
        message_data = {
            "topic": "agent_events",
            "message": {"test": "message"}
        }
        
        response = client.post("/api/v1/messaging/publish", json=message_data)
        assert response.status_code == 401
    
    def test_publish_invalid_message(self, client, authenticated_user):
        """Test publishing invalid message"""
        invalid_data = {
            "topic": "",  # Empty topic should fail
            "message": {}
        }
        
        response = client.post(
            "/api/v1/messaging/publish",
            json=invalid_data,
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 422  # Validation error
    
    def test_get_topic_stats(self, client, authenticated_user):
        """Test getting topic statistics"""
        response = client.get(
            "/api/v1/messaging/topics/agent_events/stats",
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert "topic" in data
        assert "message_count" in data
        assert "consumer_count" in data
        assert "partition_count" in data
    
    def test_list_topics(self, client, authenticated_user):
        """Test listing available topics"""
        response = client.get(
            "/api/v1/messaging/topics",
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert "topics" in data
        assert isinstance(data["topics"], list)
    
    def test_create_topic(self, client, authenticated_user):
        """Test creating new topic"""
        topic_data = {
            "name": "test_topic",
            "partitions": 3,
            "replication_factor": 1
        }
        
        response = client.post(
            "/api/v1/messaging/topics",
            json=topic_data,
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == "test_topic"
        assert data["partitions"] == 3
    
    def test_delete_topic(self, client, authenticated_user):
        """Test deleting topic"""
        # First create a topic
        topic_data = {"name": "delete_test_topic", "partitions": 1}
        create_response = client.post(
            "/api/v1/messaging/topics",
            json=topic_data,
            headers=authenticated_user["headers"]
        )
        assert create_response.status_code == 201
        
        # Then delete it
        response = client.delete(
            "/api/v1/messaging/topics/delete_test_topic",
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 204
    
    @patch('src.services.messaging.MessageQueueService.get_messages')
    def test_consume_messages(self, mock_get_messages, client, authenticated_user):
        """Test consuming messages from topic"""
        mock_get_messages.return_value = [
            {
                "id": str(uuid.uuid4()),
                "topic": "agent_events",
                "message": {"event": "test"},
                "timestamp": "2025-07-22T10:00:00Z"
            }
        ]
        
        response = client.get(
            "/api/v1/messaging/topics/agent_events/messages?limit=10",
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert "messages" in data
        assert len(data["messages"]) == 1
        assert data["messages"][0]["topic"] == "agent_events"


class TestMessageQueueService:
    """Test message queue service layer"""
    
    @pytest.mark.asyncio
    async def test_publish_message_service(self):
        """Test publishing message via service"""
        from src.services.messaging import MessageQueueService
        
        # Mock Kafka producer
        with patch('aiokafka.AIOKafkaProducer') as mock_producer:
            mock_producer_instance = AsyncMock()
            mock_producer.return_value = mock_producer_instance
            
            service = MessageQueueService()
            await service.connect()
            
            result = await service.publish_message(
                topic="test_topic",
                message={"test": "data"},
                key="test_key"
            )
            
            assert result is not None
            assert "message_id" in result
            mock_producer_instance.send_and_wait.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_subscribe_to_topic_service(self):
        """Test subscribing to topic via service"""
        from src.services.messaging import MessageQueueService
        
        with patch('aiokafka.AIOKafkaConsumer') as mock_consumer:
            mock_consumer_instance = AsyncMock()
            mock_consumer.return_value = mock_consumer_instance
            
            service = MessageQueueService()
            await service.connect()
            
            # Test subscription
            await service.subscribe_to_topic("test_topic", "test_group")
            
            mock_consumer_instance.subscribe.assert_called_once_with(["test_topic"])
    
    @pytest.mark.asyncio
    async def test_consume_messages_service(self):
        """Test consuming messages via service"""
        from src.services.messaging import MessageQueueService
        
        # Mock message
        mock_message = MagicMock()
        mock_message.topic = "test_topic"
        mock_message.key = b"test_key"
        mock_message.value = b'{"test": "data"}'
        mock_message.timestamp = 1642781234567
        mock_message.partition = 0
        mock_message.offset = 123
        
        with patch('aiokafka.AIOKafkaConsumer') as mock_consumer:
            mock_consumer_instance = AsyncMock()
            mock_consumer_instance.__aiter__ = AsyncMock(return_value=iter([mock_message]))
            mock_consumer.return_value = mock_consumer_instance
            
            service = MessageQueueService()
            await service.connect()
            await service.subscribe_to_topic("test_topic", "test_group")
            
            # Consume messages
            messages = []
            async for message in service.consume_messages():
                messages.append(message)
                break  # Just get one message for test
            
            assert len(messages) == 1
            assert messages[0]["topic"] == "test_topic"
            assert messages[0]["message"]["test"] == "data"
    
    @pytest.mark.asyncio
    async def test_create_topic_service(self):
        """Test creating topic via service"""
        from src.services.messaging import MessageQueueService
        
        with patch('kafka.admin.KafkaAdminClient') as mock_admin:
            mock_admin_instance = MagicMock()
            mock_admin.return_value = mock_admin_instance
            
            service = MessageQueueService()
            
            result = await service.create_topic(
                name="new_topic",
                num_partitions=3,
                replication_factor=1
            )
            
            assert result == True
            mock_admin_instance.create_topics.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_list_topics_service(self):
        """Test listing topics via service"""
        from src.services.messaging import MessageQueueService
        
        with patch('kafka.admin.KafkaAdminClient') as mock_admin:
            mock_admin_instance = MagicMock()
            mock_admin_instance.list_topics.return_value = ["topic1", "topic2", "topic3"]
            mock_admin.return_value = mock_admin_instance
            
            service = MessageQueueService()
            
            topics = await service.list_topics()
            
            assert len(topics) == 3
            assert "topic1" in topics
            assert "topic2" in topics
            assert "topic3" in topics
    
    @pytest.mark.asyncio
    async def test_get_topic_metadata_service(self):
        """Test getting topic metadata via service"""
        from src.services.messaging import MessageQueueService
        
        with patch('kafka.admin.KafkaAdminClient') as mock_admin:
            mock_admin_instance = MagicMock()
            mock_metadata = MagicMock()
            mock_metadata.topics = {
                "test_topic": MagicMock(
                    partitions={0: MagicMock(), 1: MagicMock(), 2: MagicMock()}
                )
            }
            mock_admin_instance.describe_topics.return_value = mock_metadata
            mock_admin.return_value = mock_admin_instance
            
            service = MessageQueueService()
            
            metadata = await service.get_topic_metadata("test_topic")
            
            assert metadata is not None
            assert "partition_count" in metadata
            assert metadata["partition_count"] == 3
    
    @pytest.mark.asyncio
    async def test_delete_topic_service(self):
        """Test deleting topic via service"""
        from src.services.messaging import MessageQueueService
        
        with patch('kafka.admin.KafkaAdminClient') as mock_admin:
            mock_admin_instance = MagicMock()
            mock_admin.return_value = mock_admin_instance
            
            service = MessageQueueService()
            
            result = await service.delete_topic("test_topic")
            
            assert result == True
            mock_admin_instance.delete_topics.assert_called_once()


class TestMessageHandlers:
    """Test message handlers and processors"""
    
    @pytest.mark.asyncio
    async def test_agent_event_handler(self):
        """Test agent event message handler"""
        from src.messaging.handlers import AgentEventHandler
        
        handler = AgentEventHandler()
        
        message = {
            "agent_id": str(uuid.uuid4()),
            "event_type": "task_completed",
            "payload": {"result": "success", "duration": 5.2}
        }
        
        result = await handler.process_message(message)
        
        assert result == True
    
    @pytest.mark.asyncio
    async def test_orchestration_event_handler(self):
        """Test orchestration event message handler"""
        from src.messaging.handlers import OrchestrationEventHandler
        
        handler = OrchestrationEventHandler()
        
        message = {
            "orchestration_id": str(uuid.uuid4()),
            "event_type": "orchestration_started",
            "payload": {"agent_count": 3, "pattern": "sequential"}
        }
        
        result = await handler.process_message(message)
        
        assert result == True
    
    @pytest.mark.asyncio
    async def test_system_event_handler(self):
        """Test system event message handler"""
        from src.messaging.handlers import SystemEventHandler
        
        handler = SystemEventHandler()
        
        message = {
            "event_type": "system_alert",
            "severity": "warning",
            "payload": {"message": "High CPU usage detected", "cpu_usage": 85.5}
        }
        
        result = await handler.process_message(message)
        
        assert result == True
    
    @pytest.mark.asyncio
    async def test_message_router(self):
        """Test message routing to appropriate handlers"""
        from src.messaging.router import MessageRouter
        
        router = MessageRouter()
        
        # Test agent event routing
        agent_message = {
            "topic": "agent_events",
            "message": {
                "agent_id": str(uuid.uuid4()),
                "event_type": "status_changed"
            }
        }
        
        handler = router.get_handler(agent_message["topic"])
        assert handler is not None
        
        # Test orchestration event routing
        orch_message = {
            "topic": "orchestration_events",
            "message": {
                "orchestration_id": str(uuid.uuid4()),
                "event_type": "task_assigned"
            }
        }
        
        handler = router.get_handler(orch_message["topic"])
        assert handler is not None


class TestMessageSerialization:
    """Test message serialization and deserialization"""
    
    def test_serialize_agent_event(self):
        """Test serializing agent event message"""
        from src.messaging.serializers import AgentEventSerializer
        
        serializer = AgentEventSerializer()
        
        event = {
            "agent_id": str(uuid.uuid4()),
            "event_type": "task_started",
            "timestamp": "2025-07-22T10:00:00Z",
            "payload": {"task_id": str(uuid.uuid4())}
        }
        
        serialized = serializer.serialize(event)
        
        assert isinstance(serialized, bytes)
        
        # Deserialize and verify
        deserialized = serializer.deserialize(serialized)
        assert deserialized["agent_id"] == event["agent_id"]
        assert deserialized["event_type"] == event["event_type"]
    
    def test_serialize_orchestration_event(self):
        """Test serializing orchestration event message"""
        from src.messaging.serializers import OrchestrationEventSerializer
        
        serializer = OrchestrationEventSerializer()
        
        event = {
            "orchestration_id": str(uuid.uuid4()),
            "event_type": "orchestration_completed",
            "timestamp": "2025-07-22T10:00:00Z",
            "payload": {"success": True, "duration": 120.5}
        }
        
        serialized = serializer.serialize(event)
        
        assert isinstance(serialized, bytes)
        
        # Deserialize and verify
        deserialized = serializer.deserialize(serialized)
        assert deserialized["orchestration_id"] == event["orchestration_id"]
        assert deserialized["event_type"] == event["event_type"]
    
    def test_serialize_invalid_message(self):
        """Test handling invalid message serialization"""
        from src.messaging.serializers import AgentEventSerializer
        
        serializer = AgentEventSerializer()
        
        # Missing required fields
        invalid_event = {
            "event_type": "invalid_event"
            # Missing agent_id
        }
        
        with pytest.raises(ValueError):
            serializer.serialize(invalid_event)
    
    def test_deserialize_corrupted_message(self):
        """Test handling corrupted message deserialization"""
        from src.messaging.serializers import AgentEventSerializer
        
        serializer = AgentEventSerializer()
        
        # Invalid JSON bytes
        corrupted_data = b"invalid json data"
        
        with pytest.raises(ValueError):
            serializer.deserialize(corrupted_data)