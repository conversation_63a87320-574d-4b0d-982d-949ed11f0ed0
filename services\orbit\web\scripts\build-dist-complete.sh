#!/bin/bash
# Complete production build with API client generation for GCP bucket deployment

set -e

# Get the workspace root - when running via <PERSON>zel, we're in the execroot
# so we need to go back to the actual workspace
if [ -n "$BUILD_WORKSPACE_DIRECTORY" ]; then
    cd "$BUILD_WORKSPACE_DIRECTORY"
elif [ -n "$TEST_WORKSPACE" ]; then
    cd "$TEST_WORKSPACE/.."
else
    # Find the workspace root by looking for WORKSPACE file
    CURRENT_DIR="$PWD"
    while [ "$CURRENT_DIR" != "/" ]; do
        if [ -f "$CURRENT_DIR/WORKSPACE" ]; then
            cd "$CURRENT_DIR"
            break
        fi
        CURRENT_DIR="$(dirname "$CURRENT_DIR")"
    done
fi

echo "🚀 Building complete production dist for GCP bucket deployment..."
echo "Working directory: $(pwd)"

# Step 1: Generate API client
echo ""
echo "📡 Step 1: Generating API client..."
if [ -z "$BUILD_WORKSPACE_DIRECTORY" ]; then
    bazel run //services/orbit/web:generate_client
else
    echo "Running in Bazel context, skipping nested Bazel call for client generation..."
    # In Bazel context, assume the API client is already available or will be handled by the build
    echo "API client will be generated as part of the build dependencies."
fi

# Step 2: Build production bundle
echo ""
echo "🔨 Step 2: Building production bundle..."
cd services/orbit/web

# Verify we're in the right place
if [ ! -f "package.json" ]; then
    echo "Error: package.json not found in $(pwd)"
    exit 1
fi

# Clean any existing dist folder
rm -rf dist

# Install dependencies if needed
echo "Installing npm dependencies..."
npm install

# Set production environment variables for GCP deployment
echo "Configuring production environment for GCP..."
cat > .env.production << 'EOF'
# Production environment for GCP bucket deployment
VITE_API_BASE_URL=https://api.twodot.ai/api/v1
NODE_ENV=production
VITE_APP_VERSION=$npm_package_version
VITE_BUILD_DATE=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
EOF

echo "✅ Production environment configured"

# Build for production with optimizations
echo "Building optimized production bundle..."
npm run build

# Verify dist folder was created
if [ ! -d "dist" ]; then
    echo "Error: dist folder was not created"
    exit 1
fi

# Step 3: Add deployment metadata and optimizations
echo ""
echo "⚙️  Step 3: Adding deployment metadata and optimizations..."

# Create deployment info with more details
cat > dist/deployment-info.json << EOF
{
  "buildDate": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "buildHost": "$(hostname)",
  "gitCommit": "$(git rev-parse HEAD 2>/dev/null || echo 'unknown')",
  "gitBranch": "$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo 'unknown')",
  "environment": "production",
  "deploymentTarget": "gcp-bucket",
  "version": "$(cat package.json | grep '"version"' | cut -d'"' -f4)",
  "nodeVersion": "$(node --version)",
  "npmVersion": "$(npm --version)"
}
EOF

# Create health check endpoint
cat > dist/health.json << 'EOF'
{
  "status": "ok",
  "service": "crm-web",
  "environment": "production",
  "timestamp": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")"
}
EOF

# Create comprehensive robots.txt for production
cat > dist/robots.txt << 'EOF'
User-agent: *
Allow: /
Disallow: /api/
Disallow: /_internal/

# Health check endpoints
Allow: /health.json
Allow: /deployment-info.json

Sitemap: https://your-domain.com/sitemap.xml
EOF

# Create a basic sitemap.xml template
cat > dist/sitemap.xml << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>https://your-domain.com/</loc>
    <lastmod>$(date -u +"%Y-%m-%d")</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>
  <url>
    <loc>https://your-domain.com/companies</loc>
    <lastmod>$(date -u +"%Y-%m-%d")</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.8</priority>
  </url>
  <url>
    <loc>https://your-domain.com/contacts</loc>
    <lastmod>$(date -u +"%Y-%m-%d")</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.8</priority>
  </url>
  <url>
    <loc>https://your-domain.com/deals</loc>
    <lastmod>$(date -u +"%Y-%m-%d")</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.8</priority>
  </url>
</urlset>
EOF

# Create security.txt for security policy
mkdir -p dist/.well-known
cat > dist/.well-known/security.txt << 'EOF'
Contact: <EMAIL>
Expires: 2025-12-31T23:59:59.000Z
Encryption: https://your-domain.com/pgp-key.txt
Acknowledgments: https://your-domain.com/security/acknowledgments
Policy: https://your-domain.com/security/policy
EOF

# Add cache headers configuration file for GCP bucket
cat > dist/_headers << 'EOF'
# Cache static assets for 1 year
/assets/*
  Cache-Control: public, max-age=31536000, immutable

# Cache fonts for 1 year
/*.woff2
  Cache-Control: public, max-age=31536000

# Cache images for 1 month
/*.png
  Cache-Control: public, max-age=2592000
/*.jpg
  Cache-Control: public, max-age=2592000
/*.jpeg
  Cache-Control: public, max-age=2592000
/*.svg
  Cache-Control: public, max-age=2592000
/*.ico
  Cache-Control: public, max-age=2592000

# Don't cache HTML files
/*.html
  Cache-Control: public, max-age=0, must-revalidate

# Don't cache API responses and dynamic content
/health.json
  Cache-Control: no-cache, no-store, must-revalidate
/deployment-info.json
  Cache-Control: no-cache, no-store, must-revalidate
EOF

# Step 4: Generate deployment report
echo ""
echo "📊 Step 4: Generating deployment report..."

# Create comprehensive build report
cat > dist/build-report.txt << EOF
CRM Web Application - Production Build Report
============================================

Build Information:
- Build Date: $(date -u +"%Y-%m-%dT%H:%M:%SZ")
- Build Host: $(hostname)
- Git Commit: $(git rev-parse HEAD 2>/dev/null || echo 'unknown')
- Git Branch: $(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo 'unknown')
- Node Version: $(node --version)
- NPM Version: $(npm --version)

Build Statistics:
- Total Files: $(find dist -type f | wc -l)
- Total Size: $(du -sh dist | cut -f1)
- JS Files: $(find dist -name "*.js" | wc -l)
- CSS Files: $(find dist -name "*.css" | wc -l)
- Asset Files: $(find dist/assets -type f 2>/dev/null | wc -l || echo 0)

Largest Files:
$(find dist -type f -exec ls -lah {} \; | sort -k5 -hr | head -10 | awk '{print $5 " " $9}')

File Types:
$(find dist -type f | sed 's/.*\.//' | sort | uniq -c | sort -nr)

Environment Configuration:
- Target: GCP Bucket Static Hosting
- Environment: Production
- API Base URL: Check .env.production file

Deployment Checklist:
□ Update VITE_API_BASE_URL in .env.production
□ Upload dist/ contents to GCP bucket
□ Configure bucket for static website hosting
□ Set up CDN (optional but recommended)
□ Configure custom domain (optional)
□ Set up monitoring and logging
□ Test all application routes
□ Verify API connectivity

Security Features:
✓ Security.txt file added
✓ Robots.txt configured
✓ Cache headers configured
✓ Health check endpoints

SEO Features:
✓ Sitemap.xml template
✓ Proper cache headers
✓ Meta tags (if configured in index.html)
EOF

echo "✅ Complete production build finished successfully!"
echo ""
echo "📦 Distribution folder contents:"
find dist -type f | head -20
if [ $(find dist -type f | wc -l) -gt 20 ]; then
    echo "... and $(( $(find dist -type f | wc -l) - 20 )) more files"
fi

echo ""
echo "📊 Build statistics:"
echo "Total files: $(find dist -type f | wc -l)"
echo "Total size: $(du -sh dist | cut -f1)"
echo ""
echo "🎯 Key files for GCP deployment:"
echo "✓ dist/index.html - Main application entry point"
echo "✓ dist/assets/ - Static assets (JS, CSS, images)"
echo "✓ dist/health.json - Health check endpoint"
echo "✓ dist/deployment-info.json - Build metadata"
echo "✓ dist/_headers - Cache configuration for GCP"
echo "✓ dist/build-report.txt - Complete build report"
echo ""
echo "🚀 Ready for GCP bucket deployment!"
echo ""
echo "📋 Next steps:"
echo "1. Review and update .env.production with your actual API URL"
echo "2. Upload dist/ folder contents to your GCP bucket"
echo "3. Configure bucket for static website hosting:"
echo "   - Set index.html as main page"
echo "   - Set index.html as 404 page (for SPA routing)"
echo "4. Optional: Set up Cloud CDN for better performance"
echo "5. Optional: Configure custom domain"
echo "6. Test the deployment thoroughly"
echo ""
echo "📖 Build report saved to: dist/build-report.txt"