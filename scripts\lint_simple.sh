#!/bin/bash
set -e

# Simple Go linting script for Bazel test environments
# This script assumes golangci-lint is already installed

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Find the root directory by looking for go.mod
if [ -n "${BUILD_WORKSPACE_DIRECTORY:-}" ] && [ -f "${BUILD_WORKSPACE_DIRECTORY}/go.mod" ]; then
    # We're running from Bazel, use the workspace directory
    ROOT_DIR="$BUILD_WORKSPACE_DIRECTORY"
elif [ -f "go.mod" ]; then
    ROOT_DIR="$(pwd)"
elif [ -f "../go.mod" ]; then
    ROOT_DIR="$(cd ".." && pwd)"
elif [ -f "../../go.mod" ]; then
    ROOT_DIR="$(cd "../.." && pwd)"
else
    # Fallback to script directory approach
    ROOT_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"
fi

# Change to the root directory
cd "$ROOT_DIR"

# Set up PATH to include Go bin directory
export PATH="$PATH:$(go env GOPATH)/bin"

# Check if golangci-lint is available
if ! command -v golangci-lint &> /dev/null; then
    echo "❌ golangci-lint not found. Please install it first:"
    echo "curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b \$(go env GOPATH)/bin v1.57.1"
    exit 1
fi

# Run golangci-lint on specific packages
echo "Running golangci-lint..."
GOWORK=off golangci-lint run --config .golangci.yml \
    ./services/orbit/auth/... \
    ./services/orbit/crm_backend/... \
    ./services/orbit/gateway/... \
    ./shared/go/... \
    ./services/examples/go/...

echo "✅ Go linting completed successfully!"