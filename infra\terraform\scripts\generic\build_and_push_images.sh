#!/bin/bash
# Generic Docker image build and push script
# Builds and pushes Docker images for any environment

# Source common utilities
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/../common/utils.sh"
source "$SCRIPT_DIR/../common/environment.sh"

# Script configuration
SCRIPT_NAME="build_and_push_images.sh"
SCRIPT_DESCRIPTION="Build and push Docker images for specified environment"

# Required tools
REQUIRED_TOOLS=("bazel" "docker" "gcloud")

# Docker image build and push function
build_and_push_images() {
    local env="$1"
    
    print_step "Step 1: Environment setup and validation"
    if ! setup_environment "$env"; then
        return 1
    fi
    
    print_environment_summary "$env"
    production_safety_check
    
    print_step "Step 2: Docker authentication"
    
    # Authenticate with Docker registry
    print_info "Authenticating with Docker registry..."
    if ! gcloud auth configure-docker "$REGION-docker.pkg.dev" --quiet; then
        print_error "Docker authentication failed"
        return 1
    fi
    
    print_success "Docker authentication completed"
    
    print_step "Step 3: Build Docker images"
    
    # List of services to build
    local services=("gateway" "auth" "crm-backend")
    
    print_info "Building Docker images for services: ${services[*]}"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_info "[DRY RUN] Would build Docker images for: ${services[*]}"
        return 0
    fi
    
    # Build all images using the unified build target
    print_info "Building all backend services..."
    
    # Build individual service images with bazel
    local build_targets=(
        "//services/orbit/gateway:gateway_image"
        "//services/orbit/auth:auth_service_image" 
        "//services/orbit/crm_backend:crm_backend_image"
    )
    
    for target in "${build_targets[@]}"; do
        print_info "Building: $target"
        if ! execute bazel build "$target"; then
            print_error "Failed to build: $target"
            return 1
        fi
    done
    
    # Load and push images to registry
    for service in "${services[@]}"; do
        print_info "Loading and pushing $service image..."
        
        local image_tar
        local local_tag
        local remote_tag="$REGISTRY_URL/$service:latest"
        
        case "$service" in
            "gateway")
                image_tar="bazel-bin/services/orbit/gateway/gateway_image.tar"
                local_tag="bazel/platform_gateway:latest"
                ;;
            "auth")
                image_tar="bazel-bin/services/orbit/auth/auth_service_image.tar"
                local_tag="bazel/platform_auth:latest"
                ;;
            "crm-backend")
                image_tar="bazel-bin/services/orbit/crm_backend/crm_backend_image.tar"
                local_tag="bazel/platform_crm_backend:latest"
                ;;
        esac
        
        if [[ -f "$image_tar" ]]; then
            # Load image into Docker
            docker load < "$image_tar"
            
            # Tag and push
            docker tag "$local_tag" "$remote_tag"
            docker push "$remote_tag"
            
            print_success "$service image pushed to registry"
        else
            print_error "Image tar not found: $image_tar"
            return 1
        fi
    done
    
    print_success "All Docker images built and pushed successfully"
    
    print_step "Step 4: Verify images in registry"
    
    # Verify images were pushed to registry
    print_info "Verifying images in registry..."
    
    for service in "${services[@]}"; do
        local image_name="$REGISTRY_URL/$service:latest"
        print_info "Checking image: $image_name"
        
        if gcloud container images describe "$image_name" --project="$PROJECT_ID" > /dev/null 2>&1; then
            print_success "✅ $service image verified"
        else
            print_error "❌ $service image not found in registry"
            return 1
        fi
    done
    
    print_success "All images verified in registry"
    
    print_step "Step 5: Display image information"
    
    # Show image details
    print_info "Image details:"
    for service in "${services[@]}"; do
        local image_name="$REGISTRY_URL/$service:latest"
        echo "  📦 $service: $image_name"
        
        # Get image digest and creation time
        local digest
        local created
        
        if digest=$(gcloud container images describe "$image_name" --format="value(image_summary.digest)" 2>/dev/null); then
            echo "    🔍 Digest: ${digest:0:20}..."
        fi
        
        if created=$(gcloud container images describe "$image_name" --format="value(image_summary.time_created)" 2>/dev/null); then
            echo "    📅 Created: $created"
        fi
    done
    
    print_success "Docker images built and pushed successfully!"
    
    echo ""
    print_info "🚀 Next steps:"
    echo "  1. Configure VM: ./scripts/generic/configure_vm.sh $env"
    echo "  2. Update images on VM: gcloud compute ssh $INSTANCE_NAME --zone=$ZONE --command='cd ~/platform && bash update-images.sh'"
    echo "  3. Check service status: gcloud compute ssh $INSTANCE_NAME --zone=$ZONE --command='cd ~/platform && docker compose ps'"
    echo ""
    
    return 0
}

# Build images only (no push)
build_images() {
    local env="$1"
    
    print_step "Building Docker images for: $env"
    
    if ! setup_environment "$env"; then
        return 1
    fi
    
    print_info "Building Docker images locally..."
    
    # Build images using bazel
    local services=("gateway" "auth" "crm-backend")
    
    for service in "${services[@]}"; do
        print_info "Building $service image..."
        
        local build_target
        case "$service" in
            "gateway")
                build_target="//services/orbit/gateway:gateway_image"
                ;;
            "auth")
                build_target="//services/orbit/auth:auth_service_image"
                ;;
            "crm-backend")
                build_target="//services/orbit/crm_backend:crm_backend_image"
                ;;
            *)
                print_error "Unknown service: $service"
                return 1
                ;;
        esac
        
        if ! execute bazel build "$build_target"; then
            print_error "Failed to build $service image"
            return 1
        fi
        
        print_success "$service image built"
    done
    
    print_success "All Docker images built successfully"
    return 0
}

# Push existing images
push_images() {
    local env="$1"
    
    print_step "Pushing Docker images for: $env"
    
    if ! setup_environment "$env"; then
        return 1
    fi
    
    # Authenticate with Docker registry
    print_info "Authenticating with Docker registry..."
    if ! gcloud auth configure-docker "$REGION-docker.pkg.dev" --quiet; then
        print_error "Docker authentication failed"
        return 1
    fi
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_info "[DRY RUN] Would push images to registry: $REGISTRY_URL"
        return 0
    fi
    
    # Push all images
    local services=("gateway" "auth" "crm-backend")
    
    for service in "${services[@]}"; do
        print_info "Pushing $service image..."
        
        # Load image into Docker (from bazel)
        local image_tar
        case "$service" in
            "gateway")
                image_tar="bazel-bin/services/orbit/gateway/gateway_image.tar"
                ;;
            "auth")
                image_tar="bazel-bin/services/orbit/auth/auth_service_image.tar"
                ;;
            "crm-backend")
                image_tar="bazel-bin/services/orbit/crm_backend/crm_backend_image.tar"
                ;;
        esac
        
        if [[ -f "$image_tar" ]]; then
            # Load image into Docker
            docker load < "$image_tar"
            
            # Tag and push
            local local_tag="bazel/platform_${service//-/_}:latest"
            local remote_tag="$REGISTRY_URL/$service:latest"
            
            docker tag "$local_tag" "$remote_tag"
            docker push "$remote_tag"
            
            print_success "$service image pushed"
        else
            print_error "Image tar not found: $image_tar"
            print_info "Build the image first with: bazel build //platform/$service:${service}_image"
            return 1
        fi
    done
    
    print_success "All Docker images pushed successfully"
    return 0
}

# List available images
list_images() {
    local env="$1"
    
    print_step "Listing Docker images for: $env"
    
    if ! setup_environment "$env"; then
        return 1
    fi
    
    print_info "Images in registry: $REGISTRY_URL"
    
    # List images in the registry
    if ! gcloud container images list --repository="$REGISTRY_URL" --project="$PROJECT_ID"; then
        print_error "Failed to list images in registry"
        return 1
    fi
    
    echo ""
    print_info "Detailed image information:"
    
    local services=("gateway" "auth" "crm-backend")
    for service in "${services[@]}"; do
        local image_name="$REGISTRY_URL/$service"
        echo ""
        print_info "Image: $service"
        
        if gcloud container images list-tags "$image_name" --project="$PROJECT_ID" --format="table(digest.slice(0:20):label=DIGEST,timestamp.date():label=CREATED,tags.list():label=TAGS)" 2>/dev/null; then
            echo ""
        else
            echo "  No images found"
        fi
    done
    
    return 0
}

# Clean up old images
cleanup_images() {
    local env="$1"
    
    print_step "Cleaning up old Docker images for: $env"
    
    if ! setup_environment "$env"; then
        return 1
    fi
    
    production_safety_check
    
    print_info "Cleaning up images in registry: $REGISTRY_URL"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_info "[DRY RUN] Would clean up old images in registry"
        return 0
    fi
    
    # Clean up old images (keep last 10 versions)
    local services=("gateway" "auth" "crm-backend")
    
    for service in "${services[@]}"; do
        local image_name="$REGISTRY_URL/$service"
        print_info "Cleaning up old versions of: $service"
        
        if ! gcloud container images list-tags "$image_name" --project="$PROJECT_ID" --limit=999 --sort-by="timestamp" --format="get(digest)" | tail -n +11 | xargs -I {} gcloud container images delete "$image_name@{}" --quiet --project="$PROJECT_ID" 2>/dev/null; then
            print_info "No old images to clean up for $service"
        else
            print_success "Cleaned up old images for $service"
        fi
    done
    
    print_success "Image cleanup completed"
    return 0
}

# Main script execution
main() {
    setup_error_handling
    
    # Parse command line arguments
    parse_args "$SCRIPT_NAME" "$@"
    
    # Print banner
    print_banner "$SCRIPT_NAME" "$SCRIPT_DESCRIPTION"
    
    # Check required tools
    if ! check_required_tools "${REQUIRED_TOOLS[@]}"; then
        exit 1
    fi
    
    # Change to workspace directory
    if ! change_to_workspace; then
        exit 1
    fi
    
    # Validate environment
    if ! validate_environment "$ENVIRONMENT"; then
        exit 1
    fi
    
    # Handle special commands
    case "$1" in
        "build")
            if build_images "$ENVIRONMENT"; then
                print_success "Docker images built successfully! 🎉"
                exit 0
            else
                print_error "Docker image build failed!"
                exit 1
            fi
            ;;
        "push")
            if push_images "$ENVIRONMENT"; then
                print_success "Docker images pushed successfully! 🎉"
                exit 0
            else
                print_error "Docker image push failed!"
                exit 1
            fi
            ;;
        "list")
            if list_images "$ENVIRONMENT"; then
                exit 0
            else
                exit 1
            fi
            ;;
        "cleanup")
            if cleanup_images "$ENVIRONMENT"; then
                print_success "Image cleanup completed! 🎉"
                exit 0
            else
                print_error "Image cleanup failed!"
                exit 1
            fi
            ;;
        *)
            # Full build and push
            if build_and_push_images "$ENVIRONMENT"; then
                print_success "Docker images built and pushed successfully! 🎉"
                exit 0
            else
                print_error "Docker image build and push failed!"
                exit 1
            fi
            ;;
    esac
}

# Show usage if no arguments provided
if [[ $# -eq 0 ]]; then
    print_usage "$SCRIPT_NAME"
    echo ""
    echo "Additional commands:"
    echo "  build ENVIRONMENT      Build Docker images only"
    echo "  push ENVIRONMENT       Push existing Docker images"
    echo "  list ENVIRONMENT       List images in registry"
    echo "  cleanup ENVIRONMENT    Clean up old images"
    exit 1
fi

# Execute main function with all arguments
main "$@"