# Generic Environment Main Configuration
# This file can be used by any environment (dev, staging, prod) by setting the environment variable

terraform {
  required_version = ">= 1.5"
  
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
    random = {
      source  = "hashicorp/random"
      version = "~> 3.1"
    }
  }
}

# Configure the Google Cloud Provider
provider "google" {
  project = var.project_id
  region  = var.region
  zone    = var.zone
}

# Import shared configuration
locals {
  # Import the shared environment base configuration
  terraform_config = {
    required_version = ">= 1.5"
    required_providers = {
      google = {
        source  = "hashicorp/google"
        version = "~> 5.0"
      }
      random = {
        source  = "hashicorp/random"
        version = "~> 3.1"
      }
    }
  }

  # Required Google Cloud APIs (standardized across all environments)
  required_apis = [
    "compute.googleapis.com",
    "container.googleapis.com", 
    "containerregistry.googleapis.com",
    "sqladmin.googleapis.com",
    "servicenetworking.googleapis.com",
    "cloudresourcemanager.googleapis.com",
    "iam.googleapis.com",
    "secretmanager.googleapis.com",
    "artifactregistry.googleapis.com",
    "cloudbuild.googleapis.com",
    "storage.googleapis.com",
    "cloudapis.googleapis.com",
    "serviceusage.googleapis.com",
    "dns.googleapis.com",
    "monitoring.googleapis.com",
    "logging.googleapis.com",
    "iap.googleapis.com",
  ]

  # Common service ports (standardized across environments)
  service_ports = {
    auth_service = 8004
    crm_backend = 8003
    gateway = 8085
  }

  # Environment-specific configurations
  environment_configs = {
    dev = {
      vpc_cidr_prefix = "10.0"
      machine_types = {
        compute = "e2-standard-2"
        database = "db-f1-micro"
      }
      database_config = {
        postgres_version = "POSTGRES_15"
        disk_size_gb = 20
        max_disk_size_gb = 100
        backup_retention_days = 7
        availability_type = "ZONAL"
        deletion_protection = false
      }
      security_config = {
        enable_ssl = true
        enable_cloud_armor = false
        enable_public_access = true
        ssh_source_ranges = ["0.0.0.0/0"]
        assign_external_ip = true
        create_static_ip = false
        enable_http_redirect = false
        enable_cdn = false
      }
      registry_config = {
        cleanup_older_than_days = 7
        cleanup_untagged_older_than_days = 1
        enable_cloud_build_access = true
        enable_compute_access = true
        enable_automated_builds = false
      }
      storage_config = {
        enable_versioning = false
        lifecycle_rules = [
          {
            action                = "Delete"
            age_days              = 30
            target_storage_class  = "STANDARD"
            matches_storage_class = ["STANDARD"]
          }
        ]
        enable_automated_deployment = false
        upload_default_files = false
      }
      compute_config = {
        boot_disk_size_gb = 20
        docker_volumes_disk_size_gb = 30
      }
      feature_flags = {
        enable_migration_automation = false
        enable_monitoring = false
        enable_backup_automation = false
        schedule_shutdown = false
        auto_scaling_enabled = false
        enable_debug_mode = true
        enable_external_access = true
      }
    }
    
    staging = {
      vpc_cidr_prefix = "10.2"
      machine_types = {
        compute = "e2-standard-2"
        database = "db-custom-1-3840"
      }
      database_config = {
        postgres_version = "POSTGRES_16"
        disk_size_gb = 50
        max_disk_size_gb = 200
        backup_retention_days = 14
        availability_type = "ZONAL"
        deletion_protection = false
      }
      security_config = {
        enable_ssl = true
        enable_cloud_armor = true
        enable_public_access = true
        ssh_source_ranges = ["0.0.0.0/0"]
        assign_external_ip = true
        create_static_ip = false
        enable_http_redirect = true
        enable_cdn = true
      }
      registry_config = {
        cleanup_older_than_days = 14
        cleanup_untagged_older_than_days = 3
        enable_cloud_build_access = true
        enable_compute_access = true
        enable_automated_builds = false
      }
      storage_config = {
        enable_versioning = true
        lifecycle_rules = [
          {
            action                = "SetStorageClass"
            age_days              = 30
            target_storage_class  = "NEARLINE"
            matches_storage_class = ["STANDARD"]
          }
        ]
        enable_automated_deployment = false
        upload_default_files = false
      }
      compute_config = {
        boot_disk_size_gb = 30
        docker_volumes_disk_size_gb = 50
      }
      feature_flags = {
        enable_migration_automation = false
        enable_monitoring = true
        enable_backup_automation = true
        schedule_shutdown = false
        auto_scaling_enabled = true
        enable_debug_mode = false
        enable_external_access = false
      }
    }
    
    prod = {
      vpc_cidr_prefix = "10.1"
      machine_types = {
        compute = "e2-standard-2"
        database = "db-custom-2-7680"
      }
      database_config = {
        postgres_version = "POSTGRES_16"
        disk_size_gb = 100
        max_disk_size_gb = 500
        backup_retention_days = 30
        availability_type = "REGIONAL"
        deletion_protection = true
      }
      security_config = {
        enable_ssl = true
        enable_cloud_armor = true
        enable_public_access = true
        ssh_source_ranges = ["0.0.0.0/0"]
        assign_external_ip = true
        create_static_ip = false
        enable_http_redirect = true
        enable_cdn = true
      }
      registry_config = {
        cleanup_older_than_days = 30
        cleanup_untagged_older_than_days = 7
        enable_cloud_build_access = true
        enable_compute_access = true
        enable_automated_builds = false
      }
      storage_config = {
        enable_versioning = true
        lifecycle_rules = [
          {
            action                = "SetStorageClass"
            age_days              = 30
            target_storage_class  = "NEARLINE"
            matches_storage_class = ["STANDARD"]
          },
          {
            action                = "SetStorageClass"
            age_days              = 90
            target_storage_class  = "COLDLINE"
            matches_storage_class = ["NEARLINE"]
          }
        ]
        enable_automated_deployment = false
        upload_default_files = false
      }
      compute_config = {
        boot_disk_size_gb = 50
        docker_volumes_disk_size_gb = 100
      }
      feature_flags = {
        enable_migration_automation = false
        enable_monitoring = true
        enable_backup_automation = true
        schedule_shutdown = false
        auto_scaling_enabled = true
        enable_debug_mode = false
        enable_external_access = false
      }
    }
  }
  
  # Get environment-specific configuration
  env_config = local.environment_configs[var.environment]
  
  # Computed values
  vpc_cidr = "${local.env_config.vpc_cidr_prefix}.0.0/16"
  public_subnet_cidr = "${local.env_config.vpc_cidr_prefix}.1.0/24"
  private_subnet_cidr = "${local.env_config.vpc_cidr_prefix}.2.0/24"
  
  # Database naming
  database_name = "platform_${var.environment}_db"
  database_user = "platform_${var.environment}_user"
  
  # Merge custom overrides (handle null/empty overrides gracefully)
  machine_types = merge(local.env_config.machine_types, coalesce(var.custom_machine_types, {}))
  security_config = merge(local.env_config.security_config, coalesce(var.custom_security_config, {}))
  feature_flags = merge(local.env_config.feature_flags, coalesce(var.custom_feature_flags, {}))
  
  # SSH source ranges with fallback
  ssh_source_ranges = coalesce(
    var.custom_network_config.ssh_source_ranges,
    var.admin_ip_ranges,
    local.env_config.security_config.ssh_source_ranges
  )
}

# Enable required Google Cloud APIs - standardized across all environments
resource "google_project_service" "required_apis" {
  for_each = toset(local.required_apis)
  
  service = each.value
  
  disable_dependent_services = true
  disable_on_destroy        = false
}

# VPC Network
module "network" {
  source = "../modules/network"
  
  project_name = var.project_name
  region = var.region
  
  public_subnet_cidr = local.public_subnet_cidr
  private_subnet_cidr = local.private_subnet_cidr
  
  ssh_source_ranges = local.ssh_source_ranges
  
  depends_on = [google_project_service.required_apis]
}

# Database
module "database" {
  source = "../modules/database"
  
  project_name = var.project_name
  environment = var.environment
  region = var.region
  
  vpc_network = module.network.vpc_self_link
  
  postgres_version = local.env_config.database_config.postgres_version
  instance_tier = coalesce(local.machine_types.database, "db-f1-micro")
  availability_type = local.env_config.database_config.availability_type
  disk_size_gb = local.env_config.database_config.disk_size_gb
  max_disk_size_gb = local.env_config.database_config.max_disk_size_gb
  backup_retention_days = local.env_config.database_config.backup_retention_days
  deletion_protection = local.env_config.database_config.deletion_protection
  
  database_name = local.database_name
  database_user = local.database_user
  
  enable_migration_automation = coalesce(local.feature_flags.enable_migration_automation, false)
  github_repo_owner = var.github_repo_owner
  github_repo_name = var.github_repo_name
  migration_branch = "main"
  
  depends_on = [google_project_service.required_apis]
}

# OAuth Secrets (with destroy protection)
module "secrets" {
  source = "../modules/secrets"
  
  project_id = var.project_id
  environment = var.environment
  
  depends_on = [google_project_service.required_apis]
}

# Artifact Registry
module "registry" {
  source = "../modules/registry"
  
  project_id = var.project_id
  project_name = var.project_name
  region = var.region
  
  # Use environment-specific cleanup policies
  cleanup_older_than_days = try(local.env_config.registry_config.cleanup_older_than_days, 30)
  cleanup_untagged_older_than_days = try(local.env_config.registry_config.cleanup_untagged_older_than_days, 7)
  
  enable_cloud_build_access = try(local.env_config.registry_config.enable_cloud_build_access, true)
  enable_compute_access = try(local.env_config.registry_config.enable_compute_access, true)
  enable_automated_builds = try(local.env_config.registry_config.enable_automated_builds, false)
  
  github_repo_owner = var.github_repo_owner
  github_repo_name = var.github_repo_name
  build_branch_pattern = "main"
  cloudbuild_file_path = "cloudbuild.yaml"
  
  depends_on = [google_project_service.required_apis]
}

# Cloud Storage for static files
module "storage" {
  source = "../modules/storage"
  
  project_name = var.project_name
  environment = var.environment
  bucket_location = var.region
  
  enable_public_access = coalesce(local.security_config.enable_public_access, true)
  enable_versioning = local.env_config.storage_config.enable_versioning
  
  lifecycle_rules = local.env_config.storage_config.lifecycle_rules
  
  enable_automated_deployment = local.env_config.storage_config.enable_automated_deployment
  github_repo_owner = var.github_repo_owner
  github_repo_name = var.github_repo_name
  deploy_branch = "main"
  
  # SPA routing configuration
  not_found_page = "index.html"
  
  upload_default_files = local.env_config.storage_config.upload_default_files
  
  depends_on = [google_project_service.required_apis]
}

# Compute Engine instance
module "compute" {
  source = "../modules/compute"
  
  project_id = var.project_id
  project_name = var.project_name
  environment = var.environment
  region = var.region
  zone = var.zone
  
  machine_type = coalesce(local.machine_types.compute, "e2-standard-2")
  boot_disk_size_gb = local.env_config.compute_config.boot_disk_size_gb
  docker_volumes_disk_size_gb = local.env_config.compute_config.docker_volumes_disk_size_gb
  
  subnet_name = module.network.private_subnet_name
  assign_external_ip = coalesce(local.security_config.assign_external_ip, true)
  create_static_ip = coalesce(local.security_config.create_static_ip, false)
  
  # Registry configuration
  registry_url = module.registry.registry_url
  
  # Database configuration
  database_host = module.database.instance_private_ip
  database_name = module.database.database_name
  database_user = module.database.database_user
  database_password_secret = module.database.database_password_secret_name
  
  # OAuth configuration
  google_oauth_client_id_secret = module.secrets.oauth_secrets.client_id
  google_oauth_client_secret_secret = module.secrets.oauth_secrets.client_secret
  oauth_state_secret = module.secrets.oauth_secrets.state_secret
  
  # Service ports (standardized)
  auth_service_port = local.service_ports.auth_service
  crm_service_port = local.service_ports.crm_backend
  gateway_port = local.service_ports.gateway
  
  # Environment-specific domain configuration
  frontend_domain = var.environment == "prod" ? "twodot.ai" : "${var.environment}.twodot.ai"
  api_domain = var.environment == "prod" ? "api.twodot.ai" : "api.${var.environment}.twodot.ai"
  ssl_domains = var.ssl_domains
  
  depends_on = [google_project_service.required_apis]
}

# Load Balancer
module "loadbalancer" {
  source = "../modules/loadbalancer"
  
  project_name = var.project_name
  environment = var.environment
  
  storage_bucket_name = module.storage.bucket_name
  api_backend_service = module.compute.backend_service_self_link
  
  # SSL configuration
  enable_ssl = coalesce(local.security_config.enable_ssl, true)
  use_managed_ssl = true
  ssl_domains = var.ssl_domains
  enable_http_redirect = coalesce(local.security_config.enable_http_redirect, false)
  
  # CDN configuration
  enable_cdn = coalesce(local.security_config.enable_cdn, false)
  
  # Security configuration
  enable_cloud_armor = coalesce(local.security_config.enable_cloud_armor, false)
  
  depends_on = [google_project_service.required_apis]
}