/**
 * Simple CRM API Client
 * Generated client for the CRM REST API
 */

export interface ApiResponse<T> {
  data: T;
  status: number;
  statusText: string;
}

export interface ApiError {
  code: string;
  message: string;
  details?: any;
}

export class ApiClient {
  private baseUrl: string;
  private headers: Record<string, string>;

  constructor(baseUrl: string, token?: string) {
    this.baseUrl = baseUrl.replace(/\/$/, ''); // Remove trailing slash
    this.headers = {
      'Content-Type': 'application/json',
    };
    
    if (token) {
      this.headers['Authorization'] = `Bearer ${token}`;
    }
  }

  private async request<T>(
    method: string,
    path: string,
    body?: any
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${path}`;
    
    const options: RequestInit = {
      method,
      headers: this.headers,
    };

    if (body) {
      options.body = JSON.stringify(body);
    }

    const response = await fetch(url, options);
    
    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    return {
      data,
      status: response.status,
      statusText: response.statusText,
    };
  }

  // Auth methods
  async signIn(email: string, password: string) {
    return this.request<any>('POST', '/auth/signin', { email, password });
  }

  async signOut() {
    return this.request<any>('POST', '/auth/signout');
  }

  async getCurrentUser() {
    return this.request<any>('GET', '/auth/user');
  }

  // Company methods
  async getCompanies(params?: { status_id?: string; include_deleted?: boolean }) {
    const query = params ? `?${new URLSearchParams(params as any)}` : '';
    return this.request<any[]>('GET', `/companies${query}`);
  }

  async getCompany(id: string) {
    return this.request<any>('GET', `/companies/${id}`);
  }

  async createCompany(company: any) {
    return this.request<any>('POST', '/companies', company);
  }

  async updateCompany(id: string, company: any) {
    return this.request<any>('PUT', `/companies/${id}`, company);
  }

  async deleteCompany(id: string) {
    return this.request<void>('DELETE', `/companies/${id}`);
  }

  async updateCompanyStatus(id: string, statusId: string) {
    return this.request<void>('PUT', `/companies/${id}/status`, { company_status_id: statusId });
  }

  async getCompanyStatuses() {
    return this.request<any[]>('GET', '/company-statuses');
  }

  // Contact methods
  async getContacts(params?: { company_id?: string; unlinked?: boolean; include_deleted?: boolean }) {
    const query = params ? `?${new URLSearchParams(params as any)}` : '';
    return this.request<any[]>('GET', `/contacts${query}`);
  }

  async getContact(id: string) {
    return this.request<any>('GET', `/contacts/${id}`);
  }

  async createContact(contact: any) {
    return this.request<any>('POST', '/contacts', contact);
  }

  async updateContact(id: string, contact: any) {
    return this.request<any>('PUT', `/contacts/${id}`, contact);
  }

  async deleteContact(id: string) {
    return this.request<void>('DELETE', `/contacts/${id}`);
  }

  async linkContactToCompany(contactId: string, companyId: string) {
    return this.request<void>('PUT', `/contacts/${contactId}/company`, { company_id: companyId });
  }

  async unlinkContactFromCompany(contactId: string) {
    return this.request<void>('DELETE', `/contacts/${contactId}/company`);
  }

  // Deal methods
  async getDeals(params?: { stage_id?: string; company_id?: string }) {
    const query = params ? `?${new URLSearchParams(params as any)}` : '';
    return this.request<any[]>('GET', `/deals${query}`);
  }

  async getDeal(id: string) {
    return this.request<any>('GET', `/deals/${id}`);
  }

  async createDeal(deal: any) {
    return this.request<any>('POST', '/deals', deal);
  }

  async updateDeal(id: string, deal: any) {
    return this.request<any>('PUT', `/deals/${id}`, deal);
  }

  async deleteDeal(id: string) {
    return this.request<void>('DELETE', `/deals/${id}`);
  }

  async getDealStages() {
    return this.request<any[]>('GET', '/deal-stages');
  }

  // User profile methods
  async getUserProfile() {
    return this.request<any>('GET', '/users/profile');
  }

  async updateUserProfile(profile: any) {
    return this.request<any>('PUT', '/users/profile', profile);
  }
}
