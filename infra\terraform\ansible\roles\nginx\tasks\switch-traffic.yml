---
- name: Check target deployment health
  uri:
    url: "http://localhost:{{ item.value.port }}-{{ target_color }}{{ item.value.health_check.path }}"
    method: GET
    status_code: 200
  retries: 5
  delay: 5
  loop: "{{ services | dict2items }}"
  when: item.value.health_check is defined
  tags: ['nginx', 'traffic-switch', 'health-check']

- name: Update Nginx upstream to target color
  lineinfile:
    path: "{{ microservices_root }}/nginx/upstream.conf"
    regexp: "^\\s*server {{ item.key }}-{{ 'blue' if target_color == 'green' else 'green' }}:{{ item.value.port }}"
    line: "    # server {{ item.key }}-{{ 'blue' if target_color == 'green' else 'green' }}:{{ item.value.port }};"
    backrefs: yes
  loop: "{{ services | dict2items }}"
  tags: ['nginx', 'traffic-switch']

- name: Enable target color services in upstream
  lineinfile:
    path: "{{ microservices_root }}/nginx/upstream.conf"
    regexp: "^\\s*#\\s*server {{ item.key }}-{{ target_color }}:{{ item.value.port }}"
    line: "    server {{ item.key }}-{{ target_color }}:{{ item.value.port }};"
    backrefs: yes
  loop: "{{ services | dict2items }}"
  notify: reload nginx container
  tags: ['nginx', 'traffic-switch']

- name: Update active deployment color
  copy:
    content: "{{ target_color }}"
    dest: "{{ microservices_root }}/active-color"
    mode: '0644'
  tags: ['nginx', 'traffic-switch']

- name: Test Nginx configuration
  docker_container:
    name: nginx-test
    image: nginx:alpine
    command: nginx -t
    volumes:
      - "{{ microservices_root }}/nginx/nginx.conf:/etc/nginx/nginx.conf:ro"
      - "{{ microservices_root }}/nginx/upstream.conf:/etc/nginx/conf.d/upstream.conf:ro"
    state: started
    detach: no
    cleanup: yes
  register: nginx_test
  tags: ['nginx', 'traffic-switch', 'test']

- name: Reload Nginx if configuration is valid
  docker_container:
    name: nginx
    state: started
    restart: no
    command: nginx -s reload
  when: nginx_test is succeeded
  tags: ['nginx', 'traffic-switch', 'reload']