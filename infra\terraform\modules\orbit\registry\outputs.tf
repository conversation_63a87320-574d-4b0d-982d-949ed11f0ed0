# Artifact Registry Module Outputs

output "repository_name" {
  description = "Name of the Docker repository"
  value       = google_artifact_registry_repository.docker_repo.name
}

output "repository_id" {
  description = "ID of the Docker repository"
  value       = google_artifact_registry_repository.docker_repo.repository_id
}

output "repository_location" {
  description = "Location of the Docker repository"
  value       = google_artifact_registry_repository.docker_repo.location
}

output "registry_url" {
  description = "Full URL of the Docker registry"
  value       = "${google_artifact_registry_repository.docker_repo.location}-docker.pkg.dev/${data.google_project.current.project_id}/${google_artifact_registry_repository.docker_repo.repository_id}"
}

output "registry_hostname" {
  description = "Hostname of the Docker registry"
  value       = "${google_artifact_registry_repository.docker_repo.location}-docker.pkg.dev"
}

# Service-specific image URLs
output "auth_service_image_url" {
  description = "Full image URL for the auth service"
  value       = "${google_artifact_registry_repository.docker_repo.location}-docker.pkg.dev/${data.google_project.current.project_id}/${google_artifact_registry_repository.docker_repo.repository_id}/auth-service"
}

output "crm_backend_image_url" {
  description = "Full image URL for the CRM backend service"
  value       = "${google_artifact_registry_repository.docker_repo.location}-docker.pkg.dev/${data.google_project.current.project_id}/${google_artifact_registry_repository.docker_repo.repository_id}/crm-backend"
}

output "gateway_image_url" {
  description = "Full image URL for the gateway service"
  value       = "${google_artifact_registry_repository.docker_repo.location}-docker.pkg.dev/${data.google_project.current.project_id}/${google_artifact_registry_repository.docker_repo.repository_id}/gateway"
}

output "web_image_url" {
  description = "Full image URL for the web frontend"
  value       = "${google_artifact_registry_repository.docker_repo.location}-docker.pkg.dev/${data.google_project.current.project_id}/${google_artifact_registry_repository.docker_repo.repository_id}/web"
}

# Build automation outputs
output "build_trigger_name" {
  description = "Name of the Cloud Build trigger for automated builds"
  value       = var.enable_automated_builds ? google_cloudbuild_trigger.docker_build[0].name : ""
}

output "build_trigger_id" {
  description = "ID of the Cloud Build trigger for automated builds"
  value       = var.enable_automated_builds ? google_cloudbuild_trigger.docker_build[0].trigger_id : ""
}

# Docker commands for common operations
output "docker_push_commands" {
  description = "Docker commands to push images to the registry"
  value = {
    auth_service = "docker push ${google_artifact_registry_repository.docker_repo.location}-docker.pkg.dev/${data.google_project.current.project_id}/${google_artifact_registry_repository.docker_repo.repository_id}/auth-service:latest"
    crm_backend  = "docker push ${google_artifact_registry_repository.docker_repo.location}-docker.pkg.dev/${data.google_project.current.project_id}/${google_artifact_registry_repository.docker_repo.repository_id}/crm-backend:latest"
    gateway      = "docker push ${google_artifact_registry_repository.docker_repo.location}-docker.pkg.dev/${data.google_project.current.project_id}/${google_artifact_registry_repository.docker_repo.repository_id}/gateway:latest"
    web          = "docker push ${google_artifact_registry_repository.docker_repo.location}-docker.pkg.dev/${data.google_project.current.project_id}/${google_artifact_registry_repository.docker_repo.repository_id}/web:latest"
  }
}

output "docker_pull_commands" {
  description = "Docker commands to pull images from the registry"
  value = {
    auth_service = "docker pull ${google_artifact_registry_repository.docker_repo.location}-docker.pkg.dev/${data.google_project.current.project_id}/${google_artifact_registry_repository.docker_repo.repository_id}/auth-service:latest"
    crm_backend  = "docker pull ${google_artifact_registry_repository.docker_repo.location}-docker.pkg.dev/${data.google_project.current.project_id}/${google_artifact_registry_repository.docker_repo.repository_id}/crm-backend:latest"
    gateway      = "docker pull ${google_artifact_registry_repository.docker_repo.location}-docker.pkg.dev/${data.google_project.current.project_id}/${google_artifact_registry_repository.docker_repo.repository_id}/gateway:latest"
    web          = "docker pull ${google_artifact_registry_repository.docker_repo.location}-docker.pkg.dev/${data.google_project.current.project_id}/${google_artifact_registry_repository.docker_repo.repository_id}/web:latest"
  }
}