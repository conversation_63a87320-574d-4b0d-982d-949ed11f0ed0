"""Prompt-based agent generation, execution, and management system.

This module provides natural language interfaces for agent operations,
allowing users to create, modify, and manage agents using conversational prompts.
"""

from typing import Dict, Any, List, Optional, Union, Tuple
from dataclasses import dataclass, field
from enum import Enum
import json
import re
import uuid
from datetime import datetime
from intelligence.gateway import AIGateway
from intelligence.providers.base import AIMessage
from utils.logging import get_logger, LoggerMixin
from utils.exceptions import AgentError, ValidationError
from .ai_workflow_generator import AgentWorkflowGenerator, GenerationRequest

logger = get_logger(__name__)

class OperationType(str, Enum):
    """Types of prompt-based operations."""
    CREATE_AGENT = "create_agent"
    MODIFY_AGENT = "modify_agent"
    EXECUTE_TASK = "execute_task"
    DEPLOY_AGENT = "deploy_agent"
    MONITOR_AGENT = "monitor_agent"
    DEBUG_AGENT = "debug_agent"
    OPTIMIZE_AGENT = "optimize_agent"
    DELETE_AGENT = "delete_agent"

@dataclass
class PromptOperation:
    """Prompt-based operation request."""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    user_prompt: str = ""
    operation_type: Optional[OperationType] = None
    context: Dict[str, Any] = field(default_factory=dict)
    parsed_intent: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)
    user_id: Optional[str] = None

@dataclass
class OperationResult:
    """Result of prompt-based operation."""
    operation_id: str
    success: bool
    result: Optional[Dict[str, Any]] = None
    generated_code: Optional[str] = None
    deployment_info: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    suggestions: List[str] = field(default_factory=list)
    follow_up_actions: List[str] = field(default_factory=list)

class PromptBasedOperations(LoggerMixin):
    """Natural language interface for agent operations."""
    
    def __init__(self, ai_gateway: AIGateway, workflow_generator: AgentWorkflowGenerator):
        self.ai_gateway = ai_gateway
        self.workflow_generator = workflow_generator
        self.operation_history: Dict[str, List[PromptOperation]] = {}
        self.intent_patterns = self._initialize_intent_patterns()
        
    def _initialize_intent_patterns(self) -> Dict[str, List[str]]:
        """Initialize patterns for intent recognition."""
        return {
            OperationType.CREATE_AGENT: [
                r"create.*agent.*that.*",
                r"build.*agent.*to.*",
                r"make.*agent.*for.*",
                r"generate.*agent.*which.*",
                r"i need.*agent.*that.*",
                r"develop.*agent.*to.*"
            ],
            OperationType.MODIFY_AGENT: [
                r"modify.*agent.*",
                r"update.*agent.*",
                r"change.*agent.*",
                r"improve.*agent.*",
                r"add.*to.*agent.*",
                r"remove.*from.*agent.*"
            ],
            OperationType.EXECUTE_TASK: [
                r"run.*task.*",
                r"execute.*",
                r"perform.*task.*",
                r"start.*task.*",
                r"do.*task.*"
            ],
            OperationType.DEPLOY_AGENT: [
                r"deploy.*agent.*",
                r"publish.*agent.*",
                r"release.*agent.*",
                r"launch.*agent.*",
                r"put.*agent.*live.*"
            ],
            OperationType.MONITOR_AGENT: [
                r"monitor.*agent.*",
                r"check.*agent.*status.*",
                r"how.*agent.*performing.*",
                r"show.*agent.*metrics.*",
                r"agent.*health.*"
            ]
        }
    
    async def process_prompt(
        self,
        user_prompt: str,
        user_id: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> OperationResult:
        """Process a natural language prompt and execute the intended operation."""
        
        operation = PromptOperation(
            user_prompt=user_prompt,
            context=context or {},
            user_id=user_id
        )
        
        self.log_operation(
            "processing_prompt",
            operation_id=operation.id,
            prompt_length=len(user_prompt),
            user_id=user_id
        )
        
        try:
            # Parse intent and extract parameters
            await self._parse_intent(operation)
            
            # Execute the appropriate operation
            if operation.operation_type == OperationType.CREATE_AGENT:
                return await self._handle_create_agent(operation)
            elif operation.operation_type == OperationType.MODIFY_AGENT:
                return await self._handle_modify_agent(operation)
            elif operation.operation_type == OperationType.EXECUTE_TASK:
                return await self._handle_execute_task(operation)
            elif operation.operation_type == OperationType.DEPLOY_AGENT:
                return await self._handle_deploy_agent(operation)
            elif operation.operation_type == OperationType.MONITOR_AGENT:
                return await self._handle_monitor_agent(operation)
            else:
                return OperationResult(
                    operation_id=operation.id,
                    success=False,
                    error="Could not understand the operation intent",
                    suggestions=[
                        "Try: 'Create an agent that processes emails'",
                        "Try: 'Deploy the customer service agent'",
                        "Try: 'Run task analysis on user data'"
                    ]
                )
                
        except Exception as e:
            self.log_error("prompt_processing_failed", e, operation_id=operation.id)
            return OperationResult(
                operation_id=operation.id,
                success=False,
                error=str(e)
            )
    
    async def _parse_intent(self, operation: PromptOperation) -> None:
        """Parse user intent and extract operation parameters."""
        
        prompt_lower = operation.user_prompt.lower()
        
        # First pass: pattern matching
        operation.operation_type = self._match_intent_patterns(prompt_lower)
        
        # Second pass: AI-powered intent parsing
        if not operation.operation_type:
            operation.operation_type = await self._ai_intent_recognition(operation.user_prompt)
        
        # Extract structured parameters using AI
        operation.parsed_intent = await self._extract_parameters(operation)
        
        # Add to history
        if operation.user_id:
            if operation.user_id not in self.operation_history:
                self.operation_history[operation.user_id] = []
            self.operation_history[operation.user_id].append(operation)
    
    def _match_intent_patterns(self, prompt: str) -> Optional[OperationType]:
        """Match prompt against predefined patterns."""
        
        for operation_type, patterns in self.intent_patterns.items():
            for pattern in patterns:
                if re.search(pattern, prompt, re.IGNORECASE):
                    return operation_type
        
        return None
    
    async def _ai_intent_recognition(self, prompt: str) -> Optional[OperationType]:
        """Use AI to recognize intent when patterns fail."""
        
        intent_prompt = f"""Analyze this user request and determine the primary intent:

User Request: "{prompt}"

Available Operations:
1. CREATE_AGENT - User wants to create/generate a new AI agent
2. MODIFY_AGENT - User wants to modify/update an existing agent  
3. EXECUTE_TASK - User wants to run a task or execute something
4. DEPLOY_AGENT - User wants to deploy/publish/launch an agent
5. MONITOR_AGENT - User wants to check status/health/performance
6. DEBUG_AGENT - User wants to debug/troubleshoot an agent
7. OPTIMIZE_AGENT - User wants to improve/optimize an agent
8. DELETE_AGENT - User wants to delete/remove an agent

Respond with only the operation name (e.g., "CREATE_AGENT") or "UNKNOWN" if unclear."""
        
        try:
            messages = [
                AIMessage(role="system", content="You are an expert at understanding user intents for AI agent operations."),
                AIMessage(role="user", content=intent_prompt)
            ]
            
            response = await self.ai_gateway.chat_completion(
                messages=messages,
                model="gpt-4o-mini",
                temperature=0.1,
                max_tokens=50
            )
            
            intent_str = response.content.strip()
            try:
                return OperationType(intent_str.lower())
            except ValueError:
                return None
                
        except Exception as e:
            self.logger.warning(f"AI intent recognition failed: {e}")
            return None
    
    async def _extract_parameters(self, operation: PromptOperation) -> Dict[str, Any]:
        """Extract structured parameters from the user prompt using AI."""
        
        extraction_prompt = f"""Extract structured parameters from this user request for {operation.operation_type.value}:

User Request: "{operation.user_prompt}"
Operation Type: {operation.operation_type.value}

Based on the operation type, extract relevant parameters as JSON:

For CREATE_AGENT, extract:
- agent_name: string
- agent_type: string (e.g., "assistant", "data_processor", "api_service")
- description: string
- capabilities: array of strings
- data_sources: array of strings
- target_platform: string
- requirements: object with specific needs

For MODIFY_AGENT, extract:
- agent_id or agent_name: string
- changes: array of change descriptions
- add_capabilities: array of new capabilities
- remove_capabilities: array of capabilities to remove
- update_config: object with configuration changes

For EXECUTE_TASK, extract:
- task_type: string
- task_description: string
- target_agent: string (if specified)
- parameters: object with task parameters
- data_inputs: array of input sources

For DEPLOY_AGENT, extract:
- agent_id or agent_name: string
- environment: string (e.g., "development", "staging", "production")
- deployment_config: object with deployment settings
- scaling_requirements: object

For MONITOR_AGENT, extract:
- agent_id or agent_name: string
- metrics_requested: array of metric types
- time_period: string
- alert_conditions: array of conditions

Respond with only valid JSON containing the extracted parameters."""
        
        try:
            messages = [
                AIMessage(role="system", content="You are an expert at extracting structured parameters from natural language requests."),
                AIMessage(role="user", content=extraction_prompt)
            ]
            
            response = await self.ai_gateway.chat_completion(
                messages=messages,
                model="gpt-4o",
                temperature=0.2,
                max_tokens=1000
            )
            
            return json.loads(response.content)
            
        except Exception as e:
            self.logger.warning(f"Parameter extraction failed: {e}")
            return {}
    
    async def _handle_create_agent(self, operation: PromptOperation) -> OperationResult:
        """Handle agent creation from natural language prompt."""
        
        try:
            params = operation.parsed_intent
            
            # Create generation request
            generation_request = GenerationRequest(
                description=params.get("description", operation.user_prompt),
                requirements=params,
                target_platform=params.get("target_platform", "python"),
                deployment_target=params.get("deployment_target", "kubernetes")
            )
            
            # Generate agent using workflow
            generation_result = await self.workflow_generator.generate_agent(generation_request)
            
            if generation_result.success:
                return OperationResult(
                    operation_id=operation.id,
                    success=True,
                    result={
                        "agent_definition": generation_result.generated_agent,
                        "generation_id": generation_result.request_id,
                        "workflow_stages": len(generation_result.workflow_log)
                    },
                    generated_code=self._extract_code_from_result(generation_result),
                    follow_up_actions=[
                        "Review the generated agent code",
                        "Run tests to validate functionality", 
                        "Deploy to development environment",
                        "Configure monitoring and alerts"
                    ]
                )
            else:
                return OperationResult(
                    operation_id=operation.id,
                    success=False,
                    error=generation_result.error,
                    suggestions=[
                        "Try providing more specific requirements",
                        "Specify the exact capabilities needed",
                        "Include data sources and expected outputs"
                    ]
                )
                
        except Exception as e:
            return OperationResult(
                operation_id=operation.id,
                success=False,
                error=f"Agent creation failed: {str(e)}"
            )
    
    async def _handle_modify_agent(self, operation: PromptOperation) -> OperationResult:
        """Handle agent modification from natural language prompt."""
        
        try:
            params = operation.parsed_intent
            agent_identifier = params.get("agent_id") or params.get("agent_name")
            
            if not agent_identifier:
                return OperationResult(
                    operation_id=operation.id,
                    success=False,
                    error="Could not identify which agent to modify",
                    suggestions=[
                        "Specify the agent name or ID",
                        "Example: 'Modify the email processing agent to handle attachments'"
                    ]
                )
            
            # Generate modification instructions using AI
            modification_prompt = f"""Generate modification instructions for an AI agent:

Agent to Modify: {agent_identifier}
Requested Changes: {operation.user_prompt}
Extracted Changes: {params.get('changes', [])}

Please provide:
1. Specific code modifications needed
2. Configuration updates required
3. New dependencies to add
4. Testing updates needed
5. Deployment considerations

Format as structured JSON with detailed instructions."""
            
            messages = [
                AIMessage(role="system", content="You are an expert at modifying AI agent implementations."),
                AIMessage(role="user", content=modification_prompt)
            ]
            
            response = await self.ai_gateway.chat_completion(
                messages=messages,
                model="claude-3-5-sonnet-20241022",
                temperature=0.3,
                max_tokens=3000
            )
            
            modification_instructions = json.loads(response.content)
            
            return OperationResult(
                operation_id=operation.id,
                success=True,
                result={
                    "agent_identifier": agent_identifier,
                    "modification_instructions": modification_instructions,
                    "changes_requested": params.get('changes', [])
                },
                follow_up_actions=[
                    "Review modification instructions",
                    "Apply changes to agent code",
                    "Run updated tests",
                    "Deploy modified agent to staging"
                ]
            )
            
        except Exception as e:
            return OperationResult(
                operation_id=operation.id,
                success=False,
                error=f"Agent modification failed: {str(e)}"
            )
    
    async def _handle_execute_task(self, operation: PromptOperation) -> OperationResult:
        """Handle task execution from natural language prompt."""
        
        try:
            params = operation.parsed_intent
            task_type = params.get("task_type", "general")
            
            # Create task execution plan
            execution_prompt = f"""Create a task execution plan:

Task Request: {operation.user_prompt}
Task Type: {task_type}
Task Description: {params.get('task_description', '')}
Target Agent: {params.get('target_agent', 'any suitable agent')}
Parameters: {params.get('parameters', {})}

Please provide:
1. Step-by-step execution plan
2. Required resources and permissions
3. Expected outputs and deliverables
4. Success criteria and validation steps
5. Error handling and recovery procedures

Format as structured JSON with executable steps."""
            
            messages = [
                AIMessage(role="system", content="You are an expert at creating executable task plans."),
                AIMessage(role="user", content=execution_prompt)
            ]
            
            response = await self.ai_gateway.chat_completion(
                messages=messages,
                model="gpt-4o",
                temperature=0.3,
                max_tokens=2000
            )
            
            execution_plan = json.loads(response.content)
            
            return OperationResult(
                operation_id=operation.id,
                success=True,
                result={
                    "task_type": task_type,
                    "execution_plan": execution_plan,
                    "estimated_duration": execution_plan.get("estimated_duration", "unknown")
                },
                follow_up_actions=[
                    "Review execution plan",
                    "Allocate required resources",
                    "Execute task steps",
                    "Monitor progress and results"
                ]
            )
            
        except Exception as e:
            return OperationResult(
                operation_id=operation.id,
                success=False,
                error=f"Task execution planning failed: {str(e)}"
            )
    
    async def _handle_deploy_agent(self, operation: PromptOperation) -> OperationResult:
        """Handle agent deployment from natural language prompt."""
        
        try:
            params = operation.parsed_intent
            agent_identifier = params.get("agent_id") or params.get("agent_name")
            environment = params.get("environment", "staging")
            
            # Create deployment plan
            deployment_plan = {
                "agent_identifier": agent_identifier,
                "target_environment": environment,
                "deployment_strategy": params.get("deployment_config", {}).get("strategy", "rolling"),
                "scaling_config": params.get("scaling_requirements", {"min_replicas": 1, "max_replicas": 3}),
                "health_checks": True,
                "rollback_enabled": True
            }
            
            return OperationResult(
                operation_id=operation.id,
                success=True,
                result=deployment_plan,
                deployment_info=deployment_plan,
                follow_up_actions=[
                    "Validate agent code and tests",
                    "Execute deployment pipeline",
                    "Monitor deployment progress",
                    "Verify agent health and functionality"
                ]
            )
            
        except Exception as e:
            return OperationResult(
                operation_id=operation.id,
                success=False,
                error=f"Deployment planning failed: {str(e)}"
            )
    
    async def _handle_monitor_agent(self, operation: PromptOperation) -> OperationResult:
        """Handle agent monitoring from natural language prompt."""
        
        try:
            params = operation.parsed_intent
            agent_identifier = params.get("agent_id") or params.get("agent_name")
            metrics_requested = params.get("metrics_requested", ["health", "performance", "errors"])
            
            # Generate monitoring report
            monitoring_data = {
                "agent_identifier": agent_identifier,
                "requested_metrics": metrics_requested,
                "time_period": params.get("time_period", "last 24 hours"),
                "alert_conditions": params.get("alert_conditions", []),
                "status": "Active monitoring initiated"
            }
            
            return OperationResult(
                operation_id=operation.id,
                success=True,
                result=monitoring_data,
                follow_up_actions=[
                    "Check agent health dashboard",
                    "Review performance metrics",
                    "Investigate any alerts or anomalies",
                    "Adjust monitoring thresholds if needed"
                ]
            )
            
        except Exception as e:
            return OperationResult(
                operation_id=operation.id,
                success=False,
                error=f"Monitoring setup failed: {str(e)}"
            )
    
    def _extract_code_from_result(self, generation_result) -> Optional[str]:
        """Extract generated code from workflow result."""
        
        if not generation_result.artifacts:
            return None
        
        code_artifacts = generation_result.artifacts.get("code_generation", {})
        if isinstance(code_artifacts, dict):
            # Look for main implementation file
            implementation = code_artifacts.get("core_implementation", "")
            if implementation:
                return str(implementation)
        
        return None
    
    def get_operation_history(self, user_id: str) -> List[Dict[str, Any]]:
        """Get operation history for a user."""
        
        operations = self.operation_history.get(user_id, [])
        return [
            {
                "id": op.id,
                "prompt": op.user_prompt[:100] + "..." if len(op.user_prompt) > 100 else op.user_prompt,
                "operation_type": op.operation_type.value if op.operation_type else "unknown",
                "created_at": op.created_at.isoformat()
            }
            for op in operations[-10:]  # Last 10 operations
        ]
    
    async def suggest_improvements(self, prompt: str) -> List[str]:
        """Suggest improvements to a user prompt for better results."""
        
        suggestion_prompt = f"""Analyze this user prompt and suggest improvements for better AI agent operation results:

Original Prompt: "{prompt}"

Provide 3-5 specific suggestions to make the prompt clearer, more detailed, and more actionable.
Focus on:
1. Adding specific requirements
2. Clarifying the expected outcome
3. Including relevant context
4. Specifying technical details
5. Defining success criteria

Format as a simple list of suggestions."""
        
        try:
            messages = [
                AIMessage(role="system", content="You are an expert at helping users write better prompts for AI agent operations."),
                AIMessage(role="user", content=suggestion_prompt)
            ]
            
            response = await self.ai_gateway.chat_completion(
                messages=messages,
                model="gpt-4o-mini",
                temperature=0.7,
                max_tokens=500
            )
            
            # Parse suggestions from response
            suggestions = [
                line.strip().lstrip('- ').lstrip('* ').lstrip('• ')
                for line in response.content.split('\n')
                if line.strip() and not line.strip().startswith('#')
            ]
            
            return suggestions[:5]  # Return top 5 suggestions
            
        except Exception as e:
            self.logger.warning(f"Suggestion generation failed: {e}")
            return [
                "Be more specific about what the agent should do",
                "Include examples of expected inputs and outputs",
                "Specify technical requirements and constraints",
                "Define success criteria and metrics",
                "Mention integration requirements and dependencies"
            ]