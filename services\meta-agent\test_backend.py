#!/usr/bin/env python3
"""
Test script to check backend status and create agents
"""
import requests
import json
import time
import sys
import os

BASE_URL = "http://localhost:8000"

def test_health():
    """Test if backend is running"""
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Backend is running!")
            print(f"Response: {response.json()}")
            return True
        else:
            print(f"❌ Backend responded with status: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Backend is not running (connection refused)")
        return False
    except requests.exceptions.Timeout:
        print("❌ Backend request timed out")
        return False
    except Exception as e:
        print(f"❌ Error checking backend: {e}")
        return False

def create_agent(name, agent_type, language, capabilities):
    """Create an agent via API"""
    try:
        payload = {
            "name": name,
            "type": agent_type,
            "language": language,
            "capabilities": capabilities,
            "description": f"A {name} agent with {', '.join(capabilities)} capabilities"
        }
        
        print(f"Creating {name} agent...")
        response = requests.post(f"{BASE_URL}/api/v1/agents", json=payload, timeout=30)
        
        if response.status_code in [200, 201]:
            agent_data = response.json()
            print(f"✅ {name} agent created successfully!")
            print(f"Agent ID: {agent_data.get('id', 'N/A')}")
            return agent_data
        else:
            print(f"❌ Failed to create {name} agent. Status: {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error creating {name} agent: {e}")
        return None

def generate_agent_code(agent_id, agent_name):
    """Generate code for an agent"""
    try:
        payload = {
            "agent_id": agent_id,
            "generate_frontend": True,
            "generate_backend": True
        }
        
        print(f"Generating code for {agent_name}...")
        response = requests.post(f"{BASE_URL}/api/v1/generation/generate", json=payload, timeout=60)
        
        if response.status_code == 200:
            generation_data = response.json()
            print(f"✅ Code generation completed for {agent_name}!")
            return generation_data
        else:
            print(f"❌ Failed to generate code for {agent_name}. Status: {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error generating code for {agent_name}: {e}")
        return None

def deploy_agent(agent_id, agent_name):
    """Deploy an agent"""
    try:
        payload = {
            "agent_id": agent_id
        }
        
        print(f"Deploying {agent_name}...")
        response = requests.post(f"{BASE_URL}/api/v1/deployment/deploy", json=payload, timeout=120)
        
        if response.status_code == 200:
            deployment_data = response.json()
            print(f"✅ {agent_name} deployed successfully!")
            frontend_url = deployment_data.get('frontend_url')
            if frontend_url:
                print(f"Frontend URL: {frontend_url}")
                return frontend_url
            return deployment_data
        else:
            print(f"❌ Failed to deploy {agent_name}. Status: {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error deploying {agent_name}: {e}")
        return None

def test_frontend(url, agent_name):
    """Test if frontend is accessible"""
    try:
        print(f"Testing {agent_name} frontend at {url}...")
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            print(f"✅ {agent_name} frontend is accessible!")
            return True
        else:
            print(f"❌ {agent_name} frontend returned status: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error testing {agent_name} frontend: {e}")
        return False

def main():
    print("🚀 AI Agent Platform - Backend Test & Agent Creation")
    print("=" * 50)
    
    # Test backend health
    if not test_health():
        print("\n❌ Backend is not running. Please start the backend first.")
        return
    
    # Create agents
    agents_to_create = [
        {
            "name": "EMI Calculator",
            "type": "fullstack",
            "language": "python",
            "capabilities": ["web_integration", "data_analysis", "visualization"]
        },
        {
            "name": "Math Calculator", 
            "type": "fullstack",
            "language": "python",
            "capabilities": ["web_integration", "data_analysis", "visualization"]
        }
    ]
    
    created_agents = []
    
    for agent_config in agents_to_create:
        agent = create_agent(**agent_config)
        if agent:
            created_agents.append((agent, agent_config["name"]))
    
    if not created_agents:
        print("\n❌ No agents were created successfully.")
        return
    
    print(f"\n✅ Created {len(created_agents)} agents successfully!")
    
    # Generate code for agents
    for agent, agent_name in created_agents:
        agent_id = agent.get('id')
        if agent_id:
            generation_result = generate_agent_code(agent_id, agent_name)
            if generation_result:
                # Deploy agent
                frontend_url = deploy_agent(agent_id, agent_name)
                if frontend_url:
                    # Test frontend
                    time.sleep(5)  # Wait for deployment
                    test_frontend(frontend_url, agent_name)
    
    print("\n🎉 Process completed!")

if __name__ == "__main__":
    main()