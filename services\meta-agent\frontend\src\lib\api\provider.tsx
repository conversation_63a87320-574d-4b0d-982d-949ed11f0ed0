'use client';

// React Query provider with optimized configuration
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
// import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { useState } from 'react';

interface ApiProviderProps {
  children: React.ReactNode;
}

export function ApiProvider({ children }: ApiProviderProps) {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            // Stale time: 5 minutes
            staleTime: 5 * 60 * 1000,
            // Cache time: 10 minutes
            gcTime: 10 * 60 * 1000,
            // Retry failed requests once
            retry: 1,
            // Don't refetch on window focus in development
            refetchOnWindowFocus: process.env.NODE_ENV === 'production',
            // Background refetch interval: 5 minutes
            refetchInterval: false,
            // Error retry delay
            retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
          },
          mutations: {
            // Retry failed mutations once
            retry: 1,
            // Error retry delay for mutations
            retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
          },
        },
      })
  );

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {/* ReactQueryDevtools disabled - package not installed */}
    </QueryClientProvider>
  );
}

// Custom error boundary for API errors
import { Component, ErrorInfo, ReactNode } from 'react';
import { ApiError } from './client';

interface ApiErrorBoundaryState {
  hasError: boolean;
  error?: Error | ApiError;
}

interface ApiErrorBoundaryProps {
  children: ReactNode;
  fallback?: (error: Error | ApiError) => ReactNode;
}

export class ApiErrorBoundary extends Component<ApiErrorBoundaryProps, ApiErrorBoundaryState> {
  constructor(props: ApiErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ApiErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('API Error caught by boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError && this.state.error) {
      if (this.props.fallback) {
        return this.props.fallback(this.state.error);
      }

      const isApiError = this.state.error instanceof ApiError;
      
      return (
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="max-w-md p-6 bg-destructive/10 border border-destructive/20 rounded-lg">
            <h2 className="text-lg font-semibold text-destructive mb-2">
              {isApiError ? 'API Error' : 'Something went wrong'}
            </h2>
            <p className="text-sm text-muted-foreground mb-4">
              {this.state.error.message}
            </p>
            {isApiError && (
              <div className="text-xs text-muted-foreground space-y-1">
                {(this.state.error as any).error_code && (
                  <p>Code: {(this.state.error as any).error_code}</p>
                )}
                {(this.state.error as any).status && (
                  <p>Status: {(this.state.error as any).status}</p>
                )}
              </div>
            )}
            <button
              onClick={() => window.location.reload()}
              className="mt-4 px-4 py-2 bg-primary text-primary-foreground rounded-md text-sm"
            >
              Reload Page
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// HOC for wrapping components with API error boundary
export function withApiErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: (error: Error | ApiError) => ReactNode
) {
  return function WithApiErrorBoundaryWrapper(props: P) {
    return (
      <ApiErrorBoundary fallback={fallback}>
        <Component {...props} />
      </ApiErrorBoundary>
    );
  };
}

// Query client singleton for use outside of React
let globalQueryClient: QueryClient | null = null;

export function getGlobalQueryClient(): QueryClient {
  if (!globalQueryClient) {
    globalQueryClient = new QueryClient({
      defaultOptions: {
        queries: {
          staleTime: 5 * 60 * 1000,
          gcTime: 10 * 60 * 1000,
          retry: 1,
          refetchOnWindowFocus: false,
        },
        mutations: {
          retry: 1,
        },
      },
    });
  }
  return globalQueryClient;
}