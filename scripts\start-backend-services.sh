#!/bin/bash
set -e

echo "🚀 Starting platform services..."

# Create network if it doesn't exist
sudo docker network create platform-network 2>/dev/null || true

# Use environment variables (passed externally)
# Default values if not set
REGISTRY_URL=${REGISTRY_URL:-"australia-southeast1-docker.pkg.dev/agent-dev-459718/platform-docker"}
DATABASE_URL=${DATABASE_URL:-"postgresql://test:test@localhost:5432/test"}
JWT_SECRET=${JWT_SECRET:-"test-secret"}

# Start CRM Backend
echo "📊 Starting CRM Backend..."
sudo docker stop platform-crm-backend 2>/dev/null || true
sudo docker run -d \
  --name platform-crm-backend \
  --network platform-network \
  --restart unless-stopped \
  -p 8003:8003 \
  -e PORT=8003 \
  -e GIN_MODE=release \
  -e DATABASE_URL="$DATABASE_URL" \
  -e AUTH_SERVICE_URL=http://platform-auth:8004 \
  $REGISTRY_URL/crm-backend:latest

# Start Auth Service
echo "🔐 Starting Auth Service..."
sudo docker stop platform-auth 2>/dev/null || true
sudo docker run -d \
  --name platform-auth \
  --network platform-network \
  --restart unless-stopped \
  -p 8004:8004 \
  -e PORT=8004 \
  -e GIN_MODE=release \
  -e DATABASE_URL="$DATABASE_URL" \
  -e JWT_SECRET="$JWT_SECRET" \
  $REGISTRY_URL/auth:latest

# Start Gateway
echo "🌐 Starting Gateway..."
sudo docker stop platform-gateway 2>/dev/null || true
sudo docker run -d \
  --name platform-gateway \
  --network platform-network \
  --restart unless-stopped \
  -p 8085:8085 \
  -e PORT=8085 \
  -e GIN_MODE=release \
  -e AUTH_SERVICE_URL=http://platform-auth:8004 \
  -e CRM_BACKEND_URL=http://platform-crm-backend:8003 \
  $REGISTRY_URL/gateway:latest

echo "✅ Backend services started!"
echo
echo "Service status:"
sudo docker ps --filter name=platform-