/* tslint:disable */
/* eslint-disable */
/**
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface Interaction
 */
export interface Interaction {
    /**
     * 
     * @type {string}
     * @memberof Interaction
     */
    id?: string;
    /**
     * 
     * @type {string}
     * @memberof Interaction
     */
    companyId?: string | null;
    /**
     * 
     * @type {string}
     * @memberof Interaction
     */
    contactId?: string | null;
    /**
     * 
     * @type {string}
     * @memberof Interaction
     */
    interactionType?: InteractionInteractionTypeEnum;
    /**
     * 
     * @type {string}
     * @memberof Interaction
     */
    notes?: string;
    /**
     * 
     * @type {Date}
     * @memberof Interaction
     */
    interactionDatetime?: Date;
    /**
     * 
     * @type {string}
     * @memberof Interaction
     */
    createdBy?: string;
    /**
     * 
     * @type {Date}
     * @memberof Interaction
     */
    createdAt?: Date;
    /**
     * 
     * @type {Date}
     * @memberof Interaction
     */
    updatedAt?: Date;
}

/**
* @export
* @enum {string}
*/
export enum InteractionInteractionTypeEnum {
    Email = 'email',
    Phone = 'phone',
    Meeting = 'meeting',
    Demo = 'demo',
    Proposal = 'proposal',
    FollowUp = 'follow-up',
    Other = 'other'
}


/**
 * Check if a given object implements the Interaction interface.
 */
export function instanceOfInteraction(value: object): value is Interaction {
    return true;
}

export function InteractionFromJSON(json: any): Interaction {
    return InteractionFromJSONTyped(json, false);
}

export function InteractionFromJSONTyped(json: any, ignoreDiscriminator: boolean): Interaction {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'] == null ? undefined : json['id'],
        'companyId': json['company_id'] == null ? undefined : json['company_id'],
        'contactId': json['contact_id'] == null ? undefined : json['contact_id'],
        'interactionType': json['interaction_type'] == null ? undefined : json['interaction_type'],
        'notes': json['notes'] == null ? undefined : json['notes'],
        'interactionDatetime': json['interaction_datetime'] == null ? undefined : (new Date(json['interaction_datetime'])),
        'createdBy': json['created_by'] == null ? undefined : json['created_by'],
        'createdAt': json['created_at'] == null ? undefined : (new Date(json['created_at'])),
        'updatedAt': json['updated_at'] == null ? undefined : (new Date(json['updated_at'])),
    };
}

  export function InteractionToJSON(json: any): Interaction {
      return InteractionToJSONTyped(json, false);
  }

  export function InteractionToJSONTyped(value?: Interaction | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'company_id': value['companyId'],
        'contact_id': value['contactId'],
        'interaction_type': value['interactionType'],
        'notes': value['notes'],
        'interaction_datetime': value['interactionDatetime'] == null ? undefined : ((value['interactionDatetime']).toISOString()),
        'created_by': value['createdBy'],
        'created_at': value['createdAt'] == null ? undefined : ((value['createdAt']).toISOString()),
        'updated_at': value['updatedAt'] == null ? undefined : ((value['updatedAt']).toISOString()),
    };
}

