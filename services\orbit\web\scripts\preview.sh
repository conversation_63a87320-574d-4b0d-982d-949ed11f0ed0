#!/bin/bash
# Preview production build

# When run via <PERSON>zel, we need to find the actual source directory
if [ -n "$BUILD_WORKSPACE_DIRECTORY" ]; then
    # Running via Bazel
    cd "$BUILD_WORKSPACE_DIRECTORY/services/orbit/web"
else
    # Running directly
    cd "$(dirname "$0")"
fi

# Run the network IP setup script
echo "Setting up preview environment with network IP configuration..."
./set-network-ip.sh

# Run preview
npm run preview