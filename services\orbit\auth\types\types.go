package types

import (
	"time"
)

// SignInRequest represents a sign-in request
type SignInRequest struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=6"`
}

// OAuthSignInRequest represents an OAuth sign-in request
type OAuthSignInRequest struct {
	Provider   string `json:"provider" binding:"required,oneof=google github"`
	RedirectTo string `json:"redirect_to,omitempty"`
}

// AuthResponse represents an authentication response
type AuthResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token,omitempty"`
	ExpiresIn    int32  `json:"expires_in"`
	TokenType    string `json:"token_type"`
	User         User   `json:"user"`
}

// User represents a user
type User struct {
	ID               string    `json:"id"`
	Email            string    `json:"email"`
	EmailConfirmedAt time.Time `json:"email_confirmed_at,omitempty"`
	CreatedAt        time.Time `json:"created_at"`
	UpdatedAt        time.Time `json:"updated_at"`
}

// Session represents a user session
type Session struct {
	ID        string    `json:"id"`
	UserID    string    `json:"user_id"`
	ExpiresAt time.Time `json:"expires_at"`
	CreatedAt time.Time `json:"created_at"`
}

// OAuthRedirectResponse represents an OAuth redirect response
type OAuthRedirectResponse struct {
	URL string `json:"url"`
}

// ValidateTokenRequest represents a token validation request
type ValidateTokenRequest struct {
	Token string `json:"token" binding:"required"`
}

// ValidateTokenResponse represents a token validation response
type ValidateTokenResponse struct {
	Valid  bool   `json:"valid"`
	UserID string `json:"user_id,omitempty"`
	Email  string `json:"email,omitempty"`
	Error  string `json:"error,omitempty"`
}