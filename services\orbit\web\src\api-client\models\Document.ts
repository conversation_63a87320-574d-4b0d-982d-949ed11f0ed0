/* tslint:disable */
/* eslint-disable */
/**
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface Document
 */
export interface Document {
    /**
     * 
     * @type {string}
     * @memberof Document
     */
    id?: string;
    /**
     * 
     * @type {string}
     * @memberof Document
     */
    content?: string;
    /**
     * 
     * @type {object}
     * @memberof Document
     */
    metadata?: object;
    /**
     * 
     * @type {Array<string>}
     * @memberof Document
     */
    filterTags?: Array<string>;
    /**
     * 
     * @type {string}
     * @memberof Document
     */
    sourceId?: string;
    /**
     * 
     * @type {Date}
     * @memberof Document
     */
    createdAt?: Date;
    /**
     * 
     * @type {Date}
     * @memberof Document
     */
    updatedAt?: Date;
}

/**
 * Check if a given object implements the Document interface.
 */
export function instanceOfDocument(value: object): value is Document {
    return true;
}

export function DocumentFromJSON(json: any): Document {
    return DocumentFromJSONTyped(json, false);
}

export function DocumentFromJSONTyped(json: any, ignoreDiscriminator: boolean): Document {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'] == null ? undefined : json['id'],
        'content': json['content'] == null ? undefined : json['content'],
        'metadata': json['metadata'] == null ? undefined : json['metadata'],
        'filterTags': json['filter_tags'] == null ? undefined : json['filter_tags'],
        'sourceId': json['source_id'] == null ? undefined : json['source_id'],
        'createdAt': json['created_at'] == null ? undefined : (new Date(json['created_at'])),
        'updatedAt': json['updated_at'] == null ? undefined : (new Date(json['updated_at'])),
    };
}

  export function DocumentToJSON(json: any): Document {
      return DocumentToJSONTyped(json, false);
  }

  export function DocumentToJSONTyped(value?: Document | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'content': value['content'],
        'metadata': value['metadata'],
        'filter_tags': value['filterTags'],
        'source_id': value['sourceId'],
        'created_at': value['createdAt'] == null ? undefined : ((value['createdAt']).toISOString()),
        'updated_at': value['updatedAt'] == null ? undefined : ((value['updatedAt']).toISOString()),
    };
}

