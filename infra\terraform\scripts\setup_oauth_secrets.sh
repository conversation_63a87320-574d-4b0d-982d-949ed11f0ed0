#!/bin/bash
set -euo pipefail

# Setup OAuth secrets in Google Secret Manager
# This script ensures OAuth secrets exist before deployment

PROJECT_ID="agent-dev-459718"
REGION="us-central1"

echo "🔐 Setting up OAuth secrets in Secret Manager..."

# Check if gcloud is authenticated
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q "@"; then
    echo "❌ Please authenticate with gcloud first: gcloud auth login"
    exit 1
fi

# Set the project
gcloud config set project "$PROJECT_ID"

# Enable Secret Manager API if not already enabled
echo "🔌 Enabling Secret Manager API..."
gcloud services enable secretmanager.googleapis.com

# Function to create secret if it doesn't exist
create_secret_if_missing() {
    local secret_name=$1
    local secret_value=$2
    
    if gcloud secrets describe "$secret_name" --project="$PROJECT_ID" >/dev/null 2>&1; then
        echo "✅ Secret '$secret_name' already exists"
        # Only update if we have real credentials (not placeholders)
        if [[ -n "${GOOGLE_OAUTH_CLIENT_ID:-}" && -n "${GOOGLE_OAUTH_CLIENT_SECRET:-}" ]]; then
            echo "$secret_value" | gcloud secrets versions add "$secret_name" --data-file=-
            echo "🔄 Updated secret '$secret_name'"
        else
            echo "⚠️  Skipping update of existing secret '$secret_name' (no credentials provided)"
        fi
    else
        echo "🆕 Creating secret '$secret_name'..."
        echo "$secret_value" | gcloud secrets create "$secret_name" --data-file=-
        echo "✅ Created secret '$secret_name'"
    fi
}

# Check if OAuth secrets are provided as environment variables
if [[ -n "${GOOGLE_OAUTH_CLIENT_ID:-}" && -n "${GOOGLE_OAUTH_CLIENT_SECRET:-}" ]]; then
    echo "📝 Using OAuth credentials from environment variables..."
    create_secret_if_missing "oauth-client-id" "$GOOGLE_OAUTH_CLIENT_ID"
    create_secret_if_missing "oauth-client-secret" "$GOOGLE_OAUTH_CLIENT_SECRET"
else
    echo "⚠️  No OAuth credentials provided in environment variables"
    echo "🔧 Creating placeholder secrets (you'll need to update these with real values)"
    
    # Check if secrets already exist before creating placeholders
    if ! gcloud secrets describe "oauth-client-id" --project="$PROJECT_ID" >/dev/null 2>&1; then
        echo "🆕 Creating placeholder OAuth client ID secret..."
        create_secret_if_missing "oauth-client-id" "your-oauth-client-id-from-google-console"
    else
        echo "✅ OAuth client ID secret already exists, skipping placeholder creation"
    fi
    
    if ! gcloud secrets describe "oauth-client-secret" --project="$PROJECT_ID" >/dev/null 2>&1; then
        echo "🆕 Creating placeholder OAuth client secret..."
        create_secret_if_missing "oauth-client-secret" "your-oauth-client-secret-from-google-console"
    else
        echo "✅ OAuth client secret already exists, skipping placeholder creation"
    fi
    
    echo ""
    echo "📋 Next steps:"
    echo "1. Go to Google Cloud Console → APIs & Services → Credentials"
    echo "2. Create OAuth 2.0 Client ID (Web application)"
    echo "3. Set authorized redirect URIs to: https://your-domain.com/auth/callback/google"
    echo "4. Update the secrets with real values:"
    echo "   echo 'real-client-id' | gcloud secrets versions add oauth-client-id --data-file=-"
    echo "   echo 'real-client-secret' | gcloud secrets versions add oauth-client-secret --data-file=-"
fi

# Create OAuth state secret
echo "🔑 Creating OAuth state secret..."
OAUTH_STATE_SECRET=$(openssl rand -hex 32)
create_secret_if_missing "oauth-state-secret" "$OAUTH_STATE_SECRET"

echo ""
echo "✅ OAuth secrets setup completed!"
echo "🔍 Verifying secrets exist:"
gcloud secrets list --filter="name:oauth" --format="table(name,createTime)" --project="$PROJECT_ID"