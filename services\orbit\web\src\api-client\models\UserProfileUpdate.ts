/* tslint:disable */
/* eslint-disable */
/**
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface UserProfileUpdate
 */
export interface UserProfileUpdate {
    /**
     * 
     * @type {string}
     * @memberof UserProfileUpdate
     */
    fullName?: string;
    /**
     * 
     * @type {string}
     * @memberof UserProfileUpdate
     */
    avatarUrl?: string;
    /**
     * 
     * @type {string}
     * @memberof UserProfileUpdate
     */
    timezone?: string;
}

/**
 * Check if a given object implements the UserProfileUpdate interface.
 */
export function instanceOfUserProfileUpdate(value: object): value is UserProfileUpdate {
    return true;
}

export function UserProfileUpdateFromJSON(json: any): UserProfileUpdate {
    return UserProfileUpdateFromJSONTyped(json, false);
}

export function UserProfileUpdateFromJSONTyped(json: any, ignoreDiscriminator: boolean): UserProfileUpdate {
    if (json == null) {
        return json;
    }
    return {
        
        'fullName': json['full_name'] == null ? undefined : json['full_name'],
        'avatarUrl': json['avatar_url'] == null ? undefined : json['avatar_url'],
        'timezone': json['timezone'] == null ? undefined : json['timezone'],
    };
}

  export function UserProfileUpdateToJSON(json: any): UserProfileUpdate {
      return UserProfileUpdateToJSONTyped(json, false);
  }

  export function UserProfileUpdateToJSONTyped(value?: UserProfileUpdate | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'full_name': value['fullName'],
        'avatar_url': value['avatarUrl'],
        'timezone': value['timezone'],
    };
}

