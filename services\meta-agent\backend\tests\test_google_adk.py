"""
Tests for Google ADK (AI Development Kit) Service
"""

import pytest
import json
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

from services.google_adk import GoogleADKService


@pytest.fixture
def adk_service():
    """Create Google ADK service for testing"""
    service = GoogleADKService()
    service.vertex_initialized = True
    service.genai_initialized = True
    return service


@pytest.fixture
def mock_model_configs():
    """Mock model configurations"""
    return {
        "gemini-pro": {
            "temperature": 0.7,
            "max_output_tokens": 2048,
            "top_p": 0.95,
            "top_k": 40
        },
        "code-bison": {
            "temperature": 0.2,
            "max_output_tokens": 2048,
            "candidate_count": 1
        }
    }


class TestGoogleADKService:
    """Test Google ADK service functionality"""
    
    @pytest.mark.asyncio
    async def test_initialization(self):
        """Test service initialization"""
        service = GoogleADKService()
        assert service.project_id is None  # Not set in test environment
        assert not service.vertex_initialized
        assert not service.genai_initialized
        
        # Test model configs exist
        assert "gemini-pro" in service.model_configs
        assert "code-bison" in service.model_configs
    
    @pytest.mark.asyncio
    @patch('src.services.google_adk.genai')
    async def test_generate_text_gemini(self, mock_genai, adk_service):
        """Test text generation with Gemini"""
        # Mock Gemini response
        mock_response = MagicMock()
        mock_response.text = "Generated text response"
        
        mock_model = MagicMock()
        mock_model.generate_content.return_value = mock_response
        mock_genai.GenerativeModel.return_value = mock_model
        
        # Test generation
        result = await adk_service.generate_text(
            prompt="Test prompt",
            model="gemini-pro"
        )
        
        assert result == "Generated text response"
        mock_genai.GenerativeModel.assert_called_with("gemini-pro")
        mock_model.generate_content.assert_called_once()
    
    @pytest.mark.asyncio
    @patch('src.services.google_adk.CodeGenerationModel')
    async def test_generate_code(self, mock_code_model, adk_service):
        """Test code generation"""
        # Mock code generation response
        mock_response = MagicMock()
        mock_response.text = "def test_function():\n    pass"
        
        mock_model_instance = MagicMock()
        mock_model_instance.predict.return_value = mock_response
        mock_code_model.from_pretrained.return_value = mock_model_instance
        
        # Test code generation
        result = await adk_service.generate_code(
            prompt="Create a test function",
            language="python",
            model="code-bison"
        )
        
        assert "def test_function" in result
        mock_code_model.from_pretrained.assert_called_with("code-bison")
        mock_model_instance.predict.assert_called_once()
    
    @pytest.mark.asyncio
    @patch('src.services.google_adk.genai')
    async def test_analyze_image(self, mock_genai, adk_service):
        """Test image analysis"""
        # Mock vision response
        mock_response = MagicMock()
        mock_response.text = "This image shows a test scene"
        
        mock_model = MagicMock()
        mock_model.generate_content.return_value = mock_response
        mock_genai.GenerativeModel.return_value = mock_model
        
        # Test image analysis
        result = await adk_service.analyze_image(
            image_data=b"fake_image_data",
            prompt="What's in this image?",
            model="gemini-pro-vision"
        )
        
        assert result == "This image shows a test scene"
        mock_genai.GenerativeModel.assert_called_with("gemini-pro-vision")
        mock_model.generate_content.assert_called_once()
    
    @pytest.mark.asyncio
    @patch('src.services.google_adk.TextEmbeddingModel')
    async def test_generate_embeddings(self, mock_embedding_model, adk_service):
        """Test embedding generation"""
        # Mock embedding response
        mock_embedding = MagicMock()
        mock_embedding.values = [0.1, 0.2, 0.3, 0.4, 0.5]
        
        mock_model_instance = MagicMock()
        mock_model_instance.get_embeddings.return_value = [mock_embedding, mock_embedding]
        mock_embedding_model.from_pretrained.return_value = mock_model_instance
        
        # Test embedding generation
        result = await adk_service.generate_embeddings(
            texts=["text1", "text2"],
            model="textembedding-gecko"
        )
        
        assert len(result) == 2
        assert result[0] == [0.1, 0.2, 0.3, 0.4, 0.5]
        mock_embedding_model.from_pretrained.assert_called_with("textembedding-gecko")
    
    @pytest.mark.asyncio
    @patch('src.services.google_adk.genai')
    async def test_chat_stream(self, mock_genai, adk_service):
        """Test streaming chat"""
        # Mock chat response chunks
        mock_chunk1 = MagicMock()
        mock_chunk1.text = "Hello"
        mock_chunk2 = MagicMock()
        mock_chunk2.text = " world!"
        
        mock_chat = MagicMock()
        mock_chat.send_message.return_value = [mock_chunk1, mock_chunk2]
        
        mock_model = MagicMock()
        mock_model.start_chat.return_value = mock_chat
        mock_genai.GenerativeModel.return_value = mock_model
        
        # Test chat streaming
        messages = [
            {"role": "user", "content": "Hello"}
        ]
        
        chunks = []
        async for chunk in adk_service.chat_stream(messages):
            chunks.append(chunk)
        
        assert chunks == ["Hello", " world!"]
        mock_model.start_chat.assert_called_once()
        mock_chat.send_message.assert_called_with(
            "Hello",
            generation_config=mock_genai.GenerationConfig.return_value,
            safety_settings=adk_service.safety_settings,
            stream=True
        )
    
    @pytest.mark.asyncio
    async def test_create_agent_with_adk(self, adk_service):
        """Test agent configuration creation"""
        # Test agent configuration
        result = await adk_service.create_agent_with_adk(
            agent_type="assistant",
            capabilities=["text_generation", "code_generation"],
            model_preferences={"text_generation": "gemini-pro"}
        )
        
        assert result["type"] == "assistant"
        assert "text_generation" in result["capabilities"]
        assert "code_generation" in result["capabilities"]
        assert result["models"]["text_generation"] == "gemini-pro"
        assert result["models"]["code_generation"] == "code-bison"
        assert result["features"]["streaming"] is True
        assert result["features"]["code_execution"] is True
    
    @pytest.mark.asyncio
    @patch('src.services.google_adk.get_vector_db')
    async def test_enhance_agent_with_knowledge(self, mock_get_vector_db, adk_service):
        """Test knowledge enhancement"""
        # Mock vector database
        mock_vector_db = AsyncMock()
        mock_get_vector_db.return_value = mock_vector_db
        
        # Mock embedding generation
        adk_service.generate_embeddings = AsyncMock(return_value=[[0.1, 0.2, 0.3]])
        
        # Test knowledge enhancement
        knowledge_sources = [
            {
                "content": "Test knowledge content",
                "metadata": {"source": "test"},
                "category": "documentation"
            }
        ]
        
        result = await adk_service.enhance_agent_with_knowledge(
            agent_id="test_agent",
            knowledge_sources=knowledge_sources
        )
        
        assert result is True
        mock_vector_db.store_agent_knowledge.assert_called_once()
        adk_service.generate_embeddings.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_analyze_agent_performance(self, adk_service):
        """Test agent performance analysis"""
        # Mock text generation for analysis
        analysis_text = """Performance Analysis:
        1. Strengths: Fast response time
        2. Weaknesses: Limited context understanding
        3. Recommendations: Increase context window
        4. Trends: Improving over time
        """
        
        adk_service.generate_text = AsyncMock(return_value=analysis_text)
        
        # Test performance analysis
        metrics = {
            "response_time": 0.5,
            "accuracy": 0.85,
            "usage_count": 100
        }
        
        result = await adk_service.analyze_agent_performance(
            agent_id="test_agent",
            metrics=metrics
        )
        
        assert result["agent_id"] == "test_agent"
        assert result["analysis"] == analysis_text
        assert "response_time" in result["metrics"]
        assert len(result["recommendations"]) > 0
        adk_service.generate_text.assert_called_once()
    
    def test_extract_recommendations(self, adk_service):
        """Test recommendation extraction"""
        analysis_text = """
        Performance Analysis Summary:
        
        Recommendations:
        1. Increase model temperature
        2. Add more training data
        3. Optimize inference pipeline
        """
        
        recommendations = adk_service._extract_recommendations(analysis_text)
        
        assert len(recommendations) > 0
        assert "Increase model temperature" in recommendations
        assert "Add more training data" in recommendations
    
    @pytest.mark.asyncio
    async def test_get_model_info(self, adk_service):
        """Test model information retrieval"""
        # Test known model
        result = await adk_service.get_model_info("gemini-pro")
        
        assert result["name"] == "Gemini Pro"
        assert "text_generation" in result["capabilities"]
        assert result["max_tokens"] == 32768
        assert result["supports_streaming"] is True
        
        # Test unknown model
        result = await adk_service.get_model_info("unknown-model")
        
        assert result["name"] == "unknown-model"
        assert "error" in result
    
    @pytest.mark.asyncio
    async def test_health_check(self, adk_service):
        """Test health check"""
        # Mock successful text generation
        adk_service.generate_text = AsyncMock(return_value="OK test response")
        
        result = await adk_service.health_check()
        
        assert result["status"] == "healthy"
        assert result["vertex_ai"]["initialized"] is True
        assert result["generative_ai"]["initialized"] is True
        assert len(result["available_models"]) > 0
        assert result["test_response"] is True
    
    @pytest.mark.asyncio
    async def test_health_check_failure(self, adk_service):
        """Test health check with failure"""
        # Mock failed initialization
        adk_service.vertex_initialized = False
        adk_service.genai_initialized = False
        
        result = await adk_service.health_check()
        
        assert result["vertex_ai"]["initialized"] is False
        assert result["generative_ai"]["initialized"] is False
        assert len(result["available_models"]) == 0
    
    @pytest.mark.asyncio
    async def test_agent_context_integration(self, adk_service):
        """Test agent context integration"""
        # Mock text generation
        adk_service.generate_text = AsyncMock(return_value="Context-aware response")
        
        agent_context = {
            "type": "assistant",
            "capabilities": ["text_generation", "reasoning"],
            "task": "help with documentation"
        }
        
        result = await adk_service.generate_text(
            prompt="Help me write documentation",
            agent_context=agent_context
        )
        
        # Verify context was passed
        call_args = adk_service.generate_text.call_args
        assert call_args is not None
        assert "Agent Context" in str(call_args)
    
    @pytest.mark.asyncio
    async def test_safety_settings_applied(self, adk_service):
        """Test that safety settings are properly applied"""
        # Verify safety settings exist
        assert len(adk_service.safety_settings) > 0
        
        # Check that all required harm categories are covered
        categories = [setting.category for setting in adk_service.safety_settings]
        
        from vertexai.generative_models import HarmCategory
        
        assert HarmCategory.HARM_CATEGORY_HATE_SPEECH in categories
        assert HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT in categories
        assert HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT in categories
        assert HarmCategory.HARM_CATEGORY_HARASSMENT in categories