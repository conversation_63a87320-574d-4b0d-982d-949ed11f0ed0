# Production Environment Variables

variable "project_id" {
  description = "GCP Project ID"
  type        = string
}

variable "project_name" {
  description = "Name of the project (used for resource naming)"
  type        = string
  default     = "platform"
}

variable "region" {
  description = "GCP region for resources"
  type        = string
  default     = "australia-southeast1"
}

variable "zone" {
  description = "GCP zone for resources"
  type        = string
  default     = "australia-southeast1-a"
}

# GitHub configuration for CI/CD
variable "github_repo_owner" {
  description = "GitHub repository owner (organization or username)"
  type        = string
}

variable "github_repo_name" {
  description = "GitHub repository name"
  type        = string
}

# SSL/TLS configuration
variable "ssl_domains" {
  description = "List of domains for SSL certificate"
  type        = list(string)
}

# Security configuration
variable "admin_ip_ranges" {
  description = "IP ranges allowed for administrative access (SSH, etc.)"
  type        = list(string)
}

# Development settings
variable "enable_debug_mode" {
  description = "Enable debug logging and additional endpoints"
  type        = bool
  default     = false
}

variable "enable_external_access" {
  description = "Enable external access to services"
  type        = bool
  default     = false
}

# Resource scaling
variable "min_instance_count" {
  description = "Minimum number of instances"
  type        = number
  default     = 2
}

variable "max_instance_count" {
  description = "Maximum number of instances"
  type        = number
  default     = 10
}

# Feature flags
variable "enable_migration_automation" {
  description = "Enable automated database migrations"
  type        = bool
  default     = false
}

variable "enable_monitoring" {
  description = "Enable monitoring and alerting"
  type        = bool
  default     = true
}

variable "enable_backup_automation" {
  description = "Enable automated backups"
  type        = bool
  default     = true
}

variable "schedule_shutdown" {
  description = "Enable scheduled shutdown for cost savings"
  type        = bool
  default     = false
}

variable "auto_scaling_enabled" {
  description = "Enable auto-scaling"
  type        = bool
  default     = true
}

# Cloud Armor security settings
variable "rate_limit_requests_per_minute" {
  description = "Rate limit requests per minute (0 to disable)"
  type        = number
  default     = 1000
}

variable "blocked_regions" {
  description = "List of region codes to block"
  type        = list(string)
  default     = []
}

variable "blocked_ip_ranges" {
  description = "List of IP ranges to block"
  type        = list(string)
  default     = []
}

# Database migration settings
variable "enable_scheduled_migrations" {
  description = "Enable scheduled automated migrations"
  type        = bool
  default     = false
}

variable "migration_schedule" {
  description = "Cron schedule for automated migrations"
  type        = string
  default     = "0 2 * * 0"  # Sundays at 2 AM
}

variable "migration_timezone" {
  description = "Timezone for scheduled migrations"
  type        = string
  default     = "UTC"
}

# Notification settings
variable "slack_webhook_url" {
  description = "Slack webhook URL for notifications"
  type        = string
  default     = ""
  sensitive   = true
}

variable "notification_email" {
  description = "Email address for notifications"
  type        = string
  default     = ""
}

# High availability settings
variable "enable_multi_region" {
  description = "Enable multi-region deployment for high availability"
  type        = bool
  default     = false
}

variable "backup_regions" {
  description = "List of backup regions for disaster recovery"
  type        = list(string)
  default     = []
}

# Monitoring and alerting
variable "enable_advanced_monitoring" {
  description = "Enable advanced monitoring and alerting"
  type        = bool
  default     = true
}

variable "alert_email_addresses" {
  description = "List of email addresses for alerts"
  type        = list(string)
  default     = []
}

# Compliance and audit
variable "enable_audit_logging" {
  description = "Enable audit logging for compliance"
  type        = bool
  default     = true
}

variable "data_retention_days" {
  description = "Number of days to retain logs and data"
  type        = number
  default     = 365
}

# Cost optimization
variable "enable_committed_use_discounts" {
  description = "Enable committed use discounts for cost optimization"
  type        = bool
  default     = false
}

variable "enable_preemptible_instances" {
  description = "Enable preemptible instances for non-critical workloads"
  type        = bool
  default     = false
}