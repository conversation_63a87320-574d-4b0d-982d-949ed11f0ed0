/**
 * AI Agent Platform - API Types
 */

// Base types
export interface BaseResponse {
  id: string;
  created_at: string;
  updated_at: string;
}

// User types
export interface User {
  id: string;
  username: string;
  email: string;
  full_name?: string;
  bio?: string;
  is_active: boolean;
  is_superuser: boolean;
  created_at: string;
  last_login?: string;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  full_name?: string;
}

export interface TokenResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
}

// Agent types
export enum AgentStatus {
  CREATED = 'created',
  STARTING = 'starting',
  RUNNING = 'running',
  PAUSED = 'paused',
  STOPPED = 'stopped',
  ERROR = 'error',
  TERMINATED = 'terminated'
}

export interface Agent extends BaseResponse {
  name: string;
  description?: string;
  type: string;
  status: AgentStatus;
  version: string;
  owner_id: string;
  config: Record<string, any>;
  capabilities: string[];
  constraints: Record<string, any>;
  last_heartbeat?: string;
  cpu_usage?: number;
  memory_usage?: number;
}

export interface CreateAgentRequest {
  name: string;
  description?: string;
  type?: string;
  config?: Record<string, any>;
  capabilities?: string[];
}

export interface UpdateAgentRequest {
  name?: string;
  description?: string;
  config?: Record<string, any>;
  capabilities?: string[];
  constraints?: Record<string, any>;
  requirements?: string;
  user_prompt?: string;
}

// Task types
export enum TaskStatus {
  PENDING = 'pending',
  ASSIGNED = 'assigned',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

export interface Task extends BaseResponse {
  title: string;
  description?: string;
  type: string;
  status: TaskStatus;
  priority: number;
  input_data?: Record<string, any>;
  output_data?: Record<string, any>;
  metadata: Record<string, any>;
  progress_percentage: number;
  steps_completed: number;
  total_steps?: number;
  assigned_agent_id?: string;
  orchestration_id?: string;
  parent_task_id?: string;
  started_at?: string;
  completed_at?: string;
  deadline?: string;
}

// Orchestration types
export enum OrchestrationPattern {
  SEQUENTIAL = 'sequential',
  PARALLEL = 'parallel',
  HIERARCHICAL = 'hierarchical',
  PEER_TO_PEER = 'peer_to_peer',
  EVENT_DRIVEN = 'event_driven'
}

export interface Orchestration extends BaseResponse {
  name: string;
  description?: string;
  pattern: OrchestrationPattern;
  status: string;
  progress_percentage: number;
  owner_id: string;
  config: Record<string, any>;
  execution_plan: Record<string, any>;
  started_at?: string;
  completed_at?: string;
  members: OrchestrationMember[];
  agent_ids: string[];
}

export interface OrchestrationMember {
  agent_id: string;
  role: string;
  join_order: number;
}

export interface CreateOrchestrationRequest {
  name: string;
  description?: string;
  pattern: OrchestrationPattern;
  config?: Record<string, any>;
  execution_plan?: Record<string, any>;
  agent_ids: string[];
}

// Runtime types
export interface RuntimeInfo {
  agent_id: string;
  status: string;
  process_id?: number;
  start_time?: string;
  last_heartbeat?: string;
  cpu_usage: number;
  memory_usage: number;
  current_task?: Record<string, any>;
  queue_size: number;
  task_history_count: number;
  capabilities: string[];
  constraints: Record<string, any>;
}

export interface SystemStats {
  total_agents: number;
  active_agents: number;
  max_concurrent_agents: number;
  status_breakdown: Record<string, number>;
  cpu_usage: number;
  memory_usage: number;
  uptime: string;
  resource_usage: {
    total_cpu_usage: number;
    total_memory_mb: number;
    average_cpu_per_agent: number;
    average_memory_per_agent: number;
  };
  task_statistics: {
    queued_tasks: number;
    completed_tasks: number;
  };
}

// API Response types
export interface ListResponse<T> {
  items: T[];
  total: number;
  limit: number;
  offset: number;
}

export interface SuccessResponse {
  success: boolean;
  message: string;
}

export interface ErrorResponse {
  error: string;
  detail?: string;
  code?: string;
}