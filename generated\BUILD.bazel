load("@rules_go//go:def.bzl", "go_library")

package(default_visibility = ["//visibility:public"])

# All generated API clients
filegroup(
    name = "all_clients",
    srcs = [
        ":go_client",
        ":typescript_client",
        ":python_client",
        ":java_client",
    ],
)

# Generated Go server and client code
filegroup(
    name = "go_client",
    srcs = glob(
        ["go/**/*.go"],
        allow_empty = True,
    ),
)

# Generated TypeScript client code
filegroup(
    name = "typescript_client",
    srcs = glob(
        ["typescript/**/*.ts"],
        allow_empty = True,
    ),
)

# Generated Python client code
filegroup(
    name = "python_client",
    srcs = glob(
        ["python/**/*.py"],
        allow_empty = True,
    ),
)

# Generated Java client code
filegroup(
    name = "java_client",
    srcs = glob(
        ["java/**/*.java"],
        allow_empty = True,
    ),
)

# Go library for generated server code
go_library(
    name = "openapi_server",
    srcs = glob(
        ["go/server/*.go"],
        allow_empty = True,
    ),
    importpath = "github.com/TwoDotAi/mono/generated/go/server",
    deps = [
        "@com_github_gin_gonic_gin//:gin",
        "@com_github_oapi_codegen_runtime//:runtime",
        "@com_github_oapi_codegen_runtime//types",
    ],
)

# Go library for generated client code
go_library(
    name = "openapi_client_go",
    srcs = glob(
        ["go/client/*.go"],
        allow_empty = True,
    ),
    importpath = "github.com/TwoDotAi/mono/generated/go/client",
    deps = [
        "@com_github_oapi_codegen_runtime//:runtime",
        "@com_github_oapi_codegen_runtime//types",
    ],
)