package config

import (
	"log"
	"os"
)

type Config struct {
	JWTSecret                string
	DatabaseURL              string
	Environment              string
	GoogleOAuthClientID      string
	GoogleOAuthClientSecret  string
	GoogleOAuthRedirectURL   string
	OAuthStateSecret         string
}

func Load() *Config {
	cfg := &Config{
		JWTSecret:                getEnv("JWT_SECRET", "your-secret-key-change-in-production"),
		DatabaseURL:              getEnv("DATABASE_URL", "postgres://postgres:postgres@localhost:5432/appdb?sslmode=disable"),
		Environment:              getEnv("ENVIRONMENT", "development"),
		GoogleOAuthClientID:      getEnv("GOOGLE_OAUTH_CLIENT_ID", ""),
		GoogleOAuthClientSecret:  getEnv("GOOGLE_OAUTH_CLIENT_SECRET", ""),
		GoogleOAuthRedirectURL:   getEnv("GOOGLE_OAUTH_REDIRECT_URL", "http://localhost:8080/auth/callback/google"),
		OAuthStateSecret:         getEnv("OAUTH_STATE_SECRET", "change-in-production"),
	}

	if cfg.Environment == "production" {
		if cfg.JWTSecret == "your-secret-key-change-in-production" {
			log.Fatal("JWT_SECRET must be set in production")
		}
		if cfg.GoogleOAuthClientID == "" {
			log.Fatal("GOOGLE_OAUTH_CLIENT_ID must be set in production")
		}
		if cfg.GoogleOAuthClientSecret == "" {
			log.Fatal("GOOGLE_OAUTH_CLIENT_SECRET must be set in production")
		}
		if cfg.OAuthStateSecret == "change-in-production" {
			log.Fatal("OAUTH_STATE_SECRET must be set in production")
		}
	}

	return cfg
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}