// AI Agent Platform API Client
// Fully typed client with React Query integration

export { ApiClient, ApiError } from './client';
export * from './types';

// Default client instance
import { ApiClient } from './client';

let defaultClient: ApiClient | null = null;

export function createClient(config?: any): ApiClient {
  return new ApiClient(config);
}

export function getDefaultClient(): ApiClient {
  if (!defaultClient) {
    defaultClient = new ApiClient();
  }
  return defaultClient;
}

export function setDefaultClient(client: ApiClient): void {
  defaultClient = client;
}

// Convenience methods using default client
export const api = {
  get client() {
    return getDefaultClient();
  },
  
  configure(config: any) {
    defaultClient = new ApiClient(config);
  },
  
  // Agent methods
  agents: {
    list: (params?: Parameters<ApiClient['getAgents']>[0]) => 
      getDefaultClient().getAgents(params),
    
    get: (id: string) => 
      getDefaultClient().getAgentById(id),
    
    create: (data: Parameters<ApiClient['createAgent']>[0]) => 
      getDefaultClient().createAgent(data),
    
    update: (id: string, data: Parameters<ApiClient['updateAgent']>[1]) => 
      getDefaultClient().updateAgent(id, data),
    
    delete: (id: string) => 
      getDefaultClient().deleteAgent(id),
    
    start: (id: string) => 
      getDefaultClient().startAgent(id),
    
    stop: (id: string) => 
      getDefaultClient().stopAgent(id),
  },
  
  // Task methods
  tasks: {
    list: (params?: Parameters<ApiClient['getTasks']>[0]) => 
      getDefaultClient().getTasks(params),
    
    get: (id: string) => 
      getDefaultClient().getTaskById(id),
  },
  
  // Migration methods
  migration: {
    analyze: (data: Parameters<ApiClient['analyzeMigration']>[0]) => 
      getDefaultClient().analyzeMigration(data),
    
    getAnalysis: (id: string) => 
      getDefaultClient().getMigrationAnalysis(id),
    
    createPlan: (data: Parameters<ApiClient['createMigrationPlan']>[0]) => 
      getDefaultClient().createMigrationPlan(data),
    
    getPlan: (id: string) => 
      getDefaultClient().getMigrationPlan(id),
    
    start: (data: Parameters<ApiClient['startMigration']>[0]) => 
      getDefaultClient().startMigration(data),
    
    getStatus: (id: string) => 
      getDefaultClient().getMigrationStatus(id),
    
    listAnalyses: () => 
      getDefaultClient().listMigrationAnalyses(),
    
    listPlans: () => 
      getDefaultClient().listMigrationPlans(),
    
    listProjects: () => 
      getDefaultClient().listMigrationProjects(),
    
    getSummary: () => 
      getDefaultClient().getMigrationSummary(),
  },
  
  // System methods
  system: {
    getStats: () => 
      getDefaultClient().getSystemStats(),
    
    getHealth: () => 
      getDefaultClient().getSystemHealth(),
  },
};