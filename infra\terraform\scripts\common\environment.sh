#!/bin/bash
# Environment detection and validation functions

# Source utilities
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/utils.sh"

# Supported environments
SUPPORTED_ENVIRONMENTS=("dev" "staging" "prod")

# Environment-specific configurations
declare -A ENVIRONMENT_CONFIG

# Dev environment configuration
ENVIRONMENT_CONFIG[dev_project_id]="agent-dev-459718"
ENVIRONMENT_CONFIG[dev_region]="australia-southeast1"
ENVIRONMENT_CONFIG[dev_zone]="australia-southeast1-a"
ENVIRONMENT_CONFIG[dev_machine_type]="e2-standard-2"
ENVIRONMENT_CONFIG[dev_db_tier]="db-f1-micro"
ENVIRONMENT_CONFIG[dev_ssl_domains]="internal.dev.twodot.ai,api.dev.twodot.ai"
ENVIRONMENT_CONFIG[dev_backup_retention]="7"
ENVIRONMENT_CONFIG[dev_enable_deletion_protection]="false"
ENVIRONMENT_CONFIG[dev_availability_type]="ZONAL"

# Staging environment configuration
ENVIRONMENT_CONFIG[staging_project_id]="agent-staging-459718"
ENVIRONMENT_CONFIG[staging_region]="australia-southeast1"
ENVIRONMENT_CONFIG[staging_zone]="australia-southeast1-a"
ENVIRONMENT_CONFIG[staging_machine_type]="e2-standard-2"
ENVIRONMENT_CONFIG[staging_db_tier]="db-standard-1"
ENVIRONMENT_CONFIG[staging_ssl_domains]="internal.stg.twodot.ai,api.stg.twodot.ai"
ENVIRONMENT_CONFIG[staging_backup_retention]="14"
ENVIRONMENT_CONFIG[staging_enable_deletion_protection]="true"
ENVIRONMENT_CONFIG[staging_availability_type]="ZONAL"

# Prod environment configuration
ENVIRONMENT_CONFIG[prod_project_id]="twodot-agent-prod"
ENVIRONMENT_CONFIG[prod_region]="australia-southeast1"
ENVIRONMENT_CONFIG[prod_zone]="australia-southeast1-a"
ENVIRONMENT_CONFIG[prod_machine_type]="e2-standard-4"
ENVIRONMENT_CONFIG[prod_db_tier]="db-standard-2"
ENVIRONMENT_CONFIG[prod_ssl_domains]="internal.twodot.ai,api.twodot.ai"
ENVIRONMENT_CONFIG[prod_backup_retention]="30"
ENVIRONMENT_CONFIG[prod_enable_deletion_protection]="true"
ENVIRONMENT_CONFIG[prod_availability_type]="REGIONAL"

# Validate environment argument
validate_environment() {
    local env="$1"
    
    if [[ -z "$env" ]]; then
        print_error "Environment is required"
        print_info "Supported environments: ${SUPPORTED_ENVIRONMENTS[*]}"
        return 1
    fi
    
    local found=false
    for supported_env in "${SUPPORTED_ENVIRONMENTS[@]}"; do
        if [[ "$env" == "$supported_env" ]]; then
            found=true
            break
        fi
    done
    
    if [[ "$found" == "false" ]]; then
        print_error "Unsupported environment: $env"
        print_info "Supported environments: ${SUPPORTED_ENVIRONMENTS[*]}"
        return 1
    fi
    
    return 0
}

# Get environment configuration value
get_env_config() {
    local env="$1"
    local key="$2"
    local config_key="${env}_${key}"
    
    if [[ -v ENVIRONMENT_CONFIG[$config_key] ]]; then
        echo "${ENVIRONMENT_CONFIG[$config_key]}"
    else
        print_error "Configuration key '$key' not found for environment '$env'"
        return 1
    fi
}

# Set environment variables for the given environment
set_environment_variables() {
    local env="$1"
    
    if ! validate_environment "$env"; then
        return 1
    fi
    
    # Set common environment variables
    export ENVIRONMENT="$env"
    export PROJECT_ID=$(get_env_config "$env" "project_id")
    export REGION=$(get_env_config "$env" "region")
    export ZONE=$(get_env_config "$env" "zone")
    export MACHINE_TYPE=$(get_env_config "$env" "machine_type")
    export DB_TIER=$(get_env_config "$env" "db_tier")
    export SSL_DOMAINS=$(get_env_config "$env" "ssl_domains")
    export BACKUP_RETENTION=$(get_env_config "$env" "backup_retention")
    export ENABLE_DELETION_PROTECTION=$(get_env_config "$env" "enable_deletion_protection")
    export AVAILABILITY_TYPE=$(get_env_config "$env" "availability_type")
    
    # Set environment-specific paths
    export TERRAFORM_DIR="terraform/environments/$env"
    export TERRAFORM_STATE_BUCKET="${PROJECT_ID}-terraform-state"
    export REGISTRY_URL="$REGION-docker.pkg.dev/$PROJECT_ID/platform"
    
    # Set service ports (consistent across environments)
    export AUTH_SERVICE_PORT="8004"
    export CRM_SERVICE_PORT="8003"
    export GATEWAY_PORT="8085"
    export NGINX_PORT="80"
    
    # Set database configuration
    export DB_NAME="platform_${env}_db"
    export DB_USER="platform_${env}_user"
    export DB_PASSWORD_SECRET="platform-db-password"
    
    # Set instance naming
    export INSTANCE_NAME="platform-platform-$env"
    
    # Environment-specific settings
    case "$env" in
        "dev")
            export ENABLE_SSL="true"
            export ENABLE_CLOUD_ARMOR="false"
            export ENABLE_PUBLIC_ACCESS="true"
            export ASSIGN_EXTERNAL_IP="true"
            export ENABLE_ADVANCED_MONITORING="false"
            export ENABLE_AUDIT_LOGGING="false"
            ;;
        "staging")
            export ENABLE_SSL="true"
            export ENABLE_CLOUD_ARMOR="false"
            export ENABLE_PUBLIC_ACCESS="true"
            export ASSIGN_EXTERNAL_IP="false"
            export ENABLE_ADVANCED_MONITORING="true"
            export ENABLE_AUDIT_LOGGING="true"
            ;;
        "prod")
            export ENABLE_SSL="true"
            export ENABLE_CLOUD_ARMOR="true"
            export ENABLE_PUBLIC_ACCESS="true"
            export ASSIGN_EXTERNAL_IP="false"
            export ENABLE_ADVANCED_MONITORING="true"
            export ENABLE_AUDIT_LOGGING="true"
            ;;
    esac
    
    print_info "Environment variables set for: $env"
    if [[ "$VERBOSE" == "true" ]]; then
        print_info "Project ID: $PROJECT_ID"
        print_info "Region: $REGION"
        print_info "Zone: $ZONE"
        print_info "Registry URL: $REGISTRY_URL"
        print_info "Terraform Directory: $TERRAFORM_DIR"
    fi
}

# Check if required GCP APIs are enabled
check_gcp_apis() {
    local project_id="$1"
    local required_apis=(
        "compute.googleapis.com"
        "sql-component.googleapis.com"
        "artifactregistry.googleapis.com"
        "storage.googleapis.com"
        "cloudbuild.googleapis.com"
        "secretmanager.googleapis.com"
        "cloudresourcemanager.googleapis.com"
        "iam.googleapis.com"
    )
    
    print_info "Checking required GCP APIs for project: $project_id"
    
    local missing_apis=()
    for api in "${required_apis[@]}"; do
        if ! gcloud services list --enabled --project="$project_id" --format="value(name)" | grep -q "^$api$"; then
            missing_apis+=("$api")
        fi
    done
    
    if [[ ${#missing_apis[@]} -gt 0 ]]; then
        print_error "Missing required APIs: ${missing_apis[*]}"
        print_info "Enable them with: gcloud services enable ${missing_apis[*]} --project=$project_id"
        return 1
    fi
    
    print_success "All required APIs are enabled"
    return 0
}

# Check if terraform directory exists and is valid
check_terraform_directory() {
    local env="$1"
    local terraform_dir="terraform/environments/$env"
    
    if [[ ! -d "$terraform_dir" ]]; then
        print_error "Terraform directory not found: $terraform_dir"
        return 1
    fi
    
    if [[ ! -f "$terraform_dir/main.tf" ]]; then
        print_error "main.tf not found in: $terraform_dir"
        return 1
    fi
    
    if [[ ! -f "$terraform_dir/variables.tf" ]]; then
        print_error "variables.tf not found in: $terraform_dir"
        return 1
    fi
    
    if [[ ! -f "$terraform_dir/terraform.tfvars" ]]; then
        print_warning "terraform.tfvars not found in: $terraform_dir"
        print_info "You may need to create it from terraform.tfvars.example"
    fi
    
    print_success "Terraform directory is valid: $terraform_dir"
    return 0
}

# Check GCP authentication
check_gcp_auth() {
    local project_id="$1"
    
    print_info "Checking GCP authentication for project: $project_id"
    
    # Check if gcloud is authenticated
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n1 > /dev/null; then
        print_error "No active GCP authentication found"
        print_info "Run: gcloud auth login"
        return 1
    fi
    
    # Check if we can access the project
    if ! gcloud projects describe "$project_id" > /dev/null 2>&1; then
        print_error "Cannot access project: $project_id"
        print_info "Check project ID and permissions"
        return 1
    fi
    
    # Set the project as default
    gcloud config set project "$project_id"
    
    print_success "GCP authentication is valid"
    return 0
}

# Check if terraform state bucket exists
check_terraform_state_bucket() {
    local project_id="$1"
    local bucket_name="${project_id}-terraform-state"
    
    print_info "Checking Terraform state bucket: $bucket_name"
    
    if ! gsutil ls -b "gs://$bucket_name" > /dev/null 2>&1; then
        print_warning "Terraform state bucket does not exist: $bucket_name"
        print_info "Creating Terraform state bucket..."
        
        # Create the bucket
        if ! gsutil mb -p "$project_id" -l "$REGION" "gs://$bucket_name"; then
            print_error "Failed to create Terraform state bucket"
            return 1
        fi
        
        # Enable versioning
        if ! gsutil versioning set on "gs://$bucket_name"; then
            print_error "Failed to enable versioning on Terraform state bucket"
            return 1
        fi
        
        print_success "Terraform state bucket created: $bucket_name"
    else
        print_success "Terraform state bucket exists: $bucket_name"
    fi
    
    return 0
}

# Complete environment setup and validation
setup_environment() {
    local env="$1"
    
    print_step "Setting up environment: $env"
    
    # Validate environment
    if ! validate_environment "$env"; then
        return 1
    fi
    
    # Set environment variables
    if ! set_environment_variables "$env"; then
        return 1
    fi
    
    # Check terraform directory
    if ! check_terraform_directory "$env"; then
        return 1
    fi
    
    # Check GCP authentication
    if ! check_gcp_auth "$PROJECT_ID"; then
        return 1
    fi
    
    # Check required APIs
    if ! check_gcp_apis "$PROJECT_ID"; then
        return 1
    fi
    
    # Check/create terraform state bucket
    if ! check_terraform_state_bucket "$PROJECT_ID"; then
        return 1
    fi
    
    print_success "Environment setup completed for: $env"
    return 0
}

# Print environment summary
print_environment_summary() {
    local env="$1"
    
    echo ""
    print_info "Environment Summary:"
    echo "  🌍 Environment: $env"
    echo "  🏗️  Project ID: $PROJECT_ID"
    echo "  📍 Region: $REGION"
    echo "  📍 Zone: $ZONE"
    echo "  🖥️  Machine Type: $MACHINE_TYPE"
    echo "  💾 Database Tier: $DB_TIER"
    echo "  🔒 SSL Domains: $SSL_DOMAINS"
    echo "  📦 Registry: $REGISTRY_URL"
    echo "  📂 Terraform Dir: $TERRAFORM_DIR"
    echo "  🗄️  State Bucket: $TERRAFORM_STATE_BUCKET"
    echo ""
}

# Check if environment is production
is_production() {
    [[ "$ENVIRONMENT" == "prod" ]]
}

# Check if environment is development
is_development() {
    [[ "$ENVIRONMENT" == "dev" ]]
}

# Check if environment is staging
is_staging() {
    [[ "$ENVIRONMENT" == "staging" ]]
}

# Production safety check
production_safety_check() {
    if is_production; then
        print_warning "⚠️  PRODUCTION ENVIRONMENT DETECTED ⚠️"
        echo ""
        print_info "You are about to perform operations on the PRODUCTION environment."
        print_info "This will affect live services and real data."
        echo ""
        
        if [[ "$FORCE" != "true" ]]; then
            if ! confirm "Are you sure you want to continue?" "n"; then
                print_info "Operation cancelled"
                exit 0
            fi
        fi
        
        print_info "Proceeding with production deployment..."
    fi
}