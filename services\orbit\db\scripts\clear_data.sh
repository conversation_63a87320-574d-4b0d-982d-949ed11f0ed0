#!/bin/bash
set -e

echo "Clearing database data (preserving schema)..."

# Check if postgres container is running
if ! docker ps | grep -q platform_postgres; then
    echo "ERROR: PostgreSQL container is not running!"
    echo "Please run 'bazel run //db:start' first."
    exit 1
fi

# Clear all data from tables in reverse order to respect foreign keys
cat << 'EOF' | docker exec -i platform_postgres psql -U postgres -d appdb
-- Clear all data from tables (in order that respects foreign key constraints)
-- NOTE: We preserve reference data tables (deal_stages, company_statuses, interaction_types)
-- since they contain default data inserted by migrations
TRUNCATE TABLE deals CASCADE;
TRUNCATE TABLE contacts CASCADE;
TRUNCATE TABLE companies CASCADE;
TRUNCATE TABLE user_profiles CASCADE;
TRUNCATE TABLE users CASCADE;
-- Do NOT truncate: deal_stages, company_statuses, interaction_types (reference data)

-- Reset sequences if any exist
DO $$
DECLARE
    seq_record RECORD;
BEGIN
    FOR seq_record IN 
        SELECT sequence_name FROM information_schema.sequences 
        WHERE sequence_schema = 'public'
    LOOP
        EXECUTE 'ALTER SEQUENCE ' || seq_record.sequence_name || ' RESTART WITH 1';
    END LOOP;
END $$;

SELECT 'Database data cleared successfully' as status;
EOF

echo ""
echo "✅ Database data cleared successfully!"
echo "💡 The schema is preserved and ready for new data."
echo "🔄 You can now run 'bazel run //db:test_data' to add fresh test data."