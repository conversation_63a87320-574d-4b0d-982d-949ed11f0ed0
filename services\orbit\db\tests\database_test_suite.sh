#!/bin/bash

# Complete database test suite for <PERSON>zel
# This script runs all database tests in the proper order

set -e

echo "🧪 Running complete database test suite..."

# Configuration
DB_CONTAINER_NAME="crm_test_db"
DB_NAME="crm_db_test" 
DB_USER="postgres"
DB_PASSWORD="testpassword"
DB_PORT="5433"

# Check if test database container is running
if ! docker ps | grep -q $DB_CONTAINER_NAME; then
    echo "❌ Test database container not running. Please run: bazel run //db:setup_test_db"
    exit 1
fi

echo ""
echo "📋 Running SQL integration tests..."
# Find the directory containing this script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Run SQL integration tests
docker exec -i $DB_CONTAINER_NAME psql -U $DB_USER -d $DB_NAME < "$SCRIPT_DIR/integration_test.sql"

echo ""
echo "✅ Database test suite completed successfully!"