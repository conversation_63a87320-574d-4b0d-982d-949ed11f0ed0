---
# Development environment group variables
environment: dev
deployment_environment: dev

# GCP Configuration
gcp_project_id: "{{ lookup('env', 'GCP_PROJECT_ID') | default('crm-platform-dev') }}"
gcp_region: us-central1
gcp_zone: us-central1-a

# Artifact Registry
artifact_registry: microservices
artifact_registry_location: us-central1

# Database Configuration
database_host: "{{ lookup('env', 'DB_HOST') | default('*********') }}"
database_port: 5432
database_name: crm_dev
database_connection_string: "postgresql://{{ database_user }}:{{ database_password }}@{{ database_host }}:{{ database_port }}/{{ database_name }}"

# Service URLs (internal communication)
auth_service_url: http://auth:8004
crm_backend_url: http://crm_backend:8003
gateway_url: http://gateway:8085

# Logging Configuration
log_level: debug
log_format: json

# Health Check Configuration
health_check_timeout: 30s
health_check_interval: 10s
health_check_retries: 3

# Docker Configuration
docker_network: microservices
docker_restart_policy: unless-stopped
docker_log_driver: json-file
docker_log_options:
  max-size: "10m"
  max-file: "3"

# Nginx Configuration
nginx_worker_processes: auto
nginx_worker_connections: 1024
nginx_keepalive_timeout: 65
nginx_client_max_body_size: 10m

# Resource Limits (Development)
resource_limits:
  gateway:
    memory: "512m"
    cpu: "0.5"
  auth:
    memory: "512m"
    cpu: "0.5"
  crm_backend:
    memory: "1g"
    cpu: "1"
  nginx:
    memory: "256m"
    cpu: "0.25"