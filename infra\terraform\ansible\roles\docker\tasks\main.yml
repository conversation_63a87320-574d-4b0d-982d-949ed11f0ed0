---
- name: Install Docker prerequisites
  apt:
    name:
      - apt-transport-https
      - ca-certificates
      - curl
      - gnupg
      - lsb-release
      - python3-pip
    state: present
    update_cache: yes
  tags: ['docker', 'prerequisites']

- name: Add Docker GPG key
  apt_key:
    url: https://download.docker.com/linux/ubuntu/gpg
    state: present
  tags: ['docker']

- name: Add Docker repository
  apt_repository:
    repo: "deb [arch=amd64] https://download.docker.com/linux/ubuntu {{ ansible_distribution_release }} stable"
    state: present
  tags: ['docker']

- name: Install Docker
  apt:
    name:
      - docker-ce
      - docker-ce-cli
      - containerd.io
    state: present
    update_cache: yes
  tags: ['docker']

- name: Install Docker Compose
  get_url:
    url: "https://github.com/docker/compose/releases/download/v{{ docker_compose_version }}/docker-compose-linux-x86_64"
    dest: /usr/local/bin/docker-compose
    mode: '0755'
  tags: ['docker', 'compose']

- name: Install Python Docker libraries
  pip:
    name:
      - docker
      - docker-compose
    state: present
  tags: ['docker', 'python']

- name: Create docker group
  group:
    name: docker
    state: present
  tags: ['docker']

- name: Add ansible user to docker group
  user:
    name: "{{ ansible_user }}"
    groups: docker
    append: yes
  tags: ['docker']

- name: Create Docker directories
  file:
    path: "{{ item }}"
    state: directory
    mode: '0755'
  loop:
    - /etc/docker
    - "{{ microservices_root }}"
    - "{{ docker_data_root }}/volumes"
    - "{{ backup_root }}"
  tags: ['docker', 'directories']

- name: Configure Docker daemon
  template:
    src: daemon.json.j2
    dest: /etc/docker/daemon.json
    mode: '0644'
  notify: restart docker
  tags: ['docker', 'config']

- name: Start and enable Docker
  systemd:
    name: docker
    state: started
    enabled: yes
    daemon_reload: yes
  tags: ['docker', 'service']

- name: Configure Docker log rotation
  template:
    src: docker-logrotate.j2
    dest: /etc/logrotate.d/docker
    mode: '0644'
  tags: ['docker', 'logging']

- name: Set up Docker prune cron job
  cron:
    name: "Docker system prune"
    minute: "0"
    hour: "2"
    day: "*"
    month: "*"
    weekday: "0"
    job: "docker system prune -af --volumes > /var/log/docker-prune.log 2>&1"
  when: docker_prune_schedule is defined
  tags: ['docker', 'maintenance']

- name: Configure GCP authentication for Docker
  include_tasks: gcp-auth.yml
  when: gcp_project_id is defined
  tags: ['docker', 'gcp']