# {{ agent_name | title }} Agent <PERSON>bernetes Deployment
# Generated on: {{ "now" | datetime }}

apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ agent_name }}-agent
  namespace: {{ deployment_config.get('namespace', 'default') }}
  labels:
    app: {{ agent_name }}-agent
    version: v1
    agent-type: {{ agent_type }}
spec:
  replicas: {{ deployment_config.get('replicas', 1) }}
  selector:
    matchLabels:
      app: {{ agent_name }}-agent
  template:
    metadata:
      labels:
        app: {{ agent_name }}-agent
        version: v1
        agent-type: {{ agent_type }}
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8080"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: agent-service-account
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 2000
      containers:
      - name: {{ agent_name }}-agent
        image: {{ deployment_config.get('image', 'ai-agent-platform/' + agent_name + ':latest') }}
        imagePullPolicy: IfNotPresent
        ports:
        {% if 'web_integration' in capabilities or 'api_integration' in capabilities %}
        - name: http
          containerPort: 8000
          protocol: TCP
        {% endif %}
        - name: metrics
          containerPort: 8080
          protocol: TCP
        env:
        - name: AGENT_NAME
          value: "{{ agent_name }}"
        - name: AGENT_TYPE
          value: "{{ agent_type }}"
        - name: AGENT_ID
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        {% if 'database' in capabilities %}
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: agent-secrets
              key: database-url
        {% endif %}
        {% if 'api_integration' in capabilities %}
        - name: API_KEY
          valueFrom:
            secretKeyRef:
              name: agent-secrets
              key: api-key
        {% endif %}
        envFrom:
        - configMapRef:
            name: {{ agent_name }}-config
        resources:
          limits:
            cpu: {{ deployment_config.get('cpu_limit', '500m') }}
            memory: {{ deployment_config.get('memory_limit', '512Mi') }}
          requests:
            cpu: {{ deployment_config.get('cpu_request', '100m') }}
            memory: {{ deployment_config.get('memory_request', '128Mi') }}
        livenessProbe:
          exec:
            command:
            - python
            - -c
            - "import asyncio; from agent import create_agent; print('OK')"
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - python
            - -c
            - "import asyncio; from agent import create_agent; print('Ready')"
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
          readOnly: true
        - name: logs-volume
          mountPath: /app/logs
        {% if 'database' in capabilities %}
        - name: data-volume
          mountPath: /app/data
        {% endif %}
      volumes:
      - name: config-volume
        configMap:
          name: {{ agent_name }}-config
      - name: logs-volume
        emptyDir: {}
      {% if 'database' in capabilities %}
      - name: data-volume
        persistentVolumeClaim:
          claimName: {{ agent_name }}-data-pvc
      {% endif %}
      restartPolicy: Always
      terminationGracePeriodSeconds: 30

---
{% if 'web_integration' in capabilities or 'api_integration' in capabilities %}
apiVersion: v1
kind: Service
metadata:
  name: {{ agent_name }}-service
  namespace: {{ deployment_config.get('namespace', 'default') }}
  labels:
    app: {{ agent_name }}-agent
spec:
  type: {{ deployment_config.get('service_type', 'ClusterIP') }}
  ports:
  - name: http
    port: 80
    targetPort: 8000
    protocol: TCP
  - name: metrics
    port: 8080
    targetPort: 8080
    protocol: TCP
  selector:
    app: {{ agent_name }}-agent

---
{% endif %}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ agent_name }}-config
  namespace: {{ deployment_config.get('namespace', 'default') }}
  labels:
    app: {{ agent_name }}-agent
data:
  agent.yaml: |
    agent:
      name: "{{ agent_name }}"
      type: "{{ agent_type }}"
      version: "1.0.0"
      description: "{{ description }}"
      capabilities:
        {% for capability in capabilities %}
        - "{{ capability }}"
        {% endfor %}
    runtime:
      max_concurrent_tasks: {{ configuration.get('max_concurrent_tasks', 5) }}
      timeout_seconds: {{ configuration.get('timeout_seconds', 300) }}
      retry_attempts: {{ configuration.get('retry_attempts', 3) }}
    custom:
      {% for key, value in configuration.items() %}
      {{ key }}: {{ value | tojson }}
      {% endfor %}

---
{% if 'database' in capabilities %}
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ agent_name }}-data-pvc
  namespace: {{ deployment_config.get('namespace', 'default') }}
  labels:
    app: {{ agent_name }}-agent
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: {{ deployment_config.get('storage_size', '1Gi') }}
  storageClassName: {{ deployment_config.get('storage_class', 'standard') }}

---
{% endif %}
{% if deployment_config.get('enable_hpa', false) %}
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{ agent_name }}-hpa
  namespace: {{ deployment_config.get('namespace', 'default') }}
  labels:
    app: {{ agent_name }}-agent
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ agent_name }}-agent
  minReplicas: {{ deployment_config.get('min_replicas', 1) }}
  maxReplicas: {{ deployment_config.get('max_replicas', 10) }}
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 25
        periodSeconds: 60

---
{% endif %}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: agent-service-account
  namespace: {{ deployment_config.get('namespace', 'default') }}
  labels:
    app: {{ agent_name }}-agent

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: agent-role
  namespace: {{ deployment_config.get('namespace', 'default') }}
rules:
- apiGroups: [""]
  resources: ["pods", "services", "configmaps"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments"]
  verbs: ["get", "list", "watch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: agent-role-binding
  namespace: {{ deployment_config.get('namespace', 'default') }}
subjects:
- kind: ServiceAccount
  name: agent-service-account
  namespace: {{ deployment_config.get('namespace', 'default') }}
roleRef:
  kind: Role
  name: agent-role
  apiGroup: rbac.authorization.k8s.io