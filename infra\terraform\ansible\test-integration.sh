#!/bin/bash
set -e

# Test script for Ansible integration with Terraform-managed infrastructure
# This script validates the Ansible setup and connectivity

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "🧪 Testing Ansible integration..."

# Auto-setup environment if not already configured
if [[ -z "$GCP_PROJECT_ID" ]] || [[ -z "$ENVIRONMENT" ]]; then
    echo "🔧 Auto-configuring environment..."
    source "$SCRIPT_DIR/setup-env.sh"
fi

echo "🎯 Testing environment: $ENVIRONMENT"
echo "🏗️  GCP Project: $GCP_PROJECT_ID"

# Check required environment variables
if [[ -z "$GCP_PROJECT_ID" ]]; then
    echo "❌ GCP_PROJECT_ID environment variable is required"
    echo "💡 Run: source setup-env.sh"
    exit 1
fi

# SSH key is optional for testing (created by Terraform)
if [[ -z "$ANSIBLE_SSH_KEY" ]]; then
    echo "⚠️  ANSIBLE_SSH_KEY not set (will be created by Terraform)"
fi

# Check required files
echo "📁 Checking required files..."
required_files=(
    "ansible.cfg"
    "requirements.yml"
    "inventory/gcp.yml"
    "playbooks/deploy-services.yml"
)

for file in "${required_files[@]}"; do
    if [[ ! -f "$file" ]]; then
        echo "❌ Required file missing: $file"
        exit 1
    fi
    echo "✅ Found: $file"
done

# Install Ansible requirements
echo "📦 Installing Ansible requirements..."
ansible-galaxy install -r requirements.yml

# Test dynamic inventory
echo "🔍 Testing GCP dynamic inventory..."
if [[ -n "$GCP_SERVICE_ACCOUNT_FILE" ]] && [[ -f "$GCP_SERVICE_ACCOUNT_FILE" ]]; then
    if ansible-inventory -i inventory/gcp.yml --list > /dev/null 2>&1; then
        echo "✅ GCP dynamic inventory is working"
        INVENTORY_FILE="inventory/gcp.yml"
    else
        echo "⚠️  GCP dynamic inventory has issues, using static inventory for testing"
        INVENTORY_FILE="inventory/static-test.yml"
    fi
else
    echo "⚠️  No GCP service account file found, using static inventory for testing"
    INVENTORY_FILE="inventory/static-test.yml"
fi

# Test connectivity to microservices hosts
echo "🔗 Testing connectivity to microservices hosts..."
if ansible all -i "$INVENTORY_FILE" -m ping > /dev/null 2>&1; then
    echo "✅ Successfully connected to hosts"
else
    echo "⚠️  Could not connect to hosts (this is expected if no instances are running)"
fi

# Validate playbooks syntax
echo "🔍 Validating playbook syntax..."
playbooks=(
    "playbooks/deploy-services.yml"
    "playbooks/update-config.yml"
    "playbooks/update-images.yml"
    "playbooks/rollback.yml"
)

for playbook in "${playbooks[@]}"; do
    if ansible-playbook -i "$INVENTORY_FILE" --syntax-check "$playbook" > /dev/null 2>&1; then
        echo "✅ Syntax valid: $playbook"
    else
        echo "❌ Syntax error in: $playbook"
        echo "   Running detailed syntax check..."
        ansible-playbook -i "$INVENTORY_FILE" --syntax-check "$playbook"
        exit 1
    fi
done

# Test dry run of main playbook (if hosts available)
echo "🧪 Testing dry run of main deployment playbook..."
if ansible-playbook -i "$INVENTORY_FILE" playbooks/deploy-services.yml --check --diff > /dev/null 2>&1; then
    echo "✅ Main playbook dry run successful"
else
    echo "⚠️  Main playbook dry run failed (expected if no hosts available)"
fi

echo ""
echo "🎉 Ansible integration test completed successfully!"
echo ""
echo "Next steps:"
echo "1. Run 'terraform apply' to create infrastructure"
echo "2. The Ansible provisioning will run automatically"
echo "3. Use 'ansible-playbook' commands for ongoing management"
echo ""
echo "Useful commands:"
echo "  ansible-inventory -i inventory/gcp.yml --list"
echo "  ansible microservices -i inventory/gcp.yml -m ping"
echo "  ansible-playbook -i inventory/gcp.yml playbooks/deploy-services.yml"