"""
Tests for Agent-to-Agent (A2A) Protocol
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, MagicMock

from protocols.a2a import (
    A2AProtocolHandler, A2AMessage, MessageType, MessagePriority,
    ProtocolVersion, AgentCapability
)


@pytest.fixture
def a2a_handler():
    """Create A2A protocol handler for testing"""
    return A2AProtocolHandler()


@pytest.fixture
def sample_capabilities():
    """Sample agent capabilities"""
    return [
        AgentCapability(
            name="text_analysis",
            description="Analyze text content",
            version="1.0",
            parameters={"max_length": 5000}
        ),
        AgentCapability(
            name="data_processing",
            description="Process structured data",
            version="1.0",
            parameters={"formats": ["json", "csv"]}
        )
    ]


@pytest.fixture
def sample_message():
    """Sample A2A message"""
    return A2AMessage(
        type=MessageType.REQUEST,
        priority=MessagePriority.MEDIUM,
        sender_id="agent1",
        sender_type="assistant",
        receiver_id="agent2",
        receiver_type="processor",
        subject="process.data",
        payload={"data": "test", "action": "analyze"}
    )


class TestA2AProtocolHandler:
    """Test A2A protocol handler functionality"""
    
    @pytest.mark.asyncio
    async def test_register_agent(self, a2a_handler, sample_capabilities):
        """Test agent registration"""
        # Register agent
        success = await a2a_handler.register_agent(
            agent_id="test_agent",
            agent_type="assistant",
            capabilities=sample_capabilities,
            metadata={"version": "1.0"}
        )
        
        assert success is True
        assert "test_agent" in a2a_handler.active_connections
        assert a2a_handler.active_connections["test_agent"]["agent_type"] == "assistant"
        assert len(a2a_handler.capability_registry["test_agent"]) == 2
    
    @pytest.mark.asyncio
    async def test_unregister_agent(self, a2a_handler, sample_capabilities):
        """Test agent unregistration"""
        # Register first
        await a2a_handler.register_agent(
            agent_id="test_agent",
            agent_type="assistant",
            capabilities=sample_capabilities
        )
        
        # Unregister
        success = await a2a_handler.unregister_agent("test_agent")
        
        assert success is True
        assert "test_agent" not in a2a_handler.active_connections
        assert "test_agent" not in a2a_handler.capability_registry
    
    @pytest.mark.asyncio
    async def test_send_message_validation(self, a2a_handler, sample_message):
        """Test message validation"""
        # Try to send without registering sender
        response = await a2a_handler.send_message(sample_message)
        
        assert response.success is False
        assert "validation failed" in response.error.lower()
    
    @pytest.mark.asyncio
    async def test_send_message_with_registered_agents(self, a2a_handler, sample_message, sample_capabilities):
        """Test sending message between registered agents"""
        # Register both agents
        await a2a_handler.register_agent(
            agent_id="agent1",
            agent_type="assistant",
            capabilities=sample_capabilities
        )
        await a2a_handler.register_agent(
            agent_id="agent2",
            agent_type="processor",
            capabilities=sample_capabilities
        )
        
        # Mock message queue
        a2a_handler._store_message = AsyncMock()
        
        # Send message
        response = await a2a_handler.send_message(sample_message)
        
        assert response.success is True
        assert response.message_id == sample_message.id
    
    @pytest.mark.asyncio
    async def test_broadcast_message(self, a2a_handler, sample_capabilities):
        """Test broadcasting messages"""
        # Register multiple agents
        for i in range(3):
            await a2a_handler.register_agent(
                agent_id=f"agent{i}",
                agent_type="assistant" if i < 2 else "processor",
                capabilities=sample_capabilities
            )
        
        # Mock message queue
        published_messages = []
        async def mock_publish(topic, message):
            published_messages.append(message)
        
        # Broadcast to assistants only
        def assistant_filter(agent_info):
            return agent_info["agent_type"] == "assistant"
        
        response = await a2a_handler.broadcast_message(
            sender_id="agent0",
            subject="test.broadcast",
            payload={"message": "hello"},
            agent_filter=assistant_filter
        )
        
        assert response.success is True
        assert response.data["broadcast_to"] == 1  # Only agent1 should receive
    
    @pytest.mark.asyncio
    async def test_establish_conversation(self, a2a_handler, sample_capabilities):
        """Test conversation establishment"""
        # Register agents
        for i in range(3):
            await a2a_handler.register_agent(
                agent_id=f"agent{i}",
                agent_type="assistant",
                capabilities=sample_capabilities
            )
        
        # Establish conversation
        conversation_id = await a2a_handler.establish_conversation(
            initiator_id="agent0",
            participant_ids=["agent1", "agent2"],
            context={"topic": "data_analysis"}
        )
        
        assert conversation_id in a2a_handler.conversation_contexts
        assert len(a2a_handler.conversation_contexts[conversation_id]["participants"]) == 3
    
    @pytest.mark.asyncio
    async def test_query_capabilities(self, a2a_handler, sample_capabilities):
        """Test capability querying"""
        # Register agents with different capabilities
        await a2a_handler.register_agent(
            agent_id="agent1",
            agent_type="assistant",
            capabilities=[sample_capabilities[0]]  # Only text_analysis
        )
        await a2a_handler.register_agent(
            agent_id="agent2",
            agent_type="processor",
            capabilities=[sample_capabilities[1]]  # Only data_processing
        )
        
        # Query all capabilities
        all_caps = await a2a_handler.query_agent_capabilities()
        assert len(all_caps) == 2
        
        # Query specific capability
        text_caps = await a2a_handler.query_agent_capabilities(
            capability_name="text_analysis"
        )
        assert len(text_caps) == 1
        assert "agent1" in text_caps
    
    @pytest.mark.asyncio
    async def test_heartbeat_monitoring(self, a2a_handler, sample_capabilities):
        """Test heartbeat monitoring"""
        # Register agent
        await a2a_handler.register_agent(
            agent_id="test_agent",
            agent_type="assistant",
            capabilities=sample_capabilities
        )
        
        # Simulate old heartbeat
        old_time = datetime.utcnow() - timedelta(seconds=90)
        a2a_handler.active_connections["test_agent"]["last_heartbeat"] = old_time
        
        # Run heartbeat monitor once
        a2a_handler._heartbeat_interval = 30
        await a2a_handler._heartbeat_monitor()
        
        # Agent should be marked inactive
        assert a2a_handler.active_connections["test_agent"]["status"] == "inactive"
    
    @pytest.mark.asyncio
    async def test_message_expiration(self, a2a_handler):
        """Test message expiration validation"""
        # Create expired message
        expired_message = A2AMessage(
            type=MessageType.REQUEST,
            sender_id="agent1",
            sender_type="assistant",
            receiver_id="agent2",
            receiver_type="processor",
            subject="test",
            payload={},
            expires_at=datetime.utcnow() - timedelta(seconds=60)
        )
        
        # Register sender to pass other validations
        await a2a_handler.register_agent(
            agent_id="agent1",
            agent_type="assistant",
            capabilities=[]
        )
        
        # Validation should fail
        is_valid = await a2a_handler._validate_message(expired_message)
        assert is_valid is False
    
    @pytest.mark.asyncio
    async def test_conversation_cleanup(self, a2a_handler, sample_capabilities):
        """Test conversation cleanup when agent disconnects"""
        # Register agents
        for i in range(3):
            await a2a_handler.register_agent(
                agent_id=f"agent{i}",
                agent_type="assistant",
                capabilities=sample_capabilities
            )
        
        # Establish conversation
        conversation_id = await a2a_handler.establish_conversation(
            initiator_id="agent0",
            participant_ids=["agent1", "agent2"],
            context={}
        )
        
        # Unregister one agent
        await a2a_handler.unregister_agent("agent1")
        
        # Conversation should still exist with 2 participants
        assert conversation_id in a2a_handler.conversation_contexts
        assert len(a2a_handler.conversation_contexts[conversation_id]["participants"]) == 2
        
        # Unregister another agent
        await a2a_handler.unregister_agent("agent2")
        
        # Conversation should be removed (less than 2 participants)
        assert conversation_id not in a2a_handler.conversation_contexts
    
    @pytest.mark.asyncio
    async def test_protocol_negotiation(self, a2a_handler):
        """Test protocol version negotiation"""
        # For now, should always return V2
        version = await a2a_handler.negotiate_protocol("agent1", "agent2")
        assert version == ProtocolVersion.V2
    
    @pytest.mark.asyncio
    async def test_message_handler_registration(self, a2a_handler):
        """Test registering custom message handlers"""
        # Track handler calls
        handler_called = False
        
        async def custom_handler(message: A2AMessage):
            nonlocal handler_called
            handler_called = True
        
        # Register handler
        a2a_handler.register_message_handler(MessageType.REQUEST, custom_handler)
        
        # Verify handler was registered
        assert custom_handler in a2a_handler.message_handlers[MessageType.REQUEST]
    
    @pytest.mark.asyncio
    async def test_conversation_history(self, a2a_handler, sample_message):
        """Test retrieving conversation history"""
        # Create conversation
        conversation_id = "test_conv_123"
        a2a_handler.conversation_contexts[conversation_id] = {
            "id": conversation_id,
            "participants": ["agent1", "agent2"],
            "messages": [sample_message, sample_message],
            "status": "active"
        }
        
        # Get history
        history = await a2a_handler.get_conversation_history(conversation_id, limit=1)
        
        assert len(history) == 1
        assert history[0].id == sample_message.id