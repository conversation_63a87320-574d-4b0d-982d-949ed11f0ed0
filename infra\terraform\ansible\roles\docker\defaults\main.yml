---
# Docker role default variables
docker_compose_version: "2.24.0"
docker_log_driver: json-file
docker_log_options:
  max-size: "10m"
  max-file: "3"
  
docker_prune_schedule: "0 2 * * 0"  # Weekly at 2 AM Sunday
docker_data_root: /var/lib/docker

# Network configuration
docker_network_name: microservices
docker_network_subnet: 172.18.0.0/16

# Resource defaults
docker_default_memory_limit: "512m"
docker_default_cpu_limit: "0.5"

# Security settings
docker_enable_userns_remap: false
docker_enable_selinux: false