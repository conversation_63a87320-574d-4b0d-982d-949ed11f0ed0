/**
 * AI Agent Platform - Agent Service
 */

import { apiService } from './api';
import {
  Agent,
  CreateAgentRequest,
  UpdateAgentRequest,
  ListResponse,
  SuccessResponse,
  RuntimeInfo,
  SystemStats,
  AgentStatus,
} from '@/types/api';

class AgentService {
  async listAgents(params?: {
    status?: AgentStatus;
    limit?: number;
    offset?: number;
  }): Promise<ListResponse<Agent>> {
    const response = await apiService.get<{
      agents: Agent[];
      total: number;
      limit: number;
      offset: number;
    }>('/agents', params);

    return {
      items: response.data.agents,
      total: response.data.total,
      limit: response.data.limit,
      offset: response.data.offset,
    };
  }

  async getAgent(id: string): Promise<Agent> {
    const response = await apiService.get<Agent>(`/agents/${id}`);
    return response.data;
  }

  async createAgent(data: CreateAgentRequest): Promise<Agent> {
    const response = await apiService.post<Agent>('/agents', data);
    return response.data;
  }

  async updateAgent(id: string, data: UpdateAgentRequest): Promise<Agent> {
    const response = await apiService.put<Agent>(`/agents/${id}`, data);
    return response.data;
  }

  async deleteAgent(id: string): Promise<void> {
    await apiService.delete(`/agents/${id}`);
  }

  async startAgent(id: string): Promise<SuccessResponse> {
    const response = await apiService.post<SuccessResponse>(`/agents/${id}/start`);
    return response.data;
  }

  async stopAgent(id: string): Promise<SuccessResponse> {
    const response = await apiService.post<SuccessResponse>(`/agents/${id}/stop`);
    return response.data;
  }

  async updateHeartbeat(id: string): Promise<void> {
    await apiService.post(`/agents/${id}/heartbeat`);
  }

  // Runtime management
  async listActiveAgents(): Promise<RuntimeInfo[]> {
    const response = await apiService.get<RuntimeInfo[]>('/runtime/agents');
    return response.data;
  }

  async getAgentRuntimeInfo(id: string): Promise<RuntimeInfo> {
    const response = await apiService.get<RuntimeInfo>(`/runtime/agents/${id}/info`);
    return response.data;
  }

  async pauseAgentRuntime(id: string): Promise<SuccessResponse> {
    const response = await apiService.post<SuccessResponse>(`/runtime/agents/${id}/pause`);
    return response.data;
  }

  async resumeAgentRuntime(id: string): Promise<SuccessResponse> {
    const response = await apiService.post<SuccessResponse>(`/runtime/agents/${id}/resume`);
    return response.data;
  }

  async executeTask(id: string, taskData: {
    task_type: string;
    input_data?: Record<string, any>;
    metadata?: Record<string, any>;
  }): Promise<any> {
    const response = await apiService.post(`/runtime/agents/${id}/execute-task`, taskData);
    return response.data;
  }

  async queueTask(id: string, taskData: {
    task_type: string;
    input_data?: Record<string, any>;
    metadata?: Record<string, any>;
  }): Promise<SuccessResponse> {
    const response = await apiService.post<SuccessResponse>(`/runtime/agents/${id}/queue-task`, taskData);
    return response.data;
  }

  // System stats
  async getSystemStats(): Promise<SystemStats> {
    const response = await apiService.get<SystemStats>('/runtime/system/stats');
    return response.data;
  }

  async getSystemHealth(): Promise<any> {
    const response = await apiService.get('/runtime/system/health');
    return response.data;
  }

  async shutdownAllAgents(): Promise<SuccessResponse> {
    const response = await apiService.post<SuccessResponse>('/runtime/system/shutdown');
    return response.data;
  }

  // Deployment management
  async getAgentDeployment(id: string): Promise<any> {
    const response = await apiService.get(`/agents/${id}/deployment`);
    return response.data;
  }

  async deployAgent(id: string): Promise<any> {
    const response = await apiService.post(`/agents/${id}/deploy`);
    return response.data;
  }

  async stopAgentDeployment(id: string): Promise<any> {
    const response = await apiService.delete(`/agents/${id}/deployment`);
    return response.data;
  }
}

export const agentService = new AgentService();