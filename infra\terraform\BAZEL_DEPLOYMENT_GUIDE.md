# 🚀 Bazel Environment-Aware Deployment Commands

This guide shows how to use the new Bazel deployment targets that automatically detect your environment from the current directory.

## ✨ Quick Start

### Prerequisites
1. Make sure you're authenticated with GCP: `gcloud auth login`
2. Set your project: `gcloud config set project <project-id>`
3. Navigate to your target environment directory

### Basic Usage

```bash
# Deploy web frontend
cd terraform/environments/dev     # or prod, staging
bazel run //terraform:deploy-web

# Deploy backend services (build & push Docker images)
cd terraform/environments/dev     # or prod, staging  
bazel run //terraform:deploy-backend
```

## 🎯 Available Commands

### 🌐 Web Frontend Deployment
```bash
bazel run //terraform:deploy-web
```
**What it does:**
- Detects environment from current directory (`dev`, `staging`, or `prod`)
- Builds React application with production optimizations
- Gets bucket name from Terraform state
- Uploads all files to environment-specific GCS bucket
- Sets proper cache headers (1 year for assets, no-cache for HTML)
- Shows access URLs

### 🔧 Backend Services Deployment
```bash
bazel run //terraform:deploy-backend
```
**What it does:**
- Detects environment from current directory
- Builds Docker images for all backend services:
  - Gateway (API gateway)
  - Auth Service (authentication)
  - CRM Backend (business logic)
- Tags images with environment-specific registry URLs
- Pushes images to Artifact Registry
- Shows deployment summary

## 📁 Environment Detection

The commands automatically detect your environment based on your current directory:

```bash
# Deploy to DEV
cd terraform/environments/dev
bazel run //terraform:deploy-web        # Deploys to dev environment

# Deploy to PRODUCTION
cd terraform/environments/prod
bazel run //terraform:deploy-web        # Deploys to prod environment

# Deploy to STAGING (when available)
cd terraform/environments/staging
bazel run //terraform:deploy-web        # Deploys to staging environment
```

## 🏗️ Environment Configuration

Each environment has its own configuration:

| Environment | Project ID | Registry URL | Bucket Name Pattern |
|-------------|------------|--------------|-------------------|
| **dev** | `agent-dev-459718` | `australia-southeast1-docker.pkg.dev/agent-dev-459718/platform-docker` | `platform-web-dev-*` |
| **staging** | `agent-staging-459718` | `australia-southeast1-docker.pkg.dev/agent-staging-459718/platform-docker` | `platform-web-staging-*` |
| **prod** | `twodot-agent-prod` | `australia-southeast1-docker.pkg.dev/twodot-agent-prod/platform-docker` | `platform-web-prod-*` |

## 🔄 Complete Deployment Workflow

### For Development
```bash
cd terraform/environments/dev

# 1. Deploy backend services
bazel run //terraform:deploy-backend

# 2. Deploy web frontend  
bazel run //terraform:deploy-web

# 3. Test the application
curl http://$(terraform output -raw load_balancer_ip)/health
```

### For Production
```bash
cd terraform/environments/prod

# 1. Deploy backend services (includes production safety checks)
bazel run //terraform:deploy-backend

# 2. Deploy web frontend
bazel run //terraform:deploy-web

# 3. Verify deployment
terraform output load_balancer_ip
```

## 🛡️ Safety Features

### Production Protection
- **Production deployments** include confirmation prompts
- **Deletion protection** is enabled for production databases
- **Manual migration approval** required for production

### Automatic Validation
- **Environment detection** from directory path
- **Terraform state validation** before deployment
- **GCP authentication checks** before proceeding
- **Bucket existence verification** before upload

## 🔍 Troubleshooting

### Common Issues

**"Could not detect environment"**
```bash
# Make sure you're in the right directory
cd terraform/environments/dev  # or prod, staging
pwd  # Should show: .../terraform/environments/dev
```

**"Could not get bucket name from Terraform"**
```bash
# Make sure infrastructure is deployed first
terraform apply
```

**"Docker authentication failed"**
```bash
# Re-authenticate with GCP
gcloud auth login
gcloud auth configure-docker australia-southeast1-docker.pkg.dev
```

**"Failed to upload to bucket"**
```bash
# Check GCP permissions
gcloud auth list
gcloud config list
```

### Debug Mode
Add verbose output to any command:
```bash
# Enable debug logging
export TF_LOG=DEBUG
bazel run //terraform:deploy-web
```

## 📊 Integration with Existing Tools

These Bazel commands integrate seamlessly with your existing workflow:

### Traditional Scripts (still available)
```bash
# Old way (still works)
cd terraform/environments/dev
./deploy-web.sh

# New way (Bazel)
cd terraform/environments/dev  
bazel run //terraform:deploy-web
```

### CI/CD Pipeline Integration
```yaml
# GitHub Actions example
- name: Deploy to dev
  run: |
    cd terraform/environments/dev
    bazel run //terraform:deploy-backend
    bazel run //terraform:deploy-web
```

## 🎉 Summary

The new Bazel deployment commands provide:

✅ **Environment auto-detection** from current directory  
✅ **Simplified commands** - no need to specify environment  
✅ **Consistent interface** across all environments  
✅ **Production safety checks** built-in  
✅ **Full integration** with existing Terraform infrastructure  

**Happy deploying!** 🚀