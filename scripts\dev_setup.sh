#!/bin/bash
# Development setup - start local services
set -e

echo "🚀 Setting up local development environment..."

# Change to workspace directory
if [ -n "$BUILD_WORKSPACE_DIRECTORY" ]; then
    cd "$BUILD_WORKSPACE_DIRECTORY"
elif [ -n "$TEST_WORKSPACE" ]; then
    cd "$TEST_WORKSPACE"
else
    # Find the workspace root by looking for WORKSPACE file
    CURRENT_DIR="$PWD"
    while [ "$CURRENT_DIR" != "/" ]; do
        if [ -f "$CURRENT_DIR/WORKSPACE" ]; then
            cd "$CURRENT_DIR"
            break
        fi
        CURRENT_DIR="$(dirname "$CURRENT_DIR")"
    done
fi

echo "🗄️  Step 1: Starting local database..."
bazel run //platform/db:start

echo "📊 Step 2: Running database migrations..."
bazel run //platform/db:migrate

echo "🧪 Step 3: Loading test data..."
bazel run //platform/db:test_data

echo "✅ Development environment setup completed!"
echo ""
echo "🛠️  Next steps:"
echo "  • Start web dev server: bazel run //platform/web:dev"
echo "  • Start auth service: bazel run //platform/auth:auth_service"
echo "  • Start CRM backend: bazel run //platform/crm_backend:crm_backend"
echo "  • Start gateway: bazel run //gateway:gateway"
echo ""
echo "🌐 URLs:"
echo "  • Web app: http://localhost:8080"
echo "  • Gateway: http://localhost:8085"
echo "  • Auth service: http://localhost:8004"
echo "  • CRM backend: http://localhost:8003"