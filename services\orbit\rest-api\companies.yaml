openapi: 3.0.3
info:
  title: CRM Companies API
  version: 1.0.0
  description: Company management endpoints for the CRM system

paths:
  /companies:
    get:
      summary: List companies
      description: Retrieve a list of all active companies
      parameters:
        - name: status_id
          in: query
          schema:
            type: string
            format: uuid
          description: Filter by company status
        - name: include_deleted
          in: query
          schema:
            type: boolean
            default: false
          description: Include soft-deleted companies
      responses:
        '200':
          description: List of companies
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Company'

    post:
      summary: Create company
      description: Create a new company
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CompanyCreate'
      responses:
        '201':
          description: Company created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Company'
        '400':
          description: Invalid input

  /companies/{id}:
    get:
      summary: Get company
      description: Retrieve a specific company by ID
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Company details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanyDetails'
        '404':
          description: Company not found

    put:
      summary: Update company
      description: Update an existing company
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CompanyUpdate'
      responses:
        '200':
          description: Company updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Company'
        '404':
          description: Company not found

    delete:
      summary: Delete company
      description: Soft delete a company (sets is_deleted to true)
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '204':
          description: Company deleted successfully
        '404':
          description: Company not found

  /companies/{id}/status:
    put:
      summary: Update company status
      description: Update the status of a company
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                company_status_id:
                  type: string
                  format: uuid
              required:
                - company_status_id
      responses:
        '200':
          description: Company status updated successfully
        '404':
          description: Company not found

  /companies/{id}/contacts:
    get:
      summary: Get company contacts
      description: Retrieve all contacts associated with a company
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: List of contacts
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Contact'

  /companies/{id}/interactions:
    get:
      summary: Get company interactions
      description: Retrieve all interactions for a company
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: List of interactions
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Interaction'

  /companies/{id}/research-notes:
    get:
      summary: Get company research notes
      description: Retrieve research notes for a company
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: List of research notes
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ResearchNote'

  /company-statuses:
    get:
      summary: List company statuses
      description: Retrieve all company statuses ordered by pipeline order
      responses:
        '200':
          description: List of company statuses
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CompanyStatus'

components:
  schemas:
    Company:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        website:
          type: string
          format: uri
        phone:
          type: string
        address:
          type: string
        notes:
          type: string
        company_status_id:
          type: string
          format: uuid
        is_deleted:
          type: boolean
          default: false
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    CompanyDetails:
      allOf:
        - $ref: '#/components/schemas/Company'
        - type: object
          properties:
            contacts:
              type: array
              items:
                $ref: '#/components/schemas/Contact'
            interactions:
              type: array
              items:
                $ref: '#/components/schemas/Interaction'

    CompanyCreate:
      type: object
      properties:
        name:
          type: string
          minLength: 1
        website:
          type: string
          format: uri
        phone:
          type: string
        address:
          type: string
        notes:
          type: string
        company_status_id:
          type: string
          format: uuid
      required:
        - name

    CompanyUpdate:
      type: object
      properties:
        name:
          type: string
          minLength: 1
        website:
          type: string
          format: uri
        phone:
          type: string
        address:
          type: string
        notes:
          type: string
        company_status_id:
          type: string
          format: uuid

    CompanyStatus:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        pipeline_order:
          type: integer
        created_at:
          type: string
          format: date-time

    Contact:
      type: object
      properties:
        id:
          type: string
          format: uuid
        first_name:
          type: string
        last_name:
          type: string
        email:
          type: string
          format: email
        phone:
          type: string
        job_title:
          type: string
        company_id:
          type: string
          format: uuid
        created_at:
          type: string
          format: date-time

    Interaction:
      type: object
      properties:
        id:
          type: string
          format: uuid
        notes:
          type: string
        interaction_type:
          type: string
        interaction_datetime:
          type: string
          format: date-time
        created_at:
          type: string
          format: date-time

    ResearchNote:
      type: object
      properties:
        id:
          type: string
          format: uuid
        entity_type:
          type: string
          enum: [company, contact]
        entity_id:
          type: string
          format: uuid
        note_content:
          type: string
        created_at:
          type: string
          format: date-time