---
# Terraform-generated variables for ${environment} environment
# This file is automatically generated - do not edit manually

# GCP Configuration
gcp_project_id: "${project_id}"
environment: "${environment}"
deployment_environment: "${environment}"

# Registry Configuration
artifact_registry: "microservices"
artifact_registry_location: "us-central1"
registry_url: "${registry_url}"

# Database Configuration
database_host: "${database_host}"
database_name: "${database_name}"
database_user: "${database_user}"
database_password_secret: "${database_password_secret}"

# Service Versions
gateway_version: "${gateway_version}"
auth_version: "${auth_version}"
backend_version: "${backend_version}"

# Deployment Configuration
deployment_color: "${deployment_color}"
deployment_strategy: "blue-green"

# Service-specific configuration overrides from Terraform
# These will be merged with the base configuration in vars/${environment}.yml