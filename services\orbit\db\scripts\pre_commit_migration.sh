#!/bin/bash
# Pre-commit hook for automatic migration generation and application
set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_error() {
    echo -e "${RED}❌ [PRE-COMMIT] Error: $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ [PRE-COMMIT] $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  [PRE-COMMIT] $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  [PRE-COMMIT] $1${NC}"
}

# Get the workspace root
if [ -n "$BUILD_WORKSPACE_DIRECTORY" ]; then
    cd "$BUILD_WORKSPACE_DIRECTORY"
elif [ -n "$TEST_WORKSPACE" ]; then
    cd "$TEST_WORKSPACE/.."
else
    # Find the workspace root by looking for WORKSPACE file
    CURRENT_DIR="$PWD"
    while [ "$CURRENT_DIR" != "/" ]; do
        if [ -f "$CURRENT_DIR/WORKSPACE" ]; then
            cd "$CURRENT_DIR"
            break
        fi
        CURRENT_DIR="$(dirname "$CURRENT_DIR")"
    done
fi

cd platform/db

# Configuration
SCHEMA_DIR="schema"
MIGRATIONS_DIR="migrations"

# Check if this is a commit that includes schema changes
check_schema_changes() {
    # Check if we're in a git repository
    if ! git rev-parse --git-dir > /dev/null 2>&1; then
        return 1
    fi
    
    # Check for staged schema files
    if git diff --cached --name-only | grep -q "^platform/db/schema/.*\.sql$"; then
        return 0
    fi
    
    return 1
}

# Function to apply migrations and add generated files to commit
apply_and_stage_migrations() {
    print_info "Schema changes detected in commit"
    
    # Run auto-migration
    if bazel run //platform/db:auto_migrate -- run local; then
        print_success "Auto-migration completed"
        
        # Stage any new migration files
        if [ -d "$MIGRATIONS_DIR" ]; then
            local new_migrations=$(git status --porcelain "$MIGRATIONS_DIR" | grep "^??" | wc -l)
            if [ "$new_migrations" -gt 0 ]; then
                print_info "Staging $new_migrations new migration files"
                git add "$MIGRATIONS_DIR"/*.sql
                print_success "New migration files added to commit"
            fi
        fi
        
        # Stage the auto_migrations directory to track processed files
        if [ -d "auto_migrations" ]; then
            git add auto_migrations/
        fi
        
        return 0
    else
        print_error "Auto-migration failed"
        return 1
    fi
}

# Function to install the pre-commit hook
install_hook() {
    local git_dir
    if git_dir=$(git rev-parse --git-dir 2>/dev/null); then
        local hook_file="$git_dir/hooks/pre-commit"
        local this_script="$(pwd)/scripts/pre_commit_migration.sh"
        
        # Create hooks directory if it doesn't exist
        mkdir -p "$git_dir/hooks"
        
        # Create or update the pre-commit hook
        if [ -f "$hook_file" ]; then
            # Check if our hook is already installed
            if grep -q "pre_commit_migration.sh" "$hook_file"; then
                print_info "Pre-commit hook already installed"
                return 0
            else
                print_info "Adding migration hook to existing pre-commit"
                echo "" >> "$hook_file"
                echo "# Auto-migration hook" >> "$hook_file"
                echo "if [ -f \"platform/db/scripts/pre_commit_migration.sh\" ]; then" >> "$hook_file"
                echo "    bash platform/db/scripts/pre_commit_migration.sh" >> "$hook_file"
                echo "fi" >> "$hook_file"
            fi
        else
            print_info "Creating new pre-commit hook"
            cat > "$hook_file" << 'EOF'
#!/bin/bash
# Pre-commit hook with auto-migration support

# Auto-migration hook
if [ -f "platform/db/scripts/pre_commit_migration.sh" ]; then
    bash platform/db/scripts/pre_commit_migration.sh
fi
EOF
        fi
        
        # Make the hook executable
        chmod +x "$hook_file"
        
        print_success "Pre-commit hook installed successfully"
        print_info "The hook will automatically generate and apply migrations when you commit schema changes"
    else
        print_error "Not in a git repository"
        return 1
    fi
}

# Function to uninstall the pre-commit hook
uninstall_hook() {
    local git_dir
    if git_dir=$(git rev-parse --git-dir 2>/dev/null); then
        local hook_file="$git_dir/hooks/pre-commit"
        
        if [ -f "$hook_file" ]; then
            if grep -q "pre_commit_migration.sh" "$hook_file"; then
                # Remove our hook from the file
                sed -i.bak '/# Auto-migration hook/,/fi/d' "$hook_file"
                rm -f "$hook_file.bak"
                
                # Remove the file if it's empty
                if [ ! -s "$hook_file" ]; then
                    rm -f "$hook_file"
                fi
                
                print_success "Pre-commit hook uninstalled"
            else
                print_info "Migration hook not found in pre-commit"
            fi
        else
            print_info "No pre-commit hook found"
        fi
    else
        print_error "Not in a git repository"
        return 1
    fi
}

# Function to show hook status
show_hook_status() {
    local git_dir
    if git_dir=$(git rev-parse --git-dir 2>/dev/null); then
        local hook_file="$git_dir/hooks/pre-commit"
        
        if [ -f "$hook_file" ]; then
            if grep -q "pre_commit_migration.sh" "$hook_file"; then
                print_success "Pre-commit migration hook is installed"
                print_info "Schema changes will automatically generate and apply migrations"
            else
                print_warning "Pre-commit hook exists but migration hook is not installed"
            fi
        else
            print_info "No pre-commit hook found"
        fi
    else
        print_error "Not in a git repository"
    fi
}

# Function to run a test migration
test_migration() {
    print_info "Testing migration system..."
    
    # Create a test schema file
    local test_file="$SCHEMA_DIR/test_migration_$(date +%s).sql"
    cat > "$test_file" << 'EOF'
-- Test migration file
CREATE TABLE IF NOT EXISTS test_migration_table (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    test_column VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
EOF
    
    print_info "Created test schema file: $test_file"
    
    # Run migration
    if bazel run //platform/db:auto_migrate -- run local; then
        print_success "Test migration completed successfully"
        
        # Clean up test file
        rm -f "$test_file"
        print_info "Test schema file removed"
        
        return 0
    else
        print_error "Test migration failed"
        rm -f "$test_file"
        return 1
    fi
}

# Help function
show_help() {
    echo "Pre-commit Migration Hook - Automatically generates and applies migrations on commit"
    echo ""
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  run           - Run the migration check (used by git pre-commit hook)"
    echo "  install       - Install the pre-commit hook"
    echo "  uninstall     - Uninstall the pre-commit hook"
    echo "  status        - Show hook installation status"
    echo "  test          - Test the migration system"
    echo "  help          - Show this help message"
    echo ""
    echo "The pre-commit hook will:"
    echo "  1. Detect schema changes in platform/db/schema/"
    echo "  2. Generate new migration files automatically"
    echo "  3. Apply migrations to local database"
    echo "  4. Add generated migration files to the commit"
    echo ""
    echo "This ensures that your database schema is always in sync with your code changes."
}

# Main script logic
case "${1:-run}" in
    "run")
        if check_schema_changes; then
            apply_and_stage_migrations
        else
            print_info "No schema changes detected, skipping migration"
        fi
        ;;
    "install")
        install_hook
        ;;
    "uninstall")
        uninstall_hook
        ;;
    "status")
        show_hook_status
        ;;
    "test")
        test_migration
        ;;
    "help"|"--help"|"-h")
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        show_help
        exit 1
        ;;
esac