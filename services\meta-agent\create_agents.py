#!/usr/bin/env python3
"""
Comprehensive script to create and deploy two fullstack agents
- EMI Calculator Agent
- Math Calculator Agent
"""

import requests
import json
import time
import sys
from typing import Dict, Any, Optional

# Configuration
BASE_URL = "http://localhost:8000"
TIMEOUT = 60

class AgentManager:
    def __init__(self, base_url: str = BASE_URL):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({'Content-Type': 'application/json'})

    def check_backend(self) -> bool:
        """Check if backend is running and healthy"""
        try:
            response = self.session.get(f"{self.base_url}/health", timeout=5)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Backend is healthy - Version: {data.get('version', 'unknown')}")
                return True
            else:
                print(f"❌ Backend unhealthy - Status: {response.status_code}")
                return False
        except requests.exceptions.ConnectionError:
            print("❌ Cannot connect to backend - is it running on port 8000?")
            return False
        except Exception as e:
            print(f"❌ Error checking backend: {e}")
            return False

    def create_agent(self, name: str, type: str, language: str, capabilities: list) -> Optional[Dict[Any, Any]]:
        """Create a new agent"""
        
        # Generate specific requirements based on the agent type
        if "EMI" in name:
            requirements = """
            Create a comprehensive EMI (Equated Monthly Installment) Calculator with the following features:
            
            1. Main EMI Calculation:
               - Principal amount input (loan amount)
               - Interest rate input (annual percentage)
               - Loan tenure input (months or years)
               - Calculate and display EMI amount
               - Show total interest payable
               - Show total amount payable
            
            2. Advanced Features:
               - Amortization schedule/table showing month-wise breakup
               - Principal vs Interest breakdown chart
               - Prepayment calculator
               - Compare different loan scenarios
               - Download results as PDF
            
            3. User Interface:
               - Clean, responsive web interface
               - Input validation and error handling
               - Real-time calculation as user types
               - Mobile-friendly design
               - Professional styling with charts/graphs
            
            4. Technical Requirements:
               - Python backend with FastAPI
               - React frontend with modern UI components
               - Data visualization using charts
               - REST API endpoints for calculations
               - Proper error handling and validation
            """
        elif "Math" in name:
            requirements = """
            Create a comprehensive Math Calculator with the following features:
            
            1. Basic Operations:
               - Addition, subtraction, multiplication, division
               - Percentage calculations
               - Square root, power operations
               - Clear and all-clear functions
            
            2. Scientific Functions:
               - Trigonometric functions (sin, cos, tan)
               - Logarithmic functions (log, ln)
               - Exponential functions
               - Factorial calculations
               - Constants (π, e)
            
            3. Advanced Features:
               - Expression parsing and evaluation
               - History of calculations
               - Memory functions (M+, M-, MR, MC)
               - Keyboard support
               - Copy/paste functionality
            
            4. User Interface:
               - Calculator-style button layout
               - Display showing current operation and result
               - Responsive design for different screen sizes
               - Visual feedback for button presses
               - Professional calculator appearance
            
            5. Technical Requirements:
               - Python backend with FastAPI for complex calculations
               - React frontend with calculator UI
               - Expression parser for complex mathematical expressions
               - REST API endpoints for calculations
               - Client-side and server-side validation
            """
        else:
            requirements = f"Create a comprehensive {name.lower()} application with web interface, backend API, and full functionality."
        
        payload = {
            "name": name,
            "type": type,
            "language": language,
            "capabilities": capabilities,
            "description": f"A comprehensive {name} with {', '.join(capabilities)} capabilities",
            "requirements": requirements,
            "framework": "fastapi",
            "config": {
                "auto_deploy": False,
                "enable_logging": True,
                "max_memory": "512MB",
                "timeout": 300,
                "generate_frontend": True,
                "generate_tests": True
            }
        }
        
        try:
            print(f"🔄 Creating {name}...")
            response = self.session.post(f"{self.base_url}/api/v1/agents", json=payload, timeout=TIMEOUT)
            
            if response.status_code in [200, 201]:
                agent_data = response.json()
                agent_id = agent_data.get('id')
                print(f"✅ {name} created successfully - ID: {agent_id}")
                return agent_data
            else:
                print(f"❌ Failed to create {name} - Status: {response.status_code}")
                try:
                    error_detail = response.json()
                    print(f"   Error details: {error_detail}")
                except:
                    print(f"   Response: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Exception creating {name}: {e}")
            return None

    def generate_code(self, agent_id: str, agent_name: str) -> Optional[Dict[Any, Any]]:
        """Generate code for an agent"""
        payload = {
            "agent_id": agent_id,
            "generate_frontend": True,
            "generate_backend": True,
            "frontend_framework": "react",
            "styling": "tailwind",
            "include_tests": True
        }
        
        try:
            print(f"🔄 Generating code for {agent_name}...")
            response = self.session.post(f"{self.base_url}/api/v1/generation/generate", json=payload, timeout=TIMEOUT * 2)
            
            if response.status_code == 200:
                generation_data = response.json()
                print(f"✅ Code generated for {agent_name}")
                return generation_data
            else:
                print(f"❌ Failed to generate code for {agent_name} - Status: {response.status_code}")
                try:
                    error_detail = response.json()
                    print(f"   Error details: {error_detail}")
                except:
                    print(f"   Response: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Exception generating code for {agent_name}: {e}")
            return None

    def deploy_agent(self, agent_id: str, agent_name: str) -> Optional[str]:
        """Deploy an agent and return frontend URL"""
        try:
            print(f"🔄 Deploying {agent_name}...")
            response = self.session.post(f"{self.base_url}/api/v1/agents/{agent_id}/deploy", timeout=TIMEOUT * 3)
            
            if response.status_code == 200:
                deployment_data = response.json()
                frontend_url = deployment_data.get('frontend_url') or deployment_data.get('deployment_url')
                backend_url = deployment_data.get('backend_url')
                
                print(f"✅ {agent_name} deployed successfully")
                if frontend_url:
                    print(f"   Frontend URL: {frontend_url}")
                if backend_url:
                    print(f"   Backend URL: {backend_url}")
                    
                return frontend_url
            else:
                print(f"❌ Failed to deploy {agent_name} - Status: {response.status_code}")
                try:
                    error_detail = response.json()
                    print(f"   Error details: {error_detail}")
                except:
                    print(f"   Response: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Exception deploying {agent_name}: {e}")
            return None

    def test_frontend(self, url: str, agent_name: str) -> bool:
        """Test if frontend is accessible and functional"""
        if not url:
            print(f"❌ No frontend URL provided for {agent_name}")
            return False
            
        try:
            print(f"🔄 Testing {agent_name} frontend...")
            response = self.session.get(url, timeout=10)
            
            if response.status_code == 200:
                content = response.text.lower()
                # Check for signs of a functional frontend
                if any(keyword in content for keyword in ['calculator', 'emi', 'math', 'react', 'app']):
                    print(f"✅ {agent_name} frontend is functional!")
                    return True
                else:
                    print(f"⚠️  {agent_name} frontend is accessible but might be placeholder content")
                    return True
            else:
                print(f"❌ {agent_name} frontend returned status: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Error testing {agent_name} frontend: {e}")
            return False

    def check_deployment_status(self, agent_id: str, agent_name: str) -> Optional[Dict[Any, Any]]:
        """Check deployment status of an agent"""
        try:
            response = self.session.get(f"{self.base_url}/api/v1/agents/{agent_id}/deployment", timeout=TIMEOUT)
            if response.status_code == 200:
                return response.json()
            else:
                print(f"⚠️  Could not get deployment status for {agent_name}")
                return None
        except Exception as e:
            print(f"⚠️  Error checking deployment status for {agent_name}: {e}")
            return None

    def wait_for_generation(self, agent_id: str, agent_name: str, max_wait: int = 300) -> bool:
        """Wait for agent code generation to complete"""
        import time
        
        print(f"⏳ Waiting for {agent_name} code generation to complete...")
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            try:
                # Check agent status
                response = self.session.get(f"{self.base_url}/api/v1/agents/{agent_id}", timeout=10)
                if response.status_code == 200:
                    agent_data = response.json()
                    config = agent_data.get('config', {})
                    
                    if config.get('code_generated'):
                        print(f"✅ Code generation completed for {agent_name}")
                        return True
                        
                    # Check deployment status
                    deployment_status = self.check_deployment_status(agent_id, agent_name)
                    if deployment_status and deployment_status.get('deployed'):
                        print(f"✅ {agent_name} is already deployed")
                        return True
                        
                print(f"⏳ Still generating code for {agent_name}... ({int(time.time() - start_time)}s)")
                time.sleep(10)
                
            except Exception as e:
                print(f"⚠️  Error checking generation status: {e}")
                time.sleep(10)
        
        print(f"⏰ Code generation timeout for {agent_name} after {max_wait}s")
        return False

    def list_agents(self) -> list:
        """List all agents"""
        try:
            response = self.session.get(f"{self.base_url}/api/v1/agents", timeout=TIMEOUT)
            if response.status_code == 200:
                data = response.json()
                return data.get('agents', []) if isinstance(data, dict) else data
            else:
                print(f"❌ Failed to list agents - Status: {response.status_code}")
                return []
        except Exception as e:
            print(f"❌ Error listing agents: {e}")
            return []

def main():
    print("🚀 AI Agent Platform - Agent Creation & Deployment")
    print("=" * 60)
    
    manager = AgentManager()
    
    # Check backend health
    if not manager.check_backend():
        print("\n💡 To start the backend, run one of these commands:")
        print("   cd /Users/<USER>/workspaces/git/ai-agent/backend")
        print("   source venv/bin/activate")
        print("   python -m uvicorn src.main:app --host 0.0.0.0 --port 8000 --reload")
        return 1
    
    # Agent configurations
    agents_config = [
        {
            "name": "EMI Calculator",
            "type": "fullstack",
            "language": "python",
            "capabilities": ["web_integration", "data_analysis", "visualization"]
        },
        {
            "name": "Math Calculator",
            "type": "fullstack", 
            "language": "python",
            "capabilities": ["web_integration", "data_analysis", "visualization"]
        }
    ]
    
    successful_deployments = []
    
    for config in agents_config:
        print(f"\n{'='*20} {config['name']} {'='*20}")
        
        # Create agent (this will automatically trigger code generation if requirements are provided)
        agent = manager.create_agent(
            name=config['name'],
            type=config['type'],
            language=config['language'],
            capabilities=config['capabilities']
        )
        if not agent:
            continue
            
        agent_id = agent.get('id')
        agent_name = config['name']
        
        # Since agents with requirements trigger automatic generation, wait for it to complete
        if not manager.wait_for_generation(agent_id, agent_name, max_wait=300):
            print(f"⚠️  Proceeding with {agent_name} deployment despite generation timeout")
        
        # Deploy agent (if not already deployed by background task)
        deployment_status = manager.check_deployment_status(agent_id, agent_name)
        
        if deployment_status and deployment_status.get('deployed'):
            frontend_url = deployment_status.get('frontend_url') or deployment_status.get('url')
            print(f"✅ {agent_name} is already deployed at: {frontend_url}")
        else:
            frontend_url = manager.deploy_agent(agent_id, agent_name)
            if not frontend_url:
                continue
                
            # Wait for deployment to stabilize
            print("⏳ Waiting for deployment to stabilize...")
            time.sleep(15)
        
        # Test frontend
        if manager.test_frontend(frontend_url, agent_name):
            successful_deployments.append({
                'name': agent_name,
                'id': agent_id,
                'frontend_url': frontend_url
            })
    
    # Summary
    print(f"\n{'='*60}")
    print("📊 DEPLOYMENT SUMMARY")
    print(f"{'='*60}")
    
    if successful_deployments:
        print(f"✅ Successfully deployed {len(successful_deployments)} agents:")
        for deployment in successful_deployments:
            print(f"   • {deployment['name']}")
            print(f"     ID: {deployment['id']}")
            print(f"     URL: {deployment['frontend_url']}")
            print()
        
        print("🎉 All agents are ready for use!")
        print("\n📋 Next steps:")
        print("1. Visit each frontend URL to test functionality")
        print("2. Verify that agents show actual functional screens (not placeholders)")
        print("3. Test the calculator features in each frontend")
        
    else:
        print("❌ No agents were successfully deployed.")
        print("\n🔧 Troubleshooting:")
        print("1. Check that the backend is running properly")
        print("2. Review the error messages above")
        print("3. Check backend logs for additional details")
    
    return 0 if successful_deployments else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)