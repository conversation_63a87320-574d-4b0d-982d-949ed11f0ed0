---
# Terraform-generated Ansible inventory for ${environment} environment
all:
  hosts:
    ${instance_name}:
      ansible_host: ${external_ip != "" ? external_ip : instance_ip}
      ansible_user: ansible
      ansible_ssh_private_key_file: "{{ lookup('env', 'ANSIBLE_SSH_KEY') }}"
      ansible_ssh_common_args: '-o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null'
      
      # GCP metadata
      gcp_project_id: ${project_id}
      gcp_zone: ${zone}
      gcp_instance_name: ${instance_name}
      environment: ${environment}
      
      # Network information
      internal_ip: ${instance_ip}
      external_ip: ${external_ip}
      
  children:
    microservices:
      hosts:
        ${instance_name}:
    ${environment}:
      hosts:
        ${instance_name}: