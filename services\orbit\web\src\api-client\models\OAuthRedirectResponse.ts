/* tslint:disable */
/* eslint-disable */
/**
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface OAuthRedirectResponse
 */
export interface OAuthRedirectResponse {
    /**
     * 
     * @type {string}
     * @memberof OAuthRedirectResponse
     */
    url?: string;
}

/**
 * Check if a given object implements the OAuthRedirectResponse interface.
 */
export function instanceOfOAuthRedirectResponse(value: object): value is OAuthRedirectResponse {
    return true;
}

export function OAuthRedirectResponseFromJSON(json: any): OAuthRedirectResponse {
    return OAuthRedirectResponseFromJSONTyped(json, false);
}

export function OAuthRedirectResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): OAuthRedirectResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'url': json['url'] == null ? undefined : json['url'],
    };
}

  export function OAuthRedirectResponseToJSON(json: any): OAuthRedirectResponse {
      return OAuthRedirectResponseToJSONTyped(json, false);
  }

  export function OAuthRedirectResponseToJSONTyped(value?: OAuthRedirectResponse | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'url': value['url'],
    };
}

