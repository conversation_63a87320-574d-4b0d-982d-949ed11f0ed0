"""
Unified AI Service - Central point for all AI provider interactions
Supports OpenAI, <PERSON><PERSON>ic (<PERSON>), and Google AI providers
"""

import os
import json
import asyncio
from typing import Dict, Any, List, Optional, Union
from enum import Enum
from dataclasses import dataclass
import logging
import anthropic
import google.generativeai as genai
from google.generativeai.types import Harm<PERSON>ategory, HarmBlockThreshold

logger = logging.getLogger(__name__)

class AIProvider(str, Enum):
    """Supported AI providers"""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GOOGLE = "google"

class AICapability(str, Enum):
    """AI capabilities for different use cases"""
    TEXT_GENERATION = "text_generation"
    CODE_GENERATION = "code_generation"
    ANALYSIS = "analysis"
    PLANNING = "planning"
    REQUIREMENTS_PARSING = "requirements_parsing"
    CONFIGURATION = "configuration"

@dataclass
class AIRequest:
    """Unified AI request structure"""
    prompt: str
    capability: AICapability
    max_tokens: int = 4000
    temperature: float = 0.7
    system_message: Optional[str] = None
    context: Optional[Dict[str, Any]] = None
    format_json: bool = False

@dataclass
class AIResponse:
    """Unified AI response structure"""
    content: str
    provider: AIProvider
    model: str
    usage: Dict[str, Any]
    metadata: Dict[str, Any]

class AIService:
    """Unified AI service for all providers"""
    
    def __init__(self):
        self.providers = {}
        self._providers_initialized = False
        self._capability_routing = None
    
    def _ensure_providers_initialized(self):
        """Ensure providers are initialized (lazy loading)"""
        if not self._providers_initialized:
            self._setup_providers()
            self._capability_routing = self._setup_capability_routing()
            self._providers_initialized = True
    
    def _setup_providers(self):
        """Initialize all available AI providers"""
        
        # OpenAI setup
        openai_key = os.getenv("OPENAI_API_KEY")
        if openai_key and openai_key != "your_openai_api_key_here":
            try:
                from openai import OpenAI
                self.providers[AIProvider.OPENAI] = {
                    "client": OpenAI(api_key=openai_key),
                    "models": {
                        "fast": "gpt-3.5-turbo",
                        "smart": "gpt-4",
                        "code": "gpt-4"
                    }
                }
                logger.info("OpenAI provider initialized")
            except Exception as e:
                logger.error(f"Failed to initialize OpenAI: {e}")
        
        # Anthropic setup
        anthropic_key = os.getenv("ANTHROPIC_API_KEY")
        if anthropic_key and anthropic_key != "your_anthropic_api_key_here":
            try:
                self.providers[AIProvider.ANTHROPIC] = {
                    "client": anthropic.Anthropic(api_key=anthropic_key),
                    "models": {
                        "fast": "claude-3-haiku-20240307",
                        "smart": "claude-3-5-sonnet-20241022",
                        "code": "claude-3-5-sonnet-20241022"
                    }
                }
                logger.info("Anthropic provider initialized")
            except Exception as e:
                logger.error(f"Failed to initialize Anthropic: {e}")
        
        # Google AI setup
        google_key = os.getenv("GOOGLE_API_KEY")
        if google_key and google_key != "your_google_api_key_here":
            try:
                genai.configure(api_key=google_key)
                self.providers[AIProvider.GOOGLE] = {
                    "client": genai,
                    "models": {
                        "fast": "gemini-1.5-flash",
                        "smart": "gemini-1.5-pro",
                        "code": "gemini-1.5-pro"
                    }
                }
                logger.info("Google AI provider initialized")
            except Exception as e:
                logger.error(f"Failed to initialize Google AI: {e}")
    
    def _setup_capability_routing(self) -> Dict[AICapability, List[AIProvider]]:
        """Define preferred providers for each capability"""
        return {
            AICapability.TEXT_GENERATION: [AIProvider.GOOGLE, AIProvider.OPENAI, AIProvider.ANTHROPIC],
            AICapability.CODE_GENERATION: [AIProvider.GOOGLE, AIProvider.OPENAI, AIProvider.ANTHROPIC],
            AICapability.ANALYSIS: [AIProvider.GOOGLE, AIProvider.OPENAI, AIProvider.ANTHROPIC],
            AICapability.PLANNING: [AIProvider.GOOGLE, AIProvider.OPENAI, AIProvider.ANTHROPIC],
            AICapability.REQUIREMENTS_PARSING: [AIProvider.GOOGLE, AIProvider.OPENAI, AIProvider.ANTHROPIC],
            AICapability.CONFIGURATION: [AIProvider.GOOGLE, AIProvider.OPENAI, AIProvider.ANTHROPIC]
        }
    
    def get_available_providers(self) -> List[AIProvider]:
        """Get list of available/configured providers"""
        self._ensure_providers_initialized()
        return list(self.providers.keys())
    
    def _select_provider(self, capability: AICapability, preferred_provider: Optional[AIProvider] = None) -> AIProvider:
        """Select best available provider for capability"""
        self._ensure_providers_initialized()
        if preferred_provider and preferred_provider in self.providers:
            return preferred_provider
        
        # Use capability routing
        preferred_providers = self._capability_routing.get(capability, list(self.providers.keys()))
        
        for provider in preferred_providers:
            if provider in self.providers:
                return provider
        
        # Fallback to any available provider
        if self.providers:
            return next(iter(self.providers.keys()))
        
        raise ValueError("No AI providers are configured")
    
    async def generate(self, request: AIRequest, preferred_provider: Optional[AIProvider] = None) -> AIResponse:
        """Generate AI response using best available provider"""
        self._ensure_providers_initialized()
        
        provider = self._select_provider(request.capability, preferred_provider)
        
        try:
            if provider == AIProvider.OPENAI:
                return await self._generate_openai(request)
            elif provider == AIProvider.ANTHROPIC:
                return await self._generate_anthropic(request)
            elif provider == AIProvider.GOOGLE:
                return await self._generate_google(request)
            else:
                raise ValueError(f"Unsupported provider: {provider}")
        
        except Exception as e:
            logger.error(f"Failed to generate with {provider}: {e}")
            
            # Try fallback providers
            fallback_providers = [p for p in self.providers.keys() if p != provider]
            for fallback in fallback_providers:
                try:
                    logger.info(f"Trying fallback provider: {fallback}")
                    if fallback == AIProvider.OPENAI:
                        return await self._generate_openai(request)
                    elif fallback == AIProvider.ANTHROPIC:
                        return await self._generate_anthropic(request)
                    elif fallback == AIProvider.GOOGLE:
                        return await self._generate_google(request)
                except Exception as fallback_e:
                    logger.error(f"Fallback {fallback} also failed: {fallback_e}")
                    continue
            
            raise Exception(f"All AI providers failed. Last error: {e}")
    
    async def _generate_openai(self, request: AIRequest) -> AIResponse:
        """Generate response using OpenAI"""
        provider_config = self.providers[AIProvider.OPENAI]
        client = provider_config["client"]
        model = provider_config["models"]["smart"]
        
        messages = []
        if request.system_message:
            messages.append({"role": "system", "content": request.system_message})
        messages.append({"role": "user", "content": request.prompt})
        
        # Use new OpenAI client interface with timeout
        response = await asyncio.wait_for(
            asyncio.get_event_loop().run_in_executor(
                None,
                lambda: client.chat.completions.create(
                    model=model,
                    messages=messages,
                    max_tokens=request.max_tokens,
                    temperature=request.temperature
                )
            ),
            timeout=30.0  # 30 second timeout
        )
        
        return AIResponse(
            content=response.choices[0].message.content,
            provider=AIProvider.OPENAI,
            model=model,
            usage=response.usage.model_dump() if response.usage else {},
            metadata={"format_json": request.format_json}
        )
    
    async def _generate_anthropic(self, request: AIRequest) -> AIResponse:
        """Generate response using Anthropic Claude"""
        provider_config = self.providers[AIProvider.ANTHROPIC]
        client = provider_config["client"]
        model = provider_config["models"]["smart"]
        
        system_message = request.system_message or "You are a helpful AI assistant."
        
        # Add timeout for API call
        message = await asyncio.wait_for(
            asyncio.get_event_loop().run_in_executor(
                None,
                lambda: client.messages.create(
                    model=model,
                    max_tokens=request.max_tokens,
                    temperature=request.temperature,
                    system=system_message,
                    messages=[{"role": "user", "content": request.prompt}]
                )
            ),
            timeout=30.0  # 30 second timeout
        )
        
        return AIResponse(
            content=message.content[0].text,
            provider=AIProvider.ANTHROPIC,
            model=model,
            usage={
                "input_tokens": message.usage.input_tokens,
                "output_tokens": message.usage.output_tokens
            },
            metadata={"format_json": request.format_json}
        )
    
    async def _generate_google(self, request: AIRequest) -> AIResponse:
        """Generate response using Google AI"""
        provider_config = self.providers[AIProvider.GOOGLE]
        model_name = provider_config["models"]["smart"]
        
        model = genai.GenerativeModel(
            model_name=model_name,
            safety_settings={
                HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
            }
        )
        
        full_prompt = request.prompt
        if request.system_message:
            full_prompt = f"{request.system_message}\n\n{request.prompt}"
        
        response = await asyncio.wait_for(
            asyncio.get_event_loop().run_in_executor(
                None,
                lambda: model.generate_content(
                    full_prompt,
                    generation_config=genai.types.GenerationConfig(
                        max_output_tokens=request.max_tokens,
                        temperature=request.temperature
                    )
                )
            ),
            timeout=30.0  # 30 second timeout
        )
        
        return AIResponse(
            content=response.text,
            provider=AIProvider.GOOGLE,
            model=model_name,
            usage={
                "prompt_token_count": response.usage_metadata.prompt_token_count if response.usage_metadata else 0,
                "completion_token_count": response.usage_metadata.candidates_token_count if response.usage_metadata else 0
            },
            metadata={"format_json": request.format_json}
        )
    
    async def parse_requirements_to_config(self, requirements: str) -> Dict[str, Any]:
        """Parse natural language requirements into agent configuration"""
        
        system_message = """You are an expert AI agent configuration parser. 
        Analyze the provided requirements and extract agent configuration parameters.
        
        Return ONLY a valid JSON object with the following structure:
        {
            "name": "suggested-agent-name",
            "type": "api_service|background_worker|data_processor|integration",
            "language": "python|javascript|typescript",
            "framework": "fastapi|flask|express|custom",
            "capabilities": ["list", "of", "relevant", "capabilities"],
            "deployment": {
                "port": 8000,
                "resources": {
                    "cpu_limit": "500m",
                    "memory_limit": "512Mi"
                },
                "auto_scale": false,
                "replicas": 1
            },
            "advanced_options": {
                "use_ai_workflow": true,
                "testing_enabled": true,
                "documentation_level": "standard"
            },
            "reasoning": "Brief explanation of configuration choices"
        }
        
        Available capabilities: conversation, data_analysis, data_processing, system_monitoring, 
        web_scraping, api_integration, web_integration, database, nlp, computer_vision, 
        task_planning, information_retrieval, visualization, statistical_analysis, 
        report_generation, etl_operations, data_validation, format_conversion, health_checks, 
        alerting, metrics_collection, ui_interaction, domain_expertise, custom_logic, 
        specialized_processing, knowledge_retrieval, workflow_management, agent_coordination, 
        task_distribution, result_aggregation.
        
        Choose the most appropriate type, language, framework, and capabilities based on the requirements.
        Return ONLY the JSON object, no additional text or markdown.
        """
        
        request = AIRequest(
            prompt=f"Requirements: {requirements}",
            capability=AICapability.REQUIREMENTS_PARSING,
            system_message=system_message,
            format_json=True,
            temperature=0.3
        )
        
        try:
            response = await self.generate(request)
            logger.info(f"AI response for requirements parsing: {response.content[:200]}...")
            
            # Clean the response content
            content = response.content.strip()
            
            # Remove markdown code blocks if present
            if content.startswith("```json"):
                content = content[7:]
            elif content.startswith("```"):
                content = content[3:]
            if content.endswith("```"):
                content = content[:-3]
            content = content.strip()
            
            # Try to parse as JSON
            config = json.loads(content)
            logger.info("Successfully parsed agent configuration from AI response")
            return config
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON parsing error: {e}")
            logger.error(f"Failed to parse content: {content[:500]}...")
            
            # Try to extract JSON from response
            import re
            json_match = re.search(r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}', content, re.DOTALL)
            if json_match:
                try:
                    config = json.loads(json_match.group())
                    logger.info("Successfully extracted JSON from response using regex")
                    return config
                except json.JSONDecodeError as e2:
                    logger.error(f"Failed to parse extracted JSON: {e2}")
                    
        except Exception as e:
            logger.error(f"Error during requirements parsing: {type(e).__name__}: {e}")
            
        # Ultimate fallback: return basic config
        logger.warning("Using default configuration due to parsing errors")
        return {
            "name": "generated-agent",
            "type": "api_service",
            "language": "python",
            "framework": "fastapi", 
            "capabilities": ["api_integration"],
            "deployment": {
                "port": 8000,
                "resources": {"cpu_limit": "500m", "memory_limit": "512Mi"},
                "auto_scale": False,
                "replicas": 1
            },
            "advanced_options": {
                "use_ai_workflow": True,
                "testing_enabled": True,
                "documentation_level": "standard"
            },
            "reasoning": "Default configuration due to parsing error - check logs for details"
        }
    
    async def analyze_code(self, code: str, language: str) -> Dict[str, Any]:
        """Analyze code for improvements and insights"""
        
        system_message = f"""You are an expert {language} code analyzer. 
        Analyze the provided code and return insights in JSON format:
        {{
            "quality_score": 85,
            "issues": ["list of issues found"],
            "suggestions": ["list of improvement suggestions"],
            "complexity": "low|medium|high",
            "maintainability": "good|fair|poor",
            "security_concerns": ["list of security issues"],
            "performance_notes": ["list of performance considerations"]
        }}"""
        
        request = AIRequest(
            prompt=f"Code to analyze:\n\n```{language}\n{code}\n```",
            capability=AICapability.ANALYSIS,
            system_message=system_message,
            format_json=True,
            temperature=0.2
        )
        
        response = await self.generate(request)
        
        try:
            return json.loads(response.content)
        except json.JSONDecodeError:
            return {
                "quality_score": 70,
                "issues": [],
                "suggestions": [],
                "complexity": "medium",
                "maintainability": "fair",
                "security_concerns": [],
                "performance_notes": []
            }
    
    async def generate_documentation(self, code: str, language: str, doc_type: str = "api") -> str:
        """Generate documentation for code"""
        
        system_message = f"""You are a technical documentation expert. 
        Generate comprehensive {doc_type} documentation for the provided {language} code.
        Include API endpoints, parameters, responses, examples, and usage instructions."""
        
        request = AIRequest(
            prompt=f"Generate {doc_type} documentation for this {language} code:\n\n```{language}\n{code}\n```",
            capability=AICapability.TEXT_GENERATION,
            system_message=system_message,
            temperature=0.3
        )
        
        response = await self.generate(request)
        return response.content

# Global AI service instance
ai_service = AIService()