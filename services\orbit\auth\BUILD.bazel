load("@rules_go//go:def.bzl", "go_binary", "go_library")
load("@rules_oci//oci:defs.bzl", "oci_image")
load("@rules_pkg//pkg:tar.bzl", "pkg_tar")

go_library(
    name = "auth_service_lib",
    srcs = ["main.go"],
    importpath = "github.com/TwoDotAi/mono/services/orbit/auth",
    visibility = ["//visibility:private"],
    deps = [
        "//shared/go/logging",
        "//services/orbit/auth/config",
        "//services/orbit/auth/database",
        "//services/orbit/auth/handlers",
        "//services/orbit/auth/middleware",
        "@com_github_gin_gonic_gin//:gin",
        "@org_uber_go_zap//:zap",
    ],
)

go_binary(
    name = "auth",
    embed = [":auth_service_lib"],
    visibility = ["//visibility:public"],
)

# Package binary for container
pkg_tar(
    name = "auth_binary_tar",
    srcs = [":auth"],
    package_dir = "/",
)

# Test suite for all auth components - you can run this with: bazel test //services/orbit/auth:all
# Or just: bazel test //services/orbit/auth/...
test_suite(
    name = "auth_tests",
    tests = [
        "//services/orbit/auth/config:config_test",
        "//services/orbit/auth/database:database_test",
        "//services/orbit/auth/handlers:handlers_test",
        "//services/orbit/auth/middleware:middleware_test",
        "//services/orbit/auth/types:types_test",
    ],
)

# OCI image for auth service
oci_image(
    name = "auth_service_image",
    base = "@distroless_base",
    entrypoint = ["/auth"],
    env = {
        "PORT": "8004",
        "GIN_MODE": "release",
    },
    exposed_ports = ["8004"],
    tars = [":auth_binary_tar"],
    visibility = ["//visibility:public"],
)
