/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export interface paths {
    "/auth/session": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get current session
         * @description Retrieve the current user session
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Session information */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["Session"];
                    };
                };
                /** @description Not authenticated */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/auth/signin": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Sign in with email/password
         * @description Authenticate user with email and password
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody: {
                content: {
                    "application/json": {
                        /** Format: email */
                        email: string;
                        password: string;
                    };
                };
            };
            responses: {
                /** @description Authentication successful */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["AuthResponse"];
                    };
                };
                /** @description Invalid credentials */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/auth/signin/oauth": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Sign in with OAuth
         * @description Authenticate user with OAuth provider (Google)
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody: {
                content: {
                    "application/json": {
                        /** @enum {string} */
                        provider: "google";
                        /** Format: uri */
                        redirectTo?: string;
                    };
                };
            };
            responses: {
                /** @description OAuth redirect URL */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            /** Format: uri */
                            url?: string;
                        };
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/auth/callback/{provider}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * OAuth callback
         * @description Handle OAuth provider callback with authorization code
         */
        get: {
            parameters: {
                query: {
                    code: string;
                    state: string;
                    error?: string;
                };
                header?: never;
                path: {
                    provider: "google";
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description OAuth authentication successful */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["AuthResponse"];
                    };
                };
                /** @description Invalid request or OAuth error */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["Error"];
                    };
                };
                /** @description Authentication failed */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["Error"];
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/auth/signout": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Sign out
         * @description End the current user session
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Successfully signed out */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Not authenticated */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/auth/user": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get current user
         * @description Retrieve current authenticated user information
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description User information */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["User"];
                    };
                };
                /** @description Not authenticated */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/users/profile": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get user profile
         * @description Retrieve the current user's profile information
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description User profile */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["UserProfile"];
                    };
                };
                /** @description Not authenticated */
                401: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        /**
         * Update user profile
         * @description Update the current user's profile information
         */
        put: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody: {
                content: {
                    "application/json": components["schemas"]["UserProfileUpdate"];
                };
            };
            responses: {
                /** @description Profile updated successfully */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["UserProfile"];
                    };
                };
                /** @description Invalid input */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/companies": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * List companies
         * @description Retrieve a list of all active companies
         */
        get: {
            parameters: {
                query?: {
                    /** @description Filter by company status */
                    status_id?: string;
                    /** @description Include soft-deleted companies */
                    include_deleted?: boolean;
                };
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description List of companies */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["Company"][];
                    };
                };
            };
        };
        put?: never;
        /**
         * Create company
         * @description Create a new company
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody: {
                content: {
                    "application/json": components["schemas"]["CompanyCreate"];
                };
            };
            responses: {
                /** @description Company created successfully */
                201: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["Company"];
                    };
                };
                /** @description Invalid input */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/companies/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get company
         * @description Retrieve a specific company by ID
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Company details */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["CompanyDetails"];
                    };
                };
                /** @description Company not found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        /**
         * Update company
         * @description Update an existing company
         */
        put: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    id: string;
                };
                cookie?: never;
            };
            requestBody: {
                content: {
                    "application/json": components["schemas"]["CompanyUpdate"];
                };
            };
            responses: {
                /** @description Company updated successfully */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["Company"];
                    };
                };
                /** @description Company not found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        post?: never;
        /**
         * Delete company
         * @description Soft delete a company (sets is_deleted to true)
         */
        delete: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Company deleted successfully */
                204: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Company not found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/companies/{id}/status": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        /**
         * Update company status
         * @description Update the status of a company
         */
        put: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    id: string;
                };
                cookie?: never;
            };
            requestBody: {
                content: {
                    "application/json": {
                        /** Format: uuid */
                        company_status_id: string;
                    };
                };
            };
            responses: {
                /** @description Company status updated successfully */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Company not found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
            };
        };
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/company-statuses": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * List company statuses
         * @description Retrieve all company statuses ordered by pipeline order
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description List of company statuses */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["CompanyStatus"][];
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/contacts": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * List contacts
         * @description Retrieve a list of all active contacts with their associated companies
         */
        get: {
            parameters: {
                query?: {
                    /** @description Filter by company ID */
                    company_id?: string;
                    /** @description Only return contacts without a company */
                    unlinked?: boolean;
                    /** @description Include soft-deleted contacts */
                    include_deleted?: boolean;
                };
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description List of contacts */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["ContactWithCompany"][];
                    };
                };
                /** @description Invalid input */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["schemas_ValidationError"];
                    };
                };
            };
        };
        put?: never;
        /**
         * Create contact
         * @description Create a new contact
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody: {
                content: {
                    "application/json": components["schemas"]["ContactCreate"];
                };
            };
            responses: {
                /** @description Contact created successfully */
                201: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["Contact"];
                    };
                };
                /** @description Invalid input */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["schemas_ValidationError"];
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/contacts/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get contact
         * @description Retrieve a specific contact by ID
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Contact details */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["ContactDetails"];
                    };
                };
                /** @description Contact not found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["ErrorResponse"];
                    };
                };
            };
        };
        /**
         * Update contact
         * @description Update an existing contact
         */
        put: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    id: string;
                };
                cookie?: never;
            };
            requestBody: {
                content: {
                    "application/json": components["schemas"]["ContactUpdate"];
                };
            };
            responses: {
                /** @description Contact updated successfully */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["Contact"];
                    };
                };
                /** @description Invalid input */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["schemas_ValidationError"];
                    };
                };
                /** @description Contact not found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["ErrorResponse"];
                    };
                };
            };
        };
        post?: never;
        /**
         * Delete contact
         * @description Soft delete a contact (sets is_deleted to true)
         */
        delete: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Contact deleted successfully */
                204: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Contact not found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["ErrorResponse"];
                    };
                };
            };
        };
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/contacts/{id}/company": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        /**
         * Link contact to company
         * @description Associate a contact with a company
         */
        put: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    id: string;
                };
                cookie?: never;
            };
            requestBody: {
                content: {
                    "application/json": {
                        /** Format: uuid */
                        company_id: string;
                    };
                };
            };
            responses: {
                /** @description Contact linked to company successfully */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Invalid input */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["schemas_ValidationError"];
                    };
                };
                /** @description Contact or company not found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["ErrorResponse"];
                    };
                };
            };
        };
        post?: never;
        /**
         * Unlink contact from company
         * @description Remove the association between a contact and company
         */
        delete: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Contact unlinked from company successfully */
                204: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Contact not found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["ErrorResponse"];
                    };
                };
            };
        };
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/deals": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * List deals
         * @description Retrieve a list of all deals with company and stage information
         */
        get: {
            parameters: {
                query?: {
                    /** @description Filter by deal stage ID */
                    stage_id?: string;
                    /** @description Filter by company ID */
                    company_id?: string;
                };
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description List of deals */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["DealWithDetails"][];
                    };
                };
                /** @description Invalid input */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["schemas_ValidationError"];
                    };
                };
            };
        };
        put?: never;
        /**
         * Create deal
         * @description Create a new deal
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody: {
                content: {
                    "application/json": components["schemas"]["DealCreate"];
                };
            };
            responses: {
                /** @description Deal created successfully */
                201: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["Deal"];
                    };
                };
                /** @description Invalid input */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["schemas_ValidationError"];
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/deals/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get deal
         * @description Retrieve a specific deal by ID
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Deal details */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["DealDetails"];
                    };
                };
                /** @description Deal not found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["ErrorResponse"];
                    };
                };
            };
        };
        /**
         * Update deal
         * @description Update an existing deal
         */
        put: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    id: string;
                };
                cookie?: never;
            };
            requestBody: {
                content: {
                    "application/json": components["schemas"]["DealUpdate"];
                };
            };
            responses: {
                /** @description Deal updated successfully */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["Deal"];
                    };
                };
                /** @description Invalid input */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["schemas_ValidationError"];
                    };
                };
                /** @description Deal not found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["ErrorResponse"];
                    };
                };
            };
        };
        post?: never;
        /**
         * Delete deal
         * @description Delete a deal
         */
        delete: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Deal deleted successfully */
                204: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Deal not found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["ErrorResponse"];
                    };
                };
            };
        };
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/deal-stages": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * List deal stages
         * @description Retrieve all deal stages ordered by pipeline order
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description List of deal stages */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["DealStage"][];
                    };
                };
            };
        };
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/interactions": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * List interactions
         * @description Retrieve a list of all interactions
         */
        get: {
            parameters: {
                query?: {
                    /** @description Filter by company ID */
                    company_id?: string;
                    /** @description Filter by contact ID */
                    contact_id?: string;
                    /** @description Filter by interaction type */
                    interaction_type?: string;
                    /** @description Filter interactions from this date */
                    from_date?: string;
                    /** @description Filter interactions to this date */
                    to_date?: string;
                    /** @description Number of interactions to return */
                    limit?: number;
                    /** @description Number of interactions to skip */
                    offset?: number;
                };
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description List of interactions */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            interactions?: components["schemas"]["InteractionWithDetails"][];
                            total_count?: number;
                            has_more?: boolean;
                        };
                    };
                };
                /** @description Invalid input */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["schemas_ValidationError"];
                    };
                };
            };
        };
        put?: never;
        /**
         * Create interaction
         * @description Create a new interaction record
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody: {
                content: {
                    "application/json": components["schemas"]["InteractionCreate"];
                };
            };
            responses: {
                /** @description Interaction created successfully */
                201: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["Interaction"];
                    };
                };
                /** @description Invalid input */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["schemas_ValidationError"];
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/interactions/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get interaction
         * @description Retrieve a specific interaction by ID
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Interaction details */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["InteractionDetails"];
                    };
                };
                /** @description Interaction not found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["ErrorResponse"];
                    };
                };
            };
        };
        /**
         * Update interaction
         * @description Update an existing interaction
         */
        put: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    id: string;
                };
                cookie?: never;
            };
            requestBody: {
                content: {
                    "application/json": components["schemas"]["InteractionUpdate"];
                };
            };
            responses: {
                /** @description Interaction updated successfully */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["Interaction"];
                    };
                };
                /** @description Invalid input */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["schemas_ValidationError"];
                    };
                };
                /** @description Interaction not found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["ErrorResponse"];
                    };
                };
            };
        };
        post?: never;
        /**
         * Delete interaction
         * @description Delete an interaction record
         */
        delete: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Interaction deleted successfully */
                204: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Interaction not found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["ErrorResponse"];
                    };
                };
            };
        };
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/arli/documents": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * List documents
         * @description Retrieve a list of processed documents
         */
        get: {
            parameters: {
                query?: {
                    /** @description Filter by tags */
                    filter_tags?: string[];
                    /** @description Filter by metadata properties */
                    metadata_filter?: Record<string, never>;
                    limit?: number;
                    offset?: number;
                };
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description List of documents */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            documents?: components["schemas"]["Document"][];
                            total_count?: number;
                            has_more?: boolean;
                        };
                    };
                };
                /** @description Invalid input */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["schemas_ValidationError"];
                    };
                };
            };
        };
        put?: never;
        /**
         * Create document
         * @description Create a new document for processing
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody: {
                content: {
                    "application/json": components["schemas"]["DocumentCreate"];
                };
            };
            responses: {
                /** @description Document created successfully */
                201: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["Document"];
                    };
                };
                /** @description Invalid input */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["schemas_ValidationError"];
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/arli/documents/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get document
         * @description Retrieve a specific document by ID
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Document details */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["DocumentDetails"];
                    };
                };
                /** @description Document not found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["ErrorResponse"];
                    };
                };
            };
        };
        /**
         * Update document
         * @description Update an existing document
         */
        put: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    id: string;
                };
                cookie?: never;
            };
            requestBody: {
                content: {
                    "application/json": components["schemas"]["DocumentUpdate"];
                };
            };
            responses: {
                /** @description Document updated successfully */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["Document"];
                    };
                };
                /** @description Invalid input */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["schemas_ValidationError"];
                    };
                };
                /** @description Document not found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["ErrorResponse"];
                    };
                };
            };
        };
        post?: never;
        /**
         * Delete document
         * @description Delete a document
         */
        delete: {
            parameters: {
                query?: never;
                header?: never;
                path: {
                    id: string;
                };
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description Document deleted successfully */
                204: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content?: never;
                };
                /** @description Document not found */
                404: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["ErrorResponse"];
                    };
                };
            };
        };
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/arli/documents/vectorize": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Vectorize document
         * @description Process a document through the AI vectorization pipeline
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody: {
                content: {
                    "application/json": {
                        /** Format: uuid */
                        document_id: string;
                        content: string;
                        metadata?: Record<string, never>;
                    };
                };
            };
            responses: {
                /** @description Document vectorized successfully */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": {
                            success?: boolean;
                            message?: string;
                            vector_id?: string;
                        };
                    };
                };
                /** @description Invalid input */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["schemas_ValidationError"];
                    };
                };
                /** @description Vectorization failed */
                500: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["ErrorResponse"];
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/arli/filter-tags": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * List filter tags
         * @description Retrieve all available filter tags
         */
        get: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody?: never;
            responses: {
                /** @description List of filter tags */
                200: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["FilterTag"][];
                    };
                };
            };
        };
        put?: never;
        /**
         * Create filter tag
         * @description Create a new filter tag
         */
        post: {
            parameters: {
                query?: never;
                header?: never;
                path?: never;
                cookie?: never;
            };
            requestBody: {
                content: {
                    "application/json": components["schemas"]["FilterTagCreate"];
                };
            };
            responses: {
                /** @description Filter tag created successfully */
                201: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["FilterTag"];
                    };
                };
                /** @description Invalid input */
                400: {
                    headers: {
                        [name: string]: unknown;
                    };
                    content: {
                        "application/json": components["schemas"]["schemas_ValidationError"];
                    };
                };
            };
        };
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
}
export type webhooks = Record<string, never>;
export interface components {
    schemas: {
        Session: {
            access_token?: string;
            refresh_token?: string;
            expires_in?: number;
            token_type?: string;
            user?: components["schemas"]["User"];
        };
        AuthResponse: {
            access_token?: string;
            refresh_token?: string;
            expires_in?: number;
            token_type?: string;
            user?: components["schemas"]["User"];
        };
        User: {
            /** Format: uuid */
            id?: string;
            /** Format: email */
            email?: string;
            /** Format: date-time */
            email_confirmed_at?: string;
            /** Format: date-time */
            created_at?: string;
            /** Format: date-time */
            updated_at?: string;
        };
        UserProfile: {
            /** Format: uuid */
            id?: string;
            full_name?: string;
            /** Format: uri */
            avatar_url?: string;
            timezone?: string;
            /** Format: date-time */
            created_at?: string;
            /** Format: date-time */
            updated_at?: string;
        };
        UserProfileUpdate: {
            full_name?: string;
            /** Format: uri */
            avatar_url?: string;
            timezone?: string;
        };
        Company: {
            /** Format: uuid */
            id?: string;
            name?: string;
            /** Format: uri */
            website?: string;
            phone?: string;
            address?: string;
            notes?: string;
            /** Format: uuid */
            company_status_id?: string;
            /** @default false */
            is_deleted: boolean;
            /** Format: date-time */
            created_at?: string;
            /** Format: date-time */
            updated_at?: string;
        };
        CompanyDetails: components["schemas"]["Company"] & {
            contacts?: components["schemas"]["Contact"][];
        };
        CompanyCreate: {
            name: string;
            /** Format: uri */
            website?: string;
            phone?: string;
            address?: string;
            notes?: string;
            /** Format: uuid */
            company_status_id?: string;
        };
        CompanyUpdate: {
            name?: string;
            /** Format: uri */
            website?: string;
            phone?: string;
            address?: string;
            notes?: string;
            /** Format: uuid */
            company_status_id?: string;
        };
        CompanyStatus: {
            /** Format: uuid */
            id?: string;
            name?: string;
            pipeline_order?: number;
            /** Format: date-time */
            created_at?: string;
        };
        CompanyInfo: {
            /** Format: uuid */
            id?: string;
            name?: string;
            is_deleted?: boolean;
        } | null;
        CompanyBasicInfo: {
            /** Format: uuid */
            id?: string;
            name?: string;
        } | null;
        Contact: {
            /** Format: uuid */
            id?: string;
            first_name?: string;
            last_name?: string;
            /** Format: email */
            email?: string;
            phone?: string;
            job_title?: string;
            /** Format: uuid */
            company_id?: string | null;
            /** Format: uuid */
            created_by?: string;
            /** @default false */
            is_deleted: boolean;
            /** Format: date-time */
            created_at?: string;
            /** Format: date-time */
            updated_at?: string;
        };
        ContactWithCompany: components["schemas"]["Contact"] & {
            company?: components["schemas"]["CompanyInfo"];
        };
        ContactDetails: components["schemas"]["Contact"] & {
            company?: components["schemas"]["CompanyBasicInfo"];
        };
        ContactCreate: {
            first_name: string;
            last_name: string;
            /** Format: email */
            email?: string;
            phone?: string;
            job_title?: string;
            /** Format: uuid */
            company_id?: string;
        };
        ContactUpdate: {
            first_name?: string;
            last_name?: string;
            /** Format: email */
            email?: string;
            phone?: string;
            job_title?: string;
            /** Format: uuid */
            company_id?: string | null;
        };
        Deal: {
            /** Format: uuid */
            id?: string;
            title?: string;
            description?: string;
            estimated_value?: number;
            /** Format: uuid */
            company_id?: string;
            /** Format: uuid */
            deal_stage_id?: string;
            /** Format: date */
            expected_close_date?: string;
            /** Format: uuid */
            created_by?: string;
            /** Format: date-time */
            created_at?: string;
            /** Format: date-time */
            updated_at?: string;
        };
        DealWithDetails: components["schemas"]["Deal"] & {
            company?: components["schemas"]["DealCompanyInfo"];
            deal_stage?: components["schemas"]["DealStageInfo"];
        };
        DealCompanyInfo: {
            /** Format: uuid */
            id?: string;
            name?: string;
        };
        DealStageInfo: {
            /** Format: uuid */
            id?: string;
            name?: string;
            pipeline_order?: number;
        };
        DealDetails: components["schemas"]["Deal"] & {
            company?: {
                /** Format: uuid */
                id?: string;
                name?: string;
                website?: string;
                phone?: string;
            };
            deal_stage?: {
                /** Format: uuid */
                id?: string;
                name?: string;
                pipeline_order?: number;
                is_closed_won?: boolean;
                is_closed_lost?: boolean;
            };
        };
        DealCreate: {
            title: string;
            description?: string;
            estimated_value?: number;
            /** Format: uuid */
            company_id: string;
            /** Format: uuid */
            deal_stage_id: string;
            /** Format: date */
            expected_close_date?: string;
        };
        DealUpdate: {
            title?: string;
            description?: string;
            estimated_value?: number;
            /** Format: uuid */
            company_id?: string;
            /** Format: uuid */
            deal_stage_id?: string;
            /** Format: date */
            expected_close_date?: string;
        };
        DealStage: {
            /** Format: uuid */
            id?: string;
            name?: string;
            pipeline_order?: number;
            /** @default false */
            is_closed_won: boolean;
            /** @default false */
            is_closed_lost: boolean;
            /** Format: date-time */
            created_at?: string;
        };
        Interaction: {
            /** Format: uuid */
            id?: string;
            /** Format: uuid */
            company_id?: string | null;
            /** Format: uuid */
            contact_id?: string | null;
            /** @enum {string} */
            interaction_type?: "email" | "phone" | "meeting" | "demo" | "proposal" | "follow-up" | "other";
            notes?: string;
            /** Format: date-time */
            interaction_datetime?: string;
            /** Format: uuid */
            created_by?: string;
            /** Format: date-time */
            created_at?: string;
            /** Format: date-time */
            updated_at?: string;
        };
        InteractionWithDetails: components["schemas"]["Interaction"] & {
            company?: {
                /** Format: uuid */
                id?: string;
                name?: string;
            } | null;
            contact?: {
                /** Format: uuid */
                id?: string;
                first_name?: string;
                last_name?: string;
                email?: string;
            } | null;
        };
        InteractionDetails: components["schemas"]["Interaction"] & {
            company?: {
                /** Format: uuid */
                id?: string;
                name?: string;
                website?: string;
                phone?: string;
            } | null;
            contact?: {
                /** Format: uuid */
                id?: string;
                first_name?: string;
                last_name?: string;
                email?: string;
                phone?: string;
                job_title?: string;
            } | null;
        };
        InteractionCreate: {
            /** Format: uuid */
            company_id?: string;
            /** Format: uuid */
            contact_id?: string;
            /** @enum {string} */
            interaction_type: "email" | "phone" | "meeting" | "demo" | "proposal" | "follow-up" | "other";
            notes: string;
            /** Format: date-time */
            interaction_datetime: string;
        };
        InteractionUpdate: {
            /** @enum {string} */
            interaction_type?: "email" | "phone" | "meeting" | "demo" | "proposal" | "follow-up" | "other";
            notes?: string;
            /** Format: date-time */
            interaction_datetime?: string;
            /** Format: uuid */
            company_id?: string;
            /** Format: uuid */
            contact_id?: string;
        };
        Document: {
            /** Format: uuid */
            id?: string;
            content?: string;
            metadata?: Record<string, never>;
            filter_tags?: string[];
            source_id?: string;
            /** Format: date-time */
            created_at?: string;
            /** Format: date-time */
            updated_at?: string;
        };
        DocumentDetails: components["schemas"]["Document"] & {
            vector_embeddings?: number[];
            /** @enum {string} */
            processing_status?: "pending" | "processing" | "completed" | "failed";
        };
        DocumentCreate: {
            content: string;
            metadata?: Record<string, never>;
            filter_tags?: string[];
            source_id?: string;
        };
        DocumentUpdate: {
            content?: string;
            metadata?: Record<string, never>;
            filter_tags?: string[];
            source_id?: string;
        };
        FilterTag: {
            /** Format: uuid */
            id?: string;
            name?: string;
            color?: string;
            /** Format: date-time */
            created_at?: string;
        };
        FilterTagCreate: {
            name: string;
            /** @default #3b82f6 */
            color: string;
        };
        Error: {
            code: string;
            message: string;
            details?: Record<string, never>;
        };
        ValidationError: {
            /** @example validation_error */
            code: string;
            /** @example Invalid input data */
            message: string;
            /** ValidationErrorDetails */
            details?: {
                field_errors?: {
                    [key: string]: string[];
                };
            };
        };
        schemas_ValidationError: {
            /** @example validation_error */
            code?: string;
            /** @example Invalid input data */
            message?: string;
            details?: Record<string, never>;
        };
        ErrorResponse: {
            error?: string;
            message?: string;
        };
    };
    responses: never;
    parameters: never;
    requestBodies: never;
    headers: never;
    pathItems: never;
}
export type $defs = Record<string, never>;
export type operations = Record<string, never>;
