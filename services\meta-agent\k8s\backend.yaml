apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-agent-backend
  namespace: ai-agent-platform
  labels:
    app: ai-agent-backend
    component: backend
    version: v1
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 1
  selector:
    matchLabels:
      app: ai-agent-backend
  template:
    metadata:
      labels:
        app: ai-agent-backend
        component: backend
        version: v1
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8090"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: ai-agent-backend-sa
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 2000
      initContainers:
      - name: db-migration
        image: ai-agent-platform/backend:latest
        command:
        - sh
        - -c
        - |
          echo "Waiting for database..."
          until pg_isready -h postgres -p 5432 -U $DATABASE_USER; do
            echo "Database not ready, waiting..."
            sleep 2
          done
          echo "Running database migrations..."
          python -m alembic upgrade head
          echo "Database setup complete."
        env:
        - name: DATABASE_USER
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: POSTGRES_USER
        envFrom:
        - configMapRef:
            name: ai-agent-platform-config
        - secretRef:
            name: ai-agent-platform-secrets
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m"
      containers:
      - name: backend
        image: ai-agent-platform/backend:latest
        imagePullPolicy: IfNotPresent
        ports:
        - name: http
          containerPort: 8000
          protocol: TCP
        - name: metrics
          containerPort: 8090
          protocol: TCP
        env:
        - name: HOST
          value: "0.0.0.0"
        - name: PORT
          value: "8000"
        - name: WORKERS
          value: "4"
        envFrom:
        - configMapRef:
            name: ai-agent-platform-config
        - secretRef:
            name: ai-agent-platform-secrets
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 20
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 10
        volumeMounts:
        - name: temp-storage
          mountPath: /tmp/agent-generation
        - name: logs
          mountPath: /app/logs
      volumes:
      - name: temp-storage
        emptyDir:
          sizeLimit: 5Gi
      - name: logs
        emptyDir:
          sizeLimit: 1Gi
      nodeSelector:
        kubernetes.io/arch: amd64
      tolerations:
      - key: "node.kubernetes.io/not-ready"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300
      - key: "node.kubernetes.io/unreachable"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300

---
apiVersion: v1
kind: Service
metadata:
  name: ai-agent-backend
  namespace: ai-agent-platform
  labels:
    app: ai-agent-backend
    component: backend
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8090"
    prometheus.io/path: "/metrics"
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 8000
    targetPort: 8000
    protocol: TCP
  - name: metrics
    port: 8090
    targetPort: 8090
    protocol: TCP
  selector:
    app: ai-agent-backend

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: ai-agent-backend-sa
  namespace: ai-agent-platform
  labels:
    app: ai-agent-backend
    component: backend

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: ai-agent-backend-role
  namespace: ai-agent-platform
rules:
- apiGroups: [""]
  resources: ["pods", "services", "endpoints"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["secrets", "configmaps"]
  verbs: ["get", "list"]
- apiGroups: ["apps"]
  resources: ["deployments"]
  verbs: ["get", "list", "watch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: ai-agent-backend-rolebinding
  namespace: ai-agent-platform
subjects:
- kind: ServiceAccount
  name: ai-agent-backend-sa
  namespace: ai-agent-platform
roleRef:
  kind: Role
  name: ai-agent-backend-role
  apiGroup: rbac.authorization.k8s.io

---
# Horizontal Pod Autoscaler
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: ai-agent-backend-hpa
  namespace: ai-agent-platform
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ai-agent-backend
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
      - type: Pods
        value: 2
        periodSeconds: 60
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 25
        periodSeconds: 60
      - type: Pods
        value: 1
        periodSeconds: 60

---
# Pod Disruption Budget
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: ai-agent-backend-pdb
  namespace: ai-agent-platform
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: ai-agent-backend

---
# Network Policy for Backend Security
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: ai-agent-backend-netpol
  namespace: ai-agent-platform
spec:
  podSelector:
    matchLabels:
      app: ai-agent-backend
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: nginx
    - podSelector:
        matchLabels:
          app: ai-agent-frontend
    ports:
    - protocol: TCP
      port: 8000
  - from: []  # Allow metrics scraping from any pod
    ports:
    - protocol: TCP
      port: 8090
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: postgres
    ports:
    - protocol: TCP
      port: 5432
  - to:
    - podSelector:
        matchLabels:
          app: redis
    ports:
    - protocol: TCP
      port: 6379
  - to:
    - podSelector:
        matchLabels:
          app: kafka
    ports:
    - protocol: TCP
      port: 9092
  - to: []  # Allow external API calls (OpenAI, Anthropic, OAuth providers)
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80
  - to: []  # Allow DNS resolution
    ports:
    - protocol: UDP
      port: 53