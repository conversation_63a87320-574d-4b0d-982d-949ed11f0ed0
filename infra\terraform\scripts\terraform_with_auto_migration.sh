#!/bin/bash
# Terraform deployment with automatic migration detection and application
set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_error() {
    echo -e "${RED}❌ Error: $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_step() {
    echo -e "${BLUE}🚀 $1${NC}"
}

# Get the workspace root
if [ -n "$BUILD_WORKSPACE_DIRECTORY" ]; then
    cd "$BUILD_WORKSPACE_DIRECTORY"
elif [ -n "$TEST_WORKSPACE" ]; then
    cd "$TEST_WORKSPACE"
else
    # Find the workspace root by looking for WORKSPACE file
    CURRENT_DIR="$PWD"
    while [ "$CURRENT_DIR" != "/" ]; do
        if [ -f "$CURRENT_DIR/WORKSPACE" ]; then
            cd "$CURRENT_DIR"
            break
        fi
        CURRENT_DIR="$(dirname "$CURRENT_DIR")"
    done
fi

echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "🏗️  TERRAFORM DEPLOYMENT WITH AUTO-MIGRATION"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""
print_info "Working directory: $(pwd)"
echo ""

# Configuration
ENVIRONMENT="${1:-dev}"
TERRAFORM_DIR="terraform/environments/$ENVIRONMENT"
SCHEMA_DIR="services/orbit/db/schema"
AUTO_MIGRATE_ENV="true"

# Step 1: Check for schema changes
print_step "Step 1: Checking for schema changes"
echo ""

# Check if we have new schema files that need migration
cd services/orbit/db
NEW_MIGRATIONS=false

if [ -d "$SCHEMA_DIR" ]; then
    print_info "Checking for new or modified schema files..."
    
    # Run auto-migration system to detect and generate migrations
    if bazel run //services/orbit/db:auto_migrate -- run local; then
        print_success "Schema check completed"
        
        # Check if new migrations were generated
        if [ -n "$(find migrations -name "V*.sql" -newer "migration_log.txt" 2>/dev/null)" ]; then
            NEW_MIGRATIONS=true
            print_info "New migrations detected"
        fi
    else
        print_error "Schema validation failed"
        exit 1
    fi
else
    print_info "No schema directory found, skipping migration check"
fi

# Return to workspace root
cd ../..

# Step 2: Navigate to terraform directory
print_step "Step 2: Terraform initialization and planning"
echo ""

if [ ! -d "$TERRAFORM_DIR" ]; then
    print_error "Terraform directory not found: $TERRAFORM_DIR"
    exit 1
fi

cd "$TERRAFORM_DIR"

# Initialize terraform
print_info "Initializing Terraform..."
terraform init

# Plan terraform changes
print_info "Planning Terraform changes..."
terraform plan -out=tfplan

# Step 3: Apply terraform changes
print_step "Step 3: Applying Terraform changes"
echo ""

print_info "Applying Terraform changes..."
if terraform apply tfplan; then
    print_success "Terraform apply completed successfully"
else
    print_error "Terraform apply failed"
    exit 1
fi

# Clean up plan file
rm -f tfplan

# Step 4: Apply database migrations if needed
if [ "$NEW_MIGRATIONS" = true ] || [ "$FORCE_MIGRATION" = true ]; then
    print_step "Step 4: Applying database migrations"
    echo ""
    
    # Get deployment configuration
    INSTANCE_NAME=$(terraform output -raw instance_name 2>/dev/null || echo "")
    DATABASE_NAME=$(terraform output -raw database_name 2>/dev/null || echo "")
    
    if [ -n "$INSTANCE_NAME" ] && [ -n "$DATABASE_NAME" ]; then
        print_info "Applying migrations to $ENVIRONMENT environment..."
        
        # Wait for infrastructure to be ready
        print_info "Waiting for infrastructure to be ready..."
        for i in {1..30}; do
            if gcloud compute ssh "$INSTANCE_NAME" --zone=australia-southeast1-a --command="echo 'Instance ready'" 2>/dev/null; then
                print_success "Infrastructure is ready"
                break
            fi
            if [ $i -eq 30 ]; then
                print_warning "Infrastructure may not be fully ready, proceeding with migration..."
                break
            fi
            echo -n "."
            sleep 10
        done
        
        # Return to workspace root for migration
        cd ../../..
        
        # Apply migrations
        if bazel run //services/orbit/db:migrate_gcp_dev_simple; then
            print_success "Database migrations applied successfully"
        else
            print_error "Database migrations failed"
            exit 1
        fi
        
        # Return to terraform directory
        cd "$TERRAFORM_DIR"
    else
        print_warning "Could not retrieve database configuration, skipping migration"
    fi
else
    print_info "No new migrations detected, skipping migration step"
fi

# Step 5: Deploy web application if requested
if [ "$DEPLOY_WEB" = true ]; then
    print_step "Step 5: Deploying web application"
    echo ""
    
    cd ../../..
    
    print_info "Building and deploying web application..."
    if bazel run //terraform:deploy_web_dev; then
        print_success "Web application deployed successfully"
    else
        print_error "Web application deployment failed"
        exit 1
    fi
    
    cd "$TERRAFORM_DIR"
fi

# Step 6: Summary
print_step "Step 6: Deployment summary"
echo ""

# Get final deployment info
LOAD_BALANCER_IP=$(terraform output -raw load_balancer_ip 2>/dev/null || echo "unknown")
WEBSITE_URL=$(terraform output -json website_urls 2>/dev/null | jq -r '.http // "unknown"' 2>/dev/null || echo "unknown")
BUCKET_NAME=$(terraform output -raw web_bucket_name 2>/dev/null || echo "unknown")

echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
print_success "TERRAFORM DEPLOYMENT WITH AUTO-MIGRATION COMPLETED!"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""
echo "🎉 Deployment completed successfully!"
echo ""
echo "📍 Access your application:"
if [ "$WEBSITE_URL" != "unknown" ]; then
    echo "  🌐 Website: $WEBSITE_URL"
else
    echo "  🌐 Load Balancer IP: http://$LOAD_BALANCER_IP"
fi
if [ "$BUCKET_NAME" != "unknown" ]; then
    echo "  🪣 Direct Bucket: https://storage.googleapis.com/$BUCKET_NAME/index.html"
fi
echo ""
echo "🗄️  Database:"
if [ "$NEW_MIGRATIONS" = true ]; then
    echo "  ✅ New migrations applied"
else
    echo "  ℹ️  No new migrations"
fi
echo ""
echo "🛠️  Next steps:"
echo "  • Test the application: $WEBSITE_URL"
echo "  • Monitor logs: gcloud logging read 'resource.type=gce_instance' --limit=50"
echo "  • SSH to instance: gcloud compute ssh $INSTANCE_NAME --zone=australia-southeast1-a"
echo ""
print_success "All done! 🎉"