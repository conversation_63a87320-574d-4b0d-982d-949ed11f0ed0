# Database Setup

This directory contains the PostgreSQL database configuration and migration management using Flyway.

## 🚀 Getting Started (Recommended Workflow)

### Option 1: Quick Start (Clean Slate)
```bash
# Complete fresh start - removes all containers and data
bazel run //platform/db:nuke

# Start database and setup with test data
bazel run //platform/db:getting_started
```

### Option 2: Manual Step-by-Step
```bash
# 1. Start the database container
bazel run //platform/db:start

# 2. Run database migrations
bazel run //platform/db:migrate

# 3. Clear existing data (important for clean test data)
bazel run //platform/db:clear_data

# 4. Insert test data
bazel run //platform/db:test_data
```

## 🧹 Cleanup Commands

```bash
# Clear all data but keep database running
bazel run //platform/db:clear_data

# Stop database container
bazel run //platform/db:stop

# Nuclear option: Remove containers, volumes, and all data
bazel run //platform/db:nuke
```

## 📊 Database Management

```bash
# Check database status
bazel run //platform/db:status

# Run only migrations
bazel run //platform/db:migrate

# Insert test data (run after clear_data for clean results)
bazel run //platform/db:test_data
```

## Structure

- `Dockerfile` - PostgreSQL 17 Alpine image configuration
- `docker-compose.yml` - Local development setup
- `flyway.conf` - Flyway migration configuration
- `migrations/` - SQL migration files (named V###__description.sql)
- `scripts/` - Database management scripts
- `BUILD.bazel` - Bazel build rules

## Initial Schema

The database includes a `users` table with:
- UUID primary keys
- Email and username (unique)
- Timestamps with automatic updates
- Optimized indexes

## Migrations

Migrations use Flyway and follow the naming convention:
- `V001__initial_schema.sql`
- `V002__add_products_table.sql`
- etc.

## Connection Details

- Host: localhost
- Port: 5432
- Database: appdb
- Username: postgres
- Password: postgres

## GCP Cloud SQL

For production deployments to GCP Cloud SQL, override the connection string in `flyway.conf`:

```
flyway.url=*****************************************************************************************************************************************
```