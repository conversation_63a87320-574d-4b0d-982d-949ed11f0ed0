#!/bin/bash
set -e

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "ERROR: Docker is not installed!"
    echo "Please install Docker from https://www.docker.com/get-started"
    exit 1
fi

# Check if Docker daemon is running
if ! docker info &> /dev/null; then
    echo "ERROR: Docker is not running!"
    echo "Please start Docker Desktop or the Docker daemon before running this script."
    exit 1
fi

echo "Starting PostgreSQL database..."

# Get the workspace root - when running via <PERSON><PERSON>, we need to navigate to the workspace
if [ -n "$BUILD_WORKSPACE_DIRECTORY" ]; then
    cd "$BUILD_WORKSPACE_DIRECTORY"
elif [ -n "$TEST_WORKSPACE" ]; then
    cd "$TEST_WORKSPACE/.."
else
    # Find the workspace root by looking for WORKSPACE file
    CURRENT_DIR="$PWD"
    while [ "$CURRENT_DIR" != "/" ]; do
        if [ -f "$CURRENT_DIR/WORKSPACE" ]; then
            cd "$CURRENT_DIR"
            break
        fi
        CURRENT_DIR="$(dirname "$CURRENT_DIR")"
    done
fi

# Now look for docker-compose.yml in the platform/db directory
cd platform/db

if [ ! -f "docker-compose.yml" ]; then
    echo "ERROR: docker-compose.yml not found in $(pwd)"
    exit 1
fi

# Try docker compose first (newer syntax), then docker-compose (older syntax)
if docker compose version &> /dev/null 2>&1; then
    echo "Using 'docker compose' command..."
    docker compose up -d
    
    # Wait for PostgreSQL to be ready
    echo "Waiting for PostgreSQL to be ready..."
    for i in {1..30}; do
        if docker compose exec -T postgres pg_isready -U postgres > /dev/null 2>&1; then
            echo "PostgreSQL is ready!"
            exit 0
        fi
        echo -n "."
        sleep 1
    done
elif command -v docker-compose &> /dev/null 2>&1; then
    echo "Using 'docker-compose' command..."
    docker-compose up -d
    
    # Wait for PostgreSQL to be ready
    echo "Waiting for PostgreSQL to be ready..."
    for i in {1..30}; do
        if docker compose exec -T postgres pg_isready -U postgres > /dev/null 2>&1 || docker-compose exec -T postgres pg_isready -U postgres > /dev/null 2>&1; then
            echo "PostgreSQL is ready!"
            exit 0
        fi
        echo -n "."
        sleep 1
    done
else
    # Use docker directly
    echo "docker-compose not found, using docker directly..."
    
    # Stop any existing container
    docker stop platform_postgres 2>/dev/null || true
    docker rm platform_postgres 2>/dev/null || true
    
    # Build the image
    docker build -t platform_postgres_image .
    
    # Run PostgreSQL container
    docker run -d \
        --name platform_postgres \
        -e POSTGRES_USER=postgres \
        -e POSTGRES_PASSWORD=postgres \
        -e POSTGRES_DB=appdb \
        -p 5432:5432 \
        -v platform_postgres_data:/var/lib/postgresql/data \
        platform_postgres_image
    
    # Wait for PostgreSQL to be ready
    echo "Waiting for PostgreSQL to be ready..."
    for i in {1..30}; do
        if docker exec platform_postgres pg_isready -U postgres > /dev/null 2>&1; then
            echo "PostgreSQL is ready!"
            exit 0
        fi
        echo -n "."
        sleep 1
    done
fi

echo "PostgreSQL failed to start within 30 seconds"
exit 1