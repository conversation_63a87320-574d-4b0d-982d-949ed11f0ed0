# Database build rules for Bazel

# Shell script targets for database operations
sh_binary(
    name = "start",
    srcs = ["//services/orbit/db/scripts:start.sh"],
    data = [
        "Dockerfile",
        "docker-compose.yml",
    ] + glob(["migrations/*.sql"]),
    visibility = ["//visibility:public"],
)

sh_binary(
    name = "stop",
    srcs = ["//services/orbit/db/scripts:stop.sh"],
    visibility = ["//visibility:public"],
)

sh_binary(
    name = "migrate",
    srcs = ["//services/orbit/db/scripts:migrate.sh"],
    data = [
        "flyway.conf",
    ] + glob(["migrations/*.sql"]),
    visibility = ["//visibility:public"],
)

sh_binary(
    name = "clean",
    srcs = ["//services/orbit/db/scripts:clean.sh"],
    visibility = ["//visibility:public"],
)

sh_binary(
    name = "clear_data",
    srcs = ["//services/orbit/db/scripts:clear_data.sh"],
    visibility = ["//visibility:public"],
)

sh_binary(
    name = "status",
    srcs = ["//services/orbit/db/scripts:status.sh"],
    visibility = ["//visibility:public"],
)

sh_binary(
    name = "test_data",
    srcs = ["//services/orbit/db/scripts:simple_test_data.sh"],
    visibility = ["//visibility:public"],
)

# Getting started script - complete fresh setup
sh_binary(
    name = "getting_started",
    srcs = ["//services/orbit/db/scripts:getting_started.sh"],
    data = [
        "docker-compose.yml",
        "flyway.conf",
        ":clear_data",
        ":migrate",
        ":start",
        ":test_data",
    ] + glob(["migrations/*.sql"]),
    visibility = ["//visibility:public"],
)

# Nuclear option - remove everything
sh_binary(
    name = "nuke",
    srcs = ["//services/orbit/db/scripts:nuke.sh"],
    visibility = ["//visibility:public"],
)

# Test database setup and teardown
sh_binary(
    name = "setup_test_db",
    srcs = ["//services/orbit/db/scripts:setup_test_db.sh"],
    data = [
        "docker-compose.yml",
        "flyway.conf",
    ] + glob(["migrations/*.sql"]),
    visibility = ["//visibility:public"],
)

sh_binary(
    name = "cleanup_test_db",
    srcs = ["//services/orbit/db/scripts:cleanup_test_db.sh"],
    visibility = ["//visibility:public"],
)

sh_binary(
    name = "fix_flyway_state",
    srcs = ["//services/orbit/db/scripts:fix_flyway_state.sh"],
    visibility = ["//visibility:public"],
)

# Generic GCP migration (works for any environment)
# Usage: bazel run //platform/db:migrate_gcp_simple -- ENVIRONMENT
# Example: bazel run //platform/db:migrate_gcp_simple -- prod
sh_binary(
    name = "migrate_gcp_simple",
    srcs = ["//services/orbit/db/scripts:migrate_gcp_simple.sh"],
    visibility = ["//visibility:public"],
)

# GCP dev test data insertion
sh_binary(
    name = "test_data_gcp_dev",
    srcs = ["//services/orbit/db/scripts:test_data_gcp_dev.sh"],
    visibility = ["//visibility:public"],
)

# Auto-migration system
sh_binary(
    name = "auto_migrate",
    srcs = ["//services/orbit/db/scripts:auto_migrate.sh"],
    data = [
        "flyway.conf",
        ":migrate",
        ":migrate_gcp_simple",
    ] + glob(["migrations/*.sql"]),
    visibility = ["//visibility:public"],
)

# Migration watcher service
sh_binary(
    name = "migration_watcher",
    srcs = ["//services/orbit/db/scripts:migration_watcher.sh"],
    data = [
        ":auto_migrate",
    ],
    visibility = ["//visibility:public"],
)

# Pre-commit migration hook
sh_binary(
    name = "pre_commit_migration",
    srcs = ["//services/orbit/db/scripts:pre_commit_migration.sh"],
    data = [
        ":auto_migrate",
    ],
    visibility = ["//visibility:public"],
)

# Group all database operations
filegroup(
    name = "migrations",
    srcs = glob(["migrations/*.sql"]),
    visibility = ["//visibility:public"],
)
