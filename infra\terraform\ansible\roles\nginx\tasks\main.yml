---
- name: Create Nginx configuration directory
  file:
    path: "{{ microservices_root }}/nginx"
    state: directory
    mode: '0755'
  tags: ['nginx', 'directories']

- name: Create Nginx SSL directory
  file:
    path: "{{ microservices_root }}/nginx/ssl"
    state: directory
    mode: '0700'
  tags: ['nginx', 'ssl']

- name: Generate self-signed SSL certificate (development)
  command: |
    openssl req -x509 -nodes -days 365 -newkey rsa:2048 
    -keyout {{ microservices_root }}/nginx/ssl/nginx.key 
    -out {{ microservices_root }}/nginx/ssl/nginx.crt
    -subj "/C=US/ST=State/L=City/O=Organization/CN={{ ansible_fqdn }}"
  args:
    creates: "{{ microservices_root }}/nginx/ssl/nginx.crt"
  when: environment == 'dev'
  tags: ['nginx', 'ssl']

- name: Deploy Nginx configuration
  template:
    src: nginx.conf.j2
    dest: "{{ microservices_root }}/nginx/nginx.conf"
    mode: '0644'
    backup: yes
  notify: reload nginx container
  tags: ['nginx', 'config']

- name: Deploy Nginx upstream configuration
  template:
    src: upstream.conf.j2
    dest: "{{ microservices_root }}/nginx/upstream.conf"
    mode: '0644'
  notify: reload nginx container
  tags: ['nginx', 'config']

- name: Deploy Nginx Docker compose configuration
  template:
    src: nginx-compose.yml.j2
    dest: "{{ microservices_root }}/nginx-compose.yml"
    mode: '0644'
  tags: ['nginx', 'compose']

- name: Start Nginx container
  community.docker.docker_compose_v2:
    project_src: "{{ microservices_root }}"
    files:
      - nginx-compose.yml
    state: present
  tags: ['nginx', 'container']

- name: Wait for Nginx to be ready
  uri:
    url: "http://localhost/health"
    method: GET
    status_code: 200
  retries: 10
  delay: 5
  tags: ['nginx', 'health']

- name: Set up Nginx log rotation
  template:
    src: nginx-logrotate.j2
    dest: /etc/logrotate.d/nginx
    mode: '0644'
  tags: ['nginx', 'logging']