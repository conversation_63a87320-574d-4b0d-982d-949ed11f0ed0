#!/bin/bash
# Generic Environment Deployment Script
# Deploy any environment (dev, staging, prod) using shared Terraform configuration

set -e

# Get the directory of this script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TERRAFORM_ROOT="$(dirname "$SCRIPT_DIR")"
WORKSPACE_ROOT="$(dirname "$TERRAFORM_ROOT")"

# Default values
ENVIRONMENT=""
ACTION="plan"
AUTO_APPROVE="false"
PROJECT_ID=""
BACKEND_BUCKET=""

# Function to display usage
usage() {
    echo "Usage: $0 -e ENVIRONMENT [OPTIONS]"
    echo ""
    echo "Required arguments:"
    echo "  -e, --environment ENVIRONMENT    Environment to deploy (dev, staging, prod)"
    echo ""
    echo "Optional arguments:"
    echo "  -a, --action ACTION              Terraform action: plan, apply, destroy (default: plan)"
    echo "  -y, --auto-approve               Auto-approve terraform apply/destroy"
    echo "  -p, --project-id PROJECT_ID      GCP Project ID (required for new environments)"
    echo "  -b, --backend-bucket BUCKET      Backend bucket name (auto-detected if not provided)"
    echo "  -h, --help                       Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 -e dev -a plan                          # Plan dev environment"
    echo "  $0 -e prod -a apply -y                     # Apply prod environment with auto-approve"
    echo "  $0 -e staging -a apply -p my-project-123   # Apply staging with specific project ID"
    echo ""
    exit 1
}

# Function to validate environment
validate_environment() {
    local env="$1"
    case "$env" in
        dev|staging|prod)
            return 0
            ;;
        *)
            echo "Error: Invalid environment '$env'. Must be one of: dev, staging, prod"
            exit 1
            ;;
    esac
}

# Function to detect or create backend bucket
setup_backend_bucket() {
    local env="$1"
    local project_id="$2"
    
    if [[ -n "$BACKEND_BUCKET" ]]; then
        echo "Using provided backend bucket: $BACKEND_BUCKET"
        return 0
    fi
    
    # Auto-detect backend bucket based on environment
    case "$env" in
        dev)
            BACKEND_BUCKET="agent-dev-459718-terraform-state"
            ;;
        prod)
            BACKEND_BUCKET="twodot-platform-terraform-state-prod"
            ;;
        staging)
            BACKEND_BUCKET="${project_id}-terraform-state-staging"
            ;;
    esac
    
    echo "Auto-detected backend bucket: $BACKEND_BUCKET"
    
    # Check if bucket exists
    if ! gsutil ls "gs://$BACKEND_BUCKET" >/dev/null 2>&1; then
        echo "Warning: Backend bucket gs://$BACKEND_BUCKET does not exist."
        echo "Creating backend bucket..."
        
        if [[ -z "$project_id" ]]; then
            echo "Error: Project ID is required to create backend bucket"
            exit 1
        fi
        
        # Create bucket
        gsutil mb -p "$project_id" -l "$REGION" "gs://$BACKEND_BUCKET"
        
        # Enable versioning
        gsutil versioning set on "gs://$BACKEND_BUCKET"
        
        echo "Backend bucket created successfully"
    fi
}

# Function to create environment-specific terraform.tfvars
create_tfvars() {
    local env="$1"
    local tfvars_file="$SCRIPT_DIR/$env.tfvars"
    
    # Check if tfvars already exists
    if [[ -f "$tfvars_file" ]]; then
        echo "Using existing tfvars file: $tfvars_file"
        return 0
    fi
    
    echo "Creating tfvars file: $tfvars_file"
    
    # Create environment-specific tfvars
    case "$env" in
        dev)
            cat > "$tfvars_file" <<EOF
# Development Environment Configuration

environment = "dev"
project_id = "$PROJECT_ID"
project_name = "platform"
region = "australia-southeast1"
zone = "australia-southeast1-a"

# SSL domains for dev environment
ssl_domains = [
  "dev.twodot.ai",
  "api.dev.twodot.ai"
]

# GitHub configuration (optional)
github_repo_owner = ""
github_repo_name = ""

# Development-specific overrides
admin_ip_ranges = ["0.0.0.0/0"]

# Custom configurations (uncomment and modify as needed)
# custom_machine_types = {
#   compute = "e2-standard-2"
#   database = "db-f1-micro"
# }

# custom_security_config = {
#   enable_ssl = true
#   enable_cloud_armor = false
# }

# custom_feature_flags = {
#   enable_debug_mode = true
#   enable_monitoring = false
# }
EOF
            ;;
        staging)
            cat > "$tfvars_file" <<EOF
# Staging Environment Configuration

environment = "staging"
project_id = "$PROJECT_ID"
project_name = "platform"
region = "australia-southeast1"
zone = "australia-southeast1-a"

# SSL domains for staging environment
ssl_domains = [
  "staging.twodot.ai",
  "api.staging.twodot.ai"
]

# GitHub configuration (optional)
github_repo_owner = ""
github_repo_name = ""

# Staging-specific settings
admin_ip_ranges = ["0.0.0.0/0"]  # Restrict this in production

# Custom configurations (uncomment and modify as needed)
# custom_security_config = {
#   enable_ssl = true
#   enable_cloud_armor = true
#   enable_cdn = true
# }

# custom_feature_flags = {
#   enable_monitoring = true
#   enable_backup_automation = true
# }
EOF
            ;;
        prod)
            cat > "$tfvars_file" <<EOF
# Production Environment Configuration

environment = "prod"
project_id = "$PROJECT_ID"
project_name = "platform"
region = "australia-southeast1"
zone = "australia-southeast1-a"

# SSL domains for production environment
ssl_domains = [
  "twodot.ai",
  "api.twodot.ai"
]

# GitHub configuration (required for CI/CD)
github_repo_owner = "your-github-org"
github_repo_name = "your-repo-name"

# Production security - RESTRICT THESE IP RANGES
admin_ip_ranges = ["0.0.0.0/0"]  # TODO: Restrict to office/VPN IPs

# Production-specific settings
rate_limit_requests_per_minute = 1000
notification_email = "<EMAIL>"
alert_email_addresses = ["<EMAIL>", "<EMAIL>"]

# Custom configurations for production
# custom_security_config = {
#   enable_ssl = true
#   enable_cloud_armor = true
#   enable_cdn = true
#   enable_http_redirect = true
# }

# custom_feature_flags = {
#   enable_monitoring = true
#   enable_backup_automation = true
#   auto_scaling_enabled = true
# }
EOF
            ;;
    esac
    
    echo "Created tfvars file. Please review and customize: $tfvars_file"
}

# Function to create backend configuration
create_backend_config() {
    local env="$1"
    local backend_file="$SCRIPT_DIR/backend-$env.tf"
    
    # Clean up any existing backend files to avoid conflicts
    rm -f "$SCRIPT_DIR"/backend-*.tf
    
    echo "Creating backend configuration: $backend_file"
    
    cat > "$backend_file" <<EOF
# Backend configuration for $env environment
# This file is auto-generated by deploy-environment.sh

terraform {
  backend "gcs" {
    bucket = "$BACKEND_BUCKET"
    prefix = "terraform/$env"
  }
}
EOF
}

# Function to run terraform
run_terraform() {
    local env="$1"
    local action="$2"
    local auto_approve="$3"
    
    echo "========================================"
    echo "Running Terraform $action for $env environment"
    echo "========================================"
    
    # Change to deployment directory
    local deploy_dir="$SCRIPT_DIR"
    cd "$deploy_dir"
    
    # Initialize terraform
    echo "Initializing Terraform..."
    export TF_INPUT=false
    terraform init -reconfigure -input=false
    
    # Select or create workspace automatically
    echo "Setting up Terraform workspace: $env"
    
    # Set environment variable to disable interactive prompts
    export TF_INPUT=false
    
    # First, ensure we can work with workspaces by selecting default
    terraform workspace select default 2>/dev/null || true
    
    # Now try to select the target workspace
    if terraform workspace select "$env" 2>/dev/null; then
        echo "Selected existing workspace: $env"
    else
        # If selection fails, try creating new workspace
        echo "Creating new workspace: $env"
        if terraform workspace new "$env" 2>/dev/null; then
            echo "Created new workspace: $env"
        else
            echo "Failed to create workspace, using default"
            terraform workspace select default 2>/dev/null || true
        fi
    fi
    
    # Run terraform command
    case "$action" in
        plan)
            terraform plan -var-file="$env.tfvars" -out="$env.tfplan"
            echo ""
            echo "Plan saved to: $env.tfplan"
            echo "To apply: $0 -e $env -a apply"
            ;;
        apply)
            if [[ "$auto_approve" == "true" ]]; then
                if [[ -f "$env.tfplan" ]]; then
                    terraform apply "$env.tfplan"
                else
                    terraform apply -var-file="$env.tfvars" -auto-approve
                fi
            else
                if [[ -f "$env.tfplan" ]]; then
                    terraform apply "$env.tfplan"
                else
                    terraform apply -var-file="$env.tfvars"
                fi
            fi
            ;;
        destroy)
            if [[ "$auto_approve" == "true" ]]; then
                terraform destroy -var-file="$env.tfvars" -auto-approve
            else
                terraform destroy -var-file="$env.tfvars"
            fi
            ;;
        *)
            echo "Error: Unknown action '$action'"
            exit 1
            ;;
    esac
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -a|--action)
            ACTION="$2"
            shift 2
            ;;
        -y|--auto-approve)
            AUTO_APPROVE="true"
            shift
            ;;
        -p|--project-id)
            PROJECT_ID="$2"
            shift 2
            ;;
        -b|--backend-bucket)
            BACKEND_BUCKET="$2"
            shift 2
            ;;
        -h|--help)
            usage
            ;;
        *)
            echo "Unknown option: $1"
            usage
            ;;
    esac
done

# Validate required arguments
if [[ -z "$ENVIRONMENT" ]]; then
    echo "Error: Environment is required"
    usage
fi

validate_environment "$ENVIRONMENT"

# Detect project ID if not provided
if [[ -z "$PROJECT_ID" ]]; then
    # Try to get from existing tfvars
    tfvars_file="$SCRIPT_DIR/$ENVIRONMENT.tfvars"
    if [[ -f "$tfvars_file" ]]; then
        PROJECT_ID=$(grep '^project_id' "$tfvars_file" | cut -d'"' -f2 2>/dev/null || true)
    fi
    
    # Try to get from gcloud
    if [[ -z "$PROJECT_ID" ]]; then
        PROJECT_ID=$(gcloud config get-value project 2>/dev/null || true)
    fi
    
    if [[ -z "$PROJECT_ID" ]]; then
        echo "Error: Project ID could not be determined. Please provide with -p option."
        exit 1
    fi
fi

# Set default region
REGION="australia-southeast1"

echo "Environment: $ENVIRONMENT"
echo "Action: $ACTION"
echo "Project ID: $PROJECT_ID"
echo "Auto-approve: $AUTO_APPROVE"
echo ""

# Setup backend bucket
setup_backend_bucket "$ENVIRONMENT" "$PROJECT_ID"

# Create tfvars file if it doesn't exist
create_tfvars "$ENVIRONMENT"

# Create backend configuration
create_backend_config "$ENVIRONMENT"

# Copy shared configuration to deployment directory
echo "Copying shared Terraform configuration..."
cp "$TERRAFORM_ROOT/shared/main.tf" "$SCRIPT_DIR/"
cp "$TERRAFORM_ROOT/shared/variables.tf" "$SCRIPT_DIR/"
cp "$TERRAFORM_ROOT/shared/outputs.tf" "$SCRIPT_DIR/"

# Clean up any residual terraform state that might reference wrong workspace
rm -f "$SCRIPT_DIR/.terraform/environment" 2>/dev/null || true

# Run terraform
run_terraform "$ENVIRONMENT" "$ACTION" "$AUTO_APPROVE"

echo ""
echo "========================================"
echo "Terraform $ACTION completed for $ENVIRONMENT environment"
echo "========================================"