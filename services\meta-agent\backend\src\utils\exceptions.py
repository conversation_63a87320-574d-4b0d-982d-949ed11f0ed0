"""Custom exception classes for the AI agent platform."""

from typing import Optional, Dict, Any

class AgentError(Exception):
    """Base exception for agent-related errors."""
    
    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message)
        self.error_code = error_code
        self.details = details or {}

class AuthenticationError(AgentError):
    """Authentication-related errors."""
    
    def __init__(self, message: str = "Authentication failed", **kwargs):
        super().__init__(message, error_code="AUTH_ERROR", **kwargs)

class AuthorizationError(AgentError):
    """Authorization-related errors."""
    
    def __init__(self, message: str = "Access denied", **kwargs):
        super().__init__(message, error_code="AUTHZ_ERROR", **kwargs)

class ValidationError(AgentError):
    """Input validation errors."""
    
    def __init__(self, message: str = "Validation failed", field: Optional[str] = None, **kwargs):
        super().__init__(message, error_code="VALIDATION_ERROR", **kwargs)
        if field:
            self.details["field"] = field

class ServiceUnavailableError(AgentError):
    """Service availability errors."""
    
    def __init__(self, message: str = "Service unavailable", service: Optional[str] = None, **kwargs):
        super().__init__(message, error_code="SERVICE_UNAVAILABLE", **kwargs)
        if service:
            self.details["service"] = service

class RateLimitError(AgentError):
    """Rate limiting errors."""
    
    def __init__(
        self,
        message: str = "Rate limit exceeded",
        retry_after: Optional[int] = None,
        **kwargs
    ):
        super().__init__(message, error_code="RATE_LIMIT", **kwargs)
        if retry_after:
            self.details["retry_after"] = retry_after

class AgentNotFoundError(AgentError):
    """Agent not found errors."""
    
    def __init__(self, agent_id: Optional[str] = None, **kwargs):
        message = f"Agent {agent_id} not found" if agent_id else "Agent not found"
        super().__init__(message, error_code="AGENT_NOT_FOUND", **kwargs)
        if agent_id:
            self.details["agent_id"] = agent_id

class AgentExecutionError(AgentError):
    """Agent execution errors."""
    
    def __init__(
        self,
        message: str = "Agent execution failed",
        agent_id: Optional[str] = None,
        **kwargs
    ):
        super().__init__(message, error_code="AGENT_EXECUTION_ERROR", **kwargs)
        if agent_id:
            self.details["agent_id"] = agent_id

class DatabaseError(AgentError):
    """Database-related errors."""
    
    def __init__(self, message: str = "Database error", **kwargs):
        super().__init__(message, error_code="DATABASE_ERROR", **kwargs)

class ExternalServiceError(AgentError):
    """External service integration errors."""
    
    def __init__(
        self,
        message: str = "External service error",
        service_name: Optional[str] = None,
        **kwargs
    ):
        super().__init__(message, error_code="EXTERNAL_SERVICE_ERROR", **kwargs)
        if service_name:
            self.details["service_name"] = service_name

class ConfigurationError(AgentError):
    """Configuration-related errors."""
    
    def __init__(self, message: str = "Configuration error", **kwargs):
        super().__init__(message, error_code="CONFIGURATION_ERROR", **kwargs)

class TaskExecutionError(AgentError):
    """Task execution errors."""
    
    def __init__(
        self,
        message: str = "Task execution failed",
        task_id: Optional[str] = None,
        **kwargs
    ):
        super().__init__(message, error_code="TASK_EXECUTION_ERROR", **kwargs)
        if task_id:
            self.details["task_id"] = task_id

class OrchestrationError(AgentError):
    """Multi-agent orchestration errors."""
    
    def __init__(self, message: str = "Orchestration error", **kwargs):
        super().__init__(message, error_code="ORCHESTRATION_ERROR", **kwargs)

class SecurityError(AgentError):
    """Security-related errors."""
    
    def __init__(self, message: str = "Security violation", **kwargs):
        super().__init__(message, error_code="SECURITY_ERROR", **kwargs)

class DeploymentError(AgentError):
    """Deployment-related errors."""
    
    def __init__(
        self,
        message: str = "Deployment failed",
        deployment_id: Optional[str] = None,
        stage: Optional[str] = None,
        **kwargs
    ):
        super().__init__(message, error_code="DEPLOYMENT_ERROR", **kwargs)
        if deployment_id:
            self.details["deployment_id"] = deployment_id
        if stage:
            self.details["stage"] = stage

class WorkflowError(AgentError):
    """Workflow execution errors."""
    
    def __init__(
        self,
        message: str = "Workflow execution failed",
        workflow_id: Optional[str] = None,
        node_id: Optional[str] = None,
        **kwargs
    ):
        super().__init__(message, error_code="WORKFLOW_ERROR", **kwargs)
        if workflow_id:
            self.details["workflow_id"] = workflow_id
        if node_id:
            self.details["node_id"] = node_id

class MigrationError(AgentError):
    """Application migration errors."""
    
    def __init__(
        self,
        message: str = "Migration failed",
        migration_id: Optional[str] = None,
        phase: Optional[str] = None,
        **kwargs
    ):
        super().__init__(message, error_code="MIGRATION_ERROR", **kwargs)
        if migration_id:
            self.details["migration_id"] = migration_id
        if phase:
            self.details["phase"] = phase