"""
AI Agent Platform - Main FastAPI Application
"""

from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
import structlog
import uvicorn
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

from config.settings import settings
from database.connection import create_tables
from api import api_router
from services.messaging import message_queue_service, MessageHandlers
from services.ai_integration import ai_integration_service


# Configure structured logging
structlog.configure(
    processors=[
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.add_log_level,
        structlog.processors.JSONRenderer()
    ],
    wrapper_class=structlog.make_filtering_bound_logger(
        getattr(structlog.stdlib, settings.monitoring.log_level.upper(), 20)
    ),
    context_class=dict,
    logger_factory=structlog.PrintLoggerFactory(),
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    logger.info("Starting AI Agent Platform", version=settings.version)
    
    # Create database tables
    await create_tables()
    logger.info("Database tables created successfully")
    
    # Initialize message queue service
    try:
        await message_queue_service.connect()
        logger.info("Message queue service connected")
        
        # Subscribe to topics with handlers
        message_queue_service.subscribe_to_topic(
            settings.message_queue.agent_events_topic,
            settings.message_queue.kafka_group_id,
            MessageHandlers.agent_event_handler
        )
        
        message_queue_service.subscribe_to_topic(
            settings.message_queue.orchestration_topic,
            settings.message_queue.kafka_group_id,
            MessageHandlers.orchestration_command_handler
        )
        
        message_queue_service.subscribe_to_topic(
            settings.message_queue.intelligence_topic,
            settings.message_queue.kafka_group_id,
            MessageHandlers.intelligence_request_handler
        )
        
        logger.info("Message queue subscriptions initialized")
        
    except Exception as e:
        logger.warning("Failed to initialize message queue service", error=str(e))
    
    # Initialize AI integration service
    try:
        await ai_integration_service.initialize()
        logger.info("AI integration service initialized")
    except Exception as e:
        logger.warning("Failed to initialize AI integration service", error=str(e))
    
    yield
    
    logger.info("Shutting down AI Agent Platform")
    
    # Cleanup message queue service
    try:
        await message_queue_service.disconnect()
        logger.info("Message queue service disconnected")
    except Exception as e:
        logger.error("Error during message queue cleanup", error=str(e))


# Create FastAPI application
app = FastAPI(
    title=settings.app_name,
    description=settings.description,
    version=settings.version,
    debug=settings.debug,
    lifespan=lifespan
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.allowed_hosts
)

# Include API routes
app.include_router(api_router, prefix="/api/v1")


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": f"Welcome to {settings.app_name}",
        "version": settings.version,
        "environment": settings.environment,
        "status": "running"
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "version": settings.version,
        "environment": settings.environment
    }


@app.get("/metrics")
async def metrics():
    """Prometheus metrics endpoint"""
    # TODO: Implement Prometheus metrics
    return {"metrics": "not_implemented_yet"}


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        workers=settings.workers if settings.environment == "production" else 1,
        reload=settings.debug,
        log_level=settings.monitoring.log_level.lower()
    )