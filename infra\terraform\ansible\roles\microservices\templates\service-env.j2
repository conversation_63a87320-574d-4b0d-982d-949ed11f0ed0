# Environment configuration for {{ item.key }}
# Generated by Ansible - DO NOT EDIT MANUALLY

# Service Information
SERVICE_NAME={{ item.key }}
SERVICE_PORT={{ item.value.port }}
ENVIRONMENT={{ environment }}
{% if deployment_strategy == 'blue-green' %}
DEPLOYMENT_COLOR={{ deployment_color | default('blue') }}
{% endif %}

# Logging
LOG_LEVEL={{ item.value.environment.LOG_LEVEL | default(log_level) }}
LOG_FORMAT={{ log_format | default('json') }}

# Common Environment Variables
{% for key, value in item.value.environment.items() %}
{{ key }}={{ value }}
{% endfor %}

# Database Configuration (if needed)
{% if item.key in ['auth', 'crm_backend'] %}
DATABASE_URL={{ database_connection_string }}
DB_HOST={{ database_host }}
DB_PORT={{ database_port }}
DB_NAME={{ database_name }}
DB_USER={{ database_user }}
DB_PASSWORD={{ database_password }}
{% endif %}

# Auth Service Configuration
{% if item.key == 'auth' %}
JWT_SECRET={{ jwt_secret }}
JWT_EXPIRY=24h
SESSION_TIMEOUT=86400
OAUTH_CLIENT_ID={{ oauth_client_id }}
OAUTH_CLIENT_SECRET={{ oauth_client_secret }}
OAUTH_REDIRECT_URL=http://localhost:8080/api/v1/auth/callback
{% endif %}

# Gateway Configuration
{% if item.key == 'gateway' %}
AUTH_SERVICE_URL={{ auth_service_url }}
CRM_BACKEND_URL={{ crm_backend_url }}
CORS_ALLOWED_ORIGINS=http://localhost:8080,http://localhost:3000
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60s
{% endif %}

# CRM Backend Configuration
{% if item.key == 'crm_backend' %}
AUTH_SERVICE_URL={{ auth_service_url }}
ENABLE_SWAGGER={{ 'true' if environment == 'dev' else 'false' }}
MAX_PAGE_SIZE=100
DEFAULT_PAGE_SIZE=20
{% endif %}

# Health Check Configuration
HEALTH_CHECK_PATH={{ item.value.health_check.path | default('/health') }}
HEALTH_CHECK_TIMEOUT={{ health_check_timeout }}

# Monitoring
METRICS_ENABLED={{ 'true' if enable_monitoring else 'false' }}
METRICS_PORT={{ metrics_port | default(9090) }}

# Feature Flags
{% if item.value.feature_flags is defined %}
{% for flag, value in item.value.feature_flags.items() %}
FEATURE_{{ flag | upper }}={{ value }}
{% endfor %}
{% endif %}