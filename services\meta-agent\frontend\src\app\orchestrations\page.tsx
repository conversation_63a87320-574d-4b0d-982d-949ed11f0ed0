/**
 * AI Agent Platform - Orchestrations Page
 * Multi-agent orchestration management
 */

'use client';

import React, { useState, useEffect } from 'react';
import { AppLayout } from '@/components/Layout/AppLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Plus,
  Play,
  Pause,
  Square,
  Edit,
  Trash2,
  Copy,
  Search,
  Filter,
  MoreHorizontal,
  Network,
  Users,
  Clock,
  CheckCircle2,
  XCircle,
  AlertCircle,
  Zap,
  GitBranch,
  Settings,
  Eye,
  Activity
} from 'lucide-react';
import Link from 'next/link';

// Mock data for orchestrations
const mockOrchestrations = [
  {
    id: '1',
    name: 'Customer Onboarding Flow',
    description: 'Multi-agent orchestration for automated customer onboarding',
    pattern: 'sequential',
    status: 'running',
    progress: 75,
    agents: [
      { id: 'agent-1', name: 'Data Validator', role: 'validator', status: 'completed' },
      { id: 'agent-2', name: 'Document Processor', role: 'processor', status: 'running' },
      { id: 'agent-3', name: 'Notification Sender', role: 'notifier', status: 'pending' }
    ],
    startedAt: '2024-01-15T10:30:00Z',
    estimatedCompletion: '2024-01-15T11:15:00Z',
    totalTasks: 12,
    completedTasks: 9,
    failedTasks: 0
  },
  {
    id: '2',
    name: 'Content Moderation Pipeline',
    description: 'Parallel processing of user-generated content with AI moderation',
    pattern: 'parallel',
    status: 'completed',
    progress: 100,
    agents: [
      { id: 'agent-4', name: 'Text Analyzer', role: 'analyzer', status: 'completed' },
      { id: 'agent-5', name: 'Image Moderator', role: 'moderator', status: 'completed' },
      { id: 'agent-6', name: 'Sentiment Analyzer', role: 'analyzer', status: 'completed' }
    ],
    startedAt: '2024-01-15T09:00:00Z',
    completedAt: '2024-01-15T09:45:00Z',
    totalTasks: 156,
    completedTasks: 156,
    failedTasks: 0
  },
  {
    id: '3',
    name: 'Data Processing Hierarchy',
    description: 'Hierarchical data processing with supervisor and worker agents',
    pattern: 'hierarchical',
    status: 'paused',
    progress: 45,
    agents: [
      { id: 'agent-7', name: 'Supervisor Agent', role: 'supervisor', status: 'paused' },
      { id: 'agent-8', name: 'Worker Agent 1', role: 'worker', status: 'paused' },
      { id: 'agent-9', name: 'Worker Agent 2', role: 'worker', status: 'paused' },
      { id: 'agent-10', name: 'Quality Checker', role: 'checker', status: 'paused' }
    ],
    startedAt: '2024-01-15T08:00:00Z',
    totalTasks: 89,
    completedTasks: 40,
    failedTasks: 2
  },
  {
    id: '4',
    name: 'Real-time Event Processing',
    description: 'Event-driven orchestration for real-time data processing',
    pattern: 'event_driven',
    status: 'error',
    progress: 25,
    agents: [
      { id: 'agent-11', name: 'Event Listener', role: 'listener', status: 'error' },
      { id: 'agent-12', name: 'Event Processor', role: 'processor', status: 'stopped' },
      { id: 'agent-13', name: 'Event Router', role: 'router', status: 'stopped' }
    ],
    startedAt: '2024-01-15T11:00:00Z',
    totalTasks: 45,
    completedTasks: 11,
    failedTasks: 3
  }
];

const statusConfig = {
  running: { color: 'bg-blue-100 text-blue-800', icon: Play },
  completed: { color: 'bg-green-100 text-green-800', icon: CheckCircle2 },
  paused: { color: 'bg-yellow-100 text-yellow-800', icon: Pause },
  error: { color: 'bg-red-100 text-red-800', icon: XCircle },
  pending: { color: 'bg-gray-100 text-gray-800', icon: Clock }
};

const patternConfig = {
  sequential: { label: 'Sequential', icon: GitBranch, color: 'bg-blue-50 text-blue-700' },
  parallel: { label: 'Parallel', icon: Network, color: 'bg-green-50 text-green-700' },
  hierarchical: { label: 'Hierarchical', icon: Users, color: 'bg-purple-50 text-purple-700' },
  event_driven: { label: 'Event Driven', icon: Zap, color: 'bg-orange-50 text-orange-700' }
};

export default function OrchestrationsPage() {
  const [orchestrations, setOrchestrations] = useState(mockOrchestrations);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [patternFilter, setPatternFilter] = useState('all');
  const [loading, setLoading] = useState(false);

  const filteredOrchestrations = orchestrations.filter(orchestration => {
    const matchesSearch = orchestration.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         orchestration.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || orchestration.status === statusFilter;
    const matchesPattern = patternFilter === 'all' || orchestration.pattern === patternFilter;
    return matchesSearch && matchesStatus && matchesPattern;
  });

  const handleOrchestrationAction = async (orchestrationId: string, action: string) => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      setOrchestrations(prev => prev.map(orchestration => {
        if (orchestration.id === orchestrationId) {
          switch (action) {
            case 'start':
              return { ...orchestration, status: 'running' };
            case 'pause':
              return { ...orchestration, status: 'paused' };
            case 'stop':
              return { ...orchestration, status: 'completed' };
            default:
              return orchestration;
          }
        }
        return orchestration;
      }));
    } catch (error) {
      console.error('Failed to perform orchestration action:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <AppLayout>
      <div className="container mx-auto py-8 px-4 max-w-7xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-3">
              <Network className="h-8 w-8 text-primary" />
              Orchestrations
            </h1>
            <p className="text-muted-foreground mt-2">
              Manage multi-agent orchestrations and compound workflows
            </p>
          </div>
          <div className="flex gap-3">
            <Button variant="outline">
              <Eye className="h-4 w-4 mr-2" />
              Monitor
            </Button>
            <Link href="/orchestrations/create">
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Create Orchestration
              </Button>
            </Link>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Orchestrations</p>
                  <p className="text-2xl font-bold">{orchestrations.length}</p>
                </div>
                <Network className="h-8 w-8 text-muted-foreground" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Active</p>
                  <p className="text-2xl font-bold text-blue-600">
                    {orchestrations.filter(o => o.status === 'running').length}
                  </p>
                </div>
                <Play className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Agents</p>
                  <p className="text-2xl font-bold">
                    {orchestrations.reduce((sum, o) => sum + o.agents.length, 0)}
                  </p>
                </div>
                <Users className="h-8 w-8 text-muted-foreground" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Success Rate</p>
                  <p className="text-2xl font-bold text-green-600">
                    {orchestrations.length > 0 ?
                      Math.round(orchestrations.filter(o => o.status === 'completed').length / orchestrations.length * 100) : 0}%
                  </p>
                </div>
                <CheckCircle2 className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search orchestrations..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                >
                  <option value="all">All Status</option>
                  <option value="running">Running</option>
                  <option value="completed">Completed</option>
                  <option value="paused">Paused</option>
                  <option value="error">Error</option>
                </select>
                <select
                  value={patternFilter}
                  onChange={(e) => setPatternFilter(e.target.value)}
                  className="flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                >
                  <option value="all">All Patterns</option>
                  <option value="sequential">Sequential</option>
                  <option value="parallel">Parallel</option>
                  <option value="hierarchical">Hierarchical</option>
                  <option value="event_driven">Event Driven</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Orchestrations List */}
        <div className="space-y-6">
          {filteredOrchestrations.map((orchestration) => {
            const StatusIcon = statusConfig[orchestration.status as keyof typeof statusConfig]?.icon || AlertCircle;
            const statusColor = statusConfig[orchestration.status as keyof typeof statusConfig]?.color || 'bg-gray-100 text-gray-800';
            const patternInfo = patternConfig[orchestration.pattern as keyof typeof patternConfig];
            const PatternIcon = patternInfo?.icon || Network;

            return (
              <Card key={orchestration.id} className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <CardTitle className="flex items-center gap-2">
                          <Link href={`/orchestrations/${orchestration.id}`} className="hover:underline">
                            {orchestration.name}
                          </Link>
                        </CardTitle>
                        <Badge className={statusColor}>
                          <StatusIcon className="h-3 w-3 mr-1" />
                          {orchestration.status}
                        </Badge>
                        <Badge className={patternInfo?.color || 'bg-gray-50 text-gray-700'}>
                          <PatternIcon className="h-3 w-3 mr-1" />
                          {patternInfo?.label || orchestration.pattern}
                        </Badge>
                      </div>
                      <CardDescription>
                        {orchestration.description}
                      </CardDescription>
                    </div>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* Progress */}
                    {orchestration.status === 'running' && (
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Progress</span>
                          <span>{orchestration.progress}%</span>
                        </div>
                        <Progress value={orchestration.progress} className="h-2" />
                      </div>
                    )}

                    {/* Stats */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <p className="text-muted-foreground">Agents</p>
                        <p className="font-medium">{orchestration.agents.length}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Total Tasks</p>
                        <p className="font-medium">{orchestration.totalTasks}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Completed</p>
                        <p className="font-medium text-green-600">{orchestration.completedTasks}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Failed</p>
                        <p className="font-medium text-red-600">{orchestration.failedTasks}</p>
                      </div>
                    </div>

                    {/* Agents */}
                    <div>
                      <p className="text-sm font-medium mb-2">Participating Agents:</p>
                      <div className="flex flex-wrap gap-2">
                        {orchestration.agents.map((agent) => (
                          <Badge key={agent.id} variant="outline" className="text-xs">
                            {agent.name} ({agent.role})
                          </Badge>
                        ))}
                      </div>
                    </div>

                    {/* Timing */}
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        Started: {new Date(orchestration.startedAt).toLocaleString()}
                      </div>
                      {orchestration.completedAt && (
                        <div className="flex items-center gap-1">
                          <CheckCircle2 className="h-4 w-4" />
                          Completed: {new Date(orchestration.completedAt).toLocaleString()}
                        </div>
                      )}
                    </div>

                    {/* Actions */}
                    <div className="flex gap-2 pt-2 border-t">
                      {orchestration.status === 'paused' ? (
                        <Button
                          size="sm"
                          onClick={() => handleOrchestrationAction(orchestration.id, 'start')}
                          disabled={loading}
                        >
                          <Play className="h-4 w-4 mr-1" />
                          Resume
                        </Button>
                      ) : orchestration.status === 'running' ? (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleOrchestrationAction(orchestration.id, 'pause')}
                          disabled={loading}
                        >
                          <Pause className="h-4 w-4 mr-1" />
                          Pause
                        </Button>
                      ) : orchestration.status === 'error' ? (
                        <Button
                          size="sm"
                          onClick={() => handleOrchestrationAction(orchestration.id, 'start')}
                          disabled={loading}
                        >
                          <Play className="h-4 w-4 mr-1" />
                          Retry
                        </Button>
                      ) : null}

                      <Link href={`/orchestrations/${orchestration.id}`}>
                        <Button size="sm" variant="outline">
                          <Eye className="h-4 w-4 mr-1" />
                          View Details
                        </Button>
                      </Link>

                      <Link href={`/orchestrations/${orchestration.id}/edit`}>
                        <Button size="sm" variant="outline">
                          <Edit className="h-4 w-4 mr-1" />
                          Edit
                        </Button>
                      </Link>

                      <Button size="sm" variant="outline">
                        <Copy className="h-4 w-4 mr-1" />
                        Clone
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {filteredOrchestrations.length === 0 && (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center py-8">
                <Network className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">No orchestrations found</h3>
                <p className="text-muted-foreground mb-4">
                  {searchTerm || statusFilter !== 'all' || patternFilter !== 'all'
                    ? 'Try adjusting your search or filters'
                    : 'Get started by creating your first orchestration'
                  }
                </p>
                {!searchTerm && statusFilter === 'all' && patternFilter === 'all' && (
                  <Link href="/orchestrations/create">
                    <Button>
                      <Plus className="h-4 w-4 mr-2" />
                      Create Your First Orchestration
                    </Button>
                  </Link>
                )}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </AppLayout>
  );
}