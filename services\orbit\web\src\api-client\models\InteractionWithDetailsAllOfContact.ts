/* tslint:disable */
/* eslint-disable */
/**
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface InteractionWithDetailsAllOfContact
 */
export interface InteractionWithDetailsAllOfContact {
    /**
     * 
     * @type {string}
     * @memberof InteractionWithDetailsAllOfContact
     */
    id?: string;
    /**
     * 
     * @type {string}
     * @memberof InteractionWithDetailsAllOfContact
     */
    firstName?: string;
    /**
     * 
     * @type {string}
     * @memberof InteractionWithDetailsAllOfContact
     */
    lastName?: string;
    /**
     * 
     * @type {string}
     * @memberof InteractionWithDetailsAllOfContact
     */
    email?: string;
}

/**
 * Check if a given object implements the InteractionWithDetailsAllOfContact interface.
 */
export function instanceOfInteractionWithDetailsAllOfContact(value: object): value is InteractionWithDetailsAllOfContact {
    return true;
}

export function InteractionWithDetailsAllOfContactFromJSON(json: any): InteractionWithDetailsAllOfContact {
    return InteractionWithDetailsAllOfContactFromJSONTyped(json, false);
}

export function InteractionWithDetailsAllOfContactFromJSONTyped(json: any, ignoreDiscriminator: boolean): InteractionWithDetailsAllOfContact {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'] == null ? undefined : json['id'],
        'firstName': json['first_name'] == null ? undefined : json['first_name'],
        'lastName': json['last_name'] == null ? undefined : json['last_name'],
        'email': json['email'] == null ? undefined : json['email'],
    };
}

  export function InteractionWithDetailsAllOfContactToJSON(json: any): InteractionWithDetailsAllOfContact {
      return InteractionWithDetailsAllOfContactToJSONTyped(json, false);
  }

  export function InteractionWithDetailsAllOfContactToJSONTyped(value?: InteractionWithDetailsAllOfContact | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'first_name': value['firstName'],
        'last_name': value['lastName'],
        'email': value['email'],
    };
}

