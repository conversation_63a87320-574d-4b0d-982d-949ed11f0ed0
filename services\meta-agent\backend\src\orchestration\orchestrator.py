"""
AI Agent Platform - Multi-Agent Orchestrator
"""

import asyncio
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional, Set
from enum import Enum
import structlog

from database.models import Orchestration, OrchestrationPattern, Task, TaskStatus
from agents.manager import agent_manager
from services.messaging import message_queue_service

logger = structlog.get_logger()


class OrchestrationStatus(str, Enum):
    """Orchestration status enumeration"""
    CREATED = "created"
    INITIALIZING = "initializing"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class OrchestrationEngine:
    """Core orchestration engine for multi-agent coordination"""
    
    def __init__(self, orchestration_id: uuid.UUID, config: Dict[str, Any]):
        self.orchestration_id = orchestration_id
        self.config = config
        self.pattern = OrchestrationPattern(config.get('pattern', OrchestrationPattern.SEQUENTIAL))
        self.status = OrchestrationStatus.CREATED
        
        # Orchestration components
        self.agent_ids: List[uuid.UUID] = config.get('agent_ids', [])
        self.execution_plan: Dict[str, Any] = config.get('execution_plan', {})
        self.context: Dict[str, Any] = {}
        self.results: Dict[str, Any] = {}
        
        # Task management
        self.tasks: Dict[uuid.UUID, Task] = {}
        self.task_dependencies: Dict[uuid.UUID, Set[uuid.UUID]] = {}
        self.completed_tasks: Set[uuid.UUID] = set()
        
        # Progress tracking
        self.total_steps = 0
        self.completed_steps = 0
        self.start_time: Optional[datetime] = None
        self.end_time: Optional[datetime] = None
        
        # Event handling
        self.event_handlers = {}
        
        logger.info(
            "Orchestration engine initialized",
            orchestration_id=str(orchestration_id),
            pattern=self.pattern,
            agent_count=len(self.agent_ids)
        )
    
    async def start(self) -> bool:
        """Start the orchestration"""
        try:
            if self.status != OrchestrationStatus.CREATED:
                logger.warning(
                    "Cannot start orchestration - invalid status",
                    orchestration_id=str(self.orchestration_id),
                    status=self.status
                )
                return False
            
            self.status = OrchestrationStatus.INITIALIZING
            self.start_time = datetime.utcnow()
            
            # Validate agents are available
            if not await self._validate_agents():
                self.status = OrchestrationStatus.FAILED
                return False
            
            # Initialize execution context
            await self._initialize_context()
            
            # Parse execution plan
            await self._parse_execution_plan()
            
            self.status = OrchestrationStatus.RUNNING
            
            # Start orchestration based on pattern
            asyncio.create_task(self._execute_orchestration())
            
            logger.info(
                "Orchestration started successfully",
                orchestration_id=str(self.orchestration_id)
            )
            
            return True
            
        except Exception as e:
            self.status = OrchestrationStatus.FAILED
            logger.error(
                "Failed to start orchestration",
                orchestration_id=str(self.orchestration_id),
                error=str(e)
            )
            return False
    
    async def stop(self, graceful: bool = True) -> bool:
        """Stop the orchestration"""
        try:
            if self.status not in [OrchestrationStatus.RUNNING, OrchestrationStatus.PAUSED]:
                return False
            
            previous_status = self.status
            self.status = OrchestrationStatus.CANCELLED
            
            if graceful:
                # Cancel pending tasks gracefully
                await self._cancel_pending_tasks()
            
            self.end_time = datetime.utcnow()
            
            logger.info(
                "Orchestration stopped",
                orchestration_id=str(self.orchestration_id),
                graceful=graceful
            )
            
            return True
            
        except Exception as e:
            logger.error(
                "Failed to stop orchestration",
                orchestration_id=str(self.orchestration_id),
                error=str(e)
            )
            return False
    
    async def pause(self) -> bool:
        """Pause the orchestration"""
        if self.status != OrchestrationStatus.RUNNING:
            return False
        
        self.status = OrchestrationStatus.PAUSED
        
        # Pause all active agents
        for agent_id in self.agent_ids:
            await agent_manager.pause_agent(agent_id)
        
        logger.info("Orchestration paused", orchestration_id=str(self.orchestration_id))
        return True
    
    async def resume(self) -> bool:
        """Resume the orchestration"""
        if self.status != OrchestrationStatus.PAUSED:
            return False
        
        self.status = OrchestrationStatus.RUNNING
        
        # Resume all paused agents
        for agent_id in self.agent_ids:
            await agent_manager.resume_agent(agent_id)
        
        logger.info("Orchestration resumed", orchestration_id=str(self.orchestration_id))
        return True
    
    async def add_task(
        self,
        task_id: uuid.UUID,
        task_type: str,
        task_data: Dict[str, Any],
        dependencies: Optional[List[uuid.UUID]] = None
    ) -> bool:
        """Add a task to the orchestration"""
        try:
            task = Task(
                id=task_id,
                title=task_data.get('title', f'Task {task_type}'),
                type=task_type,
                input_data=task_data,
                orchestration_id=self.orchestration_id,
                status=TaskStatus.PENDING
            )
            
            self.tasks[task_id] = task
            self.task_dependencies[task_id] = set(dependencies or [])
            self.total_steps += 1
            
            logger.info(
                "Task added to orchestration",
                orchestration_id=str(self.orchestration_id),
                task_id=str(task_id)
            )
            
            return True
            
        except Exception as e:
            logger.error(
                "Failed to add task",
                orchestration_id=str(self.orchestration_id),
                error=str(e)
            )
            return False
    
    async def get_progress(self) -> Dict[str, Any]:
        """Get orchestration progress"""
        progress_percentage = (self.completed_steps / self.total_steps * 100) if self.total_steps > 0 else 0
        
        return {
            'orchestration_id': str(self.orchestration_id),
            'status': self.status,
            'pattern': self.pattern,
            'progress_percentage': progress_percentage,
            'total_steps': self.total_steps,
            'completed_steps': self.completed_steps,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'duration_seconds': (
                (self.end_time - self.start_time).total_seconds()
                if self.start_time and self.end_time else None
            ),
            'agent_count': len(self.agent_ids),
            'results': self.results
        }
    
    # Private methods
    
    async def _validate_agents(self) -> bool:
        """Validate all agents are available"""
        for agent_id in self.agent_ids:
            runtime_info = await agent_manager.get_runtime_info(agent_id)
            if not runtime_info:
                logger.error(
                    "Agent not available for orchestration",
                    agent_id=str(agent_id)
                )
                return False
        return True
    
    async def _initialize_context(self):
        """Initialize orchestration context"""
        self.context = {
            'orchestration_id': str(self.orchestration_id),
            'pattern': self.pattern,
            'start_time': self.start_time,
            'agent_ids': [str(aid) for aid in self.agent_ids],
            'config': self.config
        }
    
    async def _parse_execution_plan(self):
        """Parse and validate execution plan with task decomposition"""
        try:
            # Get execution plan from config
            execution_plan = self.execution_plan
            
            if not execution_plan:
                logger.warning(
                    "No execution plan provided, creating basic plan",
                    orchestration_id=str(self.orchestration_id)
                )
                return
            
            # Parse tasks from execution plan
            raw_tasks = execution_plan.get('tasks', [])
            
            # Decompose complex tasks if needed
            for raw_task in raw_tasks:
                await self._decompose_and_add_task(raw_task)
            
            # Calculate dependencies
            await self._calculate_task_dependencies()
            
            # Validate task plan
            await self._validate_task_plan()
            
            logger.info(
                "Execution plan parsed successfully",
                orchestration_id=str(self.orchestration_id),
                total_tasks=len(self.tasks)
            )
            
        except Exception as e:
            logger.error(
                "Failed to parse execution plan",
                orchestration_id=str(self.orchestration_id),
                error=str(e)
            )
            raise
    
    async def _decompose_and_add_task(self, raw_task: Dict[str, Any]):
        """Decompose complex tasks into subtasks and add to orchestration"""
        task_id = uuid.uuid4()
        task_type = raw_task.get('type', 'generic')
        task_data = raw_task.get('data', {})
        dependencies = [uuid.UUID(dep) for dep in raw_task.get('dependencies', [])]
        
        # Check if task needs decomposition
        if self._should_decompose_task(raw_task):
            subtasks = await self._decompose_task(raw_task)
            
            # Add parent task
            await self.add_task(task_id, task_type, task_data, dependencies)
            
            # Add subtasks with parent dependency
            for subtask in subtasks:
                subtask_id = uuid.uuid4()
                await self.add_task(
                    subtask_id,
                    subtask.get('type', 'subtask'),
                    subtask.get('data', {}),
                    [task_id]  # Depend on parent task
                )
        else:
            # Add task directly
            await self.add_task(task_id, task_type, task_data, dependencies)
    
    def _should_decompose_task(self, raw_task: Dict[str, Any]) -> bool:
        """Determine if a task should be decomposed into subtasks"""
        task_type = raw_task.get('type', '')
        complexity = raw_task.get('complexity', 'simple')
        
        # Decompose if task is marked as complex or is a known complex type
        complex_types = ['analysis', 'generation', 'multi_step', 'workflow']
        
        return (
            complexity in ['complex', 'high'] or
            task_type in complex_types or
            raw_task.get('decompose', False)
        )
    
    async def _decompose_task(self, raw_task: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Decompose a complex task into smaller subtasks"""
        task_type = raw_task.get('type', '')
        task_data = raw_task.get('data', {})
        
        # Simple decomposition logic - can be enhanced with AI
        if task_type == 'analysis':
            return [
                {'type': 'data_collection', 'data': {'source': task_data.get('source')}},
                {'type': 'data_processing', 'data': {'format': task_data.get('format')}},
                {'type': 'report_generation', 'data': {'output': task_data.get('output')}}
            ]
        elif task_type == 'generation':
            return [
                {'type': 'requirement_analysis', 'data': task_data},
                {'type': 'design_creation', 'data': task_data},
                {'type': 'implementation', 'data': task_data},
                {'type': 'testing', 'data': task_data}
            ]
        else:
            # Default decomposition
            return [
                {'type': 'preparation', 'data': task_data},
                {'type': 'execution', 'data': task_data},
                {'type': 'validation', 'data': task_data}
            ]
    
    async def _calculate_task_dependencies(self):
        """Calculate and optimize task dependencies"""
        # Update total steps based on actual tasks
        self.total_steps = len(self.tasks)
        
        # Optimize dependencies to reduce critical path
        await self._optimize_dependencies()
    
    async def _optimize_dependencies(self):
        """Optimize task dependencies to minimize execution time"""
        # Identify tasks that can be parallelized
        parallel_candidates = []
        
        for task_id, dependencies in self.task_dependencies.items():
            if not dependencies:  # No dependencies - can start immediately
                parallel_candidates.append(task_id)
        
        logger.info(
            "Dependency optimization complete",
            orchestration_id=str(self.orchestration_id),
            parallel_tasks=len(parallel_candidates)
        )
    
    async def _validate_task_plan(self):
        """Validate the task execution plan"""
        # Check for circular dependencies
        if self._has_circular_dependencies():
            raise ValueError("Circular dependencies detected in task plan")
        
        # Validate all agents have required capabilities
        await self._validate_agent_capabilities()
        
        # Check resource requirements
        await self._validate_resource_requirements()
    
    def _has_circular_dependencies(self) -> bool:
        """Check for circular dependencies in task graph"""
        visited = set()
        rec_stack = set()
        
        def has_cycle(task_id: uuid.UUID) -> bool:
            if task_id in rec_stack:
                return True
            if task_id in visited:
                return False
            
            visited.add(task_id)
            rec_stack.add(task_id)
            
            for dep_id in self.task_dependencies.get(task_id, []):
                if has_cycle(dep_id):
                    return True
            
            rec_stack.remove(task_id)
            return False
        
        for task_id in self.tasks:
            if task_id not in visited:
                if has_cycle(task_id):
                    return True
        
        return False
    
    async def _validate_agent_capabilities(self):
        """Validate that agents have required capabilities for tasks"""
        # TODO: Implement capability validation with agent registry
        pass
    
    async def _validate_resource_requirements(self):
        """Validate resource requirements can be met"""
        # TODO: Implement resource requirement validation
        pass
    
    async def _execute_orchestration(self):
        """Execute orchestration based on pattern"""
        try:
            if self.pattern == OrchestrationPattern.SEQUENTIAL:
                await self._execute_sequential()
            elif self.pattern == OrchestrationPattern.PARALLEL:
                await self._execute_parallel()
            elif self.pattern == OrchestrationPattern.HIERARCHICAL:
                await self._execute_hierarchical()
            elif self.pattern == OrchestrationPattern.PEER_TO_PEER:
                await self._execute_peer_to_peer()
            elif self.pattern == OrchestrationPattern.EVENT_DRIVEN:
                await self._execute_event_driven()
            
            # Mark as completed if successful
            if self.status == OrchestrationStatus.RUNNING:
                self.status = OrchestrationStatus.COMPLETED
                self.end_time = datetime.utcnow()
                
                logger.info(
                    "Orchestration completed successfully",
                    orchestration_id=str(self.orchestration_id)
                )
                
        except Exception as e:
            self.status = OrchestrationStatus.FAILED
            self.end_time = datetime.utcnow()
            
            logger.error(
                "Orchestration execution failed",
                orchestration_id=str(self.orchestration_id),
                error=str(e)
            )
    
    async def _execute_sequential(self):
        """Execute tasks sequentially"""
        logger.info(
            "Executing sequential orchestration",
            orchestration_id=str(self.orchestration_id)
        )
        
        # Sort tasks by dependencies
        sorted_tasks = self._topological_sort_tasks()
        
        for task_id in sorted_tasks:
            if self.status != OrchestrationStatus.RUNNING:
                break
            
            task = self.tasks[task_id]
            agent_id = self._select_agent_for_task(task)
            
            # Execute task
            result = await self._execute_task_on_agent(task, agent_id)
            
            # Store result
            self.results[str(task_id)] = result
            self.completed_tasks.add(task_id)
            self.completed_steps += 1
            
            # Update progress
            await self._update_progress()
    
    async def _execute_parallel(self):
        """Execute tasks in parallel"""
        logger.info(
            "Executing parallel orchestration",
            orchestration_id=str(self.orchestration_id)
        )
        
        # Group tasks by dependency level
        task_levels = self._group_tasks_by_level()
        
        for level, level_tasks in task_levels.items():
            if self.status != OrchestrationStatus.RUNNING:
                break
            
            # Execute all tasks at this level in parallel
            tasks = []
            for task_id in level_tasks:
                task = self.tasks[task_id]
                agent_id = self._select_agent_for_task(task)
                
                # Create execution task
                exec_task = asyncio.create_task(
                    self._execute_task_on_agent(task, agent_id)
                )
                tasks.append((task_id, exec_task))
            
            # Wait for all tasks to complete
            for task_id, exec_task in tasks:
                try:
                    result = await exec_task
                    self.results[str(task_id)] = result
                    self.completed_tasks.add(task_id)
                    self.completed_steps += 1
                except Exception as e:
                    logger.error(
                        "Task execution failed",
                        task_id=str(task_id),
                        error=str(e)
                    )
            
            # Update progress
            await self._update_progress()
    
    async def _execute_hierarchical(self):
        """Execute tasks in hierarchical pattern"""
        logger.info(
            "Executing hierarchical orchestration",
            orchestration_id=str(self.orchestration_id)
        )
        
        # Implement master-worker pattern
        if len(self.agent_ids) < 2:
            raise ValueError("Hierarchical pattern requires at least 2 agents")
        
        master_agent_id = self.agent_ids[0]
        worker_agent_ids = self.agent_ids[1:]
        
        # Master coordinates task distribution
        for task_id, task in self.tasks.items():
            if self.status != OrchestrationStatus.RUNNING:
                break
            
            # Master decides which worker gets the task
            worker_id = worker_agent_ids[
                hash(task_id) % len(worker_agent_ids)
            ]
            
            # Execute on worker
            result = await self._execute_task_on_agent(task, worker_id)
            
            # Report back to master
            await self._send_result_to_master(
                master_agent_id, task_id, result
            )
            
            self.results[str(task_id)] = result
            self.completed_tasks.add(task_id)
            self.completed_steps += 1
            
            await self._update_progress()
    
    async def _execute_peer_to_peer(self):
        """Execute tasks in peer-to-peer pattern"""
        logger.info(
            "Executing peer-to-peer orchestration",
            orchestration_id=str(self.orchestration_id)
        )
        
        # Agents collaborate directly
        # Each agent can send tasks to any other agent
        
        # Distribute tasks evenly among agents
        agent_task_queues = {agent_id: [] for agent_id in self.agent_ids}
        
        for idx, (task_id, task) in enumerate(self.tasks.items()):
            agent_id = self.agent_ids[idx % len(self.agent_ids)]
            agent_task_queues[agent_id].append((task_id, task))
        
        # Execute tasks with peer collaboration
        tasks = []
        for agent_id, task_list in agent_task_queues.items():
            for task_id, task in task_list:
                exec_task = asyncio.create_task(
                    self._execute_task_with_collaboration(
                        task, agent_id, self.agent_ids
                    )
                )
                tasks.append((task_id, exec_task))
        
        # Wait for all tasks
        for task_id, exec_task in tasks:
            try:
                result = await exec_task
                self.results[str(task_id)] = result
                self.completed_tasks.add(task_id)
                self.completed_steps += 1
            except Exception as e:
                logger.error(
                    "Collaborative task failed",
                    task_id=str(task_id),
                    error=str(e)
                )
        
        await self._update_progress()
    
    async def _execute_event_driven(self):
        """Execute tasks based on events"""
        logger.info(
            "Executing event-driven orchestration",
            orchestration_id=str(self.orchestration_id)
        )
        
        # Subscribe to orchestration events
        await message_queue_service.subscribe_to_topic(
            f"orchestration.{self.orchestration_id}",
            f"orchestration_{self.orchestration_id}",
            self._handle_orchestration_event
        )
        
        # Start initial tasks (no dependencies)
        initial_tasks = [
            task_id for task_id, deps in self.task_dependencies.items()
            if not deps
        ]
        
        for task_id in initial_tasks:
            task = self.tasks[task_id]
            agent_id = self._select_agent_for_task(task)
            
            # Execute task asynchronously
            asyncio.create_task(
                self._execute_task_with_events(task, agent_id)
            )
        
        # Wait for all tasks to complete via events
        while (self.completed_steps < self.total_steps and 
               self.status == OrchestrationStatus.RUNNING):
            await asyncio.sleep(1)
        
        # Unsubscribe from events
        message_queue_service.unsubscribe_from_topic(
            f"orchestration.{self.orchestration_id}"
        )
    
    async def _execute_task_on_agent(
        self,
        task: Task,
        agent_id: uuid.UUID
    ) -> Dict[str, Any]:
        """Execute a task on a specific agent"""
        try:
            task_data = {
                'id': str(task.id),
                'type': task.type,
                'input_data': task.input_data,
                'metadata': {
                    'orchestration_id': str(self.orchestration_id),
                    'orchestration_pattern': self.pattern
                }
            }
            
            # Update task status
            task.status = TaskStatus.IN_PROGRESS
            task.assigned_agent_id = agent_id
            task.started_at = datetime.utcnow()
            
            # Execute task
            result = await agent_manager.execute_task(agent_id, task_data)
            
            # Update task status
            task.status = TaskStatus.COMPLETED
            task.completed_at = datetime.utcnow()
            task.output_data = result
            
            # Publish completion event
            await message_queue_service.publish_agent_event(
                str(agent_id),
                'task_completed',
                {
                    'task_id': str(task.id),
                    'orchestration_id': str(self.orchestration_id),
                    'result': result
                }
            )
            
            return result
            
        except Exception as e:
            task.status = TaskStatus.FAILED
            logger.error(
                "Task execution failed",
                task_id=str(task.id),
                agent_id=str(agent_id),
                error=str(e)
            )
            raise
    
    async def _execute_task_with_collaboration(
        self,
        task: Task,
        primary_agent_id: uuid.UUID,
        all_agent_ids: List[uuid.UUID]
    ) -> Dict[str, Any]:
        """Execute task with peer collaboration"""
        # Primary agent executes with ability to delegate
        task_data = {
            'id': str(task.id),
            'type': task.type,
            'input_data': task.input_data,
            'metadata': {
                'orchestration_id': str(self.orchestration_id),
                'orchestration_pattern': self.pattern,
                'peer_agents': [str(aid) for aid in all_agent_ids if aid != primary_agent_id]
            }
        }
        
        return await agent_manager.execute_task(primary_agent_id, task_data)
    
    async def _execute_task_with_events(
        self,
        task: Task,
        agent_id: uuid.UUID
    ):
        """Execute task and handle events"""
        try:
            result = await self._execute_task_on_agent(task, agent_id)
            
            # Trigger dependent tasks
            await self._trigger_dependent_tasks(task.id)
            
        except Exception as e:
            logger.error(
                "Event-driven task failed",
                task_id=str(task.id),
                error=str(e)
            )
    
    async def _handle_orchestration_event(self, message_data: Dict[str, Any]):
        """Handle orchestration events"""
        event_type = message_data.get('value', {}).get('event_type')
        
        if event_type == 'task_completed':
            task_id = uuid.UUID(message_data['value']['task_id'])
            await self._trigger_dependent_tasks(task_id)
    
    async def _trigger_dependent_tasks(self, completed_task_id: uuid.UUID):
        """Trigger tasks that depend on completed task"""
        for task_id, dependencies in self.task_dependencies.items():
            if completed_task_id in dependencies and task_id not in self.completed_tasks:
                # Check if all dependencies are satisfied
                if dependencies.issubset(self.completed_tasks):
                    task = self.tasks[task_id]
                    agent_id = self._select_agent_for_task(task)
                    
                    # Execute task
                    asyncio.create_task(
                        self._execute_task_with_events(task, agent_id)
                    )
    
    def _select_agent_for_task(self, task: Task) -> uuid.UUID:
        """Select best agent for task execution with intelligent capability matching"""
        if not self.agent_ids:
            raise ValueError("No agents available for task execution")
        
        # Get task requirements
        task_capabilities = task.input_data.get('required_capabilities', [])
        task_type = task.type
        
        # Score agents based on multiple criteria
        agent_scores = {}
        for agent_id in self.agent_ids:
            score = self._calculate_agent_score(agent_id, task_capabilities, task_type)
            agent_scores[agent_id] = score
        
        # Select agent with highest score
        best_agent = max(agent_scores.items(), key=lambda x: x[1])[0]
        
        logger.info(
            "Agent selected for task",
            task_id=str(task.id),
            selected_agent=str(best_agent),
            score=agent_scores[best_agent]
        )
        
        return best_agent
    
    def _calculate_agent_score(self, agent_id: uuid.UUID, required_capabilities: List[str], task_type: str) -> float:
        """Calculate agent suitability score based on capabilities, load, and performance"""
        try:
            # Base score
            score = 0.0
            
            # TODO: Get real agent info from agent_manager
            # For now, simulate intelligent scoring
            
            # Capability matching (40% of score)
            capability_score = self._score_capabilities(agent_id, required_capabilities)
            score += capability_score * 0.4
            
            # Load balancing (30% of score) - prefer less loaded agents
            load_score = self._score_agent_load(agent_id)
            score += load_score * 0.3
            
            # Performance history (20% of score)
            performance_score = self._score_agent_performance(agent_id, task_type)
            score += performance_score * 0.2
            
            # Availability (10% of score)
            availability_score = self._score_agent_availability(agent_id)
            score += availability_score * 0.1
            
            return score
            
        except Exception as e:
            logger.warning(
                "Error calculating agent score, using fallback",
                agent_id=str(agent_id),
                error=str(e)
            )
            return 0.5  # Fallback score
    
    def _score_capabilities(self, agent_id: uuid.UUID, required_capabilities: List[str]) -> float:
        """Score agent based on capability matching"""
        # TODO: Implement real capability matching with agent registry
        # For now, return a base score with some variation
        return 0.7 + (hash(str(agent_id)) % 30) / 100
    
    def _score_agent_load(self, agent_id: uuid.UUID) -> float:
        """Score agent based on current load (higher score = lower load)"""
        # TODO: Get real load metrics from agent_manager
        # For now, simulate load scoring
        simulated_load = (hash(str(agent_id)) % 100) / 100
        return 1.0 - simulated_load  # Invert so lower load = higher score
    
    def _score_agent_performance(self, agent_id: uuid.UUID, task_type: str) -> float:
        """Score agent based on historical performance for similar tasks"""
        # TODO: Implement performance history tracking
        # For now, return a base performance score
        return 0.8 + (hash(str(agent_id) + task_type) % 20) / 100
    
    def _score_agent_availability(self, agent_id: uuid.UUID) -> float:
        """Score agent based on availability and health"""
        # TODO: Check real agent health status
        # For now, assume most agents are available
        return 0.9
    
    def _topological_sort_tasks(self) -> List[uuid.UUID]:
        """Sort tasks based on dependencies"""
        sorted_tasks = []
        visited = set()
        
        def visit(task_id: uuid.UUID):
            if task_id in visited:
                return
            visited.add(task_id)
            
            # Visit dependencies first
            for dep_id in self.task_dependencies[task_id]:
                visit(dep_id)
            
            sorted_tasks.append(task_id)
        
        for task_id in self.tasks:
            visit(task_id)
        
        return sorted_tasks
    
    def _group_tasks_by_level(self) -> Dict[int, List[uuid.UUID]]:
        """Group tasks by dependency level"""
        levels = {}
        task_levels = {}
        
        # Calculate level for each task
        def calculate_level(task_id: uuid.UUID) -> int:
            if task_id in task_levels:
                return task_levels[task_id]
            
            if not self.task_dependencies[task_id]:
                level = 0
            else:
                level = max(
                    calculate_level(dep_id) + 1
                    for dep_id in self.task_dependencies[task_id]
                )
            
            task_levels[task_id] = level
            return level
        
        # Group by level
        for task_id in self.tasks:
            level = calculate_level(task_id)
            if level not in levels:
                levels[level] = []
            levels[level].append(task_id)
        
        return dict(sorted(levels.items()))
    
    async def _send_result_to_master(
        self,
        master_id: uuid.UUID,
        task_id: uuid.UUID,
        result: Dict[str, Any]
    ):
        """Send task result to master agent"""
        await message_queue_service.publish_orchestration_command(
            str(self.orchestration_id),
            'task_result',
            {
                'task_id': str(task_id),
                'result': result,
                'worker_id': str(self.agent_ids[0])  # Placeholder
            },
            target_agents=[str(master_id)]
        )
    
    async def _update_progress(self):
        """Update orchestration progress"""
        progress = await self.get_progress()
        
        # Publish progress update
        await message_queue_service.publish_orchestration_command(
            str(self.orchestration_id),
            'progress_update',
            progress
        )
    
    async def _cancel_pending_tasks(self):
        """Cancel all pending tasks"""
        for task_id, task in self.tasks.items():
            if task.status in [TaskStatus.PENDING, TaskStatus.ASSIGNED]:
                task.status = TaskStatus.CANCELLED