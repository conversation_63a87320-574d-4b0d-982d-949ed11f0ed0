/* tslint:disable */
/* eslint-disable */
/**
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface DealUpdate
 */
export interface DealUpdate {
    /**
     * 
     * @type {string}
     * @memberof DealUpdate
     */
    title?: string;
    /**
     * 
     * @type {string}
     * @memberof DealUpdate
     */
    description?: string;
    /**
     * 
     * @type {number}
     * @memberof DealUpdate
     */
    estimatedValue?: number;
    /**
     * 
     * @type {string}
     * @memberof DealUpdate
     */
    companyId?: string;
    /**
     * 
     * @type {string}
     * @memberof DealUpdate
     */
    dealStageId?: string;
    /**
     * 
     * @type {Date}
     * @memberof DealUpdate
     */
    expectedCloseDate?: Date;
}

/**
 * Check if a given object implements the DealUpdate interface.
 */
export function instanceOfDealUpdate(value: object): value is DealUpdate {
    return true;
}

export function DealUpdateFromJSON(json: any): DealUpdate {
    return DealUpdateFromJSONTyped(json, false);
}

export function DealUpdateFromJSONTyped(json: any, ignoreDiscriminator: boolean): DealUpdate {
    if (json == null) {
        return json;
    }
    return {
        
        'title': json['title'] == null ? undefined : json['title'],
        'description': json['description'] == null ? undefined : json['description'],
        'estimatedValue': json['estimated_value'] == null ? undefined : json['estimated_value'],
        'companyId': json['company_id'] == null ? undefined : json['company_id'],
        'dealStageId': json['deal_stage_id'] == null ? undefined : json['deal_stage_id'],
        'expectedCloseDate': json['expected_close_date'] == null ? undefined : (new Date(json['expected_close_date'])),
    };
}

  export function DealUpdateToJSON(json: any): DealUpdate {
      return DealUpdateToJSONTyped(json, false);
  }

  export function DealUpdateToJSONTyped(value?: DealUpdate | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'title': value['title'],
        'description': value['description'],
        'estimated_value': value['estimatedValue'],
        'company_id': value['companyId'],
        'deal_stage_id': value['dealStageId'],
        'expected_close_date': value['expectedCloseDate'] == null ? undefined : ((value['expectedCloseDate']).toISOString().substring(0,10)),
    };
}

