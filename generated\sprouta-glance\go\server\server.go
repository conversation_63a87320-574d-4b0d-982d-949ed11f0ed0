// Package server provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.5.0 DO NOT EDIT.
package server

import (
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/oapi-codegen/runtime"
	openapi_types "github.com/oapi-codegen/runtime/types"
)

const (
	BearerAuthScopes = "bearerAuth.Scopes"
)

// Defines values for AnalysisResponseStatus.
const (
	AnalysisResponseStatusCompleted  AnalysisResponseStatus = "completed"
	AnalysisResponseStatusFailed     AnalysisResponseStatus = "failed"
	AnalysisResponseStatusProcessing AnalysisResponseStatus = "processing"
)

// Defines values for CaseStudyDocumentType.
const (
	CaseStudyDocumentTypeDoc      CaseStudyDocumentType = "doc"
	CaseStudyDocumentTypeDocx     CaseStudyDocumentType = "docx"
	CaseStudyDocumentTypeMarkdown CaseStudyDocumentType = "markdown"
	CaseStudyDocumentTypePdf      CaseStudyDocumentType = "pdf"
)

// Defines values for CaseStudyCreateDocumentType.
const (
	CaseStudyCreateDocumentTypeDoc      CaseStudyCreateDocumentType = "doc"
	CaseStudyCreateDocumentTypeDocx     CaseStudyCreateDocumentType = "docx"
	CaseStudyCreateDocumentTypeMarkdown CaseStudyCreateDocumentType = "markdown"
	CaseStudyCreateDocumentTypePdf      CaseStudyCreateDocumentType = "pdf"
)

// Defines values for MeetingTranscriptStatus.
const (
	MeetingTranscriptStatusCompleted  MeetingTranscriptStatus = "completed"
	MeetingTranscriptStatusFailed     MeetingTranscriptStatus = "failed"
	MeetingTranscriptStatusPending    MeetingTranscriptStatus = "pending"
	MeetingTranscriptStatusProcessing MeetingTranscriptStatus = "processing"
)

// Defines values for MeetingTranscriptTranscriptFormat.
const (
	MeetingTranscriptTranscriptFormatDoc  MeetingTranscriptTranscriptFormat = "doc"
	MeetingTranscriptTranscriptFormatDocx MeetingTranscriptTranscriptFormat = "docx"
	MeetingTranscriptTranscriptFormatPdf  MeetingTranscriptTranscriptFormat = "pdf"
	MeetingTranscriptTranscriptFormatTxt  MeetingTranscriptTranscriptFormat = "txt"
	MeetingTranscriptTranscriptFormatVtt  MeetingTranscriptTranscriptFormat = "vtt"
)

// Defines values for MeetingTranscriptCreateTranscriptFormat.
const (
	MeetingTranscriptCreateTranscriptFormatDoc  MeetingTranscriptCreateTranscriptFormat = "doc"
	MeetingTranscriptCreateTranscriptFormatDocx MeetingTranscriptCreateTranscriptFormat = "docx"
	MeetingTranscriptCreateTranscriptFormatPdf  MeetingTranscriptCreateTranscriptFormat = "pdf"
	MeetingTranscriptCreateTranscriptFormatTxt  MeetingTranscriptCreateTranscriptFormat = "txt"
	MeetingTranscriptCreateTranscriptFormatVtt  MeetingTranscriptCreateTranscriptFormat = "vtt"
)

// Defines values for MeetingTranscriptUpdateStatus.
const (
	MeetingTranscriptUpdateStatusCompleted  MeetingTranscriptUpdateStatus = "completed"
	MeetingTranscriptUpdateStatusFailed     MeetingTranscriptUpdateStatus = "failed"
	MeetingTranscriptUpdateStatusPending    MeetingTranscriptUpdateStatus = "pending"
	MeetingTranscriptUpdateStatusProcessing MeetingTranscriptUpdateStatus = "processing"
)

// Defines values for TranscriptPreviewOriginalType.
const (
	TranscriptPreviewOriginalTypeDocx TranscriptPreviewOriginalType = "docx"
	TranscriptPreviewOriginalTypeMd   TranscriptPreviewOriginalType = "md"
	TranscriptPreviewOriginalTypePdf  TranscriptPreviewOriginalType = "pdf"
	TranscriptPreviewOriginalTypeVtt  TranscriptPreviewOriginalType = "vtt"
)

// Defines values for TranscriptPreviewStatus.
const (
	TranscriptPreviewStatusConverted  TranscriptPreviewStatus = "converted"
	TranscriptPreviewStatusFailed     TranscriptPreviewStatus = "failed"
	TranscriptPreviewStatusProcessing TranscriptPreviewStatus = "processing"
)

// Defines values for TranscriptUploadResponseOriginalType.
const (
	TranscriptUploadResponseOriginalTypeDocx TranscriptUploadResponseOriginalType = "docx"
	TranscriptUploadResponseOriginalTypeMd   TranscriptUploadResponseOriginalType = "md"
	TranscriptUploadResponseOriginalTypePdf  TranscriptUploadResponseOriginalType = "pdf"
	TranscriptUploadResponseOriginalTypeVtt  TranscriptUploadResponseOriginalType = "vtt"
)

// Defines values for TranscriptUploadResponseStatus.
const (
	TranscriptUploadResponseStatusConverted  TranscriptUploadResponseStatus = "converted"
	TranscriptUploadResponseStatusFailed     TranscriptUploadResponseStatus = "failed"
	TranscriptUploadResponseStatusProcessing TranscriptUploadResponseStatus = "processing"
)

// Defines values for GetAuthCallbackProviderParamsProvider.
const (
	GetAuthCallbackProviderParamsProviderGoogle GetAuthCallbackProviderParamsProvider = "google"
)

// Defines values for PostAuthSigninOauthJSONBodyProvider.
const (
	PostAuthSigninOauthJSONBodyProviderGoogle PostAuthSigninOauthJSONBodyProvider = "google"
)

// Defines values for ListMeetingTranscriptsParamsStatus.
const (
	ListMeetingTranscriptsParamsStatusCompleted  ListMeetingTranscriptsParamsStatus = "completed"
	ListMeetingTranscriptsParamsStatusFailed     ListMeetingTranscriptsParamsStatus = "failed"
	ListMeetingTranscriptsParamsStatusPending    ListMeetingTranscriptsParamsStatus = "pending"
	ListMeetingTranscriptsParamsStatusProcessing ListMeetingTranscriptsParamsStatus = "processing"
)

// AnalysisResponse defines model for AnalysisResponse.
type AnalysisResponse struct {
	Message *string                 `json:"message,omitempty"`
	Status  *AnalysisResponseStatus `json:"status,omitempty"`
}

// AnalysisResponseStatus defines model for AnalysisResponse.Status.
type AnalysisResponseStatus string

// AuthResponse defines model for AuthResponse.
type AuthResponse struct {
	AccessToken  *string `json:"access_token,omitempty"`
	ExpiresIn    *int    `json:"expires_in,omitempty"`
	RefreshToken *string `json:"refresh_token,omitempty"`
	TokenType    *string `json:"token_type,omitempty"`
	User         *User   `json:"user,omitempty"`
}

// CaseStudy defines model for CaseStudy.
type CaseStudy struct {
	CreatedAt        *time.Time             `json:"created_at,omitempty"`
	CreatedBy        *openapi_types.UUID    `json:"created_by,omitempty"`
	DocumentContent  *string                `json:"document_content,omitempty"`
	DocumentFilename *string                `json:"document_filename,omitempty"`
	DocumentType     *CaseStudyDocumentType `json:"document_type,omitempty"`
	Id               *openapi_types.UUID    `json:"id,omitempty"`
	RelatedPeople    *[]openapi_types.UUID  `json:"related_people,omitempty"`
	RelatedThemes    *[]openapi_types.UUID  `json:"related_themes,omitempty"`
	Title            *string                `json:"title,omitempty"`
	UpdatedAt        *time.Time             `json:"updated_at,omitempty"`
}

// CaseStudyDocumentType defines model for CaseStudy.DocumentType.
type CaseStudyDocumentType string

// CaseStudyCreate defines model for CaseStudyCreate.
type CaseStudyCreate struct {
	DocumentContent  *string                      `json:"document_content,omitempty"`
	DocumentFilename *string                      `json:"document_filename,omitempty"`
	DocumentType     *CaseStudyCreateDocumentType `json:"document_type,omitempty"`
	RelatedPeople    *[]openapi_types.UUID        `json:"related_people,omitempty"`
	RelatedThemes    *[]openapi_types.UUID        `json:"related_themes,omitempty"`
	Title            *string                      `json:"title,omitempty"`
}

// CaseStudyCreateDocumentType defines model for CaseStudyCreate.DocumentType.
type CaseStudyCreateDocumentType string

// CaseStudyUpdate defines model for CaseStudyUpdate.
type CaseStudyUpdate struct {
	DocumentContent *string               `json:"document_content,omitempty"`
	RelatedPeople   *[]openapi_types.UUID `json:"related_people,omitempty"`
	RelatedThemes   *[]openapi_types.UUID `json:"related_themes,omitempty"`
	Title           *string               `json:"title,omitempty"`
}

// Error defines model for Error.
type Error struct {
	Code    string                  `json:"code"`
	Details *map[string]interface{} `json:"details,omitempty"`
	Message string                  `json:"message"`
}

// MeetingTranscript defines model for MeetingTranscript.
type MeetingTranscript struct {
	Attendees          *[]openapi_types.UUID              `json:"attendees,omitempty"`
	CompanyInfo        *string                            `json:"company_info,omitempty"`
	CreatedAt          *time.Time                         `json:"created_at,omitempty"`
	CreatedBy          *openapi_types.UUID                `json:"created_by,omitempty"`
	Id                 *openapi_types.UUID                `json:"id,omitempty"`
	KeyInsights        *[]string                          `json:"key_insights,omitempty"`
	LinkedPeople       *[]openapi_types.UUID              `json:"linked_people,omitempty"`
	MeetingName        *string                            `json:"meeting_name,omitempty"`
	Status             *MeetingTranscriptStatus           `json:"status,omitempty"`
	Summary            *string                            `json:"summary,omitempty"`
	TranscriptContent  *string                            `json:"transcript_content,omitempty"`
	TranscriptFilename *string                            `json:"transcript_filename,omitempty"`
	TranscriptFormat   *MeetingTranscriptTranscriptFormat `json:"transcript_format,omitempty"`
	UpdatedAt          *time.Time                         `json:"updated_at,omitempty"`
}

// MeetingTranscriptStatus defines model for MeetingTranscript.Status.
type MeetingTranscriptStatus string

// MeetingTranscriptTranscriptFormat defines model for MeetingTranscript.TranscriptFormat.
type MeetingTranscriptTranscriptFormat string

// MeetingTranscriptCreate defines model for MeetingTranscriptCreate.
type MeetingTranscriptCreate struct {
	Attendees          *[]openapi_types.UUID                    `json:"attendees,omitempty"`
	CompanyInfo        *string                                  `json:"company_info,omitempty"`
	MeetingName        string                                   `json:"meeting_name"`
	TranscriptContent  *string                                  `json:"transcript_content,omitempty"`
	TranscriptFilename *string                                  `json:"transcript_filename,omitempty"`
	TranscriptFormat   *MeetingTranscriptCreateTranscriptFormat `json:"transcript_format,omitempty"`
}

// MeetingTranscriptCreateTranscriptFormat defines model for MeetingTranscriptCreate.TranscriptFormat.
type MeetingTranscriptCreateTranscriptFormat string

// MeetingTranscriptUpdate defines model for MeetingTranscriptUpdate.
type MeetingTranscriptUpdate struct {
	Attendees    *[]openapi_types.UUID          `json:"attendees,omitempty"`
	CompanyInfo  *string                        `json:"company_info,omitempty"`
	KeyInsights  *[]string                      `json:"key_insights,omitempty"`
	LinkedPeople *[]openapi_types.UUID          `json:"linked_people,omitempty"`
	MeetingName  *string                        `json:"meeting_name,omitempty"`
	Status       *MeetingTranscriptUpdateStatus `json:"status,omitempty"`
	Summary      *string                        `json:"summary,omitempty"`
}

// MeetingTranscriptUpdateStatus defines model for MeetingTranscriptUpdate.Status.
type MeetingTranscriptUpdateStatus string

// Person defines model for Person.
type Person struct {
	CompanyEmail *openapi_types.Email `json:"company_email,omitempty"`
	CompanyTitle *string              `json:"company_title,omitempty"`
	CreatedAt    *time.Time           `json:"created_at,omitempty"`
	CreatedBy    *openapi_types.UUID  `json:"created_by,omitempty"`
	Id           *openapi_types.UUID  `json:"id,omitempty"`
	Name         *string              `json:"name,omitempty"`
	Notes        *string              `json:"notes,omitempty"`
	PhoneNumber  *string              `json:"phone_number,omitempty"`
	Specialties  *[]string            `json:"specialties,omitempty"`
	UpdatedAt    *time.Time           `json:"updated_at,omitempty"`
}

// PersonCreate defines model for PersonCreate.
type PersonCreate struct {
	CompanyEmail *openapi_types.Email `json:"company_email,omitempty"`
	CompanyTitle *string              `json:"company_title,omitempty"`
	Name         string               `json:"name"`
	Notes        *string              `json:"notes,omitempty"`
	PhoneNumber  *string              `json:"phone_number,omitempty"`
	Specialties  *[]string            `json:"specialties,omitempty"`
}

// PersonUpdate defines model for PersonUpdate.
type PersonUpdate struct {
	CompanyEmail *openapi_types.Email `json:"company_email,omitempty"`
	CompanyTitle *string              `json:"company_title,omitempty"`
	Name         *string              `json:"name,omitempty"`
	Notes        *string              `json:"notes,omitempty"`
	PhoneNumber  *string              `json:"phone_number,omitempty"`
	Specialties  *[]string            `json:"specialties,omitempty"`
}

// Session defines model for Session.
type Session struct {
	AccessToken  *string `json:"access_token,omitempty"`
	ExpiresIn    *int    `json:"expires_in,omitempty"`
	RefreshToken *string `json:"refresh_token,omitempty"`
	TokenType    *string `json:"token_type,omitempty"`
	User         *User   `json:"user,omitempty"`
}

// Theme defines model for Theme.
type Theme struct {
	CreatedAt   *time.Time          `json:"created_at,omitempty"`
	CreatedBy   *openapi_types.UUID `json:"created_by,omitempty"`
	Description *string             `json:"description,omitempty"`
	Id          *openapi_types.UUID `json:"id,omitempty"`
	LongName    *string             `json:"long_name,omitempty"`
	ShortName   *string             `json:"short_name,omitempty"`
	Subcategory *string             `json:"subcategory,omitempty"`
	Title       *string             `json:"title,omitempty"`
	UpdatedAt   *time.Time          `json:"updated_at,omitempty"`
}

// ThemeCreate defines model for ThemeCreate.
type ThemeCreate struct {
	Description *string `json:"description,omitempty"`
	LongName    *string `json:"long_name,omitempty"`
	ShortName   string  `json:"short_name"`
	Subcategory *string `json:"subcategory,omitempty"`
	Title       string  `json:"title"`
}

// ThemeUpdate defines model for ThemeUpdate.
type ThemeUpdate struct {
	Description *string `json:"description,omitempty"`
	LongName    *string `json:"long_name,omitempty"`
	ShortName   *string `json:"short_name,omitempty"`
	Subcategory *string `json:"subcategory,omitempty"`
	Title       *string `json:"title,omitempty"`
}

// TitleGenerationResponse defines model for TitleGenerationResponse.
type TitleGenerationResponse struct {
	Message *string `json:"message,omitempty"`
	Title   *string `json:"title,omitempty"`
}

// TranscriptPrefillResponse defines model for TranscriptPrefillResponse.
type TranscriptPrefillResponse struct {
	Message              *string   `json:"message,omitempty"`
	SuggestedAttendees   *[]string `json:"suggested_attendees,omitempty"`
	SuggestedCompanyInfo *string   `json:"suggested_company_info,omitempty"`
	SuggestedMeetingName *string   `json:"suggested_meeting_name,omitempty"`
}

// TranscriptPreview defines model for TranscriptPreview.
type TranscriptPreview struct {
	// ConvertedContent Converted markdown content
	ConvertedContent *string                        `json:"convertedContent,omitempty"`
	CreatedAt        *time.Time                     `json:"createdAt,omitempty"`
	ExpiresAt        *time.Time                     `json:"expiresAt,omitempty"`
	Filename         *string                        `json:"filename,omitempty"`
	Id               *openapi_types.UUID            `json:"id,omitempty"`
	MeetingName      *string                        `json:"meetingName"`
	OriginalType     *TranscriptPreviewOriginalType `json:"originalType,omitempty"`
	Status           *TranscriptPreviewStatus       `json:"status,omitempty"`
}

// TranscriptPreviewOriginalType defines model for TranscriptPreview.OriginalType.
type TranscriptPreviewOriginalType string

// TranscriptPreviewStatus defines model for TranscriptPreview.Status.
type TranscriptPreviewStatus string

// TranscriptUploadResponse defines model for TranscriptUploadResponse.
type TranscriptUploadResponse struct {
	// ConvertedContent Converted markdown content
	ConvertedContent *string                               `json:"convertedContent,omitempty"`
	CreatedAt        *time.Time                            `json:"createdAt,omitempty"`
	ExpiresAt        *time.Time                            `json:"expiresAt,omitempty"`
	Filename         *string                               `json:"filename,omitempty"`
	Id               *openapi_types.UUID                   `json:"id,omitempty"`
	MeetingName      *string                               `json:"meetingName"`
	OriginalType     *TranscriptUploadResponseOriginalType `json:"originalType,omitempty"`
	PreviewUrl       *string                               `json:"previewUrl,omitempty"`
	Status           *TranscriptUploadResponseStatus       `json:"status,omitempty"`
}

// TranscriptUploadResponseOriginalType defines model for TranscriptUploadResponse.OriginalType.
type TranscriptUploadResponseOriginalType string

// TranscriptUploadResponseStatus defines model for TranscriptUploadResponse.Status.
type TranscriptUploadResponseStatus string

// User defines model for User.
type User struct {
	CreatedAt        *time.Time           `json:"created_at,omitempty"`
	Email            *openapi_types.Email `json:"email,omitempty"`
	EmailConfirmedAt *time.Time           `json:"email_confirmed_at,omitempty"`
	Id               *openapi_types.UUID  `json:"id,omitempty"`
	UpdatedAt        *time.Time           `json:"updated_at,omitempty"`
}

// UserProfile defines model for UserProfile.
type UserProfile struct {
	AvatarUrl *string              `json:"avatar_url,omitempty"`
	CreatedAt *time.Time           `json:"created_at,omitempty"`
	Email     *openapi_types.Email `json:"email,omitempty"`
	Id        *openapi_types.UUID  `json:"id,omitempty"`
	Name      *string              `json:"name,omitempty"`
	UpdatedAt *time.Time           `json:"updated_at,omitempty"`
}

// UserProfileUpdate defines model for UserProfileUpdate.
type UserProfileUpdate struct {
	AvatarUrl *string `json:"avatar_url,omitempty"`
	Name      *string `json:"name,omitempty"`
}

// GetAuthCallbackProviderParams defines parameters for GetAuthCallbackProvider.
type GetAuthCallbackProviderParams struct {
	Code  string  `form:"code" json:"code"`
	State string  `form:"state" json:"state"`
	Error *string `form:"error,omitempty" json:"error,omitempty"`
}

// GetAuthCallbackProviderParamsProvider defines parameters for GetAuthCallbackProvider.
type GetAuthCallbackProviderParamsProvider string

// PostAuthSigninJSONBody defines parameters for PostAuthSignin.
type PostAuthSigninJSONBody struct {
	Email    openapi_types.Email `json:"email"`
	Password string              `json:"password"`
}

// PostAuthSigninOauthJSONBody defines parameters for PostAuthSigninOauth.
type PostAuthSigninOauthJSONBody struct {
	Provider   PostAuthSigninOauthJSONBodyProvider `json:"provider"`
	RedirectTo *string                             `json:"redirectTo,omitempty"`
}

// PostAuthSigninOauthJSONBodyProvider defines parameters for PostAuthSigninOauth.
type PostAuthSigninOauthJSONBodyProvider string

// ListMeetingTranscriptsParams defines parameters for ListMeetingTranscripts.
type ListMeetingTranscriptsParams struct {
	// Status Filter by transcript status
	Status *ListMeetingTranscriptsParamsStatus `form:"status,omitempty" json:"status,omitempty"`

	// Limit Maximum number of transcripts to return
	Limit *int `form:"limit,omitempty" json:"limit,omitempty"`

	// Offset Number of transcripts to skip
	Offset *int `form:"offset,omitempty" json:"offset,omitempty"`
}

// ListMeetingTranscriptsParamsStatus defines parameters for ListMeetingTranscripts.
type ListMeetingTranscriptsParamsStatus string

// UploadTranscriptMultipartBody defines parameters for UploadTranscript.
type UploadTranscriptMultipartBody struct {
	// File Transcript file (PDF, DOCX, MD, VTT)
	File openapi_types.File `json:"file"`

	// MeetingName Optional meeting name
	MeetingName *string `json:"meeting_name,omitempty"`
}

// PostAuthSigninJSONRequestBody defines body for PostAuthSignin for application/json ContentType.
type PostAuthSigninJSONRequestBody PostAuthSigninJSONBody

// PostAuthSigninOauthJSONRequestBody defines body for PostAuthSigninOauth for application/json ContentType.
type PostAuthSigninOauthJSONRequestBody PostAuthSigninOauthJSONBody

// CreateCaseStudyJSONRequestBody defines body for CreateCaseStudy for application/json ContentType.
type CreateCaseStudyJSONRequestBody = CaseStudyCreate

// UpdateCaseStudyJSONRequestBody defines body for UpdateCaseStudy for application/json ContentType.
type UpdateCaseStudyJSONRequestBody = CaseStudyUpdate

// CreateMeetingTranscriptJSONRequestBody defines body for CreateMeetingTranscript for application/json ContentType.
type CreateMeetingTranscriptJSONRequestBody = MeetingTranscriptCreate

// UpdateMeetingTranscriptJSONRequestBody defines body for UpdateMeetingTranscript for application/json ContentType.
type UpdateMeetingTranscriptJSONRequestBody = MeetingTranscriptUpdate

// CreatePersonJSONRequestBody defines body for CreatePerson for application/json ContentType.
type CreatePersonJSONRequestBody = PersonCreate

// UpdatePersonJSONRequestBody defines body for UpdatePerson for application/json ContentType.
type UpdatePersonJSONRequestBody = PersonUpdate

// CreateThemeJSONRequestBody defines body for CreateTheme for application/json ContentType.
type CreateThemeJSONRequestBody = ThemeCreate

// UpdateThemeJSONRequestBody defines body for UpdateTheme for application/json ContentType.
type UpdateThemeJSONRequestBody = ThemeUpdate

// UploadTranscriptMultipartRequestBody defines body for UploadTranscript for multipart/form-data ContentType.
type UploadTranscriptMultipartRequestBody UploadTranscriptMultipartBody

// PutUsersProfileJSONRequestBody defines body for PutUsersProfile for application/json ContentType.
type PutUsersProfileJSONRequestBody = UserProfileUpdate

// ServerInterface represents all server handlers.
type ServerInterface interface {
	// OAuth callback
	// (GET /auth/callback/{provider})
	GetAuthCallbackProvider(c *gin.Context, provider GetAuthCallbackProviderParamsProvider, params GetAuthCallbackProviderParams)
	// Get current session
	// (GET /auth/session)
	GetAuthSession(c *gin.Context)
	// Sign in with email/password
	// (POST /auth/signin)
	PostAuthSignin(c *gin.Context)
	// Sign in with OAuth
	// (POST /auth/signin/oauth)
	PostAuthSigninOauth(c *gin.Context)
	// Sign out
	// (POST /auth/signout)
	PostAuthSignout(c *gin.Context)
	// Get current user
	// (GET /auth/user)
	GetAuthUser(c *gin.Context)
	// List case studies
	// (GET /case-studies)
	ListCaseStudies(c *gin.Context)
	// Create a new case study
	// (POST /case-studies)
	CreateCaseStudy(c *gin.Context)
	// Delete case study
	// (DELETE /case-studies/{id})
	DeleteCaseStudy(c *gin.Context, id openapi_types.UUID)
	// Get case study by ID
	// (GET /case-studies/{id})
	GetCaseStudyById(c *gin.Context, id openapi_types.UUID)
	// Update case study
	// (PUT /case-studies/{id})
	UpdateCaseStudy(c *gin.Context, id openapi_types.UUID)
	// Generate title for case study
	// (POST /case-studies/{id}/generate-title)
	GenerateCaseStudyTitle(c *gin.Context, id openapi_types.UUID)
	// List meeting transcripts
	// (GET /meeting-transcripts)
	ListMeetingTranscripts(c *gin.Context, params ListMeetingTranscriptsParams)
	// Create a new meeting transcript
	// (POST /meeting-transcripts)
	CreateMeetingTranscript(c *gin.Context)
	// Delete meeting transcript
	// (DELETE /meeting-transcripts/{id})
	DeleteMeetingTranscript(c *gin.Context, id openapi_types.UUID)
	// Get meeting transcript by ID
	// (GET /meeting-transcripts/{id})
	GetMeetingTranscriptById(c *gin.Context, id openapi_types.UUID)
	// Update meeting transcript
	// (PUT /meeting-transcripts/{id})
	UpdateMeetingTranscript(c *gin.Context, id openapi_types.UUID)
	// Trigger analysis for meeting transcript
	// (POST /meeting-transcripts/{id}/analyze)
	AnalyzeMeetingTranscript(c *gin.Context, id openapi_types.UUID)
	// Generate prefill suggestions for meeting transcript
	// (POST /meeting-transcripts/{id}/prefill)
	PrefillMeetingTranscript(c *gin.Context, id openapi_types.UUID)
	// List company people
	// (GET /people)
	ListPeople(c *gin.Context)
	// Create a new person
	// (POST /people)
	CreatePerson(c *gin.Context)
	// Delete person
	// (DELETE /people/{id})
	DeletePerson(c *gin.Context, id openapi_types.UUID)
	// Get person by ID
	// (GET /people/{id})
	GetPersonById(c *gin.Context, id openapi_types.UUID)
	// Update person
	// (PUT /people/{id})
	UpdatePerson(c *gin.Context, id openapi_types.UUID)
	// List company themes
	// (GET /themes)
	ListThemes(c *gin.Context)
	// Create a new theme
	// (POST /themes)
	CreateTheme(c *gin.Context)
	// Delete theme
	// (DELETE /themes/{id})
	DeleteTheme(c *gin.Context, id openapi_types.UUID)
	// Get theme by ID
	// (GET /themes/{id})
	GetThemeById(c *gin.Context, id openapi_types.UUID)
	// Update theme
	// (PUT /themes/{id})
	UpdateTheme(c *gin.Context, id openapi_types.UUID)
	// Delete cached transcript preview
	// (DELETE /transcripts/preview/{id})
	DeleteTranscriptPreview(c *gin.Context, id openapi_types.UUID)
	// Get converted transcript preview
	// (GET /transcripts/preview/{id})
	GetTranscriptPreview(c *gin.Context, id openapi_types.UUID)
	// Upload and convert transcript file
	// (POST /transcripts/upload)
	UploadTranscript(c *gin.Context)
	// Get user profile
	// (GET /users/profile)
	GetUsersProfile(c *gin.Context)
	// Update user profile
	// (PUT /users/profile)
	PutUsersProfile(c *gin.Context)
}

// ServerInterfaceWrapper converts contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler            ServerInterface
	HandlerMiddlewares []MiddlewareFunc
	ErrorHandler       func(*gin.Context, error, int)
}

type MiddlewareFunc func(c *gin.Context)

// GetAuthCallbackProvider operation middleware
func (siw *ServerInterfaceWrapper) GetAuthCallbackProvider(c *gin.Context) {

	var err error

	// ------------- Path parameter "provider" -------------
	var provider GetAuthCallbackProviderParamsProvider

	err = runtime.BindStyledParameterWithOptions("simple", "provider", c.Param("provider"), &provider, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter provider: %w", err), http.StatusBadRequest)
		return
	}

	// Parameter object where we will unmarshal all parameters from the context
	var params GetAuthCallbackProviderParams

	// ------------- Required query parameter "code" -------------

	if paramValue := c.Query("code"); paramValue != "" {

	} else {
		siw.ErrorHandler(c, fmt.Errorf("Query argument code is required, but not found"), http.StatusBadRequest)
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "code", c.Request.URL.Query(), &params.Code)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter code: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Required query parameter "state" -------------

	if paramValue := c.Query("state"); paramValue != "" {

	} else {
		siw.ErrorHandler(c, fmt.Errorf("Query argument state is required, but not found"), http.StatusBadRequest)
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "state", c.Request.URL.Query(), &params.State)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter state: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "error" -------------

	err = runtime.BindQueryParameter("form", true, false, "error", c.Request.URL.Query(), &params.Error)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter error: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetAuthCallbackProvider(c, provider, params)
}

// GetAuthSession operation middleware
func (siw *ServerInterfaceWrapper) GetAuthSession(c *gin.Context) {

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetAuthSession(c)
}

// PostAuthSignin operation middleware
func (siw *ServerInterfaceWrapper) PostAuthSignin(c *gin.Context) {

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostAuthSignin(c)
}

// PostAuthSigninOauth operation middleware
func (siw *ServerInterfaceWrapper) PostAuthSigninOauth(c *gin.Context) {

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostAuthSigninOauth(c)
}

// PostAuthSignout operation middleware
func (siw *ServerInterfaceWrapper) PostAuthSignout(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostAuthSignout(c)
}

// GetAuthUser operation middleware
func (siw *ServerInterfaceWrapper) GetAuthUser(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetAuthUser(c)
}

// ListCaseStudies operation middleware
func (siw *ServerInterfaceWrapper) ListCaseStudies(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.ListCaseStudies(c)
}

// CreateCaseStudy operation middleware
func (siw *ServerInterfaceWrapper) CreateCaseStudy(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.CreateCaseStudy(c)
}

// DeleteCaseStudy operation middleware
func (siw *ServerInterfaceWrapper) DeleteCaseStudy(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteCaseStudy(c, id)
}

// GetCaseStudyById operation middleware
func (siw *ServerInterfaceWrapper) GetCaseStudyById(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetCaseStudyById(c, id)
}

// UpdateCaseStudy operation middleware
func (siw *ServerInterfaceWrapper) UpdateCaseStudy(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.UpdateCaseStudy(c, id)
}

// GenerateCaseStudyTitle operation middleware
func (siw *ServerInterfaceWrapper) GenerateCaseStudyTitle(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GenerateCaseStudyTitle(c, id)
}

// ListMeetingTranscripts operation middleware
func (siw *ServerInterfaceWrapper) ListMeetingTranscripts(c *gin.Context) {

	var err error

	c.Set(BearerAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params ListMeetingTranscriptsParams

	// ------------- Optional query parameter "status" -------------

	err = runtime.BindQueryParameter("form", true, false, "status", c.Request.URL.Query(), &params.Status)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter status: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", c.Request.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter limit: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "offset" -------------

	err = runtime.BindQueryParameter("form", true, false, "offset", c.Request.URL.Query(), &params.Offset)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter offset: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.ListMeetingTranscripts(c, params)
}

// CreateMeetingTranscript operation middleware
func (siw *ServerInterfaceWrapper) CreateMeetingTranscript(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.CreateMeetingTranscript(c)
}

// DeleteMeetingTranscript operation middleware
func (siw *ServerInterfaceWrapper) DeleteMeetingTranscript(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteMeetingTranscript(c, id)
}

// GetMeetingTranscriptById operation middleware
func (siw *ServerInterfaceWrapper) GetMeetingTranscriptById(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetMeetingTranscriptById(c, id)
}

// UpdateMeetingTranscript operation middleware
func (siw *ServerInterfaceWrapper) UpdateMeetingTranscript(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.UpdateMeetingTranscript(c, id)
}

// AnalyzeMeetingTranscript operation middleware
func (siw *ServerInterfaceWrapper) AnalyzeMeetingTranscript(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.AnalyzeMeetingTranscript(c, id)
}

// PrefillMeetingTranscript operation middleware
func (siw *ServerInterfaceWrapper) PrefillMeetingTranscript(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PrefillMeetingTranscript(c, id)
}

// ListPeople operation middleware
func (siw *ServerInterfaceWrapper) ListPeople(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.ListPeople(c)
}

// CreatePerson operation middleware
func (siw *ServerInterfaceWrapper) CreatePerson(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.CreatePerson(c)
}

// DeletePerson operation middleware
func (siw *ServerInterfaceWrapper) DeletePerson(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeletePerson(c, id)
}

// GetPersonById operation middleware
func (siw *ServerInterfaceWrapper) GetPersonById(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetPersonById(c, id)
}

// UpdatePerson operation middleware
func (siw *ServerInterfaceWrapper) UpdatePerson(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.UpdatePerson(c, id)
}

// ListThemes operation middleware
func (siw *ServerInterfaceWrapper) ListThemes(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.ListThemes(c)
}

// CreateTheme operation middleware
func (siw *ServerInterfaceWrapper) CreateTheme(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.CreateTheme(c)
}

// DeleteTheme operation middleware
func (siw *ServerInterfaceWrapper) DeleteTheme(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteTheme(c, id)
}

// GetThemeById operation middleware
func (siw *ServerInterfaceWrapper) GetThemeById(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetThemeById(c, id)
}

// UpdateTheme operation middleware
func (siw *ServerInterfaceWrapper) UpdateTheme(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.UpdateTheme(c, id)
}

// DeleteTranscriptPreview operation middleware
func (siw *ServerInterfaceWrapper) DeleteTranscriptPreview(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteTranscriptPreview(c, id)
}

// GetTranscriptPreview operation middleware
func (siw *ServerInterfaceWrapper) GetTranscriptPreview(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetTranscriptPreview(c, id)
}

// UploadTranscript operation middleware
func (siw *ServerInterfaceWrapper) UploadTranscript(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.UploadTranscript(c)
}

// GetUsersProfile operation middleware
func (siw *ServerInterfaceWrapper) GetUsersProfile(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetUsersProfile(c)
}

// PutUsersProfile operation middleware
func (siw *ServerInterfaceWrapper) PutUsersProfile(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PutUsersProfile(c)
}

// GinServerOptions provides options for the Gin server.
type GinServerOptions struct {
	BaseURL      string
	Middlewares  []MiddlewareFunc
	ErrorHandler func(*gin.Context, error, int)
}

// RegisterHandlers creates http.Handler with routing matching OpenAPI spec.
func RegisterHandlers(router gin.IRouter, si ServerInterface) {
	RegisterHandlersWithOptions(router, si, GinServerOptions{})
}

// RegisterHandlersWithOptions creates http.Handler with additional options
func RegisterHandlersWithOptions(router gin.IRouter, si ServerInterface, options GinServerOptions) {
	errorHandler := options.ErrorHandler
	if errorHandler == nil {
		errorHandler = func(c *gin.Context, err error, statusCode int) {
			c.JSON(statusCode, gin.H{"msg": err.Error()})
		}
	}

	wrapper := ServerInterfaceWrapper{
		Handler:            si,
		HandlerMiddlewares: options.Middlewares,
		ErrorHandler:       errorHandler,
	}

	router.GET(options.BaseURL+"/auth/callback/:provider", wrapper.GetAuthCallbackProvider)
	router.GET(options.BaseURL+"/auth/session", wrapper.GetAuthSession)
	router.POST(options.BaseURL+"/auth/signin", wrapper.PostAuthSignin)
	router.POST(options.BaseURL+"/auth/signin/oauth", wrapper.PostAuthSigninOauth)
	router.POST(options.BaseURL+"/auth/signout", wrapper.PostAuthSignout)
	router.GET(options.BaseURL+"/auth/user", wrapper.GetAuthUser)
	router.GET(options.BaseURL+"/case-studies", wrapper.ListCaseStudies)
	router.POST(options.BaseURL+"/case-studies", wrapper.CreateCaseStudy)
	router.DELETE(options.BaseURL+"/case-studies/:id", wrapper.DeleteCaseStudy)
	router.GET(options.BaseURL+"/case-studies/:id", wrapper.GetCaseStudyById)
	router.PUT(options.BaseURL+"/case-studies/:id", wrapper.UpdateCaseStudy)
	router.POST(options.BaseURL+"/case-studies/:id/generate-title", wrapper.GenerateCaseStudyTitle)
	router.GET(options.BaseURL+"/meeting-transcripts", wrapper.ListMeetingTranscripts)
	router.POST(options.BaseURL+"/meeting-transcripts", wrapper.CreateMeetingTranscript)
	router.DELETE(options.BaseURL+"/meeting-transcripts/:id", wrapper.DeleteMeetingTranscript)
	router.GET(options.BaseURL+"/meeting-transcripts/:id", wrapper.GetMeetingTranscriptById)
	router.PUT(options.BaseURL+"/meeting-transcripts/:id", wrapper.UpdateMeetingTranscript)
	router.POST(options.BaseURL+"/meeting-transcripts/:id/analyze", wrapper.AnalyzeMeetingTranscript)
	router.POST(options.BaseURL+"/meeting-transcripts/:id/prefill", wrapper.PrefillMeetingTranscript)
	router.GET(options.BaseURL+"/people", wrapper.ListPeople)
	router.POST(options.BaseURL+"/people", wrapper.CreatePerson)
	router.DELETE(options.BaseURL+"/people/:id", wrapper.DeletePerson)
	router.GET(options.BaseURL+"/people/:id", wrapper.GetPersonById)
	router.PUT(options.BaseURL+"/people/:id", wrapper.UpdatePerson)
	router.GET(options.BaseURL+"/themes", wrapper.ListThemes)
	router.POST(options.BaseURL+"/themes", wrapper.CreateTheme)
	router.DELETE(options.BaseURL+"/themes/:id", wrapper.DeleteTheme)
	router.GET(options.BaseURL+"/themes/:id", wrapper.GetThemeById)
	router.PUT(options.BaseURL+"/themes/:id", wrapper.UpdateTheme)
	router.DELETE(options.BaseURL+"/transcripts/preview/:id", wrapper.DeleteTranscriptPreview)
	router.GET(options.BaseURL+"/transcripts/preview/:id", wrapper.GetTranscriptPreview)
	router.POST(options.BaseURL+"/transcripts/upload", wrapper.UploadTranscript)
	router.GET(options.BaseURL+"/users/profile", wrapper.GetUsersProfile)
	router.PUT(options.BaseURL+"/users/profile", wrapper.PutUsersProfile)
}
