# Shared Terraform Configuration

This document explains the unified approach to managing multiple environments (dev, staging, prod) with shared Terraform configuration.

## Overview

Previously, each environment had its own separate Terraform configuration with significant duplication. Now, all environments use a shared configuration base with environment-specific overrides.

## Architecture

```
terraform/
├── shared/                    # 🔧 Shared configuration (NEW)
│   ├── main.tf               # Generic main configuration
│   ├── variables.tf          # All variable definitions
│   └── outputs.tf            # Standardized outputs
├── environments/             # 🚀 Deployment scripts
│   ├── deploy-environment.sh # Generic deployment script
│   ├── dev/                  # Dev-specific files (LEGACY)
│   ├── staging/              # Staging-specific files  
│   └── prod/                 # Prod-specific files (LEGACY)
└── modules/                  # Module definitions (unchanged)
```

## Key Benefits

✅ **No Code Duplication** - Single source of truth for infrastructure  
✅ **Consistent Configurations** - Same APIs enabled, same patterns everywhere  
✅ **Environment-Specific Overrides** - Custom settings per environment  
✅ **Simplified Maintenance** - Update once, affects all environments  
✅ **Better Validation** - Consistent variable validation across environments  

## Environment Configurations

The shared configuration defines three environments with different characteristics:

### Development (dev)
- **Purpose**: Active development and testing
- **Machine Types**: Small (e2-standard-2, db-f1-micro)
- **Security**: Relaxed (no Cloud Armor, open SSH)
- **Features**: Debug mode enabled, minimal monitoring
- **Network**: 10.0.x.x range
- **Database**: PostgreSQL 15, ZONAL availability, 7-day backups

### Staging (staging) 
- **Purpose**: Production-like testing environment
- **Machine Types**: Medium (e2-standard-2, db-custom-1-3840)
- **Security**: Production-like (Cloud Armor enabled, SSL required)
- **Features**: Monitoring enabled, backup automation
- **Network**: 10.2.x.x range  
- **Database**: PostgreSQL 16, ZONAL availability, 14-day backups

### Production (prod)
- **Purpose**: Live production environment
- **Machine Types**: Production-sized (e2-standard-2, db-custom-2-7680)
- **Security**: Full security (Cloud Armor, SSL, rate limiting)
- **Features**: Full monitoring, automated backups, auto-scaling
- **Network**: 10.1.x.x range
- **Database**: PostgreSQL 16, REGIONAL availability, 30-day backups

## Deployment Methods

### Deployment Method

Use the generic deployment script for any environment:

```bash
# Plan dev environment
./terraform/environments/deploy-environment.sh -e dev -a plan

# Apply staging environment
./terraform/environments/deploy-environment.sh -e staging -a apply

# Apply production with auto-approval
./terraform/environments/deploy-environment.sh -e prod -a apply -y

# Destroy dev environment
./terraform/environments/deploy-environment.sh -e dev -a destroy
```

### Method 2: Legacy Environment Scripts (Deprecated)

The old environment-specific scripts still work but are deprecated:

```bash
# These still work but use the new shared configuration under the hood
bazel run //terraform:deploy_infrastructure -- dev
bazel run //terraform:deploy_web -- prod
```

## Configuration Customization

### Using Terraform Variables Files

Each environment gets its own `.tfvars` file with customizations:

```hcl
# dev.tfvars
environment = "dev"
project_id = "agent-dev-459718"
ssl_domains = ["dev.twodot.ai", "api.dev.twodot.ai"]

# Custom overrides (optional)
custom_machine_types = {
  compute = "e2-standard-4"  # Override default
}

custom_security_config = {
  enable_cloud_armor = true  # Override dev default
}
```

### Available Override Options

#### Machine Types
```hcl
custom_machine_types = {
  compute  = "e2-standard-4"      # Override compute instance size
  database = "db-custom-2-7680"   # Override database instance size
}
```

#### Network Configuration
```hcl
custom_network_config = {
  vpc_cidr            = "********/16"      # Custom VPC range
  ssh_source_ranges   = ["***********/24"] # Restrict SSH access
}
```

#### Security Settings
```hcl
custom_security_config = {
  enable_ssl           = true   # Enable/disable SSL
  enable_cloud_armor   = true   # Enable/disable Cloud Armor
  enable_cdn          = true    # Enable/disable CDN
  assign_external_ip   = false  # Control external IP assignment
}
```

#### Feature Flags
```hcl
custom_feature_flags = {
  enable_migration_automation = true   # Enable auto-migrations
  enable_monitoring          = true   # Enable monitoring
  enable_backup_automation   = true   # Enable automated backups
  auto_scaling_enabled      = true    # Enable auto-scaling
  enable_debug_mode         = false   # Enable debug features
}
```

## Migration from Legacy Configurations

### Step 1: Backup Existing State

```bash
# Backup existing terraform state files
cp terraform/environments/dev/terraform.tfstate terraform/environments/dev/terraform.tfstate.backup
cp terraform/environments/prod/terraform.tfstate terraform/environments/prod/terraform.tfstate.backup
```

### Step 2: Create Environment-Specific tfvars

The deployment script automatically creates `.tfvars` files based on your existing configurations.

### Step 3: Test with Plan

```bash
# Test the new configuration with plan first
./terraform/environments/deploy-environment.sh -e dev -a plan
./terraform/environments/deploy-environment.sh -e prod -a plan
```

### Step 4: Import Existing Resources (if needed)

If you encounter state issues, you may need to import existing resources:

```bash
# Import existing resources (example)
terraform import google_project_service.required_apis["compute.googleapis.com"] your-project-id/compute.googleapis.com
```

## Standardized Outputs

All environments now provide consistent outputs:

```hcl
# Network outputs
output "vpc_network_name" { }
output "database_private_ip" { }
output "web_bucket_name" { }
output "load_balancer_ip" { }

# Environment-specific access info
output "dev_access_info" { }     # Only populated for dev
output "staging_access_info" { } # Only populated for staging  
output "prod_access_info" { }    # Only populated for prod

# Deployment URLs
output "frontend_url" { }        # https://twodot.ai or https://dev.twodot.ai
output "api_url" { }            # https://api.twodot.ai or https://api.dev.twodot.ai
```

## Best Practices

### 1. Use Environment-Specific Overrides Sparingly
Only override defaults when necessary. The shared configuration provides sensible defaults for each environment.

### 2. Keep Security Configurations Environment-Appropriate
- **Dev**: Relaxed security for easy development
- **Staging**: Production-like security for realistic testing
- **Prod**: Full security enabled

### 3. Test Changes in Dev First
Always test configuration changes in dev environment before applying to staging/prod.

### 4. Use Consistent Naming
The shared configuration enforces consistent resource naming across environments:
- Database: `platform_{environment}_db`
- User: `platform_{environment}_user`
- Buckets: `{project_name}-{environment}-web`

### 5. Monitor Resource Costs
Different environments have different resource sizes. Monitor costs and adjust machine types as needed.

## Troubleshooting

### Backend Bucket Issues
If you encounter backend bucket issues:

```bash
# Create backend bucket manually
gsutil mb -p your-project-id gs://your-terraform-state-bucket
gsutil versioning set on gs://your-terraform-state-bucket
```

### State Migration Issues
If you need to migrate from old environment-specific state:

```bash
# Initialize with new backend
terraform init -migrate-state

# Or force reconfigure
terraform init -reconfigure
```

### Variable Validation Errors
The shared configuration has comprehensive variable validation. Common issues:

- **Project ID**: Must be 6-30 characters, lowercase, letters/numbers/hyphens
- **Region**: Must be a valid GCP region
- **SSL Domains**: Must be valid domain names
- **IP Ranges**: Must be valid IPv4 CIDR blocks

## Advanced Usage

### Using Multiple Projects
Each environment can use a different GCP project:

```bash
# Deploy to different projects
./terraform/environments/deploy-environment.sh -e dev -p dev-project-123 -a apply
./terraform/environments/deploy-environment.sh -e prod -p prod-project-456 -a apply
```

### Custom Backend Buckets
Override the default backend bucket:

```bash
./terraform/environments/deploy-environment.sh -e dev -b my-custom-state-bucket -a plan
```

### Workspace Management
The deployment script automatically manages Terraform workspaces:

```bash
# Each environment gets its own workspace
terraform workspace list
# * dev
#   staging  
#   prod
```

## Support and Feedback

For questions or issues with the shared configuration:

1. Check this documentation first
2. Review the environment-specific `.tfvars` files
3. Test with `plan` action before applying changes
4. Use the legacy environment scripts as a fallback if needed

The shared configuration is designed to be robust and backwards-compatible while providing a much cleaner foundation for multi-environment infrastructure management.