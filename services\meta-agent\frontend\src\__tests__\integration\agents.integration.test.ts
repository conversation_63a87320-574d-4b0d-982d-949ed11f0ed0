/**
 * Agent Management Integration Tests
 * Tests real API calls to the backend agent endpoints
 */

import { agentService } from '@/services/agent.service';
import { apiService } from '../../services/api.test';
import {
  User,
  LoginRequest,
  RegisterRequest,
  TokenResponse,
} from '@/types/api';

class AuthServiceTest {
  async login(credentials: LoginRequest): Promise<User> {
    const params = new URLSearchParams();
    params.append('username', credentials.username);
    params.append('password', credentials.password);

    const response = await apiService.post<TokenResponse>(
      '/auth/login',
      params
    );
    apiService.setTokens(response.data);

    // Get user data after login
    const userResponse = await apiService.get<User>('/auth/me');
    return userResponse.data;
  }

  async register(data: RegisterRequest): Promise<User> {
    const response = await apiService.post<User>('/auth/register', data);
    return response.data;
  }

  logout() {
    apiService.logout();
  }

  isAuthenticated(): boolean {
    return !!apiService.getToken();
  }
}

const authService = new AuthServiceTest();

describe('Agent Management Integration Tests', () => {
  let testUser: any;
  let testAgent: any;
  let authToken: string | null = null;
  let createdAgentIds: string[] = [];

  beforeAll(async () => {
    // Setup authenticated user for all agent tests
    testUser = (global as any).testUtils.generateTestUser();
    
    // Register user
    await authService.register({
      username: testUser.username,
      email: testUser.email,
      password: testUser.password,
      full_name: testUser.full_name
    });

    // Login user
    await authService.login({
      username: testUser.username,
      password: testUser.password
    });

    authToken = apiService.getToken();
  });

  beforeEach(() => {
    testAgent = (global as any).testUtils.generateTestAgent();
  });

  afterEach(async () => {
    // Cleanup created agents
    for (const agentId of createdAgentIds) {
      try {
        await agentService.deleteAgent(agentId);
      } catch (error) {
        // Ignore errors during cleanup
        console.warn(`Failed to cleanup agent ${agentId}:`, error);
      }
    }
    createdAgentIds = [];
  });

  afterAll(() => {
    // Cleanup authentication
    if (authToken) {
      authService.logout();
    }
  });

  describe('Agent CRUD Operations', () => {
    test('should create a new agent', async () => {
      const createData = {
        name: testAgent.name,
        description: testAgent.description,
        type: testAgent.type,
        config: testAgent.config,
        capabilities: testAgent.capabilities
      };

      const agent = await agentService.createAgent(createData);

      expect(agent).toBeDefined();
      expect(agent.id).toBeDefined();
      expect(agent.name).toBe(testAgent.name);
      expect(agent.description).toBe(testAgent.description);
      expect(agent.type).toBe(testAgent.type);
      expect(agent.config).toEqual(testAgent.config);
      expect(agent.capabilities).toEqual(testAgent.capabilities);
      expect(agent.status).toBeDefined();
      expect(agent.created_at).toBeDefined();
      expect(agent.updated_at).toBeDefined();

      createdAgentIds.push(agent.id);
    });

    test('should list agents', async () => {
      // Create a few agents first
      const agent1 = await agentService.createAgent({
        name: `${testAgent.name} 1`,
        description: testAgent.description,
        type: testAgent.type,
        config: testAgent.config,
        capabilities: testAgent.capabilities
      });
      createdAgentIds.push(agent1.id);

      const agent2 = await agentService.createAgent({
        name: `${testAgent.name} 2`,
        description: testAgent.description,
        type: testAgent.type,
        config: testAgent.config,
        capabilities: testAgent.capabilities
      });
      createdAgentIds.push(agent2.id);

      const response = await agentService.listAgents();

      expect(response).toBeDefined();
      expect(response.items).toBeDefined();
      expect(Array.isArray(response.items)).toBe(true);
      expect(response.items.length).toBeGreaterThanOrEqual(2);
      expect(response.total).toBeGreaterThanOrEqual(2);
      expect(response.limit).toBeDefined();
      expect(response.offset).toBeDefined();

      // Check that our created agents are in the list
      const agentIds = response.items.map(agent => agent.id);
      expect(agentIds).toContain(agent1.id);
      expect(agentIds).toContain(agent2.id);
    });

    test('should list agents with pagination', async () => {
      // Create multiple agents
      for (let i = 0; i < 5; i++) {
        const agent = await agentService.createAgent({
          name: `${testAgent.name} ${i}`,
          description: testAgent.description,
          type: testAgent.type,
          config: testAgent.config,
          capabilities: testAgent.capabilities
        });
        createdAgentIds.push(agent.id);
      }

      // Test pagination
      const page1 = await agentService.listAgents({ limit: 2, offset: 0 });
      expect(page1.items.length).toBeLessThanOrEqual(2);
      expect(page1.limit).toBe(2);
      expect(page1.offset).toBe(0);

      const page2 = await agentService.listAgents({ limit: 2, offset: 2 });
      expect(page2.items.length).toBeLessThanOrEqual(2);
      expect(page2.limit).toBe(2);
      expect(page2.offset).toBe(2);

      // Ensure different pages have different items
      const page1Ids = page1.items.map(agent => agent.id);
      const page2Ids = page2.items.map(agent => agent.id);
      const commonIds = page1Ids.filter(id => page2Ids.includes(id));
      expect(commonIds.length).toBe(0);
    });

    test('should get agent by id', async () => {
      // Create agent first
      const createdAgent = await agentService.createAgent({
        name: testAgent.name,
        description: testAgent.description,
        type: testAgent.type,
        config: testAgent.config,
        capabilities: testAgent.capabilities
      });
      createdAgentIds.push(createdAgent.id);

      // Get agent by id
      const retrievedAgent = await agentService.getAgent(createdAgent.id);

      expect(retrievedAgent).toBeDefined();
      expect(retrievedAgent.id).toBe(createdAgent.id);
      expect(retrievedAgent.name).toBe(createdAgent.name);
      expect(retrievedAgent.description).toBe(createdAgent.description);
      expect(retrievedAgent.type).toBe(createdAgent.type);
      expect(retrievedAgent.config).toEqual(createdAgent.config);
      expect(retrievedAgent.capabilities).toEqual(createdAgent.capabilities);
    });

    test('should update agent', async () => {
      // Create agent first
      const createdAgent = await agentService.createAgent({
        name: testAgent.name,
        description: testAgent.description,
        type: testAgent.type,
        config: testAgent.config,
        capabilities: testAgent.capabilities
      });
      createdAgentIds.push(createdAgent.id);

      // Update agent
      const updateData = {
        name: `Updated ${testAgent.name}`,
        description: 'Updated description',
        config: {
          ...testAgent.config,
          max_concurrent_tasks: 10
        }
      };

      const updatedAgent = await agentService.updateAgent(createdAgent.id, updateData);

      expect(updatedAgent.id).toBe(createdAgent.id);
      expect(updatedAgent.name).toBe(updateData.name);
      expect(updatedAgent.description).toBe(updateData.description);
      expect(updatedAgent.config.max_concurrent_tasks).toBe(10);
      expect(updatedAgent.updated_at).not.toBe(createdAgent.updated_at);
    });

    test('should delete agent', async () => {
      // Create agent first
      const createdAgent = await agentService.createAgent({
        name: testAgent.name,
        description: testAgent.description,
        type: testAgent.type,
        config: testAgent.config,
        capabilities: testAgent.capabilities
      });

      // Delete agent
      await agentService.deleteAgent(createdAgent.id);

      // Verify agent is deleted
      await expect(agentService.getAgent(createdAgent.id)).rejects.toThrow();

      // Remove from cleanup list since it's already deleted
      createdAgentIds = createdAgentIds.filter(id => id !== createdAgent.id);
    });

    test('should fail to get non-existent agent', async () => {
      const nonExistentId = 'non-existent-id-12345';
      
      await expect(agentService.getAgent(nonExistentId)).rejects.toThrow();
    });

    test('should fail to update non-existent agent', async () => {
      const nonExistentId = 'non-existent-id-12345';
      
      await expect(agentService.updateAgent(nonExistentId, {
        name: 'Updated Name'
      })).rejects.toThrow();
    });

    test('should fail to delete non-existent agent', async () => {
      const nonExistentId = 'non-existent-id-12345';
      
      await expect(agentService.deleteAgent(nonExistentId)).rejects.toThrow();
    });
  });

  describe('Agent Lifecycle Management', () => {
    let agent: any;

    beforeEach(async () => {
      // Create an agent for lifecycle tests
      agent = await agentService.createAgent({
        name: testAgent.name,
        description: testAgent.description,
        type: testAgent.type,
        config: testAgent.config,
        capabilities: testAgent.capabilities
      });
      createdAgentIds.push(agent.id);
    });

    test('should start agent', async () => {
      const result = await agentService.startAgent(agent.id);

      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      expect(result.message).toBeDefined();
    });

    test('should stop agent', async () => {
      // Start agent first
      await agentService.startAgent(agent.id);

      // Then stop it
      const result = await agentService.stopAgent(agent.id);

      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      expect(result.message).toBeDefined();
    });

    test('should update heartbeat', async () => {
      // This should not throw an error
      await expect(agentService.updateHeartbeat(agent.id)).resolves.not.toThrow();
    });

    test('should fail to start non-existent agent', async () => {
      const nonExistentId = 'non-existent-id-12345';
      
      await expect(agentService.startAgent(nonExistentId)).rejects.toThrow();
    });

    test('should fail to stop non-existent agent', async () => {
      const nonExistentId = 'non-existent-id-12345';
      
      await expect(agentService.stopAgent(nonExistentId)).rejects.toThrow();
    });
  });

  describe('Agent Runtime Management', () => {
    let agent: any;

    beforeEach(async () => {
      // Create and start an agent for runtime tests
      agent = await agentService.createAgent({
        name: testAgent.name,
        description: testAgent.description,
        type: testAgent.type,
        config: testAgent.config,
        capabilities: testAgent.capabilities
      });
      createdAgentIds.push(agent.id);
      
      // Start the agent
      await agentService.startAgent(agent.id);
    });

    test('should list active agents', async () => {
      const activeAgents = await agentService.listActiveAgents();

      expect(Array.isArray(activeAgents)).toBe(true);
      // Note: We can't guarantee our agent will be in the list immediately
      // as the runtime management might be asynchronous
    });

    test('should get system stats', async () => {
      const stats = await agentService.getSystemStats();

      expect(stats).toBeDefined();
      expect(typeof stats.total_agents).toBe('number');
      expect(typeof stats.active_agents).toBe('number');
      expect(typeof stats.cpu_usage).toBe('number');
      expect(typeof stats.memory_usage).toBe('number');
      expect(stats.uptime).toBeDefined();
    });

    test('should get system health', async () => {
      const health = await agentService.getSystemHealth();

      expect(health).toBeDefined();
      expect(health.status).toBeDefined();
    });

    test('should execute task on agent', async () => {
      const taskData = {
        task_type: 'test_task',
        input_data: {
          message: 'Hello, Agent!'
        },
        metadata: {
          priority: 'high'
        }
      };

      // This might fail if the agent doesn't support this task type
      // But it should at least make the API call successfully
      try {
        const result = await agentService.executeTask(agent.id, taskData);
        expect(result).toBeDefined();
      } catch (error) {
        // Task execution might fail due to agent implementation
        // but the API call should be properly formatted
        expect(error).toBeDefined();
      }
    });

    test('should queue task for agent', async () => {
      const taskData = {
        task_type: 'test_task',
        input_data: {
          message: 'Hello, Agent!'
        },
        metadata: {
          priority: 'low'
        }
      };

      try {
        const result = await agentService.queueTask(agent.id, taskData);
        expect(result).toBeDefined();
        expect(result.success).toBe(true);
      } catch (error) {
        // Task queueing might fail due to agent implementation
        // but the API call should be properly formatted
        expect(error).toBeDefined();
      }
    });
  });

  describe('Agent Validation', () => {
    test('should fail to create agent with invalid data', async () => {
      const invalidAgentData = {
        name: '', // Empty name
        description: testAgent.description,
        type: 'invalid_type', // Invalid type
        config: {},
        capabilities: []
      };

      await expect(agentService.createAgent(invalidAgentData)).rejects.toThrow();
    });

    test('should fail to create agent with missing required fields', async () => {
      const incompleteAgentData = {
        name: testAgent.name,
        // Missing description, type, etc.
      };

      await expect(agentService.createAgent(incompleteAgentData as any)).rejects.toThrow();
    });

    test('should fail to create agent without authentication', async () => {
      // Logout to remove authentication
      authService.logout();

      const createData = {
        name: testAgent.name,
        description: testAgent.description,
        type: testAgent.type,
        config: testAgent.config,
        capabilities: testAgent.capabilities
      };

      await expect(agentService.createAgent(createData)).rejects.toThrow();

      // Re-login for other tests
      await authService.login({
        username: testUser.username,
        password: testUser.password
      });
    });
  });
});