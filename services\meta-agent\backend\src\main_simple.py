"""
AI Agent Platform - Simplified Main FastAPI Application
"""

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, status, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, EmailStr, validator
from typing import List, Optional, Dict, Any
import os
import uuid
import time
import re
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Security
security = HTTPBearer()

# In-memory storage for demo
users_db = {}
agents_db = {}
tokens_db = {}
tasks_db = {}

# Pydantic models
class RegisterRequest(BaseModel):
    username: str
    email: str
    password: str
    full_name: str

    @validator('email')
    def validate_email(cls, v):
        # Basic email validation
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, v):
            raise ValueError('Invalid email format')
        return v

    @validator('password')
    def validate_password(cls, v):
        # Password must be at least 8 characters and contain uppercase, lowercase, digit, and special char
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not re.search(r'[A-Z]', v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not re.search(r'[a-z]', v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not re.search(r'\d', v):
            raise ValueError('Password must contain at least one digit')
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', v):
            raise ValueError('Password must contain at least one special character')
        return v

class LoginRequest(BaseModel):
    username: str
    password: str

class UpdateProfileRequest(BaseModel):
    full_name: Optional[str] = None
    email: Optional[str] = None
    bio: Optional[str] = None

    @validator('email')
    def validate_email(cls, v):
        if v is not None:
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_pattern, v):
                raise ValueError('Invalid email format')
        return v

class ChangePasswordRequest(BaseModel):
    current_password: str
    new_password: str

    @validator('new_password')
    def validate_new_password(cls, v):
        # Same validation as registration password
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not re.search(r'[A-Z]', v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not re.search(r'[a-z]', v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not re.search(r'\d', v):
            raise ValueError('Password must contain at least one digit')
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', v):
            raise ValueError('Password must contain at least one special character')
        return v

class User(BaseModel):
    id: str
    username: str
    email: str
    full_name: str
    bio: Optional[str] = None
    created_at: str
    updated_at: str

class TokenResponse(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"

class AgentCreate(BaseModel):
    name: str
    description: str
    type: str
    config: Dict[str, Any]
    capabilities: List[str]

class Agent(BaseModel):
    id: str
    name: str
    description: str
    type: str
    config: Dict[str, Any]
    capabilities: List[str]
    status: str = "inactive"
    created_at: str
    updated_at: str

class AgentListResponse(BaseModel):
    items: List[Agent]
    total: int
    limit: int
    offset: int

class TaskData(BaseModel):
    type: str
    input_data: Dict[str, Any]
    metadata: Optional[Dict[str, Any]] = None

class TaskCreate(BaseModel):
    title: str
    description: Optional[str] = None
    type: str
    priority: str = "medium"  # low, medium, high, urgent
    agent_id: Optional[str] = None
    orchestration_id: Optional[str] = None
    estimated_duration: int = 300  # seconds
    auto_retry: bool = True
    max_retries: int = 3
    timeout_seconds: int = 3600
    input_data: Optional[Dict[str, Any]] = None
    config: Optional[Dict[str, Any]] = None

class Task(BaseModel):
    id: str
    title: str
    description: Optional[str] = None
    type: str
    status: str = "pending"  # pending, running, completed, failed, cancelled
    priority: str = "medium"
    agent_id: Optional[str] = None
    agent_name: Optional[str] = None
    orchestration_id: Optional[str] = None
    orchestration_name: Optional[str] = None
    progress: int = 0
    created_at: str
    updated_at: str
    estimated_duration: Optional[int] = None
    actual_duration: Optional[int] = None
    error_message: Optional[str] = None
    result_summary: Optional[str] = None
    input_data: Optional[Dict[str, Any]] = None
    config: Optional[Dict[str, Any]] = None
    auto_retry: bool = True
    max_retries: int = 3
    timeout_seconds: int = 3600
    retry_count: int = 0
    owner_id: str

class TasksResponse(BaseModel):
    items: List[Task]
    total: int
    page: int
    per_page: int
    total_pages: int

# Helper functions
def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> User:
    token = credentials.credentials
    if token not in tokens_db:
        raise HTTPException(status_code=401, detail="Invalid token")
    user_id = tokens_db[token]
    if user_id not in users_db:
        raise HTTPException(status_code=401, detail="User not found")
    user_data = users_db[user_id]
    return User(**{k: v for k, v in user_data.items() if k != "password"})

# Create FastAPI application
app = FastAPI(
    title="AI Agent Platform",
    description="Enterprise AI Agent Platform",
    version="2.0.0",
    debug=True
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Welcome to AI Agent Platform",
        "version": "2.0.0",
        "environment": "development",
        "status": "running"
    }

@app.get("/health")
async def health():
    """Health check endpoint"""
    return {"status": "healthy"}

# Auth endpoints
@app.post("/api/v1/auth/register", response_model=User)
async def register(user_data: RegisterRequest):
    """Register a new user"""
    # Check if user already exists
    for user in users_db.values():
        if user["username"] == user_data.username or user["email"] == user_data.email:
            raise HTTPException(status_code=400, detail="User already exists")

    user_id = str(uuid.uuid4())
    now = datetime.utcnow().isoformat()

    user = {
        "id": user_id,
        "username": user_data.username,
        "email": user_data.email,
        "full_name": user_data.full_name,
        "bio": None,  # Default bio
        "password": user_data.password,  # In real app, hash this
        "created_at": now,
        "updated_at": now
    }

    users_db[user_id] = user

    return User(**{k: v for k, v in user.items() if k != "password"})

@app.post("/api/v1/auth/login", response_model=TokenResponse)
async def login(username: str = Form(...), password: str = Form(...)):
    """Login user"""
    # Find user by username or email
    user = None
    for u in users_db.values():
        if (u["username"] == username or u["email"] == username) and u["password"] == password:
            user = u
            break

    if not user:
        raise HTTPException(status_code=401, detail="Invalid credentials")

    # Generate tokens
    access_token = str(uuid.uuid4())
    refresh_token = str(uuid.uuid4())
    tokens_db[access_token] = user["id"]
    tokens_db[refresh_token] = user["id"]  # In real app, handle refresh tokens separately

    return TokenResponse(access_token=access_token, refresh_token=refresh_token)

@app.get("/api/v1/auth/me", response_model=User)
async def get_current_user_info(current_user: User = Depends(get_current_user)):
    """Get current user info"""
    return current_user

@app.put("/api/v1/auth/me", response_model=User)
async def update_profile(
    profile_data: UpdateProfileRequest,
    current_user: User = Depends(get_current_user)
):
    """Update user profile"""
    user_data = users_db[current_user.id]

    # Check if email is being changed and if it's already taken
    if profile_data.email and profile_data.email != user_data["email"]:
        for user in users_db.values():
            if user["email"] == profile_data.email and user["id"] != current_user.id:
                raise HTTPException(status_code=400, detail="Email already taken")
        user_data["email"] = profile_data.email

    if profile_data.full_name:
        user_data["full_name"] = profile_data.full_name

    if profile_data.bio is not None:  # Allow empty string
        user_data["bio"] = profile_data.bio

    user_data["updated_at"] = datetime.utcnow().isoformat()

    return User(**{k: v for k, v in user_data.items() if k != "password"})

@app.post("/api/v1/auth/change-password")
async def change_password(
    password_data: ChangePasswordRequest,
    current_user: User = Depends(get_current_user)
):
    """Change user password"""
    user_data = users_db[current_user.id]

    # Verify current password
    if user_data["password"] != password_data.current_password:
        raise HTTPException(status_code=400, detail="Current password is incorrect")

    # Update password
    user_data["password"] = password_data.new_password
    user_data["updated_at"] = datetime.utcnow().isoformat()

    return {"message": "Password changed successfully"}

# Agent endpoints
@app.post("/api/v1/agents", response_model=Agent, status_code=201)
async def create_agent(agent_data: AgentCreate, current_user: User = Depends(get_current_user)):
    """Create a new agent"""
    agent_id = str(uuid.uuid4())
    now = datetime.utcnow().isoformat()

    agent = {
        "id": agent_id,
        "name": agent_data.name,
        "description": agent_data.description,
        "type": agent_data.type,
        "config": agent_data.config,
        "capabilities": agent_data.capabilities,
        "status": "inactive",
        "owner_id": current_user.id,
        "created_at": now,
        "updated_at": now
    }

    agents_db[agent_id] = agent
    return Agent(**agent)

@app.get("/api/v1/agents", response_model=AgentListResponse)
async def list_agents(
    limit: int = 100,
    offset: int = 0,
    current_user: User = Depends(get_current_user)
):
    """List agents"""
    user_agents = [agent for agent in agents_db.values() if agent["owner_id"] == current_user.id]
    total = len(user_agents)
    items = user_agents[offset:offset + limit]

    return AgentListResponse(
        items=[Agent(**agent) for agent in items],
        total=total,
        limit=limit,
        offset=offset
    )

@app.get("/api/v1/agents/{agent_id}", response_model=Agent)
async def get_agent(agent_id: str, current_user: User = Depends(get_current_user)):
    """Get agent by ID"""
    if agent_id not in agents_db:
        raise HTTPException(status_code=404, detail="Agent not found")

    agent = agents_db[agent_id]
    if agent["owner_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")

    return Agent(**agent)

@app.put("/api/v1/agents/{agent_id}", response_model=Agent)
async def update_agent(
    agent_id: str,
    agent_data: AgentCreate,
    current_user: User = Depends(get_current_user)
):
    """Update agent"""
    if agent_id not in agents_db:
        raise HTTPException(status_code=404, detail="Agent not found")

    agent = agents_db[agent_id]
    if agent["owner_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")

    # Update agent data
    agent.update({
        "name": agent_data.name,
        "description": agent_data.description,
        "type": agent_data.type,
        "config": agent_data.config,
        "capabilities": agent_data.capabilities,
        "updated_at": datetime.utcnow().isoformat()
    })

    return Agent(**agent)

@app.delete("/api/v1/agents/{agent_id}")
async def delete_agent(agent_id: str, current_user: User = Depends(get_current_user)):
    """Delete agent"""
    if agent_id not in agents_db:
        raise HTTPException(status_code=404, detail="Agent not found")

    agent = agents_db[agent_id]
    if agent["owner_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")

    del agents_db[agent_id]
    return {"message": "Agent deleted successfully"}

# Agent lifecycle endpoints
@app.post("/api/v1/agents/{agent_id}/start")
async def start_agent(agent_id: str, current_user: User = Depends(get_current_user)):
    """Start agent"""
    if agent_id not in agents_db:
        raise HTTPException(status_code=404, detail="Agent not found")

    agent = agents_db[agent_id]
    if agent["owner_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")

    # Update agent status
    agent["status"] = "active"
    agent["updated_at"] = datetime.utcnow().isoformat()

    return {"success": True, "message": "Agent started successfully"}

@app.post("/api/v1/agents/{agent_id}/stop")
async def stop_agent(agent_id: str, current_user: User = Depends(get_current_user)):
    """Stop agent"""
    if agent_id not in agents_db:
        raise HTTPException(status_code=404, detail="Agent not found")

    agent = agents_db[agent_id]
    if agent["owner_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")

    # Update agent status
    agent["status"] = "inactive"
    agent["updated_at"] = datetime.utcnow().isoformat()

    return {"success": True, "message": "Agent stopped successfully"}

@app.post("/api/v1/agents/{agent_id}/heartbeat")
async def update_heartbeat(agent_id: str, current_user: User = Depends(get_current_user)):
    """Update agent heartbeat"""
    if agent_id not in agents_db:
        raise HTTPException(status_code=404, detail="Agent not found")

    agent = agents_db[agent_id]
    if agent["owner_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")

    # Update heartbeat timestamp
    agent["last_heartbeat"] = datetime.utcnow().isoformat()

    return {"success": True, "message": "Heartbeat updated"}

# Runtime endpoints
@app.get("/api/v1/runtime/agents/active")
async def list_active_agents(current_user: User = Depends(get_current_user)):
    """List active agents"""
    active_agents = [
        Agent(**agent) for agent in agents_db.values()
        if agent["owner_id"] == current_user.id and agent["status"] == "active"
    ]
    return active_agents

@app.get("/api/v1/runtime/system/stats")
async def get_system_stats(current_user: User = Depends(get_current_user)):
    """Get system stats"""
    user_agents = [agent for agent in agents_db.values() if agent["owner_id"] == current_user.id]
    active_agents = [agent for agent in user_agents if agent["status"] == "active"]

    return {
        "total_agents": len(user_agents),
        "active_agents": len(active_agents),
        "cpu_usage": 25.5,  # Mock data
        "memory_usage": 45.2,  # Mock data
        "uptime": "2h 30m"
    }

@app.get("/api/v1/runtime/system/health")
async def get_system_health(current_user: User = Depends(get_current_user)):
    """Get system health"""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "services": {
            "database": "healthy",
            "message_queue": "healthy",
            "ai_service": "healthy"
        }
    }

# Task execution endpoints
@app.post("/api/v1/runtime/agents/{agent_id}/execute-task")
async def execute_task(
    agent_id: str,
    task_data: TaskData,
    current_user: User = Depends(get_current_user)
):
    """Execute task on agent"""
    if agent_id not in agents_db:
        raise HTTPException(status_code=404, detail="Agent not found")

    agent = agents_db[agent_id]
    if agent["owner_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")

    # Mock task execution
    task_id = str(uuid.uuid4())
    result = {
        "task_id": task_id,
        "agent_id": agent_id,
        "status": "completed",
        "result": {
            "output": f"Task of type '{task_data.type}' executed successfully",
            "execution_time": "1.23s",
            "timestamp": datetime.utcnow().isoformat()
        },
        "message": "Task executed successfully"
    }

    return result

@app.post("/api/v1/runtime/agents/{agent_id}/queue-task")
async def queue_task(
    agent_id: str,
    task_data: TaskData,
    current_user: User = Depends(get_current_user)
):
    """Queue task for agent"""
    if agent_id not in agents_db:
        raise HTTPException(status_code=404, detail="Agent not found")

    agent = agents_db[agent_id]
    if agent["owner_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")

    # Mock task queueing
    task_id = str(uuid.uuid4())

    return {
        "success": True,
        "message": "Task added to queue successfully",
        "agent_id": agent_id,
        "task_id": task_id
    }

# Task Management Endpoints
@app.get("/api/v1/tasks", response_model=TasksResponse)
async def get_tasks(
    page: int = 1,
    per_page: int = 10,
    status: Optional[str] = None,
    priority: Optional[str] = None,
    type: Optional[str] = None,
    agent_id: Optional[str] = None,
    orchestration_id: Optional[str] = None,
    current_user: User = Depends(get_current_user)
):
    """Get list of tasks"""
    user_tasks = [task for task in tasks_db.values() if task["owner_id"] == current_user.id]

    # Apply filters
    if status:
        user_tasks = [task for task in user_tasks if task["status"] == status]
    if priority:
        user_tasks = [task for task in user_tasks if task["priority"] == priority]
    if type:
        user_tasks = [task for task in user_tasks if task["type"] == type]
    if agent_id:
        user_tasks = [task for task in user_tasks if task.get("agent_id") == agent_id]
    if orchestration_id:
        user_tasks = [task for task in user_tasks if task.get("orchestration_id") == orchestration_id]

    # Pagination
    total = len(user_tasks)
    start = (page - 1) * per_page
    end = start + per_page
    paginated_tasks = user_tasks[start:end]

    return TasksResponse(
        items=[Task(**task) for task in paginated_tasks],
        total=total,
        page=page,
        per_page=per_page,
        total_pages=(total + per_page - 1) // per_page
    )

@app.post("/api/v1/tasks", response_model=Task, status_code=201)
async def create_task(task_data: TaskCreate, current_user: User = Depends(get_current_user)):
    """Create a new task"""
    task_id = str(uuid.uuid4())
    now = datetime.utcnow().isoformat()

    # Get agent name if agent_id is provided
    agent_name = None
    if task_data.agent_id and task_data.agent_id in agents_db:
        agent_name = agents_db[task_data.agent_id]["name"]

    task = {
        "id": task_id,
        "title": task_data.title,
        "description": task_data.description,
        "type": task_data.type,
        "status": "pending",
        "priority": task_data.priority,
        "agent_id": task_data.agent_id,
        "agent_name": agent_name,
        "orchestration_id": task_data.orchestration_id,
        "orchestration_name": None,  # TODO: implement orchestrations
        "progress": 0,
        "created_at": now,
        "updated_at": now,
        "estimated_duration": task_data.estimated_duration,
        "actual_duration": None,
        "error_message": None,
        "result_summary": None,
        "input_data": task_data.input_data,
        "config": task_data.config,
        "auto_retry": task_data.auto_retry,
        "max_retries": task_data.max_retries,
        "timeout_seconds": task_data.timeout_seconds,
        "retry_count": 0,
        "owner_id": current_user.id
    }

    tasks_db[task_id] = task
    return Task(**task)

@app.get("/api/v1/tasks/{task_id}", response_model=Task)
async def get_task(task_id: str, current_user: User = Depends(get_current_user)):
    """Get task by ID"""
    if task_id not in tasks_db:
        raise HTTPException(status_code=404, detail="Task not found")

    task = tasks_db[task_id]
    if task["owner_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")

    return Task(**task)

@app.put("/api/v1/tasks/{task_id}", response_model=Task)
async def update_task(
    task_id: str,
    task_data: TaskCreate,
    current_user: User = Depends(get_current_user)
):
    """Update task"""
    if task_id not in tasks_db:
        raise HTTPException(status_code=404, detail="Task not found")

    task = tasks_db[task_id]
    if task["owner_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")

    # Update task fields
    task.update({
        "title": task_data.title,
        "description": task_data.description,
        "type": task_data.type,
        "priority": task_data.priority,
        "agent_id": task_data.agent_id,
        "orchestration_id": task_data.orchestration_id,
        "estimated_duration": task_data.estimated_duration,
        "input_data": task_data.input_data,
        "config": task_data.config,
        "auto_retry": task_data.auto_retry,
        "max_retries": task_data.max_retries,
        "timeout_seconds": task_data.timeout_seconds,
        "updated_at": datetime.utcnow().isoformat()
    })

    # Update agent name if agent_id changed
    if task_data.agent_id and task_data.agent_id in agents_db:
        task["agent_name"] = agents_db[task_data.agent_id]["name"]
    else:
        task["agent_name"] = None

    return Task(**task)

@app.delete("/api/v1/tasks/{task_id}")
async def delete_task(task_id: str, current_user: User = Depends(get_current_user)):
    """Delete task"""
    if task_id not in tasks_db:
        raise HTTPException(status_code=404, detail="Task not found")

    task = tasks_db[task_id]
    if task["owner_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")

    del tasks_db[task_id]
    return {"message": "Task deleted successfully"}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "version": "2.0.0",
        "environment": "development"
    }

@app.get("/api/v1/agents")
async def list_agents():
    """List agents endpoint"""
    return {
        "agents": [],
        "total": 0,
        "page": 1,
        "size": 10
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main_simple:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )