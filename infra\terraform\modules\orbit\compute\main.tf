# Compute Engine Module for Docker Container Services with Ansible Integration
# Creates VM instance managed by Ansible for running platform services

terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
    null = {
      source  = "hashicorp/null"
      version = "~> 3.0"
    }
    local = {
      source  = "hashicorp/local"
      version = "~> 2.0"
    }
  }
}

# Enable Compute Engine API
resource "google_project_service" "compute" {
  service = "compute.googleapis.com"
  
  disable_dependent_services = true
}

# Service account for the compute instance
resource "google_service_account" "compute_sa" {
  account_id   = "${var.project_name}-compute-sa"
  display_name = "Service Account for ${var.project_name} Compute Instance"
  description  = "Service account used by compute instances to access GCP services"
}

# IAM roles for the service account
resource "google_project_iam_member" "compute_sa_artifact_registry" {
  project = data.google_project.current.project_id
  role    = "roles/artifactregistry.reader"
  member  = "serviceAccount:${google_service_account.compute_sa.email}"
}

resource "google_project_iam_member" "compute_sa_secret_manager" {
  project = data.google_project.current.project_id
  role    = "roles/secretmanager.secretAccessor"
  member  = "serviceAccount:${google_service_account.compute_sa.email}"
}

resource "google_project_iam_member" "compute_sa_sql_client" {
  project = data.google_project.current.project_id
  role    = "roles/cloudsql.client"
  member  = "serviceAccount:${google_service_account.compute_sa.email}"
}

resource "google_project_iam_member" "compute_sa_logging" {
  project = data.google_project.current.project_id
  role    = "roles/logging.logWriter"
  member  = "serviceAccount:${google_service_account.compute_sa.email}"
}

resource "google_project_iam_member" "compute_sa_monitoring" {
  project = data.google_project.current.project_id
  role    = "roles/monitoring.metricWriter"
  member  = "serviceAccount:${google_service_account.compute_sa.email}"
}

# SSH key for Ansible access
resource "tls_private_key" "ansible_ssh" {
  algorithm = "RSA"
  rsa_bits  = 4096
}

# Save private key locally for Ansible
resource "local_sensitive_file" "ansible_private_key" {
  content  = tls_private_key.ansible_ssh.private_key_pem
  filename = "${path.module}/ansible-ssh-key.pem"
  file_permission = "0600"
}

# Enhanced startup script for Container-Optimized OS (COS)
locals {
  ansible_startup_script = <<-EOT
    #!/bin/bash
    set -e
    
    # Logging setup
    exec > >(tee /var/log/startup-script.log)
    exec 2>&1
    echo "=== Platform Services Startup Script - $$(date) ==="
    
    # Configure Docker authentication for COS
    echo "Configuring Docker authentication for COS..."
    
    # Get access token for Docker authentication  
    echo "Getting access token for Docker..."
    ACCESS_TOKEN=$$(curl -s -H "Metadata-Flavor: Google" \
      "http://metadata.google.internal/computeMetadata/v1/instance/service-accounts/default/token" | \
      python3 -c "import sys, json; print(json.load(sys.stdin)['access_token'])" 2>/dev/null || echo "")
    
    if [ -n "$$ACCESS_TOKEN" ]; then
      echo "$$ACCESS_TOKEN" | docker login -u oauth2accesstoken --password-stdin australia-southeast1-docker.pkg.dev
      echo "Docker authentication successful"
    else
      echo "ERROR: Could not get access token for Docker authentication"
      exit 1
    fi
    
    # Create platform services directory in writable location for COS
    mkdir -p /var/lib/platform
    cd /var/lib/platform
    
    # Get database password from Secret Manager
    echo "Retrieving database password from Secret Manager..."
    
    # Use REST API since gcloud is not available on Container-Optimized OS
    SECRET_RESPONSE=$$(curl -s -H "Authorization: Bearer $$ACCESS_TOKEN" \
      "https://secretmanager.googleapis.com/v1/projects/${data.google_project.current.project_id}/secrets/${var.database_password_secret}/versions/latest:access" 2>/dev/null || echo "")
    
    if [ -n "$$SECRET_RESPONSE" ]; then
      DB_PASSWORD=$$(echo "$$SECRET_RESPONSE" | python3 -c "import sys, json, base64; data = json.load(sys.stdin); print(base64.b64decode(data['payload']['data']).decode())" 2>/dev/null || echo "placeholder")
    else
      DB_PASSWORD="placeholder"
    fi
    
    if [ -z "$$DB_PASSWORD" ] || [ "$$DB_PASSWORD" = "placeholder" ]; then
      echo "ERROR: Could not retrieve database password from Secret Manager"
      echo "Using placeholder password - services will fail to start"
      DB_PASSWORD="placeholder"
    else
      echo "Database password retrieved successfully: $${#DB_PASSWORD} characters"
    fi
    
    # Create Docker network
    docker network create platform-network || true
    
    # Deploy services
    echo "=== Deploying Platform Services ==="
    
    # Pull and run Auth service
    echo "Deploying Auth service..."
    docker pull ${var.registry_url}/auth:${var.service_versions.auth}
    docker stop auth || true
    docker rm auth || true
    docker run -d --name auth \
      --network platform-network \
      -p 8004:8004 \
      -e PORT=8004 \
      -e DATABASE_URL=postgresql://${var.database_user}:$$DB_PASSWORD@${var.database_host}:5432/${var.database_name} \
      -e LOG_LEVEL=info \
      --restart unless-stopped \
      ${var.registry_url}/auth:${var.service_versions.auth}
    
    # Pull and run CRM Backend service
    echo "Deploying CRM Backend service..."
    docker pull ${var.registry_url}/crm-backend:${var.service_versions.backend}
    docker stop crm-backend || true
    docker rm crm-backend || true
    docker run -d --name crm-backend \
      --network platform-network \
      -p 8003:8003 \
      -e PORT=8003 \
      -e DATABASE_URL=postgresql://${var.database_user}:$$DB_PASSWORD@${var.database_host}:5432/${var.database_name} \
      -e LOG_LEVEL=info \
      --restart unless-stopped \
      ${var.registry_url}/crm-backend:${var.service_versions.backend}
    
    # Pull and run Gateway service
    echo "Deploying Gateway service..."
    docker pull ${var.registry_url}/gateway:${var.service_versions.gateway}
    docker stop gateway || true
    docker rm gateway || true
    docker run -d --name gateway \
      --network platform-network \
      -p 8085:8085 \
      -e PORT=8085 \
      -e AUTH_SERVICE_URL=http://auth:8004 \
      -e CRM_BACKEND_URL=http://crm-backend:8003 \
      -e LOG_LEVEL=info \
      --restart unless-stopped \
      ${var.registry_url}/gateway:${var.service_versions.gateway}
    
    # Wait for services to start
    sleep 30
    
    # Verify services are running
    echo "=== Service Status ==="
    docker ps --filter network=platform-network
    
    echo "=== Startup script completed successfully - $$(date) ==="
  EOT
}

# Compute Engine instance
resource "google_compute_instance" "platform_instance" {
  name         = "${var.project_name}-platform-${var.environment}"
  machine_type = var.machine_type
  zone         = var.zone

  tags = ["http-server", "https-server", "ssh-server"]
  
  labels = {
    ansible-managed = "true"
    environment     = var.environment
    deployment-color = var.initial_deployment_color
  }

  boot_disk {
    initialize_params {
      image = var.boot_disk_image
      size  = var.boot_disk_size_gb
      type  = "pd-ssd"
    }
  }

  # Additional disk for Docker volumes
  attached_disk {
    source      = google_compute_disk.docker_volumes.name
    device_name = "docker-volumes"
  }

  network_interface {
    subnetwork = var.subnet_name
    
    # Assign external IP only if specified
    dynamic "access_config" {
      for_each = var.assign_external_ip ? [1] : []
      content {
        # Ephemeral external IP
      }
    }
  }

  service_account {
    email  = google_service_account.compute_sa.email
    scopes = [
      "https://www.googleapis.com/auth/cloud-platform"
    ]
  }

  metadata = {
    startup-script        = file("${path.module}/startup-script.sh")
    enable-oslogin       = "FALSE"  # Disable OS Login for Ansible SSH access
    google-logging-enabled = "true"
    google-monitoring-enabled = "true"
    ssh-keys = "ansible:${tls_private_key.ansible_ssh.public_key_openssh}"
  }

  # Allow instance to be recreated
  allow_stopping_for_update = true

  depends_on = [
    google_project_service.compute,
    google_service_account.compute_sa
  ]
}

# Additional disk for Docker volumes and data
resource "google_compute_disk" "docker_volumes" {
  name = "${var.project_name}-docker-volumes-${var.environment}"
  type = "pd-ssd"
  zone = var.zone
  size = var.docker_volumes_disk_size_gb
}

# Static IP address (optional)
resource "google_compute_address" "platform_ip" {
  count = var.create_static_ip ? 1 : 0
  
  name   = "${var.project_name}-platform-ip-${var.environment}"
  region = var.region
}

# Wait for instance to be ready
resource "null_resource" "wait_for_instance" {
  provisioner "local-exec" {
    command = <<-EOT
      echo "Waiting for instance to be ready..."
      sleep 60
    EOT
  }
  
  depends_on = [google_compute_instance.platform_instance]
}

# Generate Ansible inventory
resource "local_file" "ansible_inventory" {
  content = templatefile("${path.module}/templates/ansible-inventory.yml.tpl", {
    instance_name = google_compute_instance.platform_instance.name
    instance_ip   = google_compute_instance.platform_instance.network_interface[0].network_ip
    external_ip   = try(google_compute_instance.platform_instance.network_interface[0].access_config[0].nat_ip, "")
    project_id    = data.google_project.current.project_id
    zone          = var.zone
    environment   = var.environment
  })
  
  filename = "${path.root}/../ansible/inventory/terraform-${var.environment}.yml"
  
  depends_on = [google_compute_instance.platform_instance]
}

# Generate Ansible variables
resource "local_file" "ansible_vars" {
  content = templatefile("${path.module}/templates/ansible-vars.yml.tpl", {
    project_id              = data.google_project.current.project_id
    environment             = var.environment
    registry_url            = var.registry_url
    database_host           = var.database_host
    database_name           = var.database_name
    database_user           = var.database_user
    database_password_secret = var.database_password_secret
    gateway_version         = var.service_versions.gateway
    auth_version           = var.service_versions.auth
    backend_version        = var.service_versions.backend
    deployment_color       = var.initial_deployment_color
  })
  
  filename = "${path.root}/../ansible/vars/${var.environment}-terraform.yml"
  
  depends_on = [google_compute_instance.platform_instance]
}

# Run Ansible playbook to configure the instance (optional)
resource "null_resource" "ansible_provision" {
  count = var.enable_ansible_provisioning ? 1 : 0
  # Triggers for re-running Ansible
  triggers = {
    instance_id      = google_compute_instance.platform_instance.id
    service_versions = jsonencode(var.service_versions)
    service_config   = jsonencode(var.service_config)
    playbook_checksum = filesha256("${path.root}/../ansible/playbooks/deploy-services.yml")
  }
  
  provisioner "local-exec" {
    working_dir = "${path.root}/../ansible"
    environment = {
      ANSIBLE_HOST_KEY_CHECKING = "False"
      ANSIBLE_SSH_KEY = abspath(local_sensitive_file.ansible_private_key.filename)
      GCP_PROJECT_ID = data.google_project.current.project_id
      GCP_SERVICE_ACCOUNT_FILE = var.gcp_credentials_file
      ANSIBLE_USER = "ansible"
    }
    
    command = <<-EOT
      ansible-playbook \
        -i inventory/terraform-${var.environment}.yml \
        --extra-vars "@vars/${var.environment}-terraform.yml" \
        --extra-vars "target_host=${google_compute_instance.platform_instance.name}" \
        playbooks/deploy-services.yml
    EOT
  }
  
  depends_on = [
    null_resource.wait_for_instance,
    local_file.ansible_inventory,
    local_file.ansible_vars
  ]
}

# Instance group for load balancer backend
resource "google_compute_instance_group" "platform_group" {
  name = "${var.project_name}-platform-group-${var.environment}"
  zone = var.zone

  instances = [google_compute_instance.platform_instance.id]

  named_port {
    name = "http"
    port = 80
  }

  named_port {
    name = "gateway"
    port = var.gateway_port
  }

  named_port {
    name = "auth"
    port = var.auth_service_port
  }

  named_port {
    name = "crm"
    port = var.crm_service_port
  }
}

# Health check for the gateway service
resource "google_compute_health_check" "gateway_health_check" {
  name = "${var.project_name}-gateway-health-${var.environment}"

  timeout_sec        = 5
  check_interval_sec = 10
  healthy_threshold  = 2
  unhealthy_threshold = 3

  http_health_check {
    port         = var.gateway_port
    request_path = "/health"
  }
}

# Backend service for load balancer
resource "google_compute_backend_service" "platform_backend" {
  name        = "${var.project_name}-platform-backend-${var.environment}"
  protocol    = "HTTP"
  timeout_sec = 30

  backend {
    group = google_compute_instance_group.platform_group.id
  }
  
  port_name = "gateway"

  health_checks = [google_compute_health_check.gateway_health_check.id]
}

# Data source to get current project information
data "google_project" "current" {
  project_id = var.project_id
}