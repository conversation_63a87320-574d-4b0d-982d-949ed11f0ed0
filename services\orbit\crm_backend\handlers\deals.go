package handlers

import (
	"database/sql"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/TwoDotAi/mono/services/orbit/crm_backend/database"
	"github.com/TwoDotAi/mono/shared/go/logging"
	openapi "github.com/TwoDotAi/mono/generated/orbit/go/server"
	"github.com/oapi-codegen/runtime/types"
	"go.uber.org/zap"
)

// Use generated OpenAPI types
type Deal = openapi.Deal
type DealDetails = openapi.DealDetails
type DealCreate = openapi.DealCreate
type DealUpdate = openapi.DealUpdate
type DealStage = openapi.DealStage
type DealWithDetails = openapi.DealWithDetails
type DealCompanyInfo = openapi.DealCompanyInfo
type DealStageInfo = openapi.DealStageInfo

func ListDeals(c *gin.Context) {
	db := database.GetDB()
	
	stageID := c.Query("stage_id")
	companyID := c.Query("company_id")
	
	query := `SELECT d.id, d.title, d.description, d.estimated_value, d.company_id, 
			         d.deal_stage_id, d.expected_close_date, d.created_by, d.created_at, d.updated_at,
			         c.id, c.name,
			         ds.id, ds.name, ds.pipeline_order
			  FROM deals d
			  JOIN companies c ON d.company_id = c.id
			  JOIN deal_stages ds ON d.deal_stage_id = ds.id
			  WHERE 1=1`
	args := []interface{}{}
	argIndex := 1

	if stageID != "" {
		query += " AND d.deal_stage_id = $" + string(rune(argIndex+'0'))
		args = append(args, stageID)
		argIndex++
	}

	if companyID != "" {
		query += " AND d.company_id = $" + string(rune(argIndex+'0'))
		args = append(args, companyID)
		argIndex++
	}

	query += " ORDER BY d.created_at DESC"

	rows, err := db.Query(query, args...)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
		return
	}
	defer rows.Close()

	var deals []DealWithDetails
	for rows.Next() {
		var deal DealWithDetails
		var description, expectedCloseDate sql.NullString
		var estimatedValue sql.NullFloat64
		var pipelineOrder int32
		
		// Initialize nested structs
		deal.Company = &openapi.DealCompanyInfo{}
		deal.DealStage = &openapi.DealStageInfo{}
		
		err := rows.Scan(&deal.Id, &deal.Title, &description, &estimatedValue,
			&deal.CompanyId, &deal.DealStageId, &expectedCloseDate, &deal.CreatedBy,
			&deal.CreatedAt, &deal.UpdatedAt,
			&deal.Company.Id, &deal.Company.Name,
			&deal.DealStage.Id, &deal.DealStage.Name, &pipelineOrder)
		
		// Convert sql.Null types to pointer types for OpenAPI
		deal.Description = nullStringToPtr(description)
		deal.EstimatedValue = func() *float32 {
			if estimatedValue.Valid {
				f := float32(estimatedValue.Float64)
				return &f
			}
			return nil
		}()
		deal.ExpectedCloseDate = func() *types.Date {
			if expectedCloseDate.Valid {
				return datePtr(expectedCloseDate.String)
			}
			return nil
		}()
		deal.DealStage.PipelineOrder = intPtr(pipelineOrder)
		
		if err != nil {
			logging.LogError(err, "Failed to scan deal row",
				zap.String("operation", "list_deals"),
			)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
			return
		}
		deals = append(deals, deal)
	}

	c.JSON(http.StatusOK, deals)
}

func CreateDeal(c *gin.Context) {
	userIDVal, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	
	userID, ok := userIDVal.(string)
	if !ok || userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user authentication"})
		return
	}
	
	var req DealCreate
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	db := database.GetDB()
	
	id := uuid.New().String()
	now := time.Now()
	
	deal := Deal{
		Id:                uuidPtr(id),
		Title:             stringPtr(req.Title),
		Description:       req.Description,
		EstimatedValue:    req.EstimatedValue,
		CompanyId:         func() *types.UUID { u := req.CompanyId; return &u }(),
		DealStageId:       func() *types.UUID { u := req.DealStageId; return &u }(),
		ExpectedCloseDate: req.ExpectedCloseDate,
		CreatedBy:         uuidPtr(userID),
		CreatedAt:         timePtr(now),
		UpdatedAt:         timePtr(now),
	}

	_, err := db.Exec(
		`INSERT INTO deals (id, title, description, estimated_value, company_id, deal_stage_id, expected_close_date, created_by, created_at, updated_at)
		 VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)`,
		deal.Id, deal.Title, deal.Description, deal.EstimatedValue, deal.CompanyId,
		deal.DealStageId, deal.ExpectedCloseDate, deal.CreatedBy, deal.CreatedAt, deal.UpdatedAt,
	)
	if err != nil {
		logging.LogError(err, "Failed to create deal",
			zap.String("operation", "create_deal"),
			zap.String("deal_title", ptrToString(deal.Title)),
			zap.String("deal_id", ptrToUUID(deal.Id)),
			zap.String("company_id", ptrToUUID(deal.CompanyId)),
			zap.String("deal_stage_id", ptrToUUID(deal.DealStageId)),
			zap.Any("user_id", userID),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create deal"})
		return
	}

	c.JSON(http.StatusCreated, deal)
}

func GetDeal(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Deal ID required"})
		return
	}

	db := database.GetDB()
	
	var deal DealDetails
	var description, expectedCloseDate sql.NullString
	var estimatedValue sql.NullFloat64
	var companyWebsite, companyPhone, companyAddress, companyNotes, companyStatusId sql.NullString
	var companyId, companyName string
	var companyIsDeleted bool
	var companyCreatedAt, companyUpdatedAt time.Time
	var dealStageId, dealStageName string
	var pipelineOrder int32
	var dealStageIsClosedWon, dealStageIsClosedLost bool
	var dealStageCreatedAt time.Time
	var dealId, dealTitle string
	var dealCreatedBy string
	var dealCreatedAt, dealUpdatedAt time.Time
	
	err := db.QueryRow(
		`SELECT d.id, d.title, d.description, d.estimated_value, d.company_id, 
		        d.deal_stage_id, d.expected_close_date, d.created_by, d.created_at, d.updated_at,
		        c.id, c.name, c.website, c.phone, c.address, c.notes, c.company_status_id, 
		        c.is_deleted, c.created_at, c.updated_at,
		        ds.id, ds.name, ds.pipeline_order, ds.is_closed_won, ds.is_closed_lost, ds.created_at
		 FROM deals d
		 JOIN companies c ON d.company_id = c.id
		 JOIN deal_stages ds ON d.deal_stage_id = ds.id
		 WHERE d.id = $1`,
		id,
	).Scan(&dealId, &dealTitle, &description, &estimatedValue,
		&deal.CompanyId, &deal.DealStageId, &expectedCloseDate, &dealCreatedBy,
		&dealCreatedAt, &dealUpdatedAt,
		&companyId, &companyName, &companyWebsite, &companyPhone,
		&companyAddress, &companyNotes, &companyStatusId,
		&companyIsDeleted, &companyCreatedAt, &companyUpdatedAt,
		&dealStageId, &dealStageName, &pipelineOrder,
		&dealStageIsClosedWon, &dealStageIsClosedLost, &dealStageCreatedAt)
	
	// Convert to pointer types for OpenAPI
	deal.Id = uuidPtr(dealId)
	deal.Title = stringPtr(dealTitle)
	deal.Description = nullStringToPtr(description)
	deal.EstimatedValue = func() *float32 {
		if estimatedValue.Valid {
			f := float32(estimatedValue.Float64)
			return &f
		}
		return nil
	}()
	deal.ExpectedCloseDate = func() *types.Date {
		if expectedCloseDate.Valid {
			return datePtr(expectedCloseDate.String)
		}
		return nil
	}()
	deal.CreatedBy = uuidPtr(dealCreatedBy)
	deal.CreatedAt = timePtr(dealCreatedAt)
	deal.UpdatedAt = timePtr(dealUpdatedAt)
	
	// Set company info  
	deal.Company = &struct {
		Id      *types.UUID `json:"id,omitempty"`
		Name    *string     `json:"name,omitempty"`
		Phone   *string     `json:"phone,omitempty"`
		Website *string     `json:"website,omitempty"`
	}{
		Id:      uuidPtr(companyId),
		Name:    stringPtr(companyName),
		Website: nullStringToPtr(companyWebsite),
		Phone:   nullStringToPtr(companyPhone),
	}
	
	// Set deal stage info
	deal.DealStage = &struct {
		Id            *types.UUID `json:"id,omitempty"`
		IsClosedLost  *bool       `json:"is_closed_lost,omitempty"`
		IsClosedWon   *bool       `json:"is_closed_won,omitempty"`
		Name          *string     `json:"name,omitempty"`
		PipelineOrder *int        `json:"pipeline_order,omitempty"`
	}{
		Id:            uuidPtr(dealStageId),
		Name:          stringPtr(dealStageName),
		PipelineOrder: intPtr(pipelineOrder),
		IsClosedWon:   boolPtr(dealStageIsClosedWon),
		IsClosedLost:  boolPtr(dealStageIsClosedLost),
	}

	if err == sql.ErrNoRows {
		c.JSON(http.StatusNotFound, gin.H{"error": "Deal not found"})
		return
	}
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
		return
	}

	c.JSON(http.StatusOK, deal)
}

func UpdateDeal(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Deal ID required"})
		return
	}

	var req DealUpdate
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	db := database.GetDB()
	
	// Build dynamic update query
	setParts := []string{}
	args := []interface{}{}
	argIndex := 1

	if req.Title != nil {
		setParts = append(setParts, "title = $"+string(rune(argIndex+'0')))
		args = append(args, *req.Title)
		argIndex++
	}
	if req.Description != nil {
		setParts = append(setParts, "description = $"+string(rune(argIndex+'0')))
		args = append(args, *req.Description)
		argIndex++
	}
	if req.EstimatedValue != nil {
		setParts = append(setParts, "estimated_value = $"+string(rune(argIndex+'0')))
		args = append(args, *req.EstimatedValue)
		argIndex++
	}
	if req.CompanyId != nil {
		setParts = append(setParts, "company_id = $"+string(rune(argIndex+'0')))
		args = append(args, req.CompanyId.String())
		argIndex++
	}
	if req.DealStageId != nil {
		setParts = append(setParts, "deal_stage_id = $"+string(rune(argIndex+'0')))
		args = append(args, req.DealStageId.String())
		argIndex++
	}
	if req.ExpectedCloseDate != nil {
		setParts = append(setParts, "expected_close_date = $"+string(rune(argIndex+'0')))
		args = append(args, *req.ExpectedCloseDate)
		argIndex++
	}

	if len(setParts) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No fields to update"})
		return
	}

	// Add updated_at
	setParts = append(setParts, "updated_at = $"+string(rune(argIndex+'0')))
	args = append(args, time.Now())
	argIndex++

	// Add WHERE clause
	args = append(args, id)

	query := "UPDATE deals SET " + setParts[0]
	for i := 1; i < len(setParts); i++ {
		query += ", " + setParts[i]
	}
	query += " WHERE id = $" + string(rune(argIndex+'0'))

	result, err := db.Exec(query, args...)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update deal"})
		return
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Deal not found"})
		return
	}

	// Return updated deal
	GetDeal(c)
}

func DeleteDeal(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Deal ID required"})
		return
	}

	db := database.GetDB()
	
	result, err := db.Exec("DELETE FROM deals WHERE id = $1", id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete deal"})
		return
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Deal not found"})
		return
	}

	c.Status(http.StatusNoContent)
}

func ListDealStages(c *gin.Context) {
	db := database.GetDB()
	
	rows, err := db.Query(
		"SELECT id, name, pipeline_order, is_closed_won, is_closed_lost, created_at FROM deal_stages ORDER BY pipeline_order",
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
		return
	}
	defer rows.Close()

	var stages []DealStage
	for rows.Next() {
		var stage DealStage
		var pipelineOrder int32
		err := rows.Scan(&stage.Id, &stage.Name, &pipelineOrder, &stage.IsClosedWon, &stage.IsClosedLost, &stage.CreatedAt)
		stage.PipelineOrder = intPtr(pipelineOrder)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
			return
		}
		stages = append(stages, stage)
	}

	c.JSON(http.StatusOK, stages)
}