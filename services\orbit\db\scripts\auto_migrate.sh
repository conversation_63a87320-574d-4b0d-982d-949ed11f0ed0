#!/bin/bash
# Automatic migration system - detects new SQL files and applies migrations
set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
print_error() {
    echo -e "${RED}❌ Error: $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_step() {
    echo -e "${BLUE}🚀 $1${NC}"
}

# Get the workspace root
if [ -n "$BUILD_WORKSPACE_DIRECTORY" ]; then
    cd "$BUILD_WORKSPACE_DIRECTORY"
elif [ -n "$TEST_WORKSPACE" ]; then
    cd "$TEST_WORKSPACE/.."
else
    # Find the workspace root by looking for WORKSPACE file
    CURRENT_DIR="$PWD"
    while [ "$CURRENT_DIR" != "/" ]; do
        if [ -f "$CURRENT_DIR/WORKSPACE" ]; then
            cd "$CURRENT_DIR"
            break
        fi
        CURRENT_DIR="$(dirname "$CURRENT_DIR")"
    done
fi

# Navigate to platform/db directory
cd platform/db

# Configuration
MIGRATIONS_DIR="migrations"
SCHEMA_DIR="schema"
AUTO_MIGRATIONS_DIR="auto_migrations"
MIGRATION_LOG="migration_log.txt"

# Create directories if they don't exist
mkdir -p "$SCHEMA_DIR" "$AUTO_MIGRATIONS_DIR"

# Function to get the next migration version
get_next_version() {
    local last_version=$(find "$MIGRATIONS_DIR" -name "V*.sql" | sed 's/.*V\([0-9]*\).*/\1/' | sort -n | tail -1)
    if [ -z "$last_version" ]; then
        echo "001"
    else
        printf "%03d" $((last_version + 1))
    fi
}

# Function to generate migration from schema file
generate_migration() {
    local schema_file="$1"
    local migration_name="$2"
    local version="$3"
    
    local base_name=$(basename "$schema_file" .sql)
    local migration_file="${MIGRATIONS_DIR}/V${version}__${migration_name}.sql"
    
    print_info "Generating migration: $migration_file"
    
    # Create migration file with proper header
    cat > "$migration_file" << EOF
-- Auto-generated migration from $schema_file
-- Generated on $(date)
-- V${version}__${migration_name}.sql

EOF
    
    # Add the schema content
    cat "$schema_file" >> "$migration_file"
    
    # Log the migration
    echo "$(date): Generated $migration_file from $schema_file" >> "$MIGRATION_LOG"
    
    print_success "Generated migration: $migration_file"
}

# Function to validate SQL file
validate_sql() {
    local sql_file="$1"
    
    # Basic validation - check for dangerous operations
    if grep -i "drop database\|drop schema\|truncate\|delete from.*where.*1=1" "$sql_file" >/dev/null 2>&1; then
        print_error "Dangerous SQL operation detected in $sql_file"
        return 1
    fi
    
    # Check for proper formatting
    if ! grep -q ";" "$sql_file"; then
        print_warning "SQL file $sql_file may be missing semicolons"
    fi
    
    return 0
}

# Function to check if migration should be applied
should_apply_migration() {
    local environment="$1"
    
    case "$environment" in
        "local")
            return 0  # Always apply to local
            ;;
        "dev")
            # Check if we're in dev mode
            if [ "$AUTO_MIGRATE_DEV" = "true" ]; then
                return 0
            else
                print_warning "Auto-migration to dev disabled. Set AUTO_MIGRATE_DEV=true to enable."
                return 1
            fi
            ;;
        "prod")
            print_error "Auto-migration to production is disabled for safety"
            return 1
            ;;
        *)
            print_error "Unknown environment: $environment"
            return 1
            ;;
    esac
}

# Function to run migration
run_migration() {
    local environment="$1"
    local migration_file="$2"
    
    print_step "Applying migration to $environment environment"
    
    case "$environment" in
        "local")
            if docker ps | grep -q platform_postgres; then
                print_info "Running migration on local database..."
                bazel run //platform/db:migrate
            else
                print_warning "Local database not running. Starting it..."
                bazel run //platform/db:start
                bazel run //platform/db:migrate
            fi
            ;;
        "dev")
            print_info "Running migration on GCP dev environment..."
            bazel run //platform/db:migrate_gcp_dev_simple
            ;;
        *)
            print_error "Migration to $environment not supported"
            return 1
            ;;
    esac
}

# Main auto-migration function
auto_migrate() {
    local environment="${1:-local}"
    
    print_step "Auto-migration system starting for $environment environment"
    
    # Check if we should apply migrations to this environment
    if ! should_apply_migration "$environment"; then
        return 1
    fi
    
    # Check for new or modified schema files
    local new_migrations=false
    
    if [ -d "$SCHEMA_DIR" ]; then
        for schema_file in "$SCHEMA_DIR"/*.sql; do
            if [ -f "$schema_file" ]; then
                local base_name=$(basename "$schema_file" .sql)
                local auto_migration_file="${AUTO_MIGRATIONS_DIR}/${base_name}_applied.txt"
                
                # Check if this schema file has been processed
                if [ ! -f "$auto_migration_file" ] || [ "$schema_file" -nt "$auto_migration_file" ]; then
                    print_info "Detected new/modified schema: $schema_file"
                    
                    # Validate the SQL file
                    if validate_sql "$schema_file"; then
                        local version=$(get_next_version)
                        local migration_name=$(echo "$base_name" | sed 's/_/ /g')
                        
                        # Generate migration
                        generate_migration "$schema_file" "$migration_name" "$version"
                        
                        # Mark as processed
                        echo "$(date): Processed $schema_file" > "$auto_migration_file"
                        
                        new_migrations=true
                    else
                        print_error "Validation failed for $schema_file"
                        return 1
                    fi
                fi
            fi
        done
    fi
    
    # Apply migrations if we have new ones
    if [ "$new_migrations" = true ]; then
        print_step "Applying new migrations to $environment"
        run_migration "$environment"
        print_success "Auto-migration completed successfully"
    else
        print_info "No new migrations detected"
    fi
}

# Watch mode function
watch_mode() {
    local environment="${1:-local}"
    
    print_step "Starting auto-migration watch mode for $environment environment"
    print_info "Watching for changes in $SCHEMA_DIR/"
    print_info "Press Ctrl+C to stop watching"
    
    # Initial check
    auto_migrate "$environment"
    
    # Watch for changes (requires fswatch or inotify-tools)
    if command -v fswatch >/dev/null 2>&1; then
        fswatch -o "$SCHEMA_DIR" | while read; do
            echo ""
            print_info "Schema change detected, running auto-migration..."
            auto_migrate "$environment"
        done
    elif command -v inotifywait >/dev/null 2>&1; then
        while inotifywait -e modify,create,delete "$SCHEMA_DIR"; do
            echo ""
            print_info "Schema change detected, running auto-migration..."
            auto_migrate "$environment"
        done
    else
        print_warning "File watching not available. Install fswatch or inotify-tools for real-time monitoring."
        print_info "Running periodic checks every 30 seconds..."
        
        while true; do
            sleep 30
            auto_migrate "$environment"
        done
    fi
}

# Help function
show_help() {
    echo "Auto-migration system for database schema changes"
    echo ""
    echo "Usage: $0 [command] [environment]"
    echo ""
    echo "Commands:"
    echo "  run [env]     - Run auto-migration once (default: local)"
    echo "  watch [env]   - Watch for schema changes and auto-migrate"
    echo "  setup         - Set up auto-migration directories"
    echo "  help          - Show this help message"
    echo ""
    echo "Environments:"
    echo "  local         - Local development database (default)"
    echo "  dev           - GCP dev environment (requires AUTO_MIGRATE_DEV=true)"
    echo ""
    echo "Examples:"
    echo "  $0 run local              # Apply migrations to local database"
    echo "  $0 watch local            # Watch for changes and auto-migrate locally"
    echo "  AUTO_MIGRATE_DEV=true $0 run dev  # Apply migrations to GCP dev"
    echo ""
    echo "Schema files should be placed in: $SCHEMA_DIR/"
    echo "Generated migrations will be in: $MIGRATIONS_DIR/"
}

# Setup function
setup_auto_migration() {
    print_step "Setting up auto-migration system"
    
    # Create directories
    mkdir -p "$SCHEMA_DIR" "$AUTO_MIGRATIONS_DIR"
    
    # Create example schema file
    cat > "$SCHEMA_DIR/example_table.sql" << 'EOF'
-- Example schema file
-- Place your SQL schema definitions here
-- This file will be automatically converted to a migration

CREATE TABLE IF NOT EXISTS example_table (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes
CREATE INDEX IF NOT EXISTS idx_example_table_name ON example_table(name);
EOF
    
    # Create README
    cat > "$SCHEMA_DIR/README.md" << 'EOF'
# Schema Directory

This directory contains SQL schema files that will be automatically converted to migrations.

## Usage

1. Create `.sql` files in this directory with your schema definitions
2. Run `bazel run //platform/db:auto_migrate` to generate and apply migrations
3. Use `bazel run //platform/db:auto_migrate -- watch` to monitor for changes

## Rules

- Use `CREATE TABLE IF NOT EXISTS` for table creation
- Use `CREATE INDEX IF NOT EXISTS` for index creation
- Avoid destructive operations (DROP, TRUNCATE, DELETE without WHERE)
- Use meaningful file names (e.g., `user_profiles.sql`, `order_system.sql`)

## File Processing

- Files are processed in alphabetical order
- Each file generates a separate migration
- Modifications to existing files create new migrations
- Generated migrations are placed in the `migrations/` directory
EOF
    
    print_success "Auto-migration system set up successfully"
    print_info "Created directories: $SCHEMA_DIR, $AUTO_MIGRATIONS_DIR"
    print_info "Example schema file: $SCHEMA_DIR/example_table.sql"
    print_info "Documentation: $SCHEMA_DIR/README.md"
}

# Main script logic
case "${1:-run}" in
    "run")
        auto_migrate "${2:-local}"
        ;;
    "watch")
        watch_mode "${2:-local}"
        ;;
    "setup")
        setup_auto_migration
        ;;
    "help"|"--help"|"-h")
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        show_help
        exit 1
        ;;
esac