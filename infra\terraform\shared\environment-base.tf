# Shared Environment Configuration Base
# This file contains common Terraform configuration that should be shared between all environments

# Common Terraform configuration
locals {
  # Common Terraform requirements
  terraform_config = {
    required_version = ">= 1.5"
    required_providers = {
      google = {
        source  = "hashicorp/google"
        version = "~> 5.0"
      }
      random = {
        source  = "hashicorp/random"
        version = "~> 3.1"
      }
    }
  }

  # Required Google Cloud APIs (standardized across all environments)
  required_apis = [
    "compute.googleapis.com",
    "container.googleapis.com", 
    "containerregistry.googleapis.com",
    "sqladmin.googleapis.com",
    "servicenetworking.googleapis.com",
    "cloudresourcemanager.googleapis.com",
    "iam.googleapis.com",
    "secretmanager.googleapis.com",
    "artifactregistry.googleapis.com",
    "cloudbuild.googleapis.com",
    "storage.googleapis.com",
    "cloudapis.googleapis.com",
    "serviceusage.googleapis.com",
    "dns.googleapis.com",
    "monitoring.googleapis.com",
    "logging.googleapis.com",
    "iap.googleapis.com",
  ]

  # Common service ports (standardized across environments)
  service_ports = {
    auth_service = 8004
    crm_backend = 8003
    gateway = 8085
  }

  # Common network configuration
  network_config = {
    vpc_cidr_base = "10.0.0.0"  # Will be adjusted per environment (10.0.x.x for dev, 10.1.x.x for prod)
    public_subnet_suffix = "1.0/24"
    private_subnet_suffix = "2.0/24"
  }

  # Common database configuration
  database_config_base = {
    postgres_version_stable = "POSTGRES_15"  # Stable version for most environments
    postgres_version_latest = "POSTGRES_16"  # Latest version for production
  }

  # Environment-specific overrides map
  environment_configs = {
    dev = {
      # Development environment settings
      vpc_cidr_prefix = "10.0"
      machine_types = {
        compute = "e2-standard-2"
        database = "db-f1-micro"
      }
      database_config = {
        postgres_version = local.database_config_base.postgres_version_stable
        disk_size_gb = 20
        max_disk_size_gb = 100
        backup_retention_days = 7
        availability_type = "ZONAL"
        deletion_protection = false
      }
      security_config = {
        enable_ssl = true
        enable_cloud_armor = false
        enable_public_access = true
        ssh_source_ranges = ["0.0.0.0/0"]
        assign_external_ip = true
        create_static_ip = false
        enable_http_redirect = false
        enable_cdn = false
      }
      registry_config = {
        cleanup_older_than_days = 7
        cleanup_untagged_older_than_days = 1
        enable_cloud_build_access = true
        enable_compute_access = true
        enable_automated_builds = false
      }
      storage_config = {
        enable_versioning = false
        lifecycle_rules = [
          {
            action = "Delete"
            age_days = 30
            storage_classes = ["STANDARD"]
          }
        ]
        enable_automated_deployment = false
        upload_default_files = false
      }
      compute_config = {
        boot_disk_size_gb = 20
        docker_volumes_disk_size_gb = 30
      }
      feature_flags = {
        enable_migration_automation = false
        enable_monitoring = false
        enable_backup_automation = false
        schedule_shutdown = false
        auto_scaling_enabled = false
        enable_debug_mode = true
        enable_external_access = true
      }
      scaling_config = {
        min_instance_count = 1
        max_instance_count = 3
      }
    }
    
    staging = {
      # Staging environment settings (similar to dev but more production-like)
      vpc_cidr_prefix = "10.2"
      machine_types = {
        compute = "e2-standard-2"
        database = "db-custom-1-3840"  # 1 vCPU, 3.75GB RAM
      }
      database_config = {
        postgres_version = local.database_config_base.postgres_version_latest
        disk_size_gb = 50
        max_disk_size_gb = 200
        backup_retention_days = 14
        availability_type = "ZONAL"
        deletion_protection = false
      }
      security_config = {
        enable_ssl = true
        enable_cloud_armor = true
        enable_public_access = true
        ssh_source_ranges = ["0.0.0.0/0"]  # Can be restricted later
        assign_external_ip = true
        create_static_ip = false
        enable_http_redirect = true
        enable_cdn = true
      }
      registry_config = {
        cleanup_older_than_days = 14
        cleanup_untagged_older_than_days = 3
        enable_cloud_build_access = true
        enable_compute_access = true
        enable_automated_builds = false
      }
      storage_config = {
        enable_versioning = true
        lifecycle_rules = [
          {
            action                = "SetStorageClass"
            age_days              = 30
            target_storage_class  = "NEARLINE"
            matches_storage_class = ["STANDARD"]
          }
        ]
        enable_automated_deployment = false
        upload_default_files = false
      }
      compute_config = {
        boot_disk_size_gb = 30
        docker_volumes_disk_size_gb = 50
      }
      feature_flags = {
        enable_migration_automation = false
        enable_monitoring = true
        enable_backup_automation = true
        schedule_shutdown = false
        auto_scaling_enabled = true
        enable_debug_mode = false
        enable_external_access = false
      }
      scaling_config = {
        min_instance_count = 1
        max_instance_count = 5
      }
    }
    
    prod = {
      # Production environment settings
      vpc_cidr_prefix = "10.1"
      machine_types = {
        compute = "e2-standard-2"
        database = "db-custom-2-7680"  # 2 vCPUs, 7.5GB RAM
      }
      database_config = {
        postgres_version = local.database_config_base.postgres_version_latest
        disk_size_gb = 100
        max_disk_size_gb = 500
        backup_retention_days = 30
        availability_type = "REGIONAL"
        deletion_protection = true
      }
      security_config = {
        enable_ssl = true
        enable_cloud_armor = true
        enable_public_access = true
        ssh_source_ranges = ["0.0.0.0/0"]  # Should be restricted in production
        assign_external_ip = true
        create_static_ip = false
        enable_http_redirect = true
        enable_cdn = true
      }
      registry_config = {
        cleanup_older_than_days = 30
        cleanup_untagged_older_than_days = 7
        enable_cloud_build_access = true
        enable_compute_access = true
        enable_automated_builds = false
      }
      storage_config = {
        enable_versioning = true
        lifecycle_rules = [
          {
            action                = "SetStorageClass"
            age_days              = 30
            target_storage_class  = "NEARLINE"
            matches_storage_class = ["STANDARD"]
          },
          {
            action                = "SetStorageClass"
            age_days              = 90
            target_storage_class  = "COLDLINE"
            matches_storage_class = ["NEARLINE"]
          }
        ]
        enable_automated_deployment = false
        upload_default_files = false
      }
      compute_config = {
        boot_disk_size_gb = 50
        docker_volumes_disk_size_gb = 100
      }
      feature_flags = {
        enable_migration_automation = false
        enable_monitoring = true
        enable_backup_automation = true
        schedule_shutdown = false
        auto_scaling_enabled = true
        enable_debug_mode = false
        enable_external_access = false
      }
      scaling_config = {
        min_instance_count = 2
        max_instance_count = 10
      }
    }
  }
}

# Helper function to get environment-specific configuration
locals {
  env_config = local.environment_configs[var.environment]
  
  # Computed values based on environment
  vpc_cidr = "${local.env_config.vpc_cidr_prefix}.0.0/16"
  public_subnet_cidr = "${local.env_config.vpc_cidr_prefix}.${local.network_config.public_subnet_suffix}"
  private_subnet_cidr = "${local.env_config.vpc_cidr_prefix}.${local.network_config.private_subnet_suffix}"
  
  # Database name with consistent naming pattern
  database_name = "platform_${var.environment}_db"
  database_user = "platform_${var.environment}_user"
}