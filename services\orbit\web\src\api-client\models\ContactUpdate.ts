/* tslint:disable */
/* eslint-disable */
/**
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface ContactUpdate
 */
export interface ContactUpdate {
    /**
     * 
     * @type {string}
     * @memberof ContactUpdate
     */
    firstName?: string;
    /**
     * 
     * @type {string}
     * @memberof ContactUpdate
     */
    lastName?: string;
    /**
     * 
     * @type {string}
     * @memberof ContactUpdate
     */
    email?: string;
    /**
     * 
     * @type {string}
     * @memberof ContactUpdate
     */
    phone?: string;
    /**
     * 
     * @type {string}
     * @memberof ContactUpdate
     */
    jobTitle?: string;
    /**
     * 
     * @type {string}
     * @memberof ContactUpdate
     */
    companyId?: string | null;
}

/**
 * Check if a given object implements the ContactUpdate interface.
 */
export function instanceOfContactUpdate(value: object): value is ContactUpdate {
    return true;
}

export function ContactUpdateFromJSON(json: any): ContactUpdate {
    return ContactUpdateFromJSONTyped(json, false);
}

export function ContactUpdateFromJSONTyped(json: any, ignoreDiscriminator: boolean): ContactUpdate {
    if (json == null) {
        return json;
    }
    return {
        
        'firstName': json['first_name'] == null ? undefined : json['first_name'],
        'lastName': json['last_name'] == null ? undefined : json['last_name'],
        'email': json['email'] == null ? undefined : json['email'],
        'phone': json['phone'] == null ? undefined : json['phone'],
        'jobTitle': json['job_title'] == null ? undefined : json['job_title'],
        'companyId': json['company_id'] == null ? undefined : json['company_id'],
    };
}

  export function ContactUpdateToJSON(json: any): ContactUpdate {
      return ContactUpdateToJSONTyped(json, false);
  }

  export function ContactUpdateToJSONTyped(value?: ContactUpdate | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'first_name': value['firstName'],
        'last_name': value['lastName'],
        'email': value['email'],
        'phone': value['phone'],
        'job_title': value['jobTitle'],
        'company_id': value['companyId'],
    };
}

