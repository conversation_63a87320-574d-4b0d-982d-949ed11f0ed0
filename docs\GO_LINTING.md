# Go Linting System

This document describes the Go linting system implemented for the mono repository.

## Overview

The Go linting system uses `golangci-lint` to provide comprehensive static analysis for all Go code in the repository. It includes:

- **golangci-lint configuration** (`.golangci.yml`)
- **Bazel integration** for build system integration
- **Shell script** for direct execution
- **Comprehensive linting rules** covering code quality, security, and style

## Quick Start

### Run linting directly
```bash
./scripts/lint.sh
```

### Run linting via Bazel
```bash
bazel run //scripts:lint
```

### Run linting tests
```bash
# Note: This requires golangci-lint to be pre-installed
# The test will fail if golangci-lint is not available (expected behavior)
bazel test //scripts:go_lint_test
```

## Configuration

The linting configuration is defined in `.golangci.yml` with the following key features:

### Enabled Linters
- **Code Quality**: `gocyclo`, `gocognit`, `funlen`, `dupl`
- **Security**: `gosec`
- **Style**: `gofmt`, `gofumpt`, `goimports`, `revive`
- **Bugs**: `govet`, `staticcheck`, `ineffassign`
- **Performance**: `prealloc`, `unconvert`
- **Maintainability**: `misspell`, `whitespace`, `nestif`

### Exclusions
- **Generated code**: `generated/go/` (auto-generated API clients)
- **Vendor directory**: `vendor/`
- **Test files**: Relaxed rules for `*_test.go` files
- **Type checking**: Temporarily disabled to focus on other issues

### Settings
- **Line length**: 120 characters
- **Cyclomatic complexity**: Maximum 15
- **Function length**: Maximum 80 lines, 50 statements
- **Timeout**: 5 minutes

## Integration

### Bazel Integration
The linting system is integrated with Bazel through:
- `//scripts:lint` - Direct linting execution
- `//scripts:go_lint_test` - Linting as a test target
- `//:lint_tests` - Test suite including linting

### CI/CD Integration
To integrate with CI/CD pipelines:
```bash
# In your CI script
bazel test //scripts:go_lint_test
```

## Project Structure

```
├── .golangci.yml              # Main configuration file
├── scripts/
│   ├── lint.sh                # Direct execution script
│   └── BUILD.bazel            # Bazel targets
├── tools/
│   ├── go_lint.bzl            # Custom Bazel rules
│   ├── run_lint_test.sh       # Test runner script
│   └── BUILD.bazel            # Tools configuration
└── BUILD.bazel                # Root configuration with lint targets
```

## Covered Packages

The linting system covers the following Go packages:
- `services/orbit/auth/...` - Authentication service
- `services/orbit/crm_backend/...` - CRM backend service
- `services/orbit/gateway/...` - API gateway service
- `shared/go/...` - Shared Go libraries
- `services/examples/go/...` - Example Go service

## Customization

### Adding New Linters
To add new linters, update `.golangci.yml`:
```yaml
linters:
  enable:
    - <new-linter-name>
```

### Excluding Specific Issues
To exclude specific issues:
```yaml
issues:
  exclude-rules:
    - path: specific_file.go
      linters:
        - specific-linter
```

### Package-Specific Configuration
For package-specific rules, use path-based exclusions:
```yaml
issues:
  exclude-rules:
    - path: platform/specific_package/.*
      linters:
        - specific-linter
```

## Next Steps

1. **Fix Type Issues**: Re-enable `typecheck` linter after fixing import issues
2. **Add More Rules**: Consider adding more strict linters as code quality improves
3. **Pre-commit Integration**: Add linting to pre-commit hooks
4. **IDE Integration**: Configure IDE plugins to use the same configuration

## Troubleshooting

### golangci-lint not found
The script automatically installs golangci-lint if not present:
```bash
curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $(go env GOPATH)/bin v1.57.1
```

### Configuration warnings
Update deprecated configuration options in `.golangci.yml` as needed.

### Performance issues
Adjust timeout in `.golangci.yml` if linting takes too long:
```yaml
run:
  timeout: 10m  # Increase as needed
```