# AI Agent Platform - Backend Makefile

.PHONY: help install test test-unit test-integration test-coverage clean lint format type-check run dev docker-build docker-run

# Default target
help:
	@echo "AI Agent Platform - Backend Development Commands"
	@echo ""
	@echo "Setup Commands:"
	@echo "  install          Install Python dependencies"
	@echo "  install-dev      Install development dependencies"
	@echo ""
	@echo "Testing Commands:"
	@echo "  test             Run all tests"
	@echo "  test-unit        Run unit tests only"
	@echo "  test-integration Run integration tests only"
	@echo "  test-coverage    Run tests with coverage report"
	@echo "  test-watch       Run tests in watch mode"
	@echo ""
	@echo "Quality Commands:"
	@echo "  lint             Run linting checks"
	@echo "  format           Format code with black and isort"
	@echo "  type-check       Run mypy type checking"
	@echo "  security         Run security checks with bandit"
	@echo ""
	@echo "Development Commands:"
	@echo "  run              Run the application"
	@echo "  dev              Run in development mode with hot reload"
	@echo "  shell            Start IPython shell with app context"
	@echo ""
	@echo "Database Commands:"
	@echo "  db-upgrade       Run database migrations"
	@echo "  db-downgrade     Rollback database migration"
	@echo "  db-reset         Reset database (drop and recreate)"
	@echo ""
	@echo "Docker Commands:"
	@echo "  docker-build     Build Docker image"
	@echo "  docker-run       Run application in Docker"
	@echo "  docker-test      Run tests in Docker"
	@echo ""
	@echo "Utility Commands:"
	@echo "  clean            Clean up generated files"
	@echo "  deps-update      Update dependencies"

# Installation
install:
	pip install -r requirements.txt

install-dev:
	pip install -r requirements-dev.txt

# Testing
test:
	pytest

test-unit:
	pytest -m "not integration" -v

test-integration:
	pytest -m integration -v

test-coverage:
	pytest --cov=src --cov-report=html --cov-report=term-missing

test-watch:
	pytest-watch -- --tb=short

test-specific:
	pytest $(TEST) -v

# Quality checks
lint:
	flake8 src tests
	pylint src

format:
	black src tests
	isort src tests

format-check:
	black --check src tests
	isort --check-only src tests

type-check:
	mypy src

security:
	bandit -r src -f json -o security-report.json || true
	bandit -r src

# Development
run:
	python3 -m uvicorn src.main:app --host 0.0.0.0 --port 8000

dev:
	python3 -m uvicorn src.main:app --host 0.0.0.0 --port 8000 --reload

shell:
	python -c "from src.main import app; import IPython; IPython.embed()"

# Database
db-upgrade:
	alembic upgrade head

db-downgrade:
	alembic downgrade -1

db-reset:
	alembic downgrade base
	alembic upgrade head

db-migrate:
	alembic revision --autogenerate -m "$(MSG)"

# Docker
docker-build:
	docker build -t ai-agent-platform-backend .

docker-run:
	docker run -p 8000:8000 ai-agent-platform-backend

docker-test:
	docker run ai-agent-platform-backend pytest

docker-dev:
	docker-compose up -d postgres redis kafka
	sleep 10
	uvicorn src.main:app --host 0.0.0.0 --port 8000 --reload

# Utility
clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf build/
	rm -rf dist/
	rm -rf htmlcov/
	rm -rf .coverage
	rm -rf coverage.xml
	rm -rf .pytest_cache/
	rm -rf .mypy_cache/

deps-update:
	pip-compile requirements.in
	pip-compile requirements-dev.in

# CI/CD helpers
ci-install:
	pip install --upgrade pip
	pip install -r requirements-dev.txt

ci-test:
	pytest --junitxml=test-results.xml --cov=src --cov-report=xml

ci-quality:
	black --check src tests
	isort --check-only src tests
	flake8 src tests
	mypy src
	bandit -r src

# Production helpers
prod-build:
	docker build -f Dockerfile.prod -t ai-agent-platform-backend:prod .

prod-deploy:
	# Add deployment commands here
	@echo "Deployment commands to be implemented"

# Monitoring
logs:
	docker-compose logs -f backend

status:
	docker-compose ps

# Development setup
setup-dev:
	python -m venv venv
	@echo "Virtual environment created. Activate with:"
	@echo "source venv/bin/activate  # Linux/Mac"
	@echo "venv\\Scripts\\activate     # Windows"
	@echo "Then run: make install-dev"

# Environment variables
env-check:
	@echo "Checking environment variables..."
	@python -c "from src.config.settings import settings; print('✅ Configuration loaded successfully')" || echo "❌ Configuration error"