# Flyway Configuration
flyway.url=**************************************
flyway.user=postgres
flyway.password=postgres
flyway.schemas=public
flyway.locations=filesystem:./migrations
flyway.baselineOnMigrate=true
flyway.baselineVersion=0
flyway.validateOnMigrate=true
flyway.cleanDisabled=true

# For GCP Cloud SQL connection (override these in production)
# flyway.url=*****************************************************************************************************************************************