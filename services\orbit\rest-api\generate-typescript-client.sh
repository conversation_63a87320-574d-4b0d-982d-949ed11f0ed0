#!/bin/bash
# Generate TypeScript client from OpenAPI spec

set -e

SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"
WORKSPACE_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
OPENAPI_SPEC="$SCRIPT_DIR/openapi.yaml"
OUTPUT_DIR="$WORKSPACE_ROOT/services/orbit/web/src/api-client"
JAR_FILE="$WORKSPACE_ROOT/services/orbit/tools/openapi-generator-cli.jar"

# Ensure the JAR exists
if [ ! -f "$JAR_FILE" ]; then
    echo "OpenAPI Generator JAR not found. Downloading..."
    "$WORKSPACE_ROOT/services/orbit/tools/download-openapi-generator.sh"
fi

# Create output directory
mkdir -p "$OUTPUT_DIR"

# Remove existing generated files
rm -rf "$OUTPUT_DIR"/*

echo "Generating TypeScript API client..."
echo "Input spec: $OPENAPI_SPEC"
echo "Output dir: $OUTPUT_DIR"

# Generate the TypeScript client
java -jar "$JAR_FILE" generate \
    -i "$OPENAPI_SPEC" \
    -g typescript-fetch \
    -o "$OUTPUT_DIR" \
    --additional-properties=typescriptThreePlus=true,supportsES6=true,npmName=@crm/api-client,npmVersion=1.0.0,stringEnums=true

echo "TypeScript API client generated successfully in $OUTPUT_DIR"
echo "Files generated:"
find "$OUTPUT_DIR" -name "*.ts" | head -10