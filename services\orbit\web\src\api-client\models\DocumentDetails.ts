/* tslint:disable */
/* eslint-disable */
/**
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface DocumentDetails
 */
export interface DocumentDetails {
    /**
     * 
     * @type {string}
     * @memberof DocumentDetails
     */
    id?: string;
    /**
     * 
     * @type {string}
     * @memberof DocumentDetails
     */
    content?: string;
    /**
     * 
     * @type {object}
     * @memberof DocumentDetails
     */
    metadata?: object;
    /**
     * 
     * @type {Array<string>}
     * @memberof DocumentDetails
     */
    filterTags?: Array<string>;
    /**
     * 
     * @type {string}
     * @memberof DocumentDetails
     */
    sourceId?: string;
    /**
     * 
     * @type {Date}
     * @memberof DocumentDetails
     */
    createdAt?: Date;
    /**
     * 
     * @type {Date}
     * @memberof DocumentDetails
     */
    updatedAt?: Date;
    /**
     * 
     * @type {Array<number>}
     * @memberof DocumentDetails
     */
    vectorEmbeddings?: Array<number>;
    /**
     * 
     * @type {string}
     * @memberof DocumentDetails
     */
    processingStatus?: DocumentDetailsProcessingStatusEnum;
}

/**
* @export
* @enum {string}
*/
export enum DocumentDetailsProcessingStatusEnum {
    Pending = 'pending',
    Processing = 'processing',
    Completed = 'completed',
    Failed = 'failed'
}


/**
 * Check if a given object implements the DocumentDetails interface.
 */
export function instanceOfDocumentDetails(value: object): value is DocumentDetails {
    return true;
}

export function DocumentDetailsFromJSON(json: any): DocumentDetails {
    return DocumentDetailsFromJSONTyped(json, false);
}

export function DocumentDetailsFromJSONTyped(json: any, ignoreDiscriminator: boolean): DocumentDetails {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'] == null ? undefined : json['id'],
        'content': json['content'] == null ? undefined : json['content'],
        'metadata': json['metadata'] == null ? undefined : json['metadata'],
        'filterTags': json['filter_tags'] == null ? undefined : json['filter_tags'],
        'sourceId': json['source_id'] == null ? undefined : json['source_id'],
        'createdAt': json['created_at'] == null ? undefined : (new Date(json['created_at'])),
        'updatedAt': json['updated_at'] == null ? undefined : (new Date(json['updated_at'])),
        'vectorEmbeddings': json['vector_embeddings'] == null ? undefined : json['vector_embeddings'],
        'processingStatus': json['processing_status'] == null ? undefined : json['processing_status'],
    };
}

  export function DocumentDetailsToJSON(json: any): DocumentDetails {
      return DocumentDetailsToJSONTyped(json, false);
  }

  export function DocumentDetailsToJSONTyped(value?: DocumentDetails | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'content': value['content'],
        'metadata': value['metadata'],
        'filter_tags': value['filterTags'],
        'source_id': value['sourceId'],
        'created_at': value['createdAt'] == null ? undefined : ((value['createdAt']).toISOString()),
        'updated_at': value['updatedAt'] == null ? undefined : ((value['updatedAt']).toISOString()),
        'vector_embeddings': value['vectorEmbeddings'],
        'processing_status': value['processingStatus'],
    };
}

