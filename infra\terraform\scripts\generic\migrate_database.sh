#!/bin/bash
# Generic database migration script
# Runs database migrations for any environment

# Source common utilities
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/../common/utils.sh"
source "$SCRIPT_DIR/../common/environment.sh"

# Script configuration
SCRIPT_NAME="migrate_database.sh"
SCRIPT_DESCRIPTION="Run database migrations for specified environment"

# Required tools
REQUIRED_TOOLS=("terraform" "gcloud" "psql" "bazel")

# Migration function
migrate_database() {
    local env="$1"
    
    print_step "Step 1: Environment setup and validation"
    if ! setup_environment "$env"; then
        return 1
    fi
    
    print_environment_summary "$env"
    production_safety_check
    
    print_step "Step 2: Get database connection details"
    cd "$TERRAFORM_DIR"
    
    # Get database details from Terraform outputs
    local database_ip
    local database_name
    local database_user
    
    if ! database_ip=$(terraform output -raw database_private_ip 2>/dev/null); then
        print_error "Could not get database IP from Terraform outputs"
        return 1
    fi
    
    if ! database_name=$(terraform output -raw database_name 2>/dev/null); then
        print_error "Could not get database name from Terraform outputs"
        return 1
    fi
    
    if ! database_user=$(terraform output -raw database_user 2>/dev/null); then
        print_error "Could not get database user from Terraform outputs"
        return 1
    fi
    
    print_info "Database connection details:"
    echo "  🏠 Host: $database_ip"
    echo "  🗄️  Database: $database_name"
    echo "  👤 User: $database_user"
    
    print_step "Step 3: Get database password from Secret Manager"
    local database_password
    if ! database_password=$(gcloud secrets versions access latest --secret="$DB_PASSWORD_SECRET" --project="$PROJECT_ID" 2>/dev/null); then
        print_error "Could not retrieve database password from Secret Manager"
        print_info "Secret: $DB_PASSWORD_SECRET"
        return 1
    fi
    
    print_success "Database password retrieved from Secret Manager"
    
    print_step "Step 4: Test database connection"
    
    # URL encode the password for connection string
    local encoded_password
    encoded_password=$(url_encode "$database_password")
    
    # Create connection string
    local connection_string="***************************************************************/$database_name"
    
    print_info "Testing database connection..."
    if [[ "$DRY_RUN" == "true" ]]; then
        print_info "[DRY RUN] Would test connection to: $database_ip:5432"
    else
        # Test connection with a simple query
        if ! echo "SELECT 1;" | psql "$connection_string" -t > /dev/null 2>&1; then
            print_error "Database connection test failed"
            print_info "Connection string: *************************************************/$database_name"
            return 1
        fi
    fi
    
    print_success "Database connection test passed"
    
    print_step "Step 5: Check for pending migrations"
    
    # Go back to workspace root for migration commands
    cd - > /dev/null
    
    # Check migration status
    print_info "Checking migration status..."
    
    # Set environment variables for migration
    export DATABASE_URL="$connection_string"
    export AUTO_MIGRATE_DEV="true"  # Allow migrations for dev
    
    # Use bazel to run migrations (generic target)
    local migration_target="//services/orbit/db:migrate_gcp_simple"
    
    print_step "Step 6: Run database migrations"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_info "[DRY RUN] Would run migrations with: bazel run $migration_target"
        return 0
    fi
    
    # Production safety check for migrations
    if is_production; then
        print_warning "⚠️  PRODUCTION DATABASE MIGRATION ⚠️"
        echo ""
        print_info "You are about to run database migrations on PRODUCTION."
        print_info "This will modify the production database schema."
        echo ""
        
        if [[ "$FORCE" != "true" ]]; then
            if ! confirm "Are you sure you want to run production migrations?" "n"; then
                print_info "Migration cancelled"
                return 0
            fi
        fi
    fi
    
    print_info "Running database migrations..."
    if ! execute bazel run "$migration_target" -- "$env"; then
        print_error "Database migration failed"
        return 1
    fi
    
    print_success "Database migrations completed"
    
    print_step "Step 7: Verify migration results"
    
    # Check if migrations were applied successfully
    print_info "Verifying migration results..."
    
    # Query the migration table to see applied migrations
    if ! echo "SELECT version, description, applied_at FROM flyway_schema_history ORDER BY installed_rank DESC LIMIT 5;" | psql "$connection_string" -t 2>/dev/null; then
        print_warning "Could not query migration history (this is normal for new databases)"
    else
        print_success "Migration history retrieved successfully"
    fi
    
    print_success "Database migration completed successfully!"
    
    return 0
}

# Test database connectivity
test_database_connection() {
    local env="$1"
    
    print_step "Testing database connectivity for: $env"
    
    if ! setup_environment "$env"; then
        return 1
    fi
    
    cd "$TERRAFORM_DIR"
    
    # Get database details
    local database_ip
    local database_name
    local database_user
    
    database_ip=$(terraform output -raw database_private_ip 2>/dev/null)
    database_name=$(terraform output -raw database_name 2>/dev/null)
    database_user=$(terraform output -raw database_user 2>/dev/null)
    
    if [[ -z "$database_ip" || -z "$database_name" || -z "$database_user" ]]; then
        print_error "Could not get database connection details from Terraform"
        return 1
    fi
    
    # Get password
    local database_password
    if ! database_password=$(gcloud secrets versions access latest --secret="$DB_PASSWORD_SECRET" --project="$PROJECT_ID" 2>/dev/null); then
        print_error "Could not retrieve database password"
        return 1
    fi
    
    # Test connection
    local encoded_password
    encoded_password=$(url_encode "$database_password")
    local connection_string="***************************************************************/$database_name"
    
    print_info "Testing connection to: $database_ip:5432"
    
    if echo "SELECT current_database(), current_user, version();" | psql "$connection_string" -t; then
        print_success "Database connection successful"
        return 0
    else
        print_error "Database connection failed"
        return 1
    fi
}

# Main script execution
main() {
    setup_error_handling
    
    # Parse command line arguments
    parse_args "$SCRIPT_NAME" "$@"
    
    # Print banner
    print_banner "$SCRIPT_NAME" "$SCRIPT_DESCRIPTION"
    
    # Check required tools
    if ! check_required_tools "${REQUIRED_TOOLS[@]}"; then
        exit 1
    fi
    
    # Change to workspace directory
    if ! change_to_workspace; then
        exit 1
    fi
    
    # Validate environment
    if ! validate_environment "$ENVIRONMENT"; then
        exit 1
    fi
    
    # Special handling for test command
    if [[ "$1" == "test" ]]; then
        if test_database_connection "$ENVIRONMENT"; then
            print_success "Database connection test passed! 🎉"
            exit 0
        else
            print_error "Database connection test failed!"
            exit 1
        fi
    fi
    
    # Run database migrations
    if migrate_database "$ENVIRONMENT"; then
        print_success "Database migration completed successfully! 🎉"
        exit 0
    else
        print_error "Database migration failed!"
        exit 1
    fi
}

# Show usage if no arguments provided
if [[ $# -eq 0 ]]; then
    print_usage "$SCRIPT_NAME"
    echo ""
    echo "Additional commands:"
    echo "  test ENVIRONMENT    Test database connectivity"
    exit 1
fi

# Execute main function with all arguments
main "$@"