version: '3.8'

services:
  nginx:
    image: nginx:alpine
    container_name: nginx
    restart: {{ docker_restart_policy | default('unless-stopped') }}
    ports:
      - "80:80"
      {% if environment != 'dev' %}
      - "443:443"
      {% endif %}
    volumes:
      - {{ microservices_root }}/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - {{ microservices_root }}/nginx/upstream.conf:/etc/nginx/conf.d/upstream.conf:ro
      {% if environment != 'dev' %}
      - {{ microservices_root }}/nginx/ssl:/etc/nginx/ssl:ro
      {% endif %}
      - nginx-logs:/var/log/nginx
    networks:
      - {{ docker_network_name | default('microservices') }}
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: {{ health_check_interval | default('30s') }}
      timeout: {{ health_check_timeout | default('10s') }}
      retries: {{ health_check_retries | default(3) }}
      start_period: 10s
    deploy:
      resources:
        limits:
          memory: {{ resource_limits.nginx.memory | default('256m') }}
          cpus: "{{ resource_limits.nginx.cpu | default('0.25') }}"
    labels:
      com.microservices.service: "nginx"
      com.microservices.role: "reverse-proxy"
      com.microservices.environment: "{{ environment }}"

volumes:
  nginx-logs:
    driver: local

networks:
  {{ docker_network_name | default('microservices') }}:
    external: true