#!/bin/bash

# Update environment variables on VM using Secret Manager API
# This script can be run manually on the VM to update OAuth environment variables

set -euo pipefail

PROJECT_ID="agent-dev-459718"
PLATFORM_DIR="/home/<USER>/platform"

# Get secrets from Secret Manager using metadata server
get_secret() {
    local secret_name=$1
    local access_token=$(curl -s -H 'Metadata-Flavor: Google' http://metadata.google.internal/computeMetadata/v1/instance/service-accounts/default/token | grep -o '"access_token":"[^"]*' | cut -d'"' -f4)
    local secret_data=$(curl -s -H "Authorization: Bearer $access_token" "https://secretmanager.googleapis.com/v1/projects/$PROJECT_ID/secrets/$secret_name/versions/latest:access" | grep -o '"data":"[^"]*' | cut -d'"' -f4)
    echo "$secret_data" | base64 -d
}

echo "🔧 Updating OAuth environment variables..."

# Get OAuth secrets
GOOGLE_OAUTH_CLIENT_ID=$(get_secret "oauth-client-id")
GOOGLE_OAUTH_CLIENT_SECRET=$(get_secret "oauth-client-secret")
OAUTH_STATE_SECRET=$(get_secret "oauth-state-secret")

echo "📝 Adding OAuth variables to .env file..."

# Add OAuth variables to .env file
cat >> "$PLATFORM_DIR/.env" << EOF

# OAuth Configuration (auto-generated)
GOOGLE_OAUTH_CLIENT_ID=$GOOGLE_OAUTH_CLIENT_ID
GOOGLE_OAUTH_CLIENT_SECRET=$GOOGLE_OAUTH_CLIENT_SECRET
GOOGLE_OAUTH_REDIRECT_URL=https://internal.dev.twodot.ai/auth/callback/google
OAUTH_STATE_SECRET=$OAUTH_STATE_SECRET
EOF

echo "🔄 Restarting Docker containers..."
cd "$PLATFORM_DIR"
docker-compose down
docker-compose up -d

echo "✅ OAuth environment variables updated successfully!"
echo "🔍 Verifying OAuth configuration..."
docker exec platform-auth env | grep -E "(OAUTH|GOOGLE)" || echo "❌ OAuth variables not found in container"