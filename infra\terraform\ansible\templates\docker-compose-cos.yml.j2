version: '3.8'

services:
  gateway:
    image: "{{ services.gateway.image }}"
    ports:
      - "80:{{ services.gateway.port }}"
      - "{{ services.gateway.port }}:{{ services.gateway.port }}"
    environment:
      - LOG_LEVEL={{ services.gateway.environment.LOG_LEVEL | default('info') }}
      - AUTH_SERVICE_URL={{ services.gateway.environment.AUTH_SERVICE_URL }}
      - CRM_BACKEND_URL={{ services.gateway.environment.CRM_BACKEND_URL }}
      - CORS_ALLOWED_ORIGINS={{ services.gateway.environment.CORS_ALLOWED_ORIGINS | default('*') }}
    depends_on:
      - auth
      - crm-backend
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  auth:
    image: "{{ services.auth.image }}"
    ports:
      - "{{ services.auth.port }}:{{ services.auth.port }}"
    environment:
      - LOG_LEVEL={{ services.auth.environment.LOG_LEVEL | default('info') }}
      - JWT_EXPIRY={{ services.auth.environment.JWT_EXPIRY | default('24h') }}
      - SESSION_TIMEOUT={{ services.auth.environment.SESSION_TIMEOUT | default('86400') }}
      - DATABASE_URL=postgresql://{{ database_user | default('postgres') }}:{{ database_password | default('password') }}@{{ database_host | default('localhost') }}:5432/{{ database_name | default('crm_dev') }}
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  crm-backend:
    image: "{{ services.crm_backend.image }}"
    ports:
      - "{{ services.crm_backend.port }}:{{ services.crm_backend.port }}"
    environment:
      - LOG_LEVEL={{ services.crm_backend.environment.LOG_LEVEL | default('info') }}
      - ENABLE_SWAGGER={{ services.crm_backend.environment.ENABLE_SWAGGER | default('true') }}
      - MAX_PAGE_SIZE={{ services.crm_backend.environment.MAX_PAGE_SIZE | default('100') }}
      - DEFAULT_PAGE_SIZE={{ services.crm_backend.environment.DEFAULT_PAGE_SIZE | default('20') }}
      - DATABASE_URL=postgresql://{{ database_user | default('postgres') }}:{{ database_password | default('password') }}@{{ database_host | default('localhost') }}:5432/{{ database_name | default('crm_dev') }}
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

networks:
  default:
    name: platform-network