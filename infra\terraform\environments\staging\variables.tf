# Staging Environment Variables

variable "project_id" {
  description = "GCP Project ID"
  type        = string
  default     = "agent-dev-459718"  # Using dev project for now
  
  validation {
    condition     = can(regex("^[a-z][a-z0-9-]{4,28}[a-z0-9]$", var.project_id))
    error_message = "Project ID must be 6-30 characters, start with a letter, and contain only lowercase letters, numbers, and hyphens."
  }
}

variable "project_name" {
  description = "Name of the project (used for resource naming)"
  type        = string
  default     = "platform"
  
  validation {
    condition     = can(regex("^[a-z][a-z0-9-]*[a-z0-9]$", var.project_name))
    error_message = "Project name must start with a letter, contain only lowercase letters, numbers, and hyphens."
  }
}

variable "region" {
  description = "GCP region for resources"
  type        = string
  default     = "australia-southeast1"
  
  validation {
    condition = contains([
      "australia-southeast1", "australia-southeast2",
      "us-central1", "us-east1", "us-west1", "us-west2",
      "europe-west1", "europe-west2", "europe-west3",
      "asia-east1", "asia-southeast1", "asia-northeast1"
    ], var.region)
    error_message = "Region must be a valid GCP region."
  }
}

variable "zone" {
  description = "GCP zone for resources"
  type        = string
  default     = "australia-southeast1-a"
  
  validation {
    condition     = can(regex("^[a-z]+-[a-z]+[0-9]+-[a-z]$", var.zone))
    error_message = "Zone must be a valid GCP zone format (e.g., australia-southeast1-a)."
  }
}

# GitHub configuration for CI/CD
variable "github_repo_owner" {
  description = "GitHub repository owner (organization or username)"
  type        = string
  default     = ""
}

variable "github_repo_name" {
  description = "GitHub repository name"
  type        = string
  default     = ""
}

# SSL/TLS configuration
variable "ssl_domains" {
  description = "List of domains for SSL certificate"
  type        = list(string)
  default     = []  # Optional for staging
}

# Security configuration
variable "admin_ip_ranges" {
  description = "IP ranges allowed for administrative access (SSH, etc.)"
  type        = list(string)
  default     = ["0.0.0.0/0"]  # More relaxed for staging
  
  validation {
    condition = alltrue([
      for cidr in var.admin_ip_ranges : can(cidrhost(cidr, 0))
    ])
    error_message = "All admin IP ranges must be valid IPv4 CIDR blocks."
  }
}

# Cloud Armor security settings
variable "rate_limit_requests_per_minute" {
  description = "Rate limit requests per minute (0 to disable)"
  type        = number
  default     = 500  # Lower limit for staging
  
  validation {
    condition     = var.rate_limit_requests_per_minute >= 0 && var.rate_limit_requests_per_minute <= 10000
    error_message = "Rate limit must be between 0 and 10000 requests per minute."
  }
}

variable "blocked_regions" {
  description = "List of region codes to block"
  type        = list(string)
  default     = []
}

variable "blocked_ip_ranges" {
  description = "List of IP ranges to block"
  type        = list(string)
  default     = []
  
  validation {
    condition = alltrue([
      for cidr in var.blocked_ip_ranges : can(cidrhost(cidr, 0))
    ])
    error_message = "All blocked IP ranges must be valid IPv4 CIDR blocks."
  }
}

# Database migration settings
variable "enable_scheduled_migrations" {
  description = "Enable scheduled automated migrations"
  type        = bool
  default     = true  # Allow automated migrations in staging for testing
}

variable "migration_schedule" {
  description = "Cron schedule for automated migrations"
  type        = string
  default     = "0 2 * * 0"  # Sundays at 2 AM AEST
  
  validation {
    condition     = can(regex("^[0-9*,/-]+ [0-9*,/-]+ [0-9*,/-]+ [0-9*,/-]+ [0-9*,/-]+$", var.migration_schedule))
    error_message = "Migration schedule must be a valid cron expression."
  }
}

variable "migration_timezone" {
  description = "Timezone for scheduled migrations"
  type        = string
  default     = "Australia/Sydney"
}

# Notification settings
variable "enable_migration_notifications" {
  description = "Enable migration notifications"
  type        = bool
  default     = true
}

variable "slack_webhook_url" {
  description = "Slack webhook URL for notifications"
  type        = string
  default     = ""
  sensitive   = true
}

variable "notification_email" {
  description = "Email address for notifications"
  type        = string
  default     = ""
  
  validation {
    condition = var.notification_email == "" || can(regex("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", var.notification_email))
    error_message = "Notification email must be a valid email address or empty."
  }
}

# Staging-specific settings
variable "enable_debug_mode" {
  description = "Enable debug mode features for staging"
  type        = bool
  default     = true
}

variable "enable_external_access" {
  description = "Enable external IP access to instances"
  type        = bool
  default     = false  # Keep private in staging
}

# Resource sizing for staging environment
variable "min_instance_count" {
  description = "Minimum number of instances"
  type        = number
  default     = 1
  
  validation {
    condition     = var.min_instance_count >= 1 && var.min_instance_count <= 10
    error_message = "Minimum instance count must be between 1 and 10."
  }
}

variable "max_instance_count" {
  description = "Maximum number of instances"
  type        = number
  default     = 3
  
  validation {
    condition     = var.max_instance_count >= 1 && var.max_instance_count <= 10
    error_message = "Maximum instance count must be between 1 and 10."
  }
}

# Staging feature flags
variable "enable_monitoring" {
  description = "Enable monitoring and alerting"
  type        = bool
  default     = true  # Enable monitoring in staging
}

variable "enable_backup_automation" {
  description = "Enable automated backups"
  type        = bool
  default     = true  # Enable backups in staging
}

variable "auto_scaling_enabled" {
  description = "Enable auto-scaling for compute resources"
  type        = bool
  default     = false  # Keep simple in staging
}