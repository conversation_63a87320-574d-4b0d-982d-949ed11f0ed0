---
- name: Deploy Microservices to GCP VM
  hosts: microservices
  become: yes
  gather_facts: yes
  
  vars:
    project_id: "{{ gcp_project_id }}"
    deployment_environment: "dev"
    deployment_color: "{{ 'green' if current_color | default('blue') == 'blue' else 'blue' }}"
    
  pre_tasks:
    - name: Load environment-specific variables
      include_vars: "../vars/{{ deployment_environment }}.yml"
      
    - name: Ensure required variables are defined
      assert:
        that:
          - project_id is defined
          - services is defined
        fail_msg: "Required variables are not defined"
        
  roles:
    - role: docker
      tags: ['docker', 'infrastructure']
      
    - role: nginx
      tags: ['nginx', 'infrastructure']
      
    - role: microservices
      tags: ['services', 'deployment']
      
    - role: monitoring
      tags: ['monitoring', 'health']
      
  post_tasks:
    - name: Verify deployment health
      uri:
        url: "http://localhost:{{ item.value.port }}{{ item.value.health_check.path }}"
        method: GET
        status_code: 200
      retries: 30
      delay: 10
      loop: "{{ services | dict2items }}"
      when: item.value.health_check is defined
      tags: ['health-check']
      
    - name: Save deployment metadata
      copy:
        content: |
          deployment_time: {{ ansible_date_time.iso8601 }}
          deployment_color: {{ deployment_color }}
          services:
          {% for service, config in services.items() %}
            {{ service }}: {{ config.image }}
          {% endfor %}
        dest: /var/lib/microservices/current-deployment.yml
      tags: ['metadata']