---
# Production environment configuration
environment: prod
deployment_environment: prod

# Registry configuration (matches Terraform output)
gcp_region: australia-southeast1
artifact_registry: platform-docker

# Service configurations
services:
  gateway:
    image: "australia-southeast1-docker.pkg.dev/twodot-agent-prod/platform-docker/gateway:latest"
    port: 8085
    environment:
      LOG_LEVEL: "info"
      AUTH_SERVICE_URL: "http://auth:8004"
      CRM_BACKEND_URL: "http://crm-backend:8003"
      CORS_ALLOWED_ORIGINS: "https://internal.twodot.ai,https://api.twodot.ai"
    health_check:
      path: "/health"
      interval: 30s
      timeout: 10s
      retries: 3
    depends_on:
      - auth
      
  auth:
    image: "australia-southeast1-docker.pkg.dev/twodot-agent-prod/platform-docker/auth:latest"
    port: 8004
    environment:
      LOG_LEVEL: "info"
      JWT_EXPIRY: "24h"
      SESSION_TIMEOUT: "86400"
      DATABASE_URL: "postgres://platform_prod_user:{{ database_password_encoded }}@10.244.0.2:5432/platform_prod_db?sslmode=require"
    health_check:
      path: "/health"
      interval: 30s
      timeout: 10s
      retries: 3
      
  crm_backend:
    image: "australia-southeast1-docker.pkg.dev/twodot-agent-prod/platform-docker/crm-backend:latest"
    port: 8003
    environment:
      LOG_LEVEL: "info"
      ENABLE_SWAGGER: "false"
      MAX_PAGE_SIZE: "100"
      DEFAULT_PAGE_SIZE: "20"
      DATABASE_URL: "postgres://platform_prod_user:{{ database_password_encoded }}@10.244.0.2:5432/platform_prod_db?sslmode=require"
    health_check:
      path: "/api/v1/health"
      interval: 30s
      timeout: 10s
      retries: 3
    depends_on:
      - auth

# Feature flags for production
feature_flags:
  enable_debug_endpoints: false
  enable_swagger_ui: false
  enable_profiling: false
  enable_detailed_errors: false

# Production-specific overrides
cors_enabled: true
cors_allow_all_origins: false
rate_limiting_enabled: true
ssl_enabled: true

# Security configurations
security:
  enable_csrf_protection: true
  enable_rate_limiting: true
  max_requests_per_minute: 100
  
# Monitoring configurations
monitoring:
  enable_metrics: true
  enable_tracing: true
  log_level: "info"