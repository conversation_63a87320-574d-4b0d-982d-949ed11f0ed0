#!/bin/bash
set -euo pipefail

# Terraform validation script for Bazel integration

echo "Running Terraform validation..."

# Find all Terraform directories
terraform_dirs=$(find . -name "*.tf" -exec dirname {} \; | sort -u)

exit_code=0

for dir in $terraform_dirs; do
    echo "Validating Terraform configuration in: $dir"
    
    # Change to directory
    cd "$dir"
    
    # Initialize Terraform (backend=false to avoid state initialization)
    if ! terraform init -backend=false -input=false; then
        echo "ERROR: Failed to initialize Terraform in $dir"
        exit_code=1
        cd - > /dev/null
        continue
    fi
    
    # Validate configuration
    if ! terraform validate; then
        echo "ERROR: Terraform validation failed in $dir"
        exit_code=1
    else
        echo "SUCCESS: Terraform validation passed in $dir"
    fi
    
    # Return to original directory
    cd - > /dev/null
done

if [ $exit_code -eq 0 ]; then
    echo "All Terraform configurations are valid!"
else
    echo "Some Terraform configurations failed validation!"
fi

exit $exit_code