"""Auto-deployment management API endpoints."""

from fastapi import APIRout<PERSON>, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime
import asyncio

from database.connection import get_db
from auth.dependencies import get_current_user
from services.auto_deployment import AutoDeploymentSystem, DeploymentConfig, DeploymentStrategy, DeploymentStatus
from utils.exceptions import DeploymentError
from utils.logging import get_logger

logger = get_logger(__name__)
router = APIRouter(prefix="/deployment", tags=["deployment"])

# Initialize auto-deployment system
deployment_system = AutoDeploymentSystem()

# Pydantic models
class DeploymentRequest(BaseModel):
    agent_definition: Dict[str, Any] = Field(..., description="Agent definition to deploy")
    strategy: DeploymentStrategy = Field(DeploymentStrategy.ROLLING, description="Deployment strategy")
    replicas: int = Field(1, ge=1, le=10, description="Number of replicas")
    resources: Optional[Dict[str, Any]] = Field(None, description="Resource requirements")
    environment_variables: Optional[Dict[str, str]] = Field(None, description="Environment variables")
    health_check_path: str = Field("/health", description="Health check endpoint path")
    port_range_start: Optional[int] = Field(None, ge=30000, le=32000, description="Port range start")
    port_range_end: Optional[int] = Field(None, ge=30000, le=32000, description="Port range end")

class DeploymentResponse(BaseModel):
    deployment_id: str
    status: str
    port: Optional[int]
    endpoints: List[str]
    created_at: str

class DeploymentStatusResponse(BaseModel):
    deployment_id: str
    status: str
    stage: str
    progress: float
    logs: List[str]
    health_status: str
    endpoints: List[str]
    metrics: Dict[str, Any]
    updated_at: str

class DeploymentListResponse(BaseModel):
    deployments: List[DeploymentStatusResponse]
    total: int
    active: int
    failed: int

@router.post("/deploy", response_model=DeploymentResponse)
async def deploy_agent(
    request: DeploymentRequest,
    background_tasks: BackgroundTasks,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Deploy an agent with specified configuration."""
    
    try:
        # Create deployment configuration
        config = DeploymentConfig(
            strategy=request.strategy,
            replicas=request.replicas,
            resources=request.resources or {},
            environment_variables=request.environment_variables or {},
            health_check_path=request.health_check_path,
            port_range_start=request.port_range_start,
            port_range_end=request.port_range_end
        )
        
        # Start deployment
        deployment_status = await deployment_system.deploy_agent(
            request.agent_definition, 
            config
        )
        
        logger.info(
            f"Deployment initiated",
            deployment_id=deployment_status.deployment_id,
            user_id=current_user.id,
            strategy=request.strategy.value
        )
        
        return DeploymentResponse(
            deployment_id=deployment_status.deployment_id,
            status=deployment_status.status,
            port=deployment_status.port,
            endpoints=deployment_status.endpoints,
            created_at=deployment_status.created_at.isoformat()
        )
        
    except DeploymentError as e:
        logger.error(f"Deployment failed: {e.details}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected deployment error: {str(e)}")
        raise HTTPException(status_code=500, detail="Deployment failed")

@router.get("/status/{deployment_id}", response_model=DeploymentStatusResponse)
async def get_deployment_status(
    deployment_id: str,
    current_user = Depends(get_current_user)
):
    """Get detailed status of a specific deployment."""
    
    try:
        status = await deployment_system.get_deployment_status(deployment_id)
        
        if not status:
            raise HTTPException(status_code=404, detail="Deployment not found")
        
        return DeploymentStatusResponse(
            deployment_id=status.deployment_id,
            status=status.status,
            stage=status.stage,
            progress=status.progress,
            logs=status.logs[-50:],  # Return last 50 log entries
            health_status=status.health_status,
            endpoints=status.endpoints,
            metrics=status.metrics,
            updated_at=status.updated_at.isoformat()
        )
        
    except Exception as e:
        logger.error(f"Error fetching deployment status: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to fetch deployment status")

@router.get("/list", response_model=DeploymentListResponse)
async def list_deployments(
    status: Optional[str] = None,
    limit: int = 50,
    offset: int = 0,
    current_user = Depends(get_current_user)
):
    """List deployments with optional filtering."""
    
    try:
        deployments = await deployment_system.list_deployments(
            status_filter=status,
            limit=limit,
            offset=offset
        )
        
        # Calculate summary statistics
        active_count = sum(1 for d in deployments if d.status == "running")
        failed_count = sum(1 for d in deployments if d.status == "failed")
        
        deployment_responses = [
            DeploymentStatusResponse(
                deployment_id=d.deployment_id,
                status=d.status,
                stage=d.stage,
                progress=d.progress,
                logs=d.logs[-10:],  # Return last 10 log entries for list view
                health_status=d.health_status,
                endpoints=d.endpoints,
                metrics=d.metrics,
                updated_at=d.updated_at.isoformat()
            )
            for d in deployments
        ]
        
        return DeploymentListResponse(
            deployments=deployment_responses,
            total=len(deployments),
            active=active_count,
            failed=failed_count
        )
        
    except Exception as e:
        logger.error(f"Error listing deployments: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to list deployments")

@router.post("/rollback/{deployment_id}")
async def rollback_deployment(
    deployment_id: str,
    current_user = Depends(get_current_user)
):
    """Rollback a deployment to the previous version."""
    
    try:
        result = await deployment_system.rollback_deployment(deployment_id)
        
        logger.info(
            f"Deployment rollback initiated",
            deployment_id=deployment_id,
            user_id=current_user.id
        )
        
        return {
            "message": "Rollback initiated successfully",
            "deployment_id": deployment_id,
            "rollback_status": result
        }
        
    except DeploymentError as e:
        logger.error(f"Rollback failed: {e.details}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected rollback error: {str(e)}")
        raise HTTPException(status_code=500, detail="Rollback failed")

@router.delete("/terminate/{deployment_id}")
async def terminate_deployment(
    deployment_id: str,
    force: bool = False,
    current_user = Depends(get_current_user)
):
    """Terminate a deployment."""
    
    try:
        result = await deployment_system.terminate_deployment(deployment_id, force=force)
        
        logger.info(
            f"Deployment termination requested",
            deployment_id=deployment_id,
            force=force,
            user_id=current_user.id
        )
        
        return {
            "message": "Deployment terminated successfully",
            "deployment_id": deployment_id,
            "termination_status": result
        }
        
    except DeploymentError as e:
        logger.error(f"Termination failed: {e.details}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected termination error: {str(e)}")
        raise HTTPException(status_code=500, detail="Termination failed")

@router.post("/scale/{deployment_id}")
async def scale_deployment(
    deployment_id: str,
    replicas: int,
    current_user = Depends(get_current_user)
):
    """Scale a deployment to the specified number of replicas."""
    
    try:
        result = await deployment_system.scale_deployment(deployment_id, replicas)
        
        logger.info(
            f"Deployment scaling requested",
            deployment_id=deployment_id,
            replicas=replicas,
            user_id=current_user.id
        )
        
        return {
            "message": "Deployment scaling initiated",
            "deployment_id": deployment_id,
            "target_replicas": replicas,
            "scaling_status": result
        }
        
    except DeploymentError as e:
        logger.error(f"Scaling failed: {e.details}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected scaling error: {str(e)}")
        raise HTTPException(status_code=500, detail="Scaling failed")

@router.get("/logs/{deployment_id}")
async def get_deployment_logs(
    deployment_id: str,
    lines: int = 100,
    follow: bool = False,
    current_user = Depends(get_current_user)
):
    """Get logs for a specific deployment."""
    
    try:
        logs = await deployment_system.get_deployment_logs(
            deployment_id, 
            lines=lines,
            follow=follow
        )
        
        return {
            "deployment_id": deployment_id,
            "logs": logs,
            "lines_returned": len(logs)
        }
        
    except Exception as e:
        logger.error(f"Error fetching deployment logs: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to fetch logs")

@router.get("/metrics/{deployment_id}")
async def get_deployment_metrics(
    deployment_id: str,
    time_range: str = "1h",
    current_user = Depends(get_current_user)
):
    """Get metrics for a specific deployment."""
    
    try:
        metrics = await deployment_system.get_deployment_metrics(
            deployment_id,
            time_range=time_range
        )
        
        return {
            "deployment_id": deployment_id,
            "time_range": time_range,
            "metrics": metrics
        }
        
    except Exception as e:
        logger.error(f"Error fetching deployment metrics: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to fetch metrics")

@router.get("/strategies")
async def get_deployment_strategies():
    """Get available deployment strategies and their descriptions."""
    
    return {
        "strategies": {
            "blue_green": {
                "name": "Blue-Green",
                "description": "Deploy to a separate environment and switch traffic",
                "pros": ["Zero downtime", "Easy rollback", "Full testing before switch"],
                "cons": ["Requires double resources", "Complex setup"]
            },
            "rolling": {
                "name": "Rolling Update",
                "description": "Gradually replace instances with new version",
                "pros": ["Resource efficient", "Gradual rollout", "Default strategy"],
                "cons": ["Mixed versions during deployment", "Longer deployment time"]
            },
            "canary": {
                "name": "Canary",
                "description": "Deploy to subset of instances for testing",
                "pros": ["Risk reduction", "Real traffic testing", "Gradual rollout"],
                "cons": ["Complex traffic routing", "Longer validation process"]
            },
            "recreate": {
                "name": "Recreate",
                "description": "Terminate all instances and create new ones",
                "pros": ["Simple strategy", "Clean state", "Fast for small apps"],
                "cons": ["Downtime during deployment", "All-or-nothing approach"]
            }
        }
    }

@router.get("/health")
async def deployment_service_health():
    """Check health of the deployment service."""
    
    try:
        health_status = await deployment_system.check_service_health()
        
        return {
            "status": "healthy",
            "deployment_system": health_status,
            "active_deployments": await deployment_system.get_active_deployment_count(),
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Deployment service health check failed: {str(e)}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }