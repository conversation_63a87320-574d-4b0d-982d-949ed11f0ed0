---
# Development environment configuration
environment: dev
deployment_environment: dev

# Registry configuration (matches Terraform output)
gcp_region: australia-southeast1
artifact_registry: platform-docker

# Service configurations
services:
  gateway:
    image: "australia-southeast1-docker.pkg.dev/agent-dev-459718/platform-docker/gateway:latest"
    port: 8085
    environment:
      LOG_LEVEL: "debug"
      AUTH_SERVICE_URL: "http://auth:8004"
      CRM_BACKEND_URL: "http://crm-backend:8003"
      CORS_ALLOWED_ORIGINS: "http://localhost:8080,http://localhost:3000"
    health_check:
      path: "/health"
      interval: 30s
      timeout: 10s
      retries: 3
    depends_on:
      - auth
      
  auth:
    image: "australia-southeast1-docker.pkg.dev/agent-dev-459718/platform-docker/auth:latest"
    port: 8004
    environment:
      LOG_LEVEL: "debug"
      JWT_EXPIRY: "24h"
      SESSION_TIMEOUT: "86400"
      DATABASE_URL: "postgres://platform_dev_user:{{ database_password_encoded }}@*********:5432/platform_dev_db?sslmode=disable"
    health_check:
      path: "/health"
      interval: 30s
      timeout: 10s
      retries: 3
      
  crm_backend:
    image: "australia-southeast1-docker.pkg.dev/agent-dev-459718/platform-docker/crm-backend:latest"
    port: 8003
    environment:
      LOG_LEVEL: "info"
      ENABLE_SWAGGER: "true"
      MAX_PAGE_SIZE: "100"
      DEFAULT_PAGE_SIZE: "20"
      DATABASE_URL: "postgres://platform_dev_user:{{ database_password_encoded }}@*********:5432/platform_dev_db?sslmode=disable"
    health_check:
      path: "/api/v1/health"
      interval: 30s
      timeout: 10s
      retries: 3
    depends_on:
      - auth

# Feature flags for development
feature_flags:
  enable_debug_endpoints: true
  enable_swagger_ui: true
  enable_profiling: true
  enable_detailed_errors: true

# Development-specific overrides
cors_enabled: true
cors_allow_all_origins: false
rate_limiting_enabled: false
ssl_enabled: false

# Webhook URL for notifications (optional)
# webhook_url: "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK"