#!/bin/bash
set -e

echo "Loading CRM test data with pre-generated bcrypt hashes..."

# Pre-generated bcrypt hashes (generated with Go bcrypt cost 12)
# TestPassword -> $2a$12$A2j79h6GJErYQgFze/k5x.G01IS90ULphQO4aX4CHGUeL6EcQ/LUG
# password123 -> $2a$12$6m6LQa2eSOM..9lWYR7XDulS0E1t/8aiS9Ra/e87Ci8uCKPOMPMZ6  
# admin123 -> $2a$12$RGg5Iqa83NE61tGdGrEpr.KQLnJBng/JEx1c1bX.hZFrrFw/aCbcu

# Check if postgres container is running
if ! docker ps | grep -q platform_postgres; then
    echo "ERROR: PostgreSQL container is not running!"
    echo "Please run 'docker-compose up -d' (from platform directory) or 'bazel run //platform/db:start' first."
    exit 1
fi

# Create the SQL with the pre-generated hashes
cat << 'EOF' | docker exec -i platform_postgres psql -U postgres -d appdb
-- Create test data for CRM system
-- Using pre-generated bcrypt hashes
-- Note: Assumes database migrations have been run first

-- User profiles table should exist from migrations

-- Insert test users with proper bcrypt hashes
INSERT INTO users (email, username, full_name, password_hash, email_confirmed_at)
VALUES 
    -- <EMAIL> with password "TestPassword"
    ('<EMAIL>', 'will', 'Will Smith', '$2a$12$A2j79h6GJErYQgFze/k5x.G01IS90ULphQO4aX4CHGUeL6EcQ/LUG', NOW()),
    -- <EMAIL> with password "password123" 
    ('<EMAIL>', 'testuser', 'Test User', '$2a$12$6m6LQa2eSOM..9lWYR7XDulS0E1t/8aiS9Ra/e87Ci8uCKPOMPMZ6', NOW()),
    -- <EMAIL> with password "admin123"
    ('<EMAIL>', 'admin', 'Admin User', '$2a$12$RGg5Iqa83NE61tGdGrEpr.KQLnJBng/JEx1c1bX.hZFrrFw/aCbcu', NOW())
ON CONFLICT (email) DO UPDATE SET
    username = EXCLUDED.username,
    full_name = EXCLUDED.full_name,
    password_hash = EXCLUDED.password_hash,
    email_confirmed_at = EXCLUDED.email_confirmed_at;

-- Insert user profiles (link to users table)
INSERT INTO user_profiles (id, full_name)
SELECT u.id, u.full_name
FROM users u
WHERE u.email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>')
ON CONFLICT (id) DO UPDATE SET
    full_name = EXCLUDED.full_name;

-- Insert company statuses (reference data) - MUST BE BEFORE COMPANIES
INSERT INTO company_statuses (name, pipeline_order) 
SELECT * FROM (VALUES
    ('Lead', 1),
    ('Prospect', 2),
    ('Customer', 3),
    ('Former Customer', 4)
) AS t(name, pipeline_order)
WHERE NOT EXISTS (SELECT 1 FROM company_statuses WHERE company_statuses.name = t.name);

-- Insert deal stages (reference data) - MUST BE BEFORE DEALS
INSERT INTO deal_stages (name, pipeline_order, is_closed_won, is_closed_lost)
SELECT * FROM (VALUES
    ('Prospecting', 1, FALSE, FALSE),
    ('Qualification', 2, FALSE, FALSE),
    ('Proposal', 3, FALSE, FALSE),
    ('Negotiation', 4, FALSE, FALSE),
    ('Closed Won', 5, TRUE, FALSE),
    ('Closed Lost', 6, FALSE, TRUE)
) AS t(name, pipeline_order, is_closed_won, is_closed_lost)
WHERE NOT EXISTS (SELECT 1 FROM deal_stages WHERE deal_stages.name = t.name);

-- Insert interaction types (reference data)
INSERT INTO interaction_types (name)
SELECT * FROM (VALUES
    ('Email'),
    ('Phone Call'),
    ('Meeting'),
    ('Note'),
    ('Task')
) AS t(name)
WHERE NOT EXISTS (SELECT 1 FROM interaction_types WHERE interaction_types.name = t.name);

-- Companies table should exist from migrations

-- Insert test companies
WITH will_user AS (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
     lead_status AS (SELECT id FROM company_statuses WHERE name = 'Lead' LIMIT 1),
     prospect_status AS (SELECT id FROM company_statuses WHERE name = 'Prospect' LIMIT 1),
     customer_status AS (SELECT id FROM company_statuses WHERE name = 'Customer' LIMIT 1)
INSERT INTO companies (name, website, phone, address, notes, company_status_id, created_by)
SELECT * FROM (VALUES
    ('Acme Corporation', 'https://acme.com', '******-0123', '123 Business St, San Francisco, CA', 'Large enterprise client', (SELECT id FROM prospect_status), (SELECT id FROM will_user)),
    ('TechStart Inc', 'https://techstart.io', '******-0456', '456 Startup Ave, Austin, TX', 'Growing startup, high potential', (SELECT id FROM lead_status), (SELECT id FROM will_user)),
    ('Global Industries', 'https://globalind.com', '******-0789', '789 Corporate Blvd, New York, NY', 'Existing customer, renewal coming up', (SELECT id FROM customer_status), (SELECT id FROM will_user)),
    ('Innovation Labs', 'https://innovlabs.com', '******-0321', '321 Research Dr, Seattle, WA', 'R&D focused company', (SELECT id FROM lead_status), (SELECT id FROM will_user)),
    ('Two Dot AI', 'https://twodot.ai', '******-0654', '654 AI Way, Palo Alto, CA', 'Our own company!', (SELECT id FROM customer_status), (SELECT id FROM will_user))
) AS t(name, website, phone, address, notes, company_status_id, created_by)
WHERE NOT EXISTS (SELECT 1 FROM companies WHERE companies.name = t.name);

-- Contacts table should exist from migrations

-- Insert test contacts
WITH will_user AS (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
     acme_company AS (SELECT id FROM companies WHERE name = 'Acme Corporation' LIMIT 1),
     techstart_company AS (SELECT id FROM companies WHERE name = 'TechStart Inc' LIMIT 1),
     global_company AS (SELECT id FROM companies WHERE name = 'Global Industries' LIMIT 1),
     innovation_company AS (SELECT id FROM companies WHERE name = 'Innovation Labs' LIMIT 1)
INSERT INTO contacts (first_name, last_name, email, phone, job_title, company_id, created_by)
SELECT * FROM (VALUES
    ('John', 'Smith', '<EMAIL>', '******-1001', 'CEO', (SELECT id FROM acme_company), (SELECT id FROM will_user)),
    ('Sarah', 'Johnson', '<EMAIL>', '******-1002', 'CTO', (SELECT id FROM acme_company), (SELECT id FROM will_user)),
    ('Mike', 'Davis', '<EMAIL>', '******-2001', 'Founder', (SELECT id FROM techstart_company), (SELECT id FROM will_user)),
    ('Emily', 'Chen', '<EMAIL>', '******-3001', 'VP Engineering', (SELECT id FROM global_company), (SELECT id FROM will_user)),
    ('Alex', 'Rodriguez', '<EMAIL>', '******-4001', 'Research Director', (SELECT id FROM innovation_company), (SELECT id FROM will_user)),
    ('Lisa', 'Wang', '<EMAIL>', '******-3002', 'Product Manager', (SELECT id FROM global_company), (SELECT id FROM will_user))
) AS t(first_name, last_name, email, phone, job_title, company_id, created_by)
WHERE NOT EXISTS (SELECT 1 FROM contacts WHERE contacts.email = t.email);

-- Deals table should exist from migrations

-- Insert test deals
WITH will_user AS (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
     acme_company AS (SELECT id FROM companies WHERE name = 'Acme Corporation' LIMIT 1),
     techstart_company AS (SELECT id FROM companies WHERE name = 'TechStart Inc' LIMIT 1),
     global_company AS (SELECT id FROM companies WHERE name = 'Global Industries' LIMIT 1),
     prospecting_stage AS (SELECT id FROM deal_stages WHERE name = 'Prospecting' LIMIT 1),
     qualification_stage AS (SELECT id FROM deal_stages WHERE name = 'Qualification' LIMIT 1),
     proposal_stage AS (SELECT id FROM deal_stages WHERE name = 'Proposal' LIMIT 1),
     negotiation_stage AS (SELECT id FROM deal_stages WHERE name = 'Negotiation' LIMIT 1)
INSERT INTO deals (title, description, estimated_value, company_id, deal_stage_id, expected_close_date, created_by)
SELECT * FROM (VALUES
    ('Enterprise CRM License', 'Annual license for 500 users', 150000.00, (SELECT id FROM acme_company), (SELECT id FROM proposal_stage), DATE '2025-08-15', (SELECT id FROM will_user)),
    ('Startup Package', 'Complete CRM setup for growing startup', 25000.00, (SELECT id FROM techstart_company), (SELECT id FROM qualification_stage), DATE '2025-07-30', (SELECT id FROM will_user)),
    ('License Renewal', 'Annual renewal of existing contract', 75000.00, (SELECT id FROM global_company), (SELECT id FROM negotiation_stage), DATE '2025-09-01', (SELECT id FROM will_user)),
    ('Custom Integration', 'Custom API integration project', 50000.00, (SELECT id FROM acme_company), (SELECT id FROM prospecting_stage), DATE '2025-10-15', (SELECT id FROM will_user))
) AS t(title, description, estimated_value, company_id, deal_stage_id, expected_close_date, created_by)
WHERE NOT EXISTS (SELECT 1 FROM deals WHERE deals.title = t.title AND deals.company_id = t.company_id);

-- Display created data summary
SELECT 'USERS CREATED' as table_name, COUNT(*) as count FROM users
UNION ALL
SELECT 'USER PROFILES CREATED', COUNT(*) FROM user_profiles
UNION ALL 
SELECT 'COMPANIES CREATED', COUNT(*) FROM companies
UNION ALL
SELECT 'CONTACTS CREATED', COUNT(*) FROM contacts
UNION ALL
SELECT 'DEALS CREATED', COUNT(*) FROM deals;

-- Show the test users that were created
SELECT 'Test Users:' as info, u.email, u.full_name
FROM users u
WHERE u.email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>');
EOF

echo ""
echo "✅ Test data loaded successfully!"
echo ""
echo "🔑 You can now log in with:"
echo "  Email: <EMAIL>"
echo "  Password: TestPassword"
echo ""
echo "  OR"
echo ""  
echo "  Email: <EMAIL>"
echo "  Password: password123"
echo ""
echo "  OR"
echo ""
echo "  Email: <EMAIL>"
echo "  Password: admin123"