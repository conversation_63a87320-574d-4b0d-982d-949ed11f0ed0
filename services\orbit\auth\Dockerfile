# Build stage
FROM golang:1.24-alpine AS builder

WORKDIR /app

# Copy go mod files from root
COPY go.mod go.sum ./

# Copy shared dependencies first
COPY shared/go/ ./shared/go/

# Copy service dependencies
COPY services/orbit/rest-api/ ./services/orbit/rest-api/

RUN go mod download

# Copy source code
COPY services/orbit/auth/ ./services/orbit/auth/

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -o auth ./services/orbit/auth

# Final stage
FROM alpine:latest

RUN apk --no-cache add ca-certificates wget

WORKDIR /root/

# Copy the binary from builder
COPY --from=builder /app/auth .

# Expose port
EXPOSE 8004

# Run the binary
CMD ["./auth"]