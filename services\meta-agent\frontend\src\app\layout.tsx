/**
 * AI Agent Platform - Root Layout
 * Enhanced with Tailwind CSS and ShadCN UI
 */

import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import { cn } from '@/lib/utils'
import { Providers } from '@/components/providers'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: {
    default: 'AI Agent Platform',
    template: '%s | AI Agent Platform'
  },
  description: 'Enterprise AI Agent Platform for creating, deploying, and managing AI agents at scale. Enhanced with modern UI components.',
  keywords: [
    'AI',
    'Agents',
    'Machine Learning',
    'Automation',
    'Platform',
    'ShadCN',
    'Tailwind'
  ],
  authors: [
    {
      name: 'AI Agent Platform Team',
    }
  ],
  creator: 'AI Agent Platform',
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://ai-agent-platform.com',
    title: 'AI Agent Platform',
    description: 'Enterprise AI Agent Platform for creating, deploying, and managing AI agents at scale',
    siteName: 'AI Agent Platform',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'AI Agent Platform',
    description: 'Enterprise AI Agent Platform for creating, deploying, and managing AI agents at scale',
    creator: '@ai_agent_platform',
  },
  icons: {
    icon: '/favicon.ico',
    shortcut: '/favicon-16x16.png',
    apple: '/apple-touch-icon.png',
  },
}

interface RootLayoutProps {
  children: React.ReactNode
}

export default function RootLayout({ children }: RootLayoutProps) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head />
      <body
        className={cn(
          "min-h-screen bg-background font-sans antialiased",
          inter.className
        )}
      >
        <Providers>
          <div vaul-drawer-wrapper="">
            <div className="relative flex min-h-screen flex-col bg-background">
              {children}
            </div>
          </div>
        </Providers>
      </body>
    </html>
  )
}