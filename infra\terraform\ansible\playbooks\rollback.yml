---
- name: Rollback to Previous Deployment
  hosts: microservices
  become: yes
  gather_facts: yes
  
  vars:
    rollback_strategy: "{{ strategy | default('immediate') }}"  # immediate or blue-green
    
  pre_tasks:
    - name: Load current deployment metadata
      slurp:
        src: /var/lib/microservices/current-deployment.yml
      register: current_deployment_raw
      
    - name: Parse deployment metadata
      set_fact:
        current_deployment: "{{ current_deployment_raw.content | b64decode | from_yaml }}"
        
    - name: Verify rollback is possible
      assert:
        that:
          - current_deployment.previous_color is defined
          - current_deployment.previous_color != 'none'
        fail_msg: "No previous deployment available for rollback"
        
  tasks:
    - name: Immediate rollback - Switch nginx routing
      include_role:
        name: ../roles/nginx
        tasks_from: switch-traffic
      vars:
        target_color: "{{ current_deployment.previous_color }}"
      when: rollback_strategy == 'immediate'
      tags: ['immediate-rollback']
      
    - name: Blue-green rollback - Redeploy previous versions
      block:
        - name: Get previous image versions
          uri:
            url: "https://{{ region }}-docker.pkg.dev/{{ project_id }}/{{ artifact_registry }}/{{ item }}/tags/list"
            headers:
              Authorization: "Bearer {{ gcp_auth_token }}"
          loop: "{{ services.keys() | list }}"
          register: previous_tags
          
        - name: Deploy previous versions
          include_role:
            name: ../roles/microservices
            tasks_from: deploy
          vars:
            rollback_images: true
            target_versions: "{{ previous_tags.results }}"
      when: rollback_strategy == 'blue-green'
      tags: ['blue-green-rollback']
      
    - name: Verify rollback health
      uri:
        url: "http://localhost:{{ item.value.port }}{{ item.value.health_check.path }}"
        method: GET
        status_code: 200
      retries: 30
      delay: 10
      loop: "{{ services | dict2items }}"
      when: item.value.health_check is defined
      tags: ['health-check']
      
  post_tasks:
    - name: Update deployment metadata after rollback
      copy:
        content: |
          deployment_time: {{ ansible_date_time.iso8601 }}
          deployment_type: rollback
          rollback_strategy: {{ rollback_strategy }}
          deployment_color: {{ current_deployment.previous_color }}
          previous_deployment: {{ current_deployment.deployment_time }}
        dest: /var/lib/microservices/rollback-metadata.yml
      tags: ['metadata']
      
    - name: Send rollback notification
      uri:
        url: "{{ webhook_url | default('http://localhost/webhook') }}"
        method: POST
        body_format: json
        body:
          event: deployment_rollback
          environment: "{{ environment }}"
          timestamp: "{{ ansible_date_time.iso8601 }}"
          strategy: "{{ rollback_strategy }}"
      when: webhook_url is defined
      ignore_errors: yes
      tags: ['notification']