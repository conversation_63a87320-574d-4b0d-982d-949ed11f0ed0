# AI Agent Platform - Makefile
# Comprehensive automation for development, testing, and deployment

.PHONY: help install clean test build deploy backup restore
.DEFAULT_GOAL := help

# Variables
PYTHON := python3
NODE := npm
DOCKER := docker
DOCKER_COMPOSE := docker-compose
ENVIRONMENT ?= development
TAG ?= latest
REGISTRY ?= 
NAMESPACE ?= ai-platform
DOMAIN ?= localhost

# Colors for output
RED = \033[0;31m
GREEN = \033[0;32m
YELLOW = \033[1;33m
BLUE = \033[0;34m
NC = \033[0m

# Helper function to print colored output
define print_status
	@echo -e "$(BLUE)[INFO]$(NC) $(1)"
endef

define print_success
	@echo -e "$(GREEN)[SUCCESS]$(NC) $(1)"
endef

define print_warning
	@echo -e "$(YELLOW)[WARNING]$(NC) $(1)"
endef

define print_error
	@echo -e "$(RED)[ERROR]$(NC) $(1)"
endef

##@ Help
help: ## Display this help message
	@awk 'BEGIN {FS = ":.*##"; printf "\nUsage:\n  make \033[36m<target>\033[0m\n"} /^[a-zA-Z_0-9-]+:.*?##/ { printf "  \033[36m%-20s\033[0m %s\n", $$1, $$2 } /^##@/ { printf "\n\033[1m%s\033[0m\n", substr($$0, 5) } ' $(MAKEFILE_LIST)

##@ Setup
install: install-backend install-frontend ## Install all dependencies

install-backend: ## Install backend dependencies
	$(call print_status,"Installing backend dependencies...")
	cd backend && $(PYTHON) -m pip install --upgrade pip
	cd backend && $(PYTHON) -m pip install -r requirements.txt
	$(call print_success,"Backend dependencies installed")

install-frontend: ## Install frontend dependencies
	$(call print_status,"Installing frontend dependencies...")
	cd frontend && $(NODE) ci
	$(call print_success,"Frontend dependencies installed")

setup: ## Setup development environment
	$(call print_status,"Setting up development environment...")
	./scripts/dev-setup.sh
	$(call print_success,"Development environment ready")

clean: clean-backend clean-frontend clean-docker ## Clean all build artifacts

clean-backend: ## Clean backend artifacts
	$(call print_status,"Cleaning backend artifacts...")
	cd backend && find . -type d -name __pycache__ -exec rm -rf {} + 2>/dev/null || true
	cd backend && find . -name "*.pyc" -delete 2>/dev/null || true
	cd backend && find . -name "*.pyo" -delete 2>/dev/null || true
	cd backend && rm -rf .pytest_cache/ .coverage htmlcov/ 2>/dev/null || true
	$(call print_success,"Backend artifacts cleaned")

clean-frontend: ## Clean frontend artifacts
	$(call print_status,"Cleaning frontend artifacts...")
	cd frontend && rm -rf .next/ out/ coverage/ 2>/dev/null || true
	cd frontend && $(NODE) run clean 2>/dev/null || true
	$(call print_success,"Frontend artifacts cleaned")

clean-docker: ## Clean Docker artifacts
	$(call print_status,"Cleaning Docker artifacts...")
	$(DOCKER) system prune -f
	$(DOCKER) volume prune -f
	$(call print_success,"Docker artifacts cleaned")

##@ Development
dev: ## Start development environment
	$(call print_status,"Starting development environment...")
	./scripts/start-dev.sh

dev-backend: ## Start backend development server
	$(call print_status,"Starting backend development server...")
	./scripts/start-backend.sh

dev-frontend: ## Start frontend development server
	$(call print_status,"Starting frontend development server...")
	./scripts/start-frontend.sh

dev-services: ## Start development services (DB, Redis, etc.)
	$(call print_status,"Starting development services...")
	$(DOCKER_COMPOSE) -f docker-compose.dev.yml up -d
	$(call print_success,"Development services started")

stop-services: ## Stop development services
	$(call print_status,"Stopping development services...")
	$(DOCKER_COMPOSE) -f docker-compose.dev.yml down
	$(call print_success,"Development services stopped")

db-setup: ## Setup database
	$(call print_status,"Setting up database...")
	./scripts/setup-db.sh

db-migrate: ## Run database migrations
	$(call print_status,"Running database migrations...")
	cd backend && $(PYTHON) -m alembic upgrade head
	$(call print_success,"Database migrations completed")

db-reset: ## Reset database (WARNING: destroys all data)
	$(call print_warning,"This will destroy all database data!")
	@read -p "Are you sure? [y/N] " confirm; \
	if [ "$$confirm" = "y" ] || [ "$$confirm" = "Y" ]; then \
		$(DOCKER_COMPOSE) -f docker-compose.dev.yml down -v; \
		$(DOCKER_COMPOSE) -f docker-compose.dev.yml up -d postgres; \
		sleep 5; \
		make db-migrate; \
		$(call print_success,"Database reset completed"); \
	else \
		$(call print_status,"Database reset cancelled"); \
	fi

shell-backend: ## Open backend shell
	cd backend && $(PYTHON) -c "from src import *; import IPython; IPython.embed()"

##@ Code Quality
lint: lint-backend lint-frontend ## Run all linting

lint-backend: ## Lint backend code
	$(call print_status,"Linting backend code...")
	cd backend && $(PYTHON) -m black --check src/ tests/
	cd backend && $(PYTHON) -m isort --check-only src/ tests/
	cd backend && $(PYTHON) -m flake8 src/ tests/
	$(call print_success,"Backend linting passed")

lint-frontend: ## Lint frontend code
	$(call print_status,"Linting frontend code...")
	cd frontend && $(NODE) run lint
	$(call print_success,"Frontend linting passed")

format: format-backend format-frontend ## Format all code

format-backend: ## Format backend code
	$(call print_status,"Formatting backend code...")
	cd backend && $(PYTHON) -m black src/ tests/
	cd backend && $(PYTHON) -m isort src/ tests/
	$(call print_success,"Backend code formatted")

format-frontend: ## Format frontend code
	$(call print_status,"Formatting frontend code...")
	cd frontend && $(NODE) run format 2>/dev/null || $(call print_warning,"Frontend formatter not configured")
	$(call print_success,"Frontend code formatted")

type-check: type-check-backend type-check-frontend ## Run type checking

type-check-backend: ## Type check backend code
	$(call print_status,"Type checking backend code...")
	cd backend && $(PYTHON) -m mypy src/ --ignore-missing-imports
	$(call print_success,"Backend type checking passed")

type-check-frontend: ## Type check frontend code
	$(call print_status,"Type checking frontend code...")
	cd frontend && $(NODE) run type-check
	$(call print_success,"Frontend type checking passed")

quality: lint type-check ## Run all code quality checks
	./scripts/check-quality.sh

##@ Testing
test: test-backend test-frontend ## Run all tests

test-backend: ## Run backend tests
	$(call print_status,"Running backend tests...")
	cd backend && $(PYTHON) -m pytest tests/ -v
	$(call print_success,"Backend tests passed")

test-frontend: ## Run frontend tests
	$(call print_status,"Running frontend tests...")
	cd frontend && $(NODE) test -- --watchAll=false
	$(call print_success,"Frontend tests passed")

test-coverage: ## Run tests with coverage
	$(call print_status,"Running tests with coverage...")
	cd backend && $(PYTHON) -m pytest tests/ --cov=src --cov-report=html --cov-report=term
	cd frontend && $(NODE) test -- --coverage --watchAll=false
	$(call print_success,"Coverage reports generated")

test-integration: ## Run integration tests
	$(call print_status,"Running integration tests...")
	# Ensure services are running
	make dev-services
	sleep 10
	cd backend && $(PYTHON) -m pytest tests/integration/ -v
	$(call print_success,"Integration tests passed")

test-e2e: ## Run end-to-end tests
	$(call print_status,"Running E2E tests...")
	# Start full environment
	make dev &
	sleep 30
	cd frontend && $(NODE) run test:e2e 2>/dev/null || $(call print_warning,"E2E tests not configured")
	$(call print_success,"E2E tests completed")

##@ Building
build: build-backend build-frontend ## Build all components

build-backend: ## Build backend
	$(call print_status,"Building backend...")
	$(DOCKER) build -t ai-platform-backend:$(TAG) \
		--build-arg ENVIRONMENT=$(ENVIRONMENT) \
		backend/
	$(call print_success,"Backend built")

build-frontend: ## Build frontend
	$(call print_status,"Building frontend...")
	cd frontend && $(NODE) run build
	$(DOCKER) build -t ai-platform-frontend:$(TAG) \
		--build-arg ENVIRONMENT=$(ENVIRONMENT) \
		frontend/
	$(call print_success,"Frontend built")

build-push: build ## Build and push images
	$(call print_status,"Pushing images to registry...")
ifdef REGISTRY
	$(DOCKER) tag ai-platform-backend:$(TAG) $(REGISTRY)/ai-platform-backend:$(TAG)
	$(DOCKER) tag ai-platform-frontend:$(TAG) $(REGISTRY)/ai-platform-frontend:$(TAG)
	$(DOCKER) push $(REGISTRY)/ai-platform-backend:$(TAG)
	$(DOCKER) push $(REGISTRY)/ai-platform-frontend:$(TAG)
	$(call print_success,"Images pushed to registry")
else
	$(call print_error,"REGISTRY variable not set")
	exit 1
endif

##@ Deployment
deploy-docker: ## Deploy using Docker Compose
	$(call print_status,"Deploying with Docker Compose...")
	./scripts/deploy.sh --method docker-compose --environment $(ENVIRONMENT)

deploy-k8s: ## Deploy to Kubernetes
	$(call print_status,"Deploying to Kubernetes...")
	./scripts/deploy.sh --method kubernetes --namespace $(NAMESPACE) --domain $(DOMAIN)

deploy-staging: ## Deploy to staging environment
	$(call print_status,"Deploying to staging...")
	make deploy-docker ENVIRONMENT=staging

deploy-production: ## Deploy to production environment
	$(call print_status,"Deploying to production...")
	$(call print_warning,"Deploying to production environment!")
	@read -p "Are you sure? [y/N] " confirm; \
	if [ "$$confirm" = "y" ] || [ "$$confirm" = "Y" ]; then \
		make deploy-docker ENVIRONMENT=production; \
	else \
		$(call print_status,"Production deployment cancelled"); \
	fi

##@ Monitoring & Maintenance
logs: ## Show application logs
	$(call print_status,"Showing application logs...")
	$(DOCKER_COMPOSE) -f docker-compose.dev.yml logs -f

logs-backend: ## Show backend logs
	$(DOCKER_COMPOSE) -f docker-compose.dev.yml logs -f backend

logs-frontend: ## Show frontend logs
	$(DOCKER_COMPOSE) -f docker-compose.dev.yml logs -f frontend

status: ## Show service status
	$(call print_status,"Service status:")
	$(DOCKER_COMPOSE) -f docker-compose.dev.yml ps

health: ## Check service health
	$(call print_status,"Checking service health...")
	curl -f http://localhost:8000/health || $(call print_error,"Backend health check failed")
	curl -f http://localhost:3000/api/health || $(call print_error,"Frontend health check failed")
	$(call print_success,"Health checks passed")

metrics: ## Show metrics (requires Prometheus)
	$(call print_status,"Opening metrics dashboard...")
	open http://localhost:9090 2>/dev/null || echo "Open http://localhost:9090 for Prometheus metrics"

monitoring: ## Show monitoring dashboard (requires Grafana)
	$(call print_status,"Opening monitoring dashboard...")
	open http://localhost:3001 2>/dev/null || echo "Open http://localhost:3001 for Grafana (admin/admin)"

##@ Backup & Restore
backup: ## Create full backup
	$(call print_status,"Creating backup...")
	./scripts/backup-restore.sh backup --include-logs

backup-config: ## Backup configuration only
	$(call print_status,"Backing up configuration...")
	./scripts/backup-restore.sh export

restore: ## Restore from backup (requires BACKUP_FILE)
	$(call print_status,"Restoring from backup...")
ifdef BACKUP_FILE
	./scripts/backup-restore.sh restore $(BACKUP_FILE)
else
	$(call print_error,"BACKUP_FILE variable not set")
	$(call print_status,"Usage: make restore BACKUP_FILE=path/to/backup.tar.gz")
	exit 1
endif

list-backups: ## List available backups
	./scripts/backup-restore.sh list

cleanup-backups: ## Clean up old backups
	./scripts/backup-restore.sh cleanup

##@ Security
security-scan: ## Run security scans
	$(call print_status,"Running security scans...")
	$(DOCKER) run --rm -v $(PWD):/workspace aquasec/trivy fs /workspace

vulnerability-check: ## Check for known vulnerabilities
	$(call print_status,"Checking for vulnerabilities...")
	cd backend && $(PYTHON) -m safety check
	cd frontend && $(NODE) audit

##@ Documentation
docs: ## Generate documentation
	$(call print_status,"Generating documentation...")
	# Backend API docs
	cd backend && $(PYTHON) openapi_generator.py
	# Frontend component docs (if configured)
	cd frontend && $(NODE) run build-storybook 2>/dev/null || $(call print_warning,"Storybook not configured")
	$(call print_success,"Documentation generated")

docs-serve: ## Serve documentation locally
	$(call print_status,"Serving documentation...")
	open http://localhost:8000/docs 2>/dev/null || echo "Open http://localhost:8000/docs for API documentation"

##@ Utilities
generate-openapi: ## Generate OpenAPI specification
	$(call print_status,"Generating OpenAPI specification...")
	cd backend && $(PYTHON) openapi_generator.py
	cd frontend && $(NODE) run generate:api
	$(call print_success,"OpenAPI specification generated")

generate-env: ## Generate environment file templates
	$(call print_status,"Generating environment templates...")
	cp backend/.env.example backend/.env 2>/dev/null || $(call print_warning,"Backend .env.example not found")
	cp frontend/.env.local.example frontend/.env.local 2>/dev/null || $(call print_warning,"Frontend .env.local.example not found")
	$(call print_success,"Environment templates copied")

port-check: ## Check if required ports are available
	$(call print_status,"Checking port availability...")
	@for port in 3000 8000 5432 6379 9090 3001; do \
		if lsof -Pi :$$port -sTCP:LISTEN -t >/dev/null 2>&1; then \
			$(call print_warning,"Port $$port is in use"); \
		else \
			$(call print_status,"Port $$port is available"); \
		fi; \
	done

system-info: ## Show system information
	$(call print_status,"System Information:")
	@echo "OS: $$(uname -s) $$(uname -r)"
	@echo "Python: $$($(PYTHON) --version 2>&1)"
	@echo "Node.js: $$(node --version 2>/dev/null || echo 'Not installed')"
	@echo "Docker: $$($(DOCKER) --version 2>/dev/null || echo 'Not installed')"
	@echo "Docker Compose: $$($(DOCKER_COMPOSE) --version 2>/dev/null || echo 'Not installed')"

##@ CI/CD
ci: lint type-check test build ## Run CI pipeline locally
	$(call print_success,"CI pipeline completed successfully")

ci-backend: lint-backend type-check-backend test-backend build-backend ## Run backend CI pipeline

ci-frontend: lint-frontend type-check-frontend test-frontend build-frontend ## Run frontend CI pipeline

precommit: format lint test ## Run pre-commit checks
	$(call print_success,"Pre-commit checks passed")

release: ## Create a new release (requires VERSION)
	$(call print_status,"Creating release...")
ifdef VERSION
	git tag -a v$(VERSION) -m "Release version $(VERSION)"
	git push origin v$(VERSION)
	$(call print_success,"Release v$(VERSION) created")
else
	$(call print_error,"VERSION variable not set")
	$(call print_status,"Usage: make release VERSION=1.0.0")
	exit 1
endif

##@ Quick Actions
quick-start: setup dev ## Quick start development environment

quick-test: dev-services test ## Quick test with services

quick-deploy: build deploy-docker ## Quick deployment

all: clean install build test deploy-docker ## Run complete pipeline