load("@rules_go//go:def.bzl", "go_library")

package(default_visibility = ["//visibility:public"])

# OpenAPI specification files
exports_files([
    "openapi.yaml",
    "auth.yaml",
    "companies.yaml",
    "contacts.yaml",
    "deals.yaml",
    "interactions.yaml",
    "arli.yaml",
])

# Validate OpenAPI spec using Redocly
genrule(
    name = "validate_spec",
    srcs = ["openapi.yaml"],
    outs = ["validation_result.txt"],
    cmd = """
        redocly lint $(location openapi.yaml) > $@ 2>&1 || echo "Validation completed with warnings" > $@
    """,
)




# Generate TypeScript client using openapi-typescript
genrule(
    name = "generate_typescript_client",
    srcs = ["openapi.yaml"] + glob(
        ["**/*.yaml"],
        exclude = [
            "openapi.yaml",
            "oapi-codegen.yaml",
        ],
    ),
    outs = ["client.ts"],
    cmd = """
        # Bundle the OpenAPI spec
        redocly bundle $(location openapi.yaml) --output openapi-bundled.yaml
        
        # Use openapi-typescript for better TypeScript generation
        npx openapi-typescript openapi-bundled.yaml --output $(location client.ts)
    """,
)

# Generate Go server code using oapi-codegen
genrule(
    name = "generate_go_server",
    srcs = [
        "openapi.yaml",
        "oapi-codegen.yaml",
        "tools.go",
    ] + glob(
        ["**/*.yaml"],
        exclude = [
            "openapi.yaml",
            "oapi-codegen.yaml",
        ],
    ),
    outs = ["server.go"],
    cmd = """
        # Set up Go environment
        export GOPATH=$$(mktemp -d)
        export GOMODCACHE=$$GOPATH/pkg/mod
        export GOCACHE=$$GOPATH/cache
        
        # Bundle the OpenAPI spec
        redocly bundle $(location openapi.yaml) --output openapi-bundled.yaml
        
        # Generate the Go code using oapi-codegen
        # Install oapi-codegen if not available
        go install github.com/oapi-codegen/oapi-codegen/v2/cmd/oapi-codegen@latest
        
        # Generate the Go server code
        $$GOPATH/bin/oapi-codegen -generate gin,types -package server openapi-bundled.yaml > $(location server.go)
    """,
)

# Generate Go client code using oapi-codegen
genrule(
    name = "generate_go_client",
    srcs = [
        "openapi.yaml",
        "oapi-codegen.yaml",
        "tools.go",
    ] + glob(
        ["**/*.yaml"],
        exclude = [
            "openapi.yaml",
            "oapi-codegen.yaml",
        ],
    ),
    outs = ["client.go"],
    cmd = """
        # Set up Go environment
        export GOPATH=$$(mktemp -d)
        export GOMODCACHE=$$GOPATH/pkg/mod
        export GOCACHE=$$GOPATH/cache
        
        # Bundle the OpenAPI spec
        redocly bundle $(location openapi.yaml) --output openapi-bundled.yaml
        
        # Generate the Go code using oapi-codegen
        # Install oapi-codegen if not available
        go install github.com/oapi-codegen/oapi-codegen/v2/cmd/oapi-codegen@latest
        
        # Generate the Go client code
        $$GOPATH/bin/oapi-codegen -generate client,types -package client openapi-bundled.yaml > $(location client.go)
    """,
)


# Copy generated files to the generated/ directory
sh_binary(
    name = "update_generated",
    srcs = ["update_generated.sh"],
    data = [
        ":generate_go_server",
        ":generate_go_client",
        ":generate_typescript_client",
    ],
)

# All clients target (for compatibility)
filegroup(
    name = "all_clients",
    srcs = [
        ":generate_go_server",
        ":generate_go_client",
        ":generate_typescript_client",
        ":validate_spec",
    ],
)

# Clean target to remove generated files
sh_binary(
    name = "clean",
    srcs = ["clean.sh"],
)

