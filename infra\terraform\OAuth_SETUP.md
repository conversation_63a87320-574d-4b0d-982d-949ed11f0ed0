# OAuth Setup Guide

This guide explains how to set up OAuth functionality for the platform when deploying to a new environment.

## Quick Start (First-time Setup)

For a complete new environment setup including OAuth:

```bash
# Set OAuth credentials (optional, but recommended)
export GOOGLE_OAUTH_CLIENT_ID="your-client-id"
export GOOGLE_OAUTH_CLIENT_SECRET="your-client-secret"

# Run complete setup
bazel run //terraform:setup_new_environment
```

## OAuth-Only Setup

If you only need to set up OAuth secrets:

```bash
# With OAuth credentials from environment
export GOOGLE_OAUTH_CLIENT_ID="your-client-id"
export GOOGLE_OAUTH_CLIENT_SECRET="your-client-secret"
bazel run //terraform:setup_oauth_secrets

# Without credentials (creates placeholders)
bazel run //terraform:setup_oauth_secrets
```

## Getting OAuth Credentials

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Navigate to **APIs & Services** → **Credentials**
3. Click **Create Credentials** → **OAuth 2.0 Client ID**
4. Select **Web application**
5. Add authorized redirect URIs:
   - `https://your-domain.com/auth/callback/google`
   - `http://localhost:8080/auth/callback/google` (for local development)
6. Copy the Client ID and Client Secret

## Updating OAuth Secrets

If you need to update existing OAuth secrets:

```bash
# Update client ID
echo 'new-client-id' | gcloud secrets versions add oauth-client-id --data-file=-

# Update client secret
echo 'new-client-secret' | gcloud secrets versions add oauth-client-secret --data-file=-

# Update and redeploy
bazel run //terraform:build_and_push_backend_dev
```

## Testing OAuth

1. Deploy your application:
   ```bash
   bazel run //terraform:build_and_push_backend_dev
   ```

2. Test the OAuth flow:
   ```bash
   # Test OAuth initiation
   curl -X POST https://your-domain.com/api/v1/auth/signin/oauth \
     -H "Content-Type: application/json" \
     -d '{"provider": "google", "redirectTo": "/dashboard"}'
   ```

3. Check that the response contains a full OAuth URL (not truncated)

## OAuth Configuration Files

The OAuth setup affects these files:

- `terraform/scripts/setup_oauth_secrets.sh` - Creates OAuth secrets
- `terraform/modules/compute/startup-script.sh` - Loads secrets on VM startup
- `terraform/docker-compose.yml` - Includes OAuth environment variables
- `platform/auth/config/config.go` - OAuth configuration loading
- `platform/auth/handlers/auth.go` - OAuth implementation

## Troubleshooting

### OAuth URL is truncated
- **Issue**: OAuth response shows `{"url":"https://accounts.google.com/oauth/authorize?..."}`
- **Solution**: Ensure debug code is removed from `buildGoogleOAuthURL` function

### OAuth secrets not found
- **Issue**: `OAuth not configured` error
- **Solution**: Run `bazel run //terraform:setup_oauth_secrets` and update with real values

### OAuth callback fails
- **Issue**: `Invalid redirect URI` error
- **Solution**: Update authorized redirect URIs in Google Cloud Console

### Environment variables not loaded
- **Issue**: OAuth credentials not available in container
- **Solution**: Check that docker-compose.yml includes OAuth environment variables

## Architecture

The OAuth flow works as follows:

1. **Frontend** calls `/api/v1/auth/signin/oauth` with provider and redirect URL
2. **Gateway** routes request to **Auth Service**
3. **Auth Service** generates OAuth URL with state parameter
4. **User** is redirected to Google for authentication
5. **Google** redirects back to `/auth/callback/google` with authorization code
6. **Auth Service** exchanges code for access token and user info
7. **Auth Service** creates user account and returns JWT token

## Security Features

- **State Parameter**: CSRF protection using JWT-signed state
- **Redirect Validation**: Only allows configured redirect URLs
- **Token Expiration**: JWT tokens expire after 24 hours
- **Secure Secrets**: OAuth credentials stored in Google Secret Manager
- **HTTPS Only**: OAuth redirects only work over HTTPS in production