#!/bin/bash
set -e

echo "Cleaning database (removing all data)..."

# Navigate to db directory
cd "$(dirname "$0")/.."

# Check if docker-compose is available
if command -v docker-compose &> /dev/null; then
    # Stop and remove containers, volumes
    docker-compose down -v
else
    # Clean using docker directly
    docker stop platform_postgres 2>/dev/null || true
    docker rm platform_postgres 2>/dev/null || true
    docker volume rm platform_postgres_data 2>/dev/null || true
fi

echo "Database cleaned. All data removed."