# Database Migration Tests Makefile

.PHONY: test test-docker test-local clean help

# Default target
help:
	@echo "Available targets:"
	@echo "  test        - Run tests with <PERSON><PERSON> (recommended)"
	@echo "  test-docker - Run tests with Docker container"
	@echo "  test-local  - Run tests with local PostgreSQL"
	@echo "  test-sql    - Run only SQL integration tests"
	@echo "  test-go     - Run only Go tests"
	@echo "  clean       - Clean up test containers"
	@echo "  help        - Show this help message"

# Default test target uses Docker
test: test-docker

# Run tests with Docker
test-docker:
	@echo "Running database migration tests with Docker..."
	./test_with_docker.sh

# Run tests with local PostgreSQL (requires PostgreSQL running)
test-local:
	@echo "Running database migration tests with local PostgreSQL..."
	./run_tests.sh

# Run only SQL integration tests
test-sql:
	@echo "Running SQL integration tests..."
	@if command -v psql &> /dev/null; then \
		psql ${DATABASE_URL} -f integration_test.sql; \
	else \
		echo "PostgreSQL client (psql) not found. Please install PostgreSQL."; \
		exit 1; \
	fi

# Run only Go tests
test-go:
	@echo "Running Go tests..."
	@if command -v go &> /dev/null; then \
		go test -v ./... -tags=integration; \
	else \
		echo "Go not found. Please install Go."; \
		exit 1; \
	fi

# Clean up test containers
clean:
	@echo "Cleaning up test containers..."
	@docker stop crm_test_db 2>/dev/null || true
	@docker rm crm_test_db 2>/dev/null || true
	@echo "Cleanup complete."

# Test with Bazel (if available)
test-bazel:
	@echo "Running tests with Bazel..."
	cd ../.. && bazel test //db/tests:migration_test --test_tag_filters=manual