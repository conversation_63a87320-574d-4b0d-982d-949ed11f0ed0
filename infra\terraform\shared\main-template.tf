# Generic Environment Main Configuration Template
# This template should be used as the basis for all environment main.tf files
# Copy this file to each environment directory and customize the backend configuration

terraform {
  required_version = ">= 1.5"
  
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
    random = {
      source  = "hashicorp/random"
      version = "~> 3.1"
    }
  }

  # Backend configuration - customize per environment
  # backend "gcs" {
  #   bucket = "YOUR-PROJECT-terraform-state-ENVIRONMENT"
  #   prefix = "terraform/state"
  # }
}

# Configure the Google Cloud Provider
provider "google" {
  project = var.project_id
  region  = var.region
  zone    = var.zone
}

# Enable required Google Cloud APIs - standardized across all environments
resource "google_project_service" "required_apis" {
  for_each = toset([
    "compute.googleapis.com",
    "container.googleapis.com",
    "containerregistry.googleapis.com",
    "sqladmin.googleapis.com",
    "servicenetworking.googleapis.com",
    "cloudresourcemanager.googleapis.com",
    "iam.googleapis.com",
    "secretmanager.googleapis.com",
    "artifactregistry.googleapis.com",
    "cloudbuild.googleapis.com",
    "storage.googleapis.com",
    "cloudapis.googleapis.com",
    "serviceusage.googleapis.com",
    "dns.googleapis.com",
    "monitoring.googleapis.com",
    "logging.googleapis.com",
    "iap.googleapis.com",
  ])
  
  service = each.value
  
  disable_dependent_services = true
  disable_on_destroy        = false
}

# Load shared environment configuration
locals {
  # Get environment-specific configuration from shared base
  env_config = local.environment_configs[var.environment]
  
  # Merge with any custom overrides
  machine_types = merge(local.env_config.machine_types, var.custom_machine_types)
  
  # Network configuration with overrides
  vpc_cidr = coalesce(
    var.custom_network_config.vpc_cidr,
    "${local.env_config.vpc_cidr_prefix}.0.0/16"
  )
  public_subnet_cidr = coalesce(
    var.custom_network_config.public_subnet_cidr,
    "${local.env_config.vpc_cidr_prefix}.1.0/24"
  )
  private_subnet_cidr = coalesce(
    var.custom_network_config.private_subnet_cidr,
    "${local.env_config.vpc_cidr_prefix}.2.0/24"
  )
  
  # Security configuration with overrides
  security_config = merge(local.env_config.security_config, var.custom_security_config)
  
  # Feature flags with overrides
  feature_flags = merge(local.env_config.feature_flags, var.custom_feature_flags)
  
  # Scaling configuration with overrides
  scaling_config = merge(local.env_config.scaling_config, var.custom_scaling_config)
  
  # SSH source ranges with override
  ssh_source_ranges = coalesce(
    var.custom_network_config.ssh_source_ranges,
    var.admin_ip_ranges,
    local.env_config.security_config.ssh_source_ranges
  )
  
  # Database configuration
  database_name = "platform_${var.environment}_db"
  database_user = "platform_${var.environment}_user"
}

# VPC Network
module "network" {
  source = "../../modules/network"
  
  project_name = var.project_name
  region = var.region
  
  public_subnet_cidr = local.public_subnet_cidr
  private_subnet_cidr = local.private_subnet_cidr
  
  ssh_source_ranges = local.ssh_source_ranges
  
  depends_on = [google_project_service.required_apis]
}

# Database
module "database" {
  source = "../../modules/database"
  
  project_name = var.project_name
  environment = var.environment
  region = var.region
  
  vpc_network = module.network.vpc_self_link
  
  postgres_version = local.env_config.database_config.postgres_version
  instance_tier = local.machine_types.database
  availability_type = local.env_config.database_config.availability_type
  disk_size_gb = local.env_config.database_config.disk_size_gb
  max_disk_size_gb = local.env_config.database_config.max_disk_size_gb
  backup_retention_days = local.env_config.database_config.backup_retention_days
  deletion_protection = local.env_config.database_config.deletion_protection
  
  database_name = local.database_name
  database_user = local.database_user
  
  enable_migration_automation = local.feature_flags.enable_migration_automation
  github_repo_owner = var.github_repo_owner
  github_repo_name = var.github_repo_name
  migration_branch = "main"
  
  depends_on = [google_project_service.required_apis]
}

# OAuth Secrets (with destroy protection)
module "secrets" {
  source = "../../modules/secrets"
  
  project_id = var.project_id
  environment = var.environment
  
  depends_on = [google_project_service.required_apis]
}

# Artifact Registry
module "registry" {
  source = "../../modules/registry"
  
  project_id = var.project_id
  project_name = var.project_name
  region = var.region
  
  # Use environment-specific cleanup policies
  cleanup_older_than_days = local.env_config.registry_config.cleanup_older_than_days
  cleanup_untagged_older_than_days = local.env_config.registry_config.cleanup_untagged_older_than_days
  
  enable_cloud_build_access = local.env_config.registry_config.enable_cloud_build_access
  enable_compute_access = local.env_config.registry_config.enable_compute_access
  enable_automated_builds = local.env_config.registry_config.enable_automated_builds
  
  github_repo_owner = var.github_repo_owner
  github_repo_name = var.github_repo_name
  build_branch_pattern = "main"
  cloudbuild_file_path = "cloudbuild.yaml"
  
  depends_on = [google_project_service.required_apis]
}

# Cloud Storage for static files
module "storage" {
  source = "../../modules/storage"
  
  project_name = var.project_name
  environment = var.environment
  bucket_location = var.region
  
  enable_public_access = local.security_config.enable_public_access
  enable_versioning = local.env_config.storage_config.enable_versioning
  
  lifecycle_rules = local.env_config.storage_config.lifecycle_rules
  
  enable_automated_deployment = local.env_config.storage_config.enable_automated_deployment
  github_repo_owner = var.github_repo_owner
  github_repo_name = var.github_repo_name
  deploy_branch = "main"
  
  # SPA routing configuration
  not_found_page = "index.html"
  
  upload_default_files = local.env_config.storage_config.upload_default_files
  
  depends_on = [google_project_service.required_apis]
}

# Compute Engine instance
module "compute" {
  source = "../../modules/compute"
  
  project_id = var.project_id
  project_name = var.project_name
  environment = var.environment
  region = var.region
  zone = var.zone
  
  machine_type = local.machine_types.compute
  boot_disk_size_gb = local.env_config.compute_config.boot_disk_size_gb
  docker_volumes_disk_size_gb = local.env_config.compute_config.docker_volumes_disk_size_gb
  
  subnet_name = module.network.private_subnet_name
  assign_external_ip = local.security_config.assign_external_ip
  create_static_ip = local.security_config.create_static_ip
  
  # Registry configuration
  registry_url = module.registry.registry_url
  
  # Database configuration
  database_host = module.database.instance_private_ip
  database_name = module.database.database_name
  database_user = module.database.database_user
  database_password_secret = module.database.database_password_secret_name
  
  # OAuth configuration
  google_oauth_client_id_secret = module.secrets.oauth_secrets.client_id
  google_oauth_client_secret_secret = module.secrets.oauth_secrets.client_secret
  oauth_state_secret = module.secrets.oauth_secrets.state_secret
  
  # Service ports (standardized)
  auth_service_port = local.service_ports.auth_service
  crm_service_port = local.service_ports.crm_backend
  gateway_port = local.service_ports.gateway
  
  # Environment-specific domain configuration
  frontend_domain = var.environment == "prod" ? "twodot.ai" : "${var.environment}.twodot.ai"
  api_domain = var.environment == "prod" ? "api.twodot.ai" : "api.${var.environment}.twodot.ai"
  ssl_domains = var.ssl_domains
  
  depends_on = [google_project_service.required_apis]
}

# Load Balancer
module "loadbalancer" {
  source = "../../modules/loadbalancer"
  
  project_name = var.project_name
  environment = var.environment
  
  storage_bucket_name = module.storage.bucket_name
  api_backend_service = module.compute.backend_service_self_link
  
  # SSL configuration
  enable_ssl = local.security_config.enable_ssl
  use_managed_ssl = true
  ssl_domains = var.ssl_domains
  enable_http_redirect = local.security_config.enable_http_redirect
  
  # CDN configuration
  enable_cdn = local.security_config.enable_cdn
  
  # Security configuration
  enable_cloud_armor = local.security_config.enable_cloud_armor
  
  depends_on = [google_project_service.required_apis]
}