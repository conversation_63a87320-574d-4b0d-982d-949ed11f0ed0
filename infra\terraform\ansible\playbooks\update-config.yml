---
- name: Update Service Configuration
  hosts: microservices
  become: yes
  gather_facts: yes
  
  vars:
    environment: "{{ deployment_environment | default('dev') }}"
    
  pre_tasks:
    - name: Load environment-specific variables
      include_vars: "../vars/{{ environment }}.yml"
      
    - name: Backup current configuration
      archive:
        path: /etc/microservices
        dest: "/var/backups/microservices-config-{{ ansible_date_time.epoch }}.tar.gz"
        format: gz
      tags: ['backup']
      
  tasks:
    - name: Update service environment files
      template:
        src: "../templates/env-files/{{ item.key }}.env.j2"
        dest: "/etc/microservices/{{ item.key }}.env"
        mode: '0600'
        backup: yes
      loop: "{{ services | dict2items }}"
      notify: restart services
      tags: ['config']
      
    - name: Update docker-compose configuration
      template:
        src: "../templates/docker-compose.yml.j2"
        dest: "/etc/microservices/docker-compose.yml"
        mode: '0644'
        backup: yes
      notify: restart services
      tags: ['compose']
      
    - name: Validate configuration
      community.docker.docker_compose_v2:
        project_src: /etc/microservices
        state: present
        pull: no
        services: []
      check_mode: yes
      register: config_validation
      tags: ['validate']
      
  handlers:
    - name: restart services
      community.docker.docker_compose_v2:
        project_src: /etc/microservices
        restarted: yes
      tags: ['restart']