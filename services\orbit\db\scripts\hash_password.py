#!/usr/bin/env python3
"""
Password hashing utility for CRM test data
Generates bcrypt hashes that are compatible with the backend authentication
"""

import bcrypt
import sys

def hash_password(password: str) -> str:
    """Generate a bcrypt hash for the given password"""
    # Generate salt and hash the password
    salt = bcrypt.gensalt(rounds=12)
    hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
    return hashed.decode('utf-8')

def main():
    if len(sys.argv) != 2:
        print("Usage: python3 hash_password.py <password>")
        print("Example: python3 hash_password.py 'TestPassword'")
        sys.exit(1)
    
    password = sys.argv[1]
    hashed = hash_password(password)
    print(hashed)

if __name__ == "__main__":
    main()