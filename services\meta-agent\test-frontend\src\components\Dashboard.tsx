import { h, FunctionalComponent } from 'preact';
import { useState, useEffect } from 'preact/hooks';
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { api } from '@/services/api';

interface DashboardProps {

}

const Dashboard: FunctionalComponent<DashboardProps> = (props) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [arAgingData, setArAgingData] = useState<any[]>([]); // Replace 'any' with the actual data type
  const [keyMetrics, setKeyMetrics] = useState<any>(null); // Replace 'any' with the actual data type
  const [recentActivity, setRecentActivity] = useState<any[]>([]); // Replace 'any' with the actual data type

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Example API calls - replace with actual API calls
        const arAgingResponse = await api.get('/ar-aging'); 
        const keyMetricsResponse = await api.get('/key-metrics');
        const recentActivityResponse = await api.get('/recent-activity');

        setArAgingData(arAgingResponse.data);
        setKeyMetrics(keyMetricsResponse.data);
        setRecentActivity(recentActivityResponse.data);
      } catch (err: any) {
        setError(err.message || 'An error occurred while fetching data.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);


  if (isLoading) {
    return <div class="text-center py-4">Loading...</div>;
  }

  if (error) {
    return <div class="text-center py-4 text-red-500">Error: {error}</div>;
  }

  return (
    <div class="container mx-auto p-4">
      <h1 class="text-2xl font-bold mb-4">Dashboard</h1>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {/* AR Aging Chart */}
        <div class="bg-white shadow-md rounded-lg p-4">
          <h2 class="text-lg font-semibold mb-2">AR Aging</h2>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={arAgingData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Area type="monotone" dataKey="value" stroke="#8884d8" fill="#8884d8" />
            </AreaChart>
          </ResponsiveContainer>
        </div>

        {/* Key Metrics */}
        <div class="bg-white shadow-md rounded-lg p-4">
          <h2 class="text-lg font-semibold mb-2">Key Metrics</h2>
          {/* Display key metrics here */}
          <pre>{JSON.stringify(keyMetrics, null, 2)}</pre>
        </div>

        {/* Recent Activity */}
        <div class="bg-white shadow-md rounded-lg p-4">
          <h2 class="text-lg font-semibold mb-2">Recent Activity</h2>
          {/* Display recent activity here */}
          <pre>{JSON.stringify(recentActivity, null, 2)}</pre>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
