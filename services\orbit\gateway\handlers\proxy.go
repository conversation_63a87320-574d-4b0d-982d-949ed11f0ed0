package handlers

import (
	"bytes"
	"io"
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/TwoDotAi/mono/services/orbit/gateway/config"
)

type ProxyHandler struct {
	Config *config.Config
}

func NewProxyHandler(cfg *config.Config) *ProxyHandler {
	return &ProxyHandler{Config: cfg}
}

func (p *ProxyHandler) ProxyToService(serviceName string) gin.HandlerFunc {
	return func(c *gin.Context) {
		serviceConfig, exists := p.Config.Services[serviceName]
		if !exists {
			c.JSON(http.StatusServiceUnavailable, gin.H{
				"error": "Service not available",
				"service": serviceName,
			})
			return
		}

		// Build target URL
		targetURL := serviceConfig.BaseURL + c.Request.URL.Path
		if c.Request.URL.RawQuery != "" {
			targetURL += "?" + c.Request.URL.RawQuery
		}

		// Create new request
		var body io.Reader
		if c.Request.Body != nil {
			bodyBytes, err := io.ReadAll(c.Request.Body)
			if err != nil {
				log.Printf("Error reading request body: %v", err)
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to read request body"})
				return
			}
			body = bytes.NewReader(bodyBytes)
		}

		req, err := http.NewRequest(c.Request.Method, targetURL, body)
		if err != nil {
			log.Printf("Error creating request: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create request"})
			return
		}

		// Copy headers
		for name, values := range c.Request.Header {
			for _, value := range values {
				req.Header.Add(name, value)
			}
		}

		// Set timeout
		client := &http.Client{
			Timeout: time.Duration(serviceConfig.Timeout) * time.Second,
		}

		// Make request
		resp, err := client.Do(req)
		if err != nil {
			log.Printf("Error making request to %s: %v", targetURL, err)
			c.JSON(http.StatusServiceUnavailable, gin.H{
				"error": "Service unavailable",
				"service": serviceName,
			})
			return
		}
		defer resp.Body.Close()

		// Copy response headers
		for name, values := range resp.Header {
			for _, value := range values {
				c.Header(name, value)
			}
		}

		// Copy response body
		respBody, err := io.ReadAll(resp.Body)
		if err != nil {
			log.Printf("Error reading response body: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to read response"})
			return
		}

		c.Data(resp.StatusCode, resp.Header.Get("Content-Type"), respBody)
	}
}