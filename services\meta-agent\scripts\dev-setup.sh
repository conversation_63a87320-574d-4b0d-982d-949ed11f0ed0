#!/bin/bash

# AI Agent Platform - Development Setup Script
# This script sets up the development environment for both backend and frontend

set -e  # Exit on any error

echo "🚀 AI Agent Platform - Development Setup"
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running on macOS or Linux
OS="$(uname -s)"
print_status "Detected OS: $OS"

# Check for required tools
check_requirements() {
    print_status "Checking requirements..."
    
    # Check Python 3.8+
    if ! command -v python3 &> /dev/null; then
        print_error "Python 3 is required but not installed"
        exit 1
    fi
    
    python_version=$(python3 --version 2>&1 | grep -o '[0-9]\+\.[0-9]\+')
    major_version=$(echo $python_version | cut -d. -f1)
    minor_version=$(echo $python_version | cut -d. -f2)
    
    if [[ $major_version -lt 3 || ($major_version -eq 3 && $minor_version -lt 8) ]]; then
        print_error "Python 3.8+ is required. Found: $python_version"
        exit 1
    fi
    
    print_success "Python 3 version: $(python3 --version)"
    
    # Check Node.js 18+
    if ! command -v node &> /dev/null; then
        print_error "Node.js is required but not installed"
        exit 1
    fi
    
    node_version=$(node --version | grep -o '[0-9]\+\.[0-9]\+')
    major_version=$(echo $node_version | cut -d. -f1)
    
    if [[ $major_version -lt 18 ]]; then
        print_error "Node.js 18+ is required. Found: $node_version"
        exit 1
    fi
    
    print_success "Node.js version: $(node --version)"
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        print_error "npm is required but not installed"
        exit 1
    fi
    
    print_success "npm version: $(npm --version)"
    
    # Check Docker (optional)
    if command -v docker &> /dev/null; then
        print_success "Docker found: $(docker --version)"
    else
        print_warning "Docker not found - some features may not work"
    fi
    
    # Check Git
    if ! command -v git &> /dev/null; then
        print_error "Git is required but not installed"
        exit 1
    fi
    
    print_success "Git version: $(git --version)"
}

# Set up Python backend
setup_backend() {
    print_status "Setting up Python backend..."
    
    cd backend
    
    # Check if virtual environment exists
    if [[ ! -d "venv" ]]; then
        print_status "Creating Python virtual environment..."
        python3 -m venv venv
    fi
    
    # Activate virtual environment
    print_status "Activating virtual environment..."
    source venv/bin/activate
    
    # Upgrade pip
    print_status "Upgrading pip..."
    pip install --upgrade pip
    
    # Install dependencies
    if [[ -f "requirements.txt" ]]; then
        print_status "Installing Python dependencies..."
        pip install -r requirements.txt
    else
        print_warning "requirements.txt not found, installing basic dependencies..."
        pip install fastapi uvicorn python-multipart aiofiles
        pip install sqlalchemy alembic asyncpg psycopg2-binary
        pip install redis celery
        pip install pytest pytest-asyncio httpx
        pip install python-dotenv pydantic[email]
        pip install openai anthropic google-generativeai
        pip install langchain langchain-community langchain-openai
        pip install docker kubernetes
        pip install prometheus-client
        pip install websockets aiohttp
    fi
    
    # Create .env file if it doesn't exist
    if [[ ! -f ".env" ]]; then
        print_status "Creating .env file..."
        cat > .env << EOF
# AI Agent Platform - Backend Environment Variables

# Database
DATABASE_URL=postgresql://postgres:password@localhost:5432/ai_agent_platform
REDIS_URL=redis://localhost:6379

# API Keys (add your actual keys)
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
GOOGLE_API_KEY=your_google_api_key_here

# Security
SECRET_KEY=your_secret_key_here
JWT_SECRET_KEY=your_jwt_secret_here

# Development
DEBUG=true
LOG_LEVEL=info
ENVIRONMENT=development

# Platform
PLATFORM_NAME=AI Agent Platform
PLATFORM_VERSION=2.0.0

# Docker
DOCKER_HOST=unix:///var/run/docker.sock

# Deployment
DEFAULT_PORT_RANGE_START=30000
DEFAULT_PORT_RANGE_END=32000
EOF
        print_warning "Created .env file - please update with your actual API keys!"
    fi
    
    print_success "Backend setup completed!"
    cd ..
}

# Set up Next.js frontend
setup_frontend() {
    print_status "Setting up Next.js frontend..."
    
    cd frontend
    
    # Install dependencies
    if [[ -f "package.json" ]]; then
        print_status "Installing Node.js dependencies..."
        npm install
    else
        print_error "package.json not found in frontend directory"
        exit 1
    fi
    
    # Create .env.local file if it doesn't exist
    if [[ ! -f ".env.local" ]]; then
        print_status "Creating .env.local file..."
        cat > .env.local << EOF
# AI Agent Platform - Frontend Environment Variables

# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_WS_URL=ws://localhost:8000/ws

# App Configuration
NEXT_PUBLIC_APP_NAME=AI Agent Platform
NEXT_PUBLIC_APP_VERSION=2.0.0
NEXT_PUBLIC_ENVIRONMENT=development

# Feature Flags
NEXT_PUBLIC_ENABLE_DEVTOOLS=true
NEXT_PUBLIC_ENABLE_DEBUG=true

# Authentication (if using external auth)
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret_here
EOF
        print_success "Created .env.local file"
    fi
    
    print_success "Frontend setup completed!"
    cd ..
}

# Set up Docker services
setup_docker() {
    print_status "Setting up Docker services..."
    
    if ! command -v docker &> /dev/null; then
        print_warning "Docker not found, skipping Docker setup"
        return
    fi
    
    # Create docker-compose.yml for development services
    if [[ ! -f "docker-compose.dev.yml" ]]; then
        print_status "Creating docker-compose.dev.yml..."
        cat > docker-compose.dev.yml << EOF
version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: ai-platform-postgres
    environment:
      POSTGRES_DB: ai_agent_platform
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: ai-platform-redis
    ports:
      - "6379:6379"
    restart: unless-stopped

  prometheus:
    image: prom/prometheus:latest
    container_name: ai-platform-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    container_name: ai-platform-grafana
    ports:
      - "3001:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin
    volumes:
      - grafana_data:/var/lib/grafana
    restart: unless-stopped

volumes:
  postgres_data:
  grafana_data:

networks:
  default:
    name: ai-platform-network
EOF
    fi
    
    # Create Prometheus configuration
    mkdir -p monitoring
    if [[ ! -f "monitoring/prometheus.yml" ]]; then
        print_status "Creating Prometheus configuration..."
        cat > monitoring/prometheus.yml << EOF
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: 'ai-platform-backend'
    static_configs:
      - targets: ['host.docker.internal:8000']
    metrics_path: '/metrics'
    scrape_interval: 5s

  - job_name: 'ai-platform-agents'
    static_configs:
      - targets: ['host.docker.internal:30000', 'host.docker.internal:30001']
    metrics_path: '/metrics'
    scrape_interval: 10s
EOF
    fi
    
    print_success "Docker configuration created!"
}

# Create development scripts
create_dev_scripts() {
    print_status "Creating development scripts..."
    
    mkdir -p scripts
    
    # Backend start script
    cat > scripts/start-backend.sh << 'EOF'
#!/bin/bash
echo "🚀 Starting AI Agent Platform Backend"
cd backend
source venv/bin/activate
uvicorn src.main:app --reload --host 0.0.0.0 --port 8000
EOF
    chmod +x scripts/start-backend.sh
    
    # Frontend start script
    cat > scripts/start-frontend.sh << 'EOF'
#!/bin/bash
echo "🚀 Starting AI Agent Platform Frontend"
cd frontend
npm run dev
EOF
    chmod +x scripts/start-frontend.sh
    
    # Full development start script
    cat > scripts/start-dev.sh << 'EOF'
#!/bin/bash
echo "🚀 Starting AI Agent Platform - Full Development Environment"

# Start Docker services
echo "Starting Docker services..."
docker-compose -f docker-compose.dev.yml up -d

# Wait for services to be ready
echo "Waiting for services to start..."
sleep 5

# Start backend in background
echo "Starting backend..."
cd backend
source venv/bin/activate
uvicorn src.main:app --reload --host 0.0.0.0 --port 8000 &
BACKEND_PID=$!

# Start frontend
echo "Starting frontend..."
cd ../frontend
npm run dev &
FRONTEND_PID=$!

echo "🎉 Development environment started!"
echo "Backend: http://localhost:8000"
echo "Frontend: http://localhost:3000"
echo "Postgres: localhost:5432"
echo "Redis: localhost:6379"
echo "Prometheus: http://localhost:9090"
echo "Grafana: http://localhost:3001 (admin/admin)"

# Handle cleanup
cleanup() {
    echo "Shutting down services..."
    kill $BACKEND_PID $FRONTEND_PID 2>/dev/null
    docker-compose -f docker-compose.dev.yml down
    exit 0
}

trap cleanup SIGINT SIGTERM

# Wait for processes
wait
EOF
    chmod +x scripts/start-dev.sh
    
    # Database setup script
    cat > scripts/setup-db.sh << 'EOF'
#!/bin/bash
echo "🗄️ Setting up AI Agent Platform Database"

# Start PostgreSQL if not running
docker-compose -f docker-compose.dev.yml up -d postgres

# Wait for PostgreSQL
echo "Waiting for PostgreSQL to start..."
sleep 5

# Run migrations (if using Alembic)
cd backend
source venv/bin/activate

if [ -f "alembic.ini" ]; then
    echo "Running database migrations..."
    alembic upgrade head
else
    echo "No migrations found, creating database schema..."
    python -c "
from src.database import engine, Base
Base.metadata.create_all(engine)
print('Database schema created!')
"
fi

echo "✅ Database setup completed!"
EOF
    chmod +x scripts/setup-db.sh
    
    # Test script
    cat > scripts/run-tests.sh << 'EOF'
#!/bin/bash
echo "🧪 Running AI Agent Platform Tests"

# Backend tests
echo "Running backend tests..."
cd backend
source venv/bin/activate
pytest tests/ -v

# Frontend tests (if configured)
echo "Running frontend tests..."
cd ../frontend
if [ -f "jest.config.js" ] || [ -f "vitest.config.ts" ]; then
    npm test
else
    echo "No frontend tests configured"
fi

echo "✅ Tests completed!"
EOF
    chmod +x scripts/run-tests.sh
    
    # Code quality script
    cat > scripts/check-quality.sh << 'EOF'
#!/bin/bash
echo "✨ Running Code Quality Checks"

# Backend linting
echo "Checking backend code quality..."
cd backend
source venv/bin/activate

# Install development tools if needed
pip install black flake8 mypy isort

# Format code
echo "Formatting Python code..."
black src/ tests/
isort src/ tests/

# Lint code
echo "Linting Python code..."
flake8 src/ tests/

# Type checking
echo "Type checking Python code..."
mypy src/

# Frontend linting
echo "Checking frontend code quality..."
cd ../frontend
npm run lint 2>/dev/null || echo "Frontend linting not configured"
npm run type-check 2>/dev/null || echo "Frontend type checking not configured"

echo "✅ Code quality checks completed!"
EOF
    chmod +x scripts/check-quality.sh
    
    print_success "Development scripts created!"
}

# Main setup function
main() {
    print_status "Starting AI Agent Platform development setup..."
    
    # Check if we're in the right directory
    if [[ ! -d "backend" ]] || [[ ! -d "frontend" ]]; then
        print_error "Please run this script from the project root directory"
        exit 1
    fi
    
    check_requirements
    setup_backend
    setup_frontend
    setup_docker
    create_dev_scripts
    
    print_success "🎉 Development setup completed successfully!"
    echo
    echo "Next steps:"
    echo "1. Update API keys in backend/.env"
    echo "2. Start development environment: ./scripts/start-dev.sh"
    echo "3. Or start services individually:"
    echo "   - Backend: ./scripts/start-backend.sh"
    echo "   - Frontend: ./scripts/start-frontend.sh"
    echo "   - Database: ./scripts/setup-db.sh"
    echo
    echo "Available scripts:"
    echo "- ./scripts/start-dev.sh      - Start full development environment"
    echo "- ./scripts/setup-db.sh       - Setup database"
    echo "- ./scripts/run-tests.sh      - Run all tests"
    echo "- ./scripts/check-quality.sh  - Check code quality"
    echo
    echo "Documentation: Check the README.md for more information"
}

# Run main function
main "$@"