# Production Environment Terraform Configuration
# This configuration deploys the platform to the production environment

terraform {
  required_version = ">= 1.5"
  
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
    random = {
      source  = "hashicorp/random"
      version = "~> 3.1"
    }
  }

  backend "gcs" {
    bucket = "twodot-platform-terraform-state-prod"  # GCS bucket for Terraform state
    prefix = "terraform/state"
  }
}

# Configure the Google Cloud Provider
provider "google" {
  project = var.project_id
  region  = var.region
  zone    = var.zone
}

# Enable required Google Cloud APIs
resource "google_project_service" "required_apis" {
  for_each = toset([
    "compute.googleapis.com",
    "container.googleapis.com",
    "containerregistry.googleapis.com",
    "sqladmin.googleapis.com",
    "servicenetworking.googleapis.com",
    "cloudresourcemanager.googleapis.com",
    "iam.googleapis.com",
    "secretmanager.googleapis.com",
    "artifactregistry.googleapis.com",
    "cloudbuild.googleapis.com",
    "storage.googleapis.com",
    "cloudapis.googleapis.com",
    "serviceusage.googleapis.com",
    "dns.googleapis.com",
    "monitoring.googleapis.com",
    "logging.googleapis.com",
    "iap.googleapis.com",
  ])
  
  service = each.value
  
  disable_dependent_services = true
  disable_on_destroy        = false
}

# Local values for production environment
locals {
  environment = "prod"
  project_name = var.project_name
  
  # Production-specific configurations
  machine_types = {
    compute = "e2-standard-2"    # Production sizing
    database = "db-custom-2-7680"  # Production database (2 vCPUs, 7.5GB RAM)
  }
  
  # Networking
  vpc_cidr = "10.1.0.0/16"
  public_subnet_cidr = "10.1.1.0/24"
  private_subnet_cidr = "10.1.2.0/24"
  
  # Database settings
  database_config = {
    postgres_version = "POSTGRES_16"  # Use supported version
    disk_size_gb = 100
    max_disk_size_gb = 500
    backup_retention_days = 30
    availability_type = "REGIONAL"  # High availability
    deletion_protection = true  # Protect production data
  }
}

# VPC Network
module "network" {
  source = "../../modules/network"
  
  project_name = local.project_name
  region = var.region
  
  public_subnet_cidr = local.public_subnet_cidr
  private_subnet_cidr = local.private_subnet_cidr
  
  # Allow SSH access for administration and deployment
  ssh_source_ranges = ["0.0.0.0/0"]  # For deployment automation - restrict in production if needed
  
  depends_on = [google_project_service.required_apis]
}

# Database
module "database" {
  source = "../../modules/database"
  
  project_name = local.project_name
  environment = local.environment
  region = var.region
  
  vpc_network = module.network.vpc_self_link
  
  postgres_version = local.database_config.postgres_version
  instance_tier = local.machine_types.database
  availability_type = local.database_config.availability_type
  disk_size_gb = local.database_config.disk_size_gb
  max_disk_size_gb = local.database_config.max_disk_size_gb
  backup_retention_days = local.database_config.backup_retention_days
  deletion_protection = local.database_config.deletion_protection
  
  database_name = "platform_prod_db"
  database_user = "platform_prod_user"
  
  # Disable migration automation in production - require manual approval
  enable_migration_automation = false
  github_repo_owner = var.github_repo_owner
  github_repo_name = var.github_repo_name
  migration_branch = "main"
  
  depends_on = [google_project_service.required_apis]
}

# OAuth Secrets (with destroy protection)
module "secrets" {
  source = "../../modules/secrets"
  
  project_id = var.project_id
  environment = local.environment
  
  depends_on = [google_project_service.required_apis]
}

# Artifact Registry
module "registry" {
  source = "../../modules/registry"
  
  project_id = var.project_id
  project_name = local.project_name
  region = var.region
  
  depends_on = [google_project_service.required_apis]
}

# Cloud Storage for static files
module "storage" {
  source = "../../modules/storage"
  
  project_name = local.project_name
  environment = local.environment
  bucket_location = "australia-southeast1"  # Same region as compute
  
  # Enable public access for load balancer to serve files
  enable_public_access = true
  
  # Fix lifecycle rules for production
  lifecycle_rules = [
    {
      action                = "SetStorageClass"
      age_days              = 30
      target_storage_class  = "NEARLINE"
      matches_storage_class = ["STANDARD"]
    },
    {
      action                = "SetStorageClass"
      age_days              = 90
      target_storage_class  = "COLDLINE"
      matches_storage_class = ["NEARLINE"]
    }
  ]
  
  depends_on = [google_project_service.required_apis]
}

# Compute Engine instance
module "compute" {
  source = "../../modules/compute"
  
  project_id = var.project_id
  project_name = local.project_name
  environment = local.environment
  region = var.region
  zone = var.zone
  
  machine_type = local.machine_types.compute
  boot_disk_size_gb = 50
  docker_volumes_disk_size_gb = 100
  
  subnet_name = module.network.private_subnet_name
  assign_external_ip = true  # Production needs external IP for load balancer
  create_static_ip = false
  
  # Registry configuration
  registry_url = module.registry.registry_url
  
  # Database configuration
  database_host = module.database.instance_private_ip
  database_name = module.database.database_name
  database_user = module.database.database_user
  database_password_secret = module.database.database_password_secret_name
  
  # OAuth configuration
  google_oauth_client_id_secret = module.secrets.oauth_secrets.client_id
  google_oauth_client_secret_secret = module.secrets.oauth_secrets.client_secret
  oauth_state_secret = module.secrets.oauth_secrets.state_secret
  
  # Service ports
  auth_service_port = 8004
  crm_service_port = 8003
  gateway_port = 8085
  
  # Environment variables
  frontend_domain = "twodot.ai"
  api_domain = "api.twodot.ai"
  ssl_domains = var.ssl_domains
  
  depends_on = [google_project_service.required_apis]
}

# Load Balancer
module "loadbalancer" {
  source = "../../modules/loadbalancer"
  
  project_name = local.project_name
  environment = local.environment
  
  storage_bucket_name = module.storage.bucket_name
  api_backend_service = module.compute.backend_service_self_link
  
  # Enable SSL with managed certificates
  enable_ssl = true
  use_managed_ssl = true
  ssl_domains = var.ssl_domains
  
  depends_on = [google_project_service.required_apis]
}