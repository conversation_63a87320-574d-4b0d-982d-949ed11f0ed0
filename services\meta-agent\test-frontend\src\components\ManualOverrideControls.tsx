import { h, FunctionalComponent } from 'preact';
import { useState } from 'preact/hooks';
import { api } from '@/services/api';

interface ManualOverrideControlsProps {
  /** Callback function to be executed when automation is paused */
  onPause?: () => void;
  /** Callback function to be executed when automation is resumed */
  onResume?: () => void;
  /** Callback function to be executed when escalation is triggered */
  onEscalate?: () => void;
}

/**
 * Provides manual controls for overriding automated actions.
 */
const ManualOverrideControls: FunctionalComponent<ManualOverrideControlsProps> = ({ onPause, onResume, onEscalate }) => {
  const [isPaused, setIsPaused] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handlePauseResume = async () => {
    setIsLoading(true);
    setError(null);
    try {
      if (isPaused) {
        // Placeholder for resume API call if needed
        // await api.resumeAutomation();
        if (onResume) onResume();
      } else {
        // Placeholder for pause API call if needed
        // await api.pauseAutomation();
        if (onPause) onPause();
      }
      setIsPaused(!isPaused);
    } catch (err) {
      setError('An error occurred.');
      console.error(err); // Log the error for debugging
    } finally {
      setIsLoading(false);
    }
  };

  const handleEscalate = async () => {
    setIsLoading(true);
    setError(null);
    try {
      // Placeholder for escalation API call
      // await api.escalateIssue();
      if (onEscalate) onEscalate();
    } catch (err) {
      setError('An error occurred during escalation.');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div class="flex flex-col sm:flex-row items-center justify-center space-y-2 sm:space-y-0 sm:space-x-4">
      <button
        class={`px-4 py-2 rounded ${isPaused ? 'bg-green-500 text-white' : 'bg-blue-500 text-white'} ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
        onClick={handlePauseResume}
        disabled={isLoading}
      >
        {isLoading ? 'Loading...' : isPaused ? 'Resume' : 'Pause'}
      </button>
      <button
        class={`px-4 py-2 rounded bg-red-500 text-white ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
        onClick={handleEscalate}
        disabled={isLoading}
      >
        {isLoading ? 'Loading...' : 'Escalate'}
      </button>
      {error && <p class="text-red-500 text-sm">{error}</p>}
    </div>
  );
};

export default ManualOverrideControls;
