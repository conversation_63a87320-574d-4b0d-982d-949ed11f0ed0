/* tslint:disable */
/* eslint-disable */
/**
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  ErrorResponse,
  Interaction,
  InteractionCreate,
  InteractionDetails,
  InteractionUpdate,
  InteractionsGet200Response,
  ValidationError,
} from '../models/index';
import {
    ErrorResponseFromJSON,
    ErrorResponseToJSON,
    InteractionFromJSON,
    InteractionToJSON,
    InteractionCreateFromJSON,
    InteractionCreateToJSON,
    InteractionDetailsFromJSON,
    InteractionDetailsToJSON,
    InteractionUpdateFromJSON,
    InteractionUpdateToJSON,
    InteractionsGet200ResponseFromJSON,
    InteractionsGet200ResponseToJSON,
    ValidationErrorFromJSON,
    ValidationErrorToJSON,
} from '../models/index';

export interface InteractionsGetRequest {
    companyId?: string;
    contactId?: string;
    interactionType?: string;
    fromDate?: Date;
    toDate?: Date;
    limit?: number;
    offset?: number;
}

export interface InteractionsIdDeleteRequest {
    id: string;
}

export interface InteractionsIdGetRequest {
    id: string;
}

export interface InteractionsIdPutRequest {
    id: string;
    interactionUpdate: InteractionUpdate;
}

export interface InteractionsPostRequest {
    interactionCreate: InteractionCreate;
}

/**
 * 
 */
export class InteractionsApi extends runtime.BaseAPI {

    /**
     * Retrieve a list of all interactions
     * List interactions
     */
    async interactionsGetRaw(requestParameters: InteractionsGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<InteractionsGet200Response>> {
        const queryParameters: any = {};

        if (requestParameters['companyId'] != null) {
            queryParameters['company_id'] = requestParameters['companyId'];
        }

        if (requestParameters['contactId'] != null) {
            queryParameters['contact_id'] = requestParameters['contactId'];
        }

        if (requestParameters['interactionType'] != null) {
            queryParameters['interaction_type'] = requestParameters['interactionType'];
        }

        if (requestParameters['fromDate'] != null) {
            queryParameters['from_date'] = (requestParameters['fromDate'] as any).toISOString();
        }

        if (requestParameters['toDate'] != null) {
            queryParameters['to_date'] = (requestParameters['toDate'] as any).toISOString();
        }

        if (requestParameters['limit'] != null) {
            queryParameters['limit'] = requestParameters['limit'];
        }

        if (requestParameters['offset'] != null) {
            queryParameters['offset'] = requestParameters['offset'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("bearerAuth", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/interactions`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => InteractionsGet200ResponseFromJSON(jsonValue));
    }

    /**
     * Retrieve a list of all interactions
     * List interactions
     */
    async interactionsGet(requestParameters: InteractionsGetRequest = {}, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<InteractionsGet200Response> {
        const response = await this.interactionsGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Delete an interaction record
     * Delete interaction
     */
    async interactionsIdDeleteRaw(requestParameters: InteractionsIdDeleteRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<void>> {
        if (requestParameters['id'] == null) {
            throw new runtime.RequiredError(
                'id',
                'Required parameter "id" was null or undefined when calling interactionsIdDelete().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("bearerAuth", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/interactions/{id}`.replace(`{${"id"}}`, encodeURIComponent(String(requestParameters['id']))),
            method: 'DELETE',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.VoidApiResponse(response);
    }

    /**
     * Delete an interaction record
     * Delete interaction
     */
    async interactionsIdDelete(requestParameters: InteractionsIdDeleteRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void> {
        await this.interactionsIdDeleteRaw(requestParameters, initOverrides);
    }

    /**
     * Retrieve a specific interaction by ID
     * Get interaction
     */
    async interactionsIdGetRaw(requestParameters: InteractionsIdGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<InteractionDetails>> {
        if (requestParameters['id'] == null) {
            throw new runtime.RequiredError(
                'id',
                'Required parameter "id" was null or undefined when calling interactionsIdGet().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("bearerAuth", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/interactions/{id}`.replace(`{${"id"}}`, encodeURIComponent(String(requestParameters['id']))),
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => InteractionDetailsFromJSON(jsonValue));
    }

    /**
     * Retrieve a specific interaction by ID
     * Get interaction
     */
    async interactionsIdGet(requestParameters: InteractionsIdGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<InteractionDetails> {
        const response = await this.interactionsIdGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Update an existing interaction
     * Update interaction
     */
    async interactionsIdPutRaw(requestParameters: InteractionsIdPutRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Interaction>> {
        if (requestParameters['id'] == null) {
            throw new runtime.RequiredError(
                'id',
                'Required parameter "id" was null or undefined when calling interactionsIdPut().'
            );
        }

        if (requestParameters['interactionUpdate'] == null) {
            throw new runtime.RequiredError(
                'interactionUpdate',
                'Required parameter "interactionUpdate" was null or undefined when calling interactionsIdPut().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("bearerAuth", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/interactions/{id}`.replace(`{${"id"}}`, encodeURIComponent(String(requestParameters['id']))),
            method: 'PUT',
            headers: headerParameters,
            query: queryParameters,
            body: InteractionUpdateToJSON(requestParameters['interactionUpdate']),
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => InteractionFromJSON(jsonValue));
    }

    /**
     * Update an existing interaction
     * Update interaction
     */
    async interactionsIdPut(requestParameters: InteractionsIdPutRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Interaction> {
        const response = await this.interactionsIdPutRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Create a new interaction record
     * Create interaction
     */
    async interactionsPostRaw(requestParameters: InteractionsPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Interaction>> {
        if (requestParameters['interactionCreate'] == null) {
            throw new runtime.RequiredError(
                'interactionCreate',
                'Required parameter "interactionCreate" was null or undefined when calling interactionsPost().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("bearerAuth", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/interactions`,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: InteractionCreateToJSON(requestParameters['interactionCreate']),
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => InteractionFromJSON(jsonValue));
    }

    /**
     * Create a new interaction record
     * Create interaction
     */
    async interactionsPost(requestParameters: InteractionsPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Interaction> {
        const response = await this.interactionsPostRaw(requestParameters, initOverrides);
        return await response.value();
    }

}
