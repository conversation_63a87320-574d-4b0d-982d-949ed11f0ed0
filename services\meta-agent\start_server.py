#!/usr/bin/env python3
"""
Minimal startup script for AI Agent Platform backend
"""
import os
import sys
import asyncio
import signal
from pathlib import Path

# Add backend src to path
backend_path = Path(__file__).parent / "backend" / "src"
sys.path.insert(0, str(backend_path))

# Set required environment variables
os.environ.setdefault('DATABASE_URL', 'sqlite:///ai_agent_platform.db')
os.environ.setdefault('ENVIRONMENT', 'development')
os.environ.setdefault('DEBUG', 'true')
os.environ.setdefault('SECRET_KEY', 'dev-secret-key-for-testing')
os.environ.setdefault('CORS_ORIGINS', '["http://localhost:3000","http://localhost:3001","http://localhost:5173"]')
os.environ.setdefault('HOST', '0.0.0.0')
os.environ.setdefault('PORT', '8000')

def signal_handler(signum, frame):
    print(f"\nReceived signal {signum}, shutting down...")
    sys.exit(0)

# Set up signal handlers
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

try:
    print("Starting AI Agent Platform backend...")
    print(f"Backend src path: {backend_path}")
    
    # Change to backend directory
    os.chdir(str(Path(__file__).parent / "backend"))
    
    # Try to import and run the main app
    try:
        import uvicorn
        print("Starting server with uvicorn...")
        uvicorn.run(
            "src.main:app",
            host="0.0.0.0",
            port=8000,
            reload=True,
            log_level="info"
        )
    except ImportError:
        print("uvicorn not found, trying direct import...")
        from src.main import app
        print("App imported successfully, but need uvicorn to serve it")
        
except Exception as e:
    print(f"Error starting server: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)