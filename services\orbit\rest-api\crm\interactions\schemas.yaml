# Interaction schemas
Interaction:
  type: object
  properties:
    id:
      type: string
      format: uuid
    company_id:
      type: string
      format: uuid
      nullable: true
    contact_id:
      type: string
      format: uuid
      nullable: true
    interaction_type:
      type: string
      enum: ["email", "phone", "meeting", "demo", "proposal", "follow-up", "other"]
    notes:
      type: string
    interaction_datetime:
      type: string
      format: date-time
    created_by:
      type: string
      format: uuid
    created_at:
      type: string
      format: date-time
    updated_at:
      type: string
      format: date-time

InteractionWithDetails:
  allOf:
    - $ref: '#/Interaction'
    - type: object
      properties:
        company:
          type: object
          properties:
            id:
              type: string
              format: uuid
            name:
              type: string
          nullable: true
        contact:
          type: object
          properties:
            id:
              type: string
              format: uuid
            first_name:
              type: string
            last_name:
              type: string
            email:
              type: string
          nullable: true

InteractionDetails:
  allOf:
    - $ref: '#/Interaction'
    - type: object
      properties:
        company:
          type: object
          properties:
            id:
              type: string
              format: uuid
            name:
              type: string
            website:
              type: string
            phone:
              type: string
          nullable: true
        contact:
          type: object
          properties:
            id:
              type: string
              format: uuid
            first_name:
              type: string
            last_name:
              type: string
            email:
              type: string
            phone:
              type: string
            job_title:
              type: string
          nullable: true

InteractionCreate:
  type: object
  properties:
    company_id:
      type: string
      format: uuid
    contact_id:
      type: string
      format: uuid
    interaction_type:
      type: string
      enum: ["email", "phone", "meeting", "demo", "proposal", "follow-up", "other"]
    notes:
      type: string
      minLength: 1
    interaction_datetime:
      type: string
      format: date-time
  required:
    - interaction_type
    - notes
    - interaction_datetime

InteractionUpdate:
  type: object
  properties:
    interaction_type:
      type: string
      enum: ["email", "phone", "meeting", "demo", "proposal", "follow-up", "other"]
    notes:
      type: string
      minLength: 1
    interaction_datetime:
      type: string
      format: date-time
    company_id:
      type: string
      format: uuid
    contact_id:
      type: string
      format: uuid