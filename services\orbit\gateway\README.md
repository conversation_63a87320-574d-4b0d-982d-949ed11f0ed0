# Gateway Service

A Go-based API gateway that routes REST API traffic to appropriate microservices based on the OpenAPI specification.

## Overview

The gateway service acts as a single entry point for all API requests, routing them to the appropriate backend services:

- **crm_backend**: Handles all CRM-related endpoints (companies, contacts, deals, interactions, arli)
- **auth**: Future authentication service (currently handled by crm_backend)

## Architecture

```
Client -> Gateway (8085) -> Services
                   |
                   ├── crm_backend (8003) - /api/v1/companies, /api/v1/contacts, etc.
                   └── auth (8004) - Future /api/v1/auth endpoints
```

## Configuration

The gateway uses environment variables for configuration:

- `GATEWAY_PORT`: Gateway listening port (default: 8085)
- `CRM_BACKEND_URL`: CRM backend service URL (default: http://localhost:8003)
- `AUTH_SERVICE_URL`: Auth service URL (default: http://localhost:8004)
- `ENVIRONMENT`: Environment (development/production)
- `LOG_LEVEL`: Logging level (info/debug/error)

## API Routes

All routes under `/api/v1/*` are currently routed to the crm_backend service:

### Authentication Routes
- `GET /api/v1/auth/session`
- `POST /api/v1/auth/signin`
- `POST /api/v1/auth/signin/oauth`
- `POST /api/v1/auth/signout`
- `GET /api/v1/auth/user`

### User Routes
- `GET /api/v1/users/profile`
- `PUT /api/v1/users/profile`

### Company Routes
- `GET /api/v1/companies`
- `POST /api/v1/companies`
- `GET /api/v1/companies/{id}`
- `PUT /api/v1/companies/{id}`
- `DELETE /api/v1/companies/{id}`
- `PUT /api/v1/companies/{id}/status`
- `GET /api/v1/company-statuses`

### Contact Routes
- `GET /api/v1/contacts`
- `POST /api/v1/contacts`
- `GET /api/v1/contacts/{id}`
- `PUT /api/v1/contacts/{id}`
- `DELETE /api/v1/contacts/{id}`
- `PUT /api/v1/contacts/{id}/company`
- `DELETE /api/v1/contacts/{id}/company`

### Deal Routes
- `GET /api/v1/deals`
- `POST /api/v1/deals`
- `GET /api/v1/deals/{id}`
- `PUT /api/v1/deals/{id}`
- `DELETE /api/v1/deals/{id}`
- `GET /api/v1/deal-stages`

### Interaction Routes
- `GET /api/v1/interactions`
- `POST /api/v1/interactions`
- `GET /api/v1/interactions/{id}`
- `PUT /api/v1/interactions/{id}`
- `DELETE /api/v1/interactions/{id}`

### Arli AI Routes
- `GET /api/v1/arli/documents`
- `POST /api/v1/arli/documents`
- `GET /api/v1/arli/documents/{id}`
- And other Arli endpoints...

## Building and Running

### Using Bazel

```bash
# Build the gateway
bazel build //platform/gateway:gateway

# Run the gateway
bazel run //platform/gateway:gateway
```

### Using Go directly

```bash
cd gateway
go run main.go
```

## Health Check

The gateway provides a health check endpoint:

```bash
GET /health
```

Response:
```json
{
  "status": "healthy",
  "service": "gateway",
  "version": "1.0.0"
}
```

## Future Enhancements

1. **Service Discovery**: Add automatic service discovery
2. **Load Balancing**: Add load balancing for multiple service instances
3. **Rate Limiting**: Add rate limiting middleware
4. **Authentication**: Add gateway-level authentication
5. **Metrics**: Add Prometheus metrics
6. **Circuit Breaker**: Add circuit breaker pattern for resilience
7. **Caching**: Add response caching for read operations