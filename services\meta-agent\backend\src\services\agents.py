"""
AI Agent Platform - Agent Service Layer
"""

from typing import List, Optional, Dict, Any
from uuid import UUID
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, func
from sqlalchemy.orm import selectinload
import structlog

from database.models import Agent, AgentStatus, User
from database.connection import get_db
from agents.manager import agent_manager

logger = structlog.get_logger()


class AgentService:
    """Service for managing AI agents"""
    
    def __init__(self, db_session: AsyncSession):
        self.db = db_session
    
    async def create_agent(
        self,
        owner_id: UUID,
        name: str,
        description: Optional[str] = None,
        agent_type: str = "assistant",
        config: Dict[str, Any] = None,
        capabilities: List[str] = None
    ) -> Agent:
        """Create a new agent"""
        try:
            agent = Agent(
                name=name,
                description=description,
                type=agent_type,
                owner_id=owner_id,
                config=config or {},
                capabilities=capabilities or [],
                status=AgentStatus.CREATED
            )
            
            self.db.add(agent)
            await self.db.commit()
            # await self.db.refresh(agent)  # Temporarily disabled due to type conversion issue
            
            logger.info(
                "Agent created successfully",
                agent_id=str(agent.id),
                name=name,
                owner_id=str(owner_id)
            )
            
            return agent
            
        except Exception as e:
            await self.db.rollback()
            logger.error("Failed to create agent", error=str(e))
            raise
    
    async def get_agent(self, agent_id: UUID) -> Optional[Agent]:
        """Get agent by ID"""
        try:
            result = await self.db.execute(
                select(Agent)
                .options(selectinload(Agent.owner))
                .where(Agent.id == agent_id)
            )
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error("Failed to get agent", agent_id=str(agent_id), error=str(e))
            raise
    
    async def list_agents(
        self,
        owner_id: Optional[UUID] = None,
        status: Optional[AgentStatus] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[Agent]:
        """List agents with filters"""
        try:
            query = select(Agent).options(selectinload(Agent.owner))
            
            if owner_id:
                query = query.where(Agent.owner_id == owner_id)
            
            if status:
                query = query.where(Agent.status == status)
            
            query = query.limit(limit).offset(offset)
            
            result = await self.db.execute(query)
            return list(result.scalars().all())
            
        except Exception as e:
            logger.error("Failed to list agents", error=str(e))
            raise
    
    async def count_agents(
        self,
        owner_id: Optional[UUID] = None,
        status: Optional[AgentStatus] = None
    ) -> int:
        """Count agents with filters"""
        try:
            query = select(func.count(Agent.id))
            
            if owner_id:
                query = query.where(Agent.owner_id == owner_id)
            
            if status:
                query = query.where(Agent.status == status)
            
            result = await self.db.execute(query)
            return result.scalar() or 0
            
        except Exception as e:
            logger.error("Failed to count agents", error=str(e))
            raise
    
    async def update_agent(
        self,
        agent_id: UUID,
        **updates
    ) -> Optional[Agent]:
        """Update agent"""
        try:
            result = await self.db.execute(
                update(Agent)
                .where(Agent.id == agent_id)
                .values(**updates)
                .returning(Agent)
            )
            
            agent = result.scalar_one_or_none()
            if agent:
                await self.db.commit()
                logger.info("Agent updated", agent_id=str(agent_id))
            
            return agent
            
        except Exception as e:
            await self.db.rollback()
            logger.error("Failed to update agent", agent_id=str(agent_id), error=str(e))
            raise
    
    async def start_agent(self, agent_id: UUID) -> bool:
        """Start an agent"""
        try:
            agent = await self.get_agent(agent_id)
            if not agent:
                return False
            
            if agent.status not in [AgentStatus.CREATED, AgentStatus.STOPPED]:
                logger.warning(
                    "Cannot start agent - invalid status",
                    agent_id=str(agent_id),
                    current_status=agent.status
                )
                return False
            
            # Update status to starting
            await self.update_agent(agent_id, status=AgentStatus.STARTING)
            
            # Start agent runtime using agent manager
            agent_obj = await self.get_agent(agent_id)
            if not agent_obj:
                logger.error("Agent not found for startup", agent_id=str(agent_id))
                return False
            
            # Use agent manager to start the runtime
            runtime_started = await agent_manager.start_agent(agent_obj)
            
            if runtime_started:
                await self.update_agent(agent_id, status=AgentStatus.RUNNING)
            else:
                await self.update_agent(agent_id, status=AgentStatus.ERROR)
                return False
            
            logger.info("Agent started successfully", agent_id=str(agent_id))
            return True
            
        except Exception as e:
            logger.error("Failed to start agent", agent_id=str(agent_id), error=str(e))
            return False
    
    async def stop_agent(self, agent_id: UUID) -> bool:
        """Stop an agent"""
        try:
            agent = await self.get_agent(agent_id)
            if not agent:
                return False
            
            if agent.status not in [AgentStatus.RUNNING, AgentStatus.PAUSED]:
                logger.warning(
                    "Cannot stop agent - invalid status",
                    agent_id=str(agent_id),
                    current_status=agent.status
                )
                return False
            
            # Stop agent runtime using agent manager
            runtime_stopped = await agent_manager.stop_agent(agent_id, graceful=True)
            
            if runtime_stopped:
                await self.update_agent(agent_id, status=AgentStatus.STOPPED)
            else:
                logger.error("Failed to stop agent runtime", agent_id=str(agent_id))
                return False
            
            logger.info("Agent stopped successfully", agent_id=str(agent_id))
            return True
            
        except Exception as e:
            logger.error("Failed to stop agent", agent_id=str(agent_id), error=str(e))
            return False
    
    async def delete_agent(self, agent_id: UUID) -> bool:
        """Delete an agent"""
        try:
            # First stop the agent if running
            agent = await self.get_agent(agent_id)
            if agent and agent.status == AgentStatus.RUNNING:
                await self.stop_agent(agent_id)
            
            # Delete the agent
            result = await self.db.execute(
                delete(Agent).where(Agent.id == agent_id)
            )
            
            if result.rowcount > 0:
                await self.db.commit()
                logger.info("Agent deleted successfully", agent_id=str(agent_id))
                return True
            
            return False
            
        except Exception as e:
            await self.db.rollback()
            logger.error("Failed to delete agent", agent_id=str(agent_id), error=str(e))
            raise
    
    async def update_heartbeat(self, agent_id: UUID) -> bool:
        """Update agent heartbeat"""
        try:
            from datetime import datetime
            
            result = await self.db.execute(
                update(Agent)
                .where(Agent.id == agent_id)
                .values(last_heartbeat=datetime.utcnow())
            )
            
            if result.rowcount > 0:
                await self.db.commit()
                return True
            
            return False
            
        except Exception as e:
            logger.error("Failed to update heartbeat", agent_id=str(agent_id), error=str(e))
            return False