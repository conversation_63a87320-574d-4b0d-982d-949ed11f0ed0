/* tslint:disable */
/* eslint-disable */
/**
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface DocumentCreate
 */
export interface DocumentCreate {
    /**
     * 
     * @type {string}
     * @memberof DocumentCreate
     */
    content: string;
    /**
     * 
     * @type {object}
     * @memberof DocumentCreate
     */
    metadata?: object;
    /**
     * 
     * @type {Array<string>}
     * @memberof DocumentCreate
     */
    filterTags?: Array<string>;
    /**
     * 
     * @type {string}
     * @memberof DocumentCreate
     */
    sourceId?: string;
}

/**
 * Check if a given object implements the DocumentCreate interface.
 */
export function instanceOfDocumentCreate(value: object): value is DocumentCreate {
    if (!('content' in value) || value['content'] === undefined) return false;
    return true;
}

export function DocumentCreateFromJSON(json: any): DocumentCreate {
    return DocumentCreateFromJSONTyped(json, false);
}

export function DocumentCreateFromJSONTyped(json: any, ignoreDiscriminator: boolean): DocumentCreate {
    if (json == null) {
        return json;
    }
    return {
        
        'content': json['content'],
        'metadata': json['metadata'] == null ? undefined : json['metadata'],
        'filterTags': json['filter_tags'] == null ? undefined : json['filter_tags'],
        'sourceId': json['source_id'] == null ? undefined : json['source_id'],
    };
}

  export function DocumentCreateToJSON(json: any): DocumentCreate {
      return DocumentCreateToJSONTyped(json, false);
  }

  export function DocumentCreateToJSONTyped(value?: DocumentCreate | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'content': value['content'],
        'metadata': value['metadata'],
        'filter_tags': value['filterTags'],
        'source_id': value['sourceId'],
    };
}

