# Database Migration Automation Module
# Extends the main database module with migration automation capabilities

# Get current project information
data "google_project" "current" {}

# Service account for Cloud Build migration jobs
resource "google_service_account" "db_migration_sa" {
  count = var.enable_migration_automation ? 1 : 0
  
  account_id   = "${var.project_name}-db-migration-sa"
  display_name = "Database Migration Service Account for ${var.project_name}"
  description  = "Service account used by Cloud Build for database migrations"
}

# IAM roles for the migration service account
resource "google_project_iam_member" "migration_sa_sql_client" {
  count = var.enable_migration_automation ? 1 : 0
  
  project = data.google_project.current.project_id
  role    = "roles/cloudsql.client"
  member  = "serviceAccount:${google_service_account.db_migration_sa[0].email}"
}

resource "google_project_iam_member" "migration_sa_secret_accessor" {
  count = var.enable_migration_automation ? 1 : 0
  
  project = data.google_project.current.project_id
  role    = "roles/secretmanager.secretAccessor"
  member  = "serviceAccount:${google_service_account.db_migration_sa[0].email}"
}

resource "google_project_iam_member" "migration_sa_logging" {
  count = var.enable_migration_automation ? 1 : 0
  
  project = data.google_project.current.project_id
  role    = "roles/logging.logWriter"
  member  = "serviceAccount:${google_service_account.db_migration_sa[0].email}"
}

# Cloud Build trigger for manual migrations
resource "google_cloudbuild_trigger" "manual_migration" {
  count = var.enable_migration_automation ? 1 : 0
  
  name        = "${var.project_name}-manual-db-migration"
  description = "Manual database migration trigger for ${var.project_name}"

  # Manual trigger (no automatic triggers)
  source_to_build {
    uri       = "https://github.com/${var.github_repo_owner}/${var.github_repo_name}"
    ref       = "refs/heads/${var.migration_branch}"
    repo_type = "GITHUB"
  }

  git_file_source {
    path      = var.migration_cloudbuild_file
    uri       = "https://github.com/${var.github_repo_owner}/${var.github_repo_name}"
    revision  = "refs/heads/${var.migration_branch}"
    repo_type = "GITHUB"
  }

  substitutions = {
    _INSTANCE_CONNECTION_NAME = google_sql_database_instance.postgres.connection_name
    _DATABASE_NAME           = var.database_name
    _DATABASE_USER           = var.database_user
    _SECRET_NAME             = google_secret_manager_secret.db_password.secret_id
    _PROJECT_ID              = data.google_project.current.project_id
  }

  service_account = var.enable_migration_automation ? google_service_account.db_migration_sa[0].id : null

  depends_on = [google_project_service.cloudbuild]
}

# Cloud Storage bucket for build logs
resource "google_storage_bucket" "build_logs" {
  count = var.enable_migration_automation ? 1 : 0
  
  name     = "${var.project_name}-build-logs-${random_string.bucket_suffix[0].result}"
  location = var.region
  
  uniform_bucket_level_access = true
  public_access_prevention    = "enforced"

  lifecycle_rule {
    action {
      type = "Delete"
    }
    condition {
      age = 30  # Delete logs after 30 days
    }
  }

  versioning {
    enabled = false
  }
}

# Random suffix for bucket name uniqueness
resource "random_string" "bucket_suffix" {
  count = var.enable_migration_automation ? 1 : 0
  
  length  = 8
  special = false
  upper   = false
}

# IAM binding for Cloud Build to write logs
resource "google_storage_bucket_iam_member" "build_logs_writer" {
  count = var.enable_migration_automation ? 1 : 0
  
  bucket = google_storage_bucket.build_logs[0].name
  role   = "roles/storage.objectCreator"
  member = "serviceAccount:${google_service_account.db_migration_sa[0].email}"
}

# Cloud Scheduler job for automated migrations (optional)
resource "google_cloud_scheduler_job" "scheduled_migration" {
  count = var.enable_migration_automation && var.enable_scheduled_migrations ? 1 : 0
  
  name        = "${var.project_name}-scheduled-migration"
  description = "Scheduled database migration for ${var.project_name}"
  schedule    = var.migration_schedule
  time_zone   = var.migration_timezone

  http_target {
    http_method = "POST"
    uri         = "https://cloudbuild.googleapis.com/v1/projects/${data.google_project.current.project_id}/triggers/${google_cloudbuild_trigger.manual_migration[0].trigger_id}:run"
    
    headers = {
      "Content-Type" = "application/json"
    }

    body = base64encode(jsonencode({
      branchName = var.migration_branch
    }))

    oauth_token {
      service_account_email = google_service_account.db_migration_sa[0].email
    }
  }

  depends_on = [google_project_service.cloudscheduler]
}

# Enable Cloud Scheduler API
resource "google_project_service" "cloudscheduler" {
  count   = var.enable_migration_automation && var.enable_scheduled_migrations ? 1 : 0
  service = "cloudscheduler.googleapis.com"
  
  disable_dependent_services = true
}

# Cloud Function for migration notifications (optional)
resource "google_cloudfunctions_function" "migration_notifier" {
  count = var.enable_migration_automation && var.enable_migration_notifications ? 1 : 0
  
  name        = "${var.project_name}-migration-notifier"
  description = "Sends notifications for database migration events"
  region      = var.region

  runtime = "python39"
  
  available_memory_mb   = 128
  source_archive_bucket = google_storage_bucket.function_source[0].name
  source_archive_object = google_storage_bucket_object.function_source[0].name
  entry_point          = "migration_notification_handler"

  event_trigger {
    event_type = "providers/cloud.pubsub/eventTypes/topic.publish"
    resource   = google_pubsub_topic.migration_events[0].name
  }

  environment_variables = {
    SLACK_WEBHOOK_URL = var.slack_webhook_url
    PROJECT_ID        = data.google_project.current.project_id
  }

  depends_on = [google_project_service.cloudfunctions]
}

# Pub/Sub topic for migration events
resource "google_pubsub_topic" "migration_events" {
  count = var.enable_migration_automation && var.enable_migration_notifications ? 1 : 0
  
  name = "${var.project_name}-migration-events"
}

# Cloud Storage bucket for Cloud Function source
resource "google_storage_bucket" "function_source" {
  count = var.enable_migration_automation && var.enable_migration_notifications ? 1 : 0
  
  name     = "${var.project_name}-function-source-${random_string.bucket_suffix[0].result}"
  location = var.region
  
  uniform_bucket_level_access = true
  public_access_prevention    = "enforced"
}

# Upload Cloud Function source code
resource "google_storage_bucket_object" "function_source" {
  count = var.enable_migration_automation && var.enable_migration_notifications ? 1 : 0
  
  name   = "migration-notifier-source.zip"
  bucket = google_storage_bucket.function_source[0].name
  source = "${path.module}/migration-notifier.zip"
}

# Enable Cloud Functions API
resource "google_project_service" "cloudfunctions" {
  count   = var.enable_migration_automation && var.enable_migration_notifications ? 1 : 0
  service = "cloudfunctions.googleapis.com"
  
  disable_dependent_services = true
}