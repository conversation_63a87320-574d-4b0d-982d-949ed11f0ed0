#!/bin/bash
# Deploy to GCP bucket script

set -e

# Configuration
BUCKET_NAME="${GCP_BUCKET_NAME:-}"
PROJECT_ID="${GCP_PROJECT_ID:-}"

# Find dist directory - handle both Bazel and direct execution contexts
if [ -n "$BUILD_WORKSPACE_DIRECTORY" ]; then
    # Running in Bazel context - dist is in the workspace
    DIST_DIR="$BUILD_WORKSPACE_DIRECTORY/services/orbit/web/dist"
elif [ -d "dist" ]; then
    # Running directly from web directory
    DIST_DIR="$(pwd)/dist"
elif [ -d "services/orbit/web/dist" ]; then
    # Running from repo root
    DIST_DIR="$(pwd)/services/orbit/web/dist"
else
    # Default fallback
    DIST_DIR="$(pwd)/dist"
fi

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Helper functions
print_error() {
    echo -e "${RED}❌ Error: $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "ℹ️  $1"
}

# Check requirements
check_requirements() {
    print_info "Checking deployment requirements..."
    
    # Check if gcloud is installed
    if ! command -v gcloud &> /dev/null; then
        print_error "gcloud CLI is not installed. Please install it first:"
        echo "https://cloud.google.com/sdk/docs/install"
        exit 1
    fi
    
    # Check if gsutil is available
    if ! command -v gsutil &> /dev/null; then
        print_error "gsutil is not available. It should come with gcloud SDK."
        exit 1
    fi
    
    # Check if dist folder exists
    if [ ! -d "$DIST_DIR" ]; then
        print_error "Distribution folder not found at $DIST_DIR"
        echo "Run 'bazel run //services/orbit/web:build_dist_complete' first"
        exit 1
    fi
    
    print_success "All requirements met"
}

# Get terraform outputs for environment
get_terraform_config() {
    local env="$1"
    
    if [ -z "$env" ]; then
        return 1
    fi
    
    print_info "Getting terraform configuration for environment: $env"
    
    # Find terraform directory
    local terraform_dir=""
    if [ -n "$BUILD_WORKSPACE_DIRECTORY" ]; then
        # Running in Bazel context
        terraform_dir="$BUILD_WORKSPACE_DIRECTORY/terraform/environments"
    elif [ -d "terraform/environments" ]; then
        terraform_dir="terraform/environments"
    elif [ -d "../terraform/environments" ]; then
        terraform_dir="../terraform/environments"
    elif [ -d "../../terraform/environments" ]; then
        terraform_dir="../../terraform/environments"
    else
        print_warning "Terraform directory not found"
        return 1
    fi
    
    if [ ! -d "$terraform_dir" ]; then
        print_warning "Terraform directory not found at $terraform_dir"
        return 1
    fi
    
    # Get terraform outputs
    cd "$terraform_dir" || return 1
    
    # Initialize terraform if needed
    if [ ! -f ".terraform/environment" ] || [ "$(cat .terraform/environment 2>/dev/null)" != "$env" ]; then
        print_info "Initializing terraform for $env environment..."
        terraform init -reconfigure >/dev/null 2>&1 || return 1
        terraform workspace select "$env" >/dev/null 2>&1 || return 1
    fi
    
    # Get bucket name from terraform output (suppress warnings)
    local bucket_output
    bucket_output=$(terraform output -raw web_bucket_name 2>/dev/null | grep -v "Warning:" | grep -v "No outputs found" | head -1)
    if [ -n "$bucket_output" ] && [ "$bucket_output" != "" ]; then
        BUCKET_NAME="$bucket_output"
        print_success "Found bucket from terraform: $BUCKET_NAME"
    else
        print_warning "Could not get bucket name from terraform outputs (infrastructure may not be deployed yet)"
    fi
    
    # Get project ID from terraform output (suppress warnings)
    local project_output
    project_output=$(terraform output -raw project_id 2>/dev/null | grep -v "Warning:" | grep -v "No outputs found" | head -1)
    if [ -n "$project_output" ] && [ "$project_output" != "" ]; then
        PROJECT_ID="$project_output"
        print_success "Found project ID from terraform: $PROJECT_ID"
    else
        print_warning "Could not get project ID from terraform outputs"
        # Fallback: try to get from tfvars file
        local tfvars_file="${env}.tfvars"
        if [ -f "$tfvars_file" ]; then
            local project_from_tfvars
            project_from_tfvars=$(grep '^project_id' "$tfvars_file" | cut -d'=' -f2 | tr -d ' "' | head -1)
            if [ -n "$project_from_tfvars" ]; then
                PROJECT_ID="$project_from_tfvars"
                print_success "Found project ID from tfvars: $PROJECT_ID"
            fi
        fi
    fi
    
    cd - >/dev/null
    return 0
}

# Get configuration
get_config() {
    print_info "Getting deployment configuration..."
    
    # Try to get config from terraform if environment is specified
    if [ -n "$ENVIRONMENT" ]; then
        if get_terraform_config "$ENVIRONMENT"; then
            print_success "Using terraform configuration for $ENVIRONMENT environment"
        else
            print_warning "Could not get terraform configuration, falling back to manual input"
        fi
    fi
    
    # Get bucket name
    if [ -z "$BUCKET_NAME" ]; then
        echo "Enter GCP bucket name:"
        read -r BUCKET_NAME
        if [ -z "$BUCKET_NAME" ]; then
            print_error "Bucket name is required"
            exit 1
        fi
    fi
    
    # Get project ID
    if [ -z "$PROJECT_ID" ]; then
        # Try to get from gcloud config
        PROJECT_ID=$(gcloud config get-value project 2>/dev/null || echo "")
        if [ -z "$PROJECT_ID" ]; then
            echo "Enter GCP project ID:"
            read -r PROJECT_ID
            if [ -z "$PROJECT_ID" ]; then
                print_error "Project ID is required"
                exit 1
            fi
        fi
    fi
    
    print_info "Configuration:"
    echo "  Environment: ${ENVIRONMENT:-manual}"
    echo "  Project ID: $PROJECT_ID"
    echo "  Bucket Name: $BUCKET_NAME"
    echo "  Dist Directory: $DIST_DIR"
}

# Verify bucket exists and is configured
verify_bucket() {
    print_info "Verifying bucket configuration..."
    
    # Check if bucket exists
    if ! gsutil ls -b "gs://$BUCKET_NAME" &> /dev/null; then
        print_warning "Bucket gs://$BUCKET_NAME does not exist or is not accessible"
        echo "Do you want to create it? (y/N)"
        read -r create_bucket
        if [[ $create_bucket =~ ^[Yy]$ ]]; then
            create_bucket
        else
            print_error "Cannot proceed without a valid bucket"
            exit 1
        fi
    else
        print_success "Bucket exists and is accessible"
    fi
    
    # Check if bucket is configured for website hosting
    if ! gsutil web get "gs://$BUCKET_NAME" &> /dev/null; then
        print_warning "Bucket is not configured for static website hosting"
        echo "Do you want to configure it? (y/N)"
        read -r configure_website
        if [[ $configure_website =~ ^[Yy]$ ]]; then
            configure_website_hosting
        fi
    fi
}

# Create bucket
create_bucket() {
    print_info "Creating bucket gs://$BUCKET_NAME..."
    
    # Create bucket
    gsutil mb -p "$PROJECT_ID" "gs://$BUCKET_NAME"
    
    # Make bucket publicly readable
    gsutil iam ch allUsers:objectViewer "gs://$BUCKET_NAME"
    
    print_success "Bucket created successfully"
    
    # Configure for website hosting
    configure_website_hosting
}

# Configure website hosting
configure_website_hosting() {
    print_info "Configuring bucket for static website hosting..."
    
    # Set main page and 404 page (for SPA routing)
    gsutil web set -m index.html -e index.html "gs://$BUCKET_NAME"
    
    print_success "Website hosting configured"
}

# Deploy files
deploy_files() {
    print_info "Deploying files to gs://$BUCKET_NAME..."
    
    # Sync files with proper cache headers
    gsutil -m rsync -r -d "$DIST_DIR" "gs://$BUCKET_NAME"
    
    # Set cache headers for different file types
    print_info "Setting cache headers..."
    
    # Long cache for assets
    gsutil -m setmeta -h "Cache-Control:public, max-age=********, immutable" \
        "gs://$BUCKET_NAME/assets/**"
    
    # Long cache for fonts
    gsutil -m setmeta -h "Cache-Control:public, max-age=********" \
        "gs://$BUCKET_NAME/**.woff2"
    
    # Medium cache for images
    gsutil -m setmeta -h "Cache-Control:public, max-age=2592000" \
        "gs://$BUCKET_NAME/**.png" \
        "gs://$BUCKET_NAME/**.jpg" \
        "gs://$BUCKET_NAME/**.jpeg" \
        "gs://$BUCKET_NAME/**.svg" \
        "gs://$BUCKET_NAME/**.ico" 2>/dev/null || true
    
    # No cache for HTML and API responses
    gsutil -m setmeta -h "Cache-Control:public, max-age=0, must-revalidate" \
        "gs://$BUCKET_NAME/index.html"
    
    gsutil -m setmeta -h "Cache-Control:no-cache, no-store, must-revalidate" \
        "gs://$BUCKET_NAME/health.json" \
        "gs://$BUCKET_NAME/deployment-info.json" 2>/dev/null || true
    
    print_success "Files deployed successfully"
}

# Post-deployment verification
verify_deployment() {
    print_info "Verifying deployment..."
    
    local bucket_url="https://storage.googleapis.com/$BUCKET_NAME"
    local website_url="https://$BUCKET_NAME"
    
    # Test health endpoint
    if curl -s "$bucket_url/health.json" | grep -q "ok"; then
        print_success "Health check passed"
    else
        print_warning "Health check failed or not accessible"
    fi
    
    print_success "Deployment completed!"
    echo ""
    print_info "Your application is now available at:"
    echo "  Storage URL: $bucket_url"
    echo "  Website URL: $website_url (if custom domain not configured)"
    echo ""
    print_info "Useful URLs:"
    echo "  Health Check: $bucket_url/health.json"
    echo "  Build Info: $bucket_url/deployment-info.json"
    echo "  Build Report: $bucket_url/build-report.txt"
}

# Show help
show_help() {
    echo "GCP Bucket Deployment Script"
    echo ""
    echo "Usage: $0 [OPTIONS] [ENVIRONMENT]"
    echo ""
    echo "Options:"
    echo "  -h, --help            Show this help message"
    echo "  -e, --environment ENV Environment (dev, staging, prod) - auto-detects bucket from terraform"
    echo "  -b, --bucket NAME     GCP bucket name (overrides terraform detection)"
    echo "  -p, --project ID      GCP project ID (overrides terraform detection)"
    echo "  --dry-run            Show what would be deployed without actually deploying"
    echo ""
    echo "Environment Variables:"
    echo "  GCP_BUCKET_NAME     GCP bucket name"
    echo "  GCP_PROJECT_ID      GCP project ID"
    echo ""
    echo "Examples:"
    echo "  $0 dev                              # Auto-detect dev bucket from terraform"
    echo "  $0 -e dev                          # Same as above"
    echo "  $0 -b my-app-bucket -p my-project-id  # Manual bucket and project"
    echo "  GCP_BUCKET_NAME=my-app-bucket $0   # Using environment variables"
}

# Parse command line arguments
ENVIRONMENT=""
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -b|--bucket)
            BUCKET_NAME="$2"
            shift 2
            ;;
        -p|--project)
            PROJECT_ID="$2"
            shift 2
            ;;
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        dev|staging|prod)
            # Allow environment as positional argument
            ENVIRONMENT="$1"
            shift
            ;;
        *)
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Main deployment flow
main() {
    echo "🚀 GCP Bucket Deployment Script"
    echo "================================"
    echo ""
    
    check_requirements
    get_config
    
    if [ "$DRY_RUN" = true ]; then
        print_info "DRY RUN MODE - No actual deployment will occur"
        echo ""
        echo "Would deploy:"
        find "$DIST_DIR" -type f | head -10
        if [ $(find "$DIST_DIR" -type f | wc -l) -gt 10 ]; then
            echo "... and $(( $(find "$DIST_DIR" -type f | wc -l) - 10 )) more files"
        fi
        echo ""
        echo "To bucket: gs://$BUCKET_NAME"
        echo "Project: $PROJECT_ID"
        exit 0
    fi
    
    verify_bucket
    deploy_files
    verify_deployment
}

# Run main function
main "$@"