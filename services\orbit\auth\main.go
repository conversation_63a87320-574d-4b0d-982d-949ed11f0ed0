package main

import (
	"flag"
	"fmt"
	"net/http"

	"github.com/TwoDotAi/mono/shared/go/logging"
	"github.com/TwoDotAi/mono/services/orbit/auth/config"
	"github.com/TwoDotAi/mono/services/orbit/auth/database"
	"github.com/TwoDotAi/mono/services/orbit/auth/handlers"
	"github.com/TwoDotAi/mono/services/orbit/auth/middleware"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

func main() {
	var port string
	flag.StringVar(&port, "port", "8004", "Port to run the server on")
	flag.Parse()

	// Load configuration
	cfg := config.Load()

	// Initialize logger
	logging.InitLogger("AUTH_SERVICE", logging.IsDevelopment())
	defer logging.Sync()

	logging.ConfigMessage("AUTH_SERVICE", cfg.Environment)

	// Initialize database connection
	if err := database.InitDB(cfg.DatabaseURL); err != nil {
		logging.LogErrorWithExit(err, "Failed to initialize database",
			zap.String("database_url", cfg.DatabaseURL),
		)
	}
	defer database.CloseDB()

	logging.Info("Database initialized successfully",
		zap.String("database_url", cfg.DatabaseURL),
	)

	// Initialize Gin router
	r := gin.New()
	r.Use(gin.Recovery())
	r.Use(logging.ZapLogger("AUTH_SERVICE"))    // Enhanced Zap logger
	r.Use(logging.ErrorHandler("AUTH_SERVICE")) // Enhanced error handler
	r.Use(middleware.CORS())

	// Health check endpoint
	r.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"status": "healthy"})
	})

	// Auth routes
	auth := r.Group("/api/v1/auth")
	{
		auth.POST("/signin", handlers.SignIn)
		auth.POST("/signin/oauth", handlers.SignInOAuth)
		auth.GET("/callback/:provider", handlers.OAuthCallback)
		auth.POST("/signout", handlers.SignOut)
		auth.GET("/session", handlers.GetSession)
		auth.POST("/validate", handlers.ValidateToken)

		// Protected route
		auth.GET("/user", middleware.AuthRequired(), handlers.GetCurrentUser)
	}

	addr := fmt.Sprintf(":%s", port)
	logging.StartupMessage("AUTH_SERVICE", port)
	if err := r.Run(addr); err != nil {
		logging.LogErrorWithExit(err, "Failed to start server",
			zap.String("port", port),
			zap.String("address", addr),
		)
	}
}
