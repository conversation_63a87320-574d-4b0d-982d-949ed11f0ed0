/* tslint:disable */
/* eslint-disable */
/**
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface DocumentUpdate
 */
export interface DocumentUpdate {
    /**
     * 
     * @type {string}
     * @memberof DocumentUpdate
     */
    content?: string;
    /**
     * 
     * @type {object}
     * @memberof DocumentUpdate
     */
    metadata?: object;
    /**
     * 
     * @type {Array<string>}
     * @memberof DocumentUpdate
     */
    filterTags?: Array<string>;
    /**
     * 
     * @type {string}
     * @memberof DocumentUpdate
     */
    sourceId?: string;
}

/**
 * Check if a given object implements the DocumentUpdate interface.
 */
export function instanceOfDocumentUpdate(value: object): value is DocumentUpdate {
    return true;
}

export function DocumentUpdateFromJSON(json: any): DocumentUpdate {
    return DocumentUpdateFromJSONTyped(json, false);
}

export function DocumentUpdateFromJSONTyped(json: any, ignoreDiscriminator: boolean): DocumentUpdate {
    if (json == null) {
        return json;
    }
    return {
        
        'content': json['content'] == null ? undefined : json['content'],
        'metadata': json['metadata'] == null ? undefined : json['metadata'],
        'filterTags': json['filter_tags'] == null ? undefined : json['filter_tags'],
        'sourceId': json['source_id'] == null ? undefined : json['source_id'],
    };
}

  export function DocumentUpdateToJSON(json: any): DocumentUpdate {
      return DocumentUpdateToJSONTyped(json, false);
  }

  export function DocumentUpdateToJSONTyped(value?: DocumentUpdate | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'content': value['content'],
        'metadata': value['metadata'],
        'filter_tags': value['filterTags'],
        'source_id': value['sourceId'],
    };
}

