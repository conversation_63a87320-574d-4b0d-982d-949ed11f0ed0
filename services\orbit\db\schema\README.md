# Schema Directory

This directory contains SQL schema files that will be automatically converted to migrations.

## Usage

1. Create `.sql` files in this directory with your schema definitions
2. Run `bazel run //platform/db:auto_migrate` to generate and apply migrations
3. Use `bazel run //platform/db:auto_migrate -- watch` to monitor for changes

## Rules

- Use `CREATE TABLE IF NOT EXISTS` for table creation
- Use `CREATE INDEX IF NOT EXISTS` for index creation
- Avoid destructive operations (DROP, TRUNCATE, DELETE without WHERE)
- Use meaningful file names (e.g., `user_profiles.sql`, `order_system.sql`)

## File Processing

- Files are processed in alphabetical order
- Each file generates a separate migration
- Modifications to existing files create new migrations
- Generated migrations are placed in the `migrations/` directory
