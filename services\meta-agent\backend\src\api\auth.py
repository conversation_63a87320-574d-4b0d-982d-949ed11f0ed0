"""
AI Agent Platform - Comprehensive Authentication API
Includes basic auth, OAuth, MFA, and RBAC endpoints
"""

from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from fastapi import APIRouter, HTTPException, Depends, status, Request, Response, Form
from fastapi.security import OAuth2PasswordRequestForm, HTTPBearer, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field, EmailStr
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session
import structlog

from database.connection import get_db
from database.models import User, LoginAttempt
from auth.oauth import oauth_service
from auth.mfa import mfa_service
from auth.rbac import rbac_service, SystemRoles
from auth.dependencies import get_current_user, get_current_active_user, get_current_superuser
from auth.security import SecurityService
from config.settings import settings
from .schemas import (
    UserCreate, UserResponse, Token, UserUpdate,
    PasswordChangeRequest
)

logger = structlog.get_logger(__name__)
security = HTTPBearer()

router = APIRouter(prefix="/auth", tags=["authentication"])

# Pydantic Models for Enhanced Features
class OAuthInitRequest(BaseModel):
    provider: str = Field(..., description="OAuth provider (google, github, microsoft)")
    redirect_url: Optional[str] = Field(None, description="URL to redirect after successful auth")

class OAuthInitResponse(BaseModel):
    authorization_url: str
    state: str

class OAuthCallbackRequest(BaseModel):
    provider: str
    code: str
    state: str

class MFASetupTOTPRequest(BaseModel):
    device_name: str = Field(..., min_length=1, max_length=100)

class MFASetupSMSRequest(BaseModel):
    device_name: str = Field(..., min_length=1, max_length=100)
    phone_number: str = Field(..., min_length=10, max_length=20)

class MFAVerifyRequest(BaseModel):
    device_id: str
    code: str

class MFATokenRequest(BaseModel):
    token: str

class RoleAssignmentRequest(BaseModel):
    user_id: str
    role_name: str
    expires_at: Optional[datetime] = None

class CreateRoleRequest(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    display_name: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = None
    permissions: List[str] = Field(..., min_items=1)

class LoginWithMFARequest(BaseModel):
    email: EmailStr
    password: str
    mfa_token: Optional[str] = None

class UserPermissionsResponse(BaseModel):
    permissions: List[str]
    roles: List[Dict[str, Any]]

# =============================================================================
# BASIC AUTHENTICATION ENDPOINTS
# =============================================================================

@router.post("/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def register(
    user_data: UserCreate,
    db: AsyncSession = Depends(get_db)
):
    """Register a new user"""
    try:
        security_service = SecurityService(db)
        
        user = await security_service.create_user(
            username=user_data.username,
            email=user_data.email,
            password=user_data.password,
            full_name=user_data.full_name
        )
        
        return UserResponse.from_orm(user)
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error("User registration failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed"
        )

@router.post("/login", response_model=Token)
async def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: AsyncSession = Depends(get_db)
):
    """Login with username/email and password"""
    logger.info("Login attempt", username=form_data.username)
    try:
        security_service = SecurityService(db)
        
        user = await security_service.authenticate_user(
            username=form_data.username,  # Can be username or email
            password=form_data.password
        )
        
        if not user:
            logger.warning("Login failed: Invalid credentials", username=form_data.username)
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect username/email or password",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        logger.info("Login successful", username=form_data.username, user_id=user.id)
        # Create tokens
        access_token_expires = timedelta(
            minutes=settings.security.access_token_expire_minutes
        )
        access_token = security_service.create_access_token(
            data={"sub": str(user.id)}, expires_delta=access_token_expires
        )
        refresh_token = security_service.create_refresh_token(
            data={"sub": str(user.id)}
        )
        
        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Login failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )

@router.post("/refresh", response_model=Token)
async def refresh_token(
    refresh_token: str = Form(...),
    db: AsyncSession = Depends(get_db)
):
    """Refresh access token using refresh token"""
    try:
        security_service = SecurityService(db)
        
        payload = security_service.verify_token(refresh_token)
        user_id: str = payload.get("sub")
        
        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid refresh token"
            )
        
        # Create new tokens
        access_token_expires = timedelta(
            minutes=security_service.settings.security.access_token_expire_minutes
        )
        access_token = security_service.create_access_token(
            data={"sub": user_id}, expires_delta=access_token_expires
        )
        new_refresh_token = security_service.create_refresh_token(
            data={"sub": user_id}
        )
        
        return {
            "access_token": access_token,
            "refresh_token": new_refresh_token,
            "token_type": "bearer"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Token refresh failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token"
        )

@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user: User = Depends(get_current_active_user)
):
    """Get current user information"""
    return UserResponse.from_orm(current_user)

@router.put("/me", response_model=UserResponse)
async def update_current_user(
    user_update: UserUpdate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Update current user information"""
    try:
        security_service = SecurityService(db)
        
        updated_user = await security_service.update_user(
            user_id=current_user.id,
            username=user_update.username,
            email=user_update.email,
            full_name=user_update.full_name
        )
        
        return UserResponse.from_orm(updated_user)
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error("User update failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Update failed"
        )

@router.post("/change-password")
async def change_password(
    password_data: PasswordChangeRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Change user password"""
    try:
        security_service = SecurityService(db)
        
        success = await security_service.change_password(
            user_id=current_user.id,
            current_password=password_data.current_password,
            new_password=password_data.new_password
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Current password is incorrect"
            )
        
        return {"message": "Password changed successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Password change failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password change failed"
        )

@router.post("/create-admin", response_model=UserResponse)
async def create_admin_user(
    user_data: UserCreate,
    current_user: User = Depends(get_current_superuser),
    db: AsyncSession = Depends(get_db)
):
    """Create admin user (superuser only)"""
    try:
        security_service = SecurityService(db)
        
        user = await security_service.create_user(
            username=user_data.username,
            email=user_data.email,
            password=user_data.password,
            full_name=user_data.full_name,
            is_superuser=True
        )
        
        return UserResponse.from_orm(user)
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error("Admin user creation failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Admin user creation failed"
        )

# =============================================================================
# OAUTH ENDPOINTS
# =============================================================================

@router.get("/oauth/providers")
async def get_oauth_providers():
    """Get available OAuth providers"""
    try:
        providers = oauth_service.get_available_providers()
        return {"providers": providers}
    except Exception as e:
        logger.error(f"Failed to get OAuth providers: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get OAuth providers"
        )

@router.post("/oauth/init", response_model=OAuthInitResponse)
async def init_oauth_flow(request: OAuthInitRequest):
    """Initialize OAuth authentication flow"""
    try:
        provider = oauth_service.get_provider(request.provider)
        state = oauth_service.generate_state(request.provider, request.redirect_url)
        authorization_url = provider.generate_authorization_url(state)
        
        logger.info(f"OAuth flow initiated for provider {request.provider}")
        
        return OAuthInitResponse(
            authorization_url=authorization_url,
            state=state
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"OAuth initialization failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="OAuth initialization failed"
        )

@router.post("/oauth/callback")
async def oauth_callback(
    request: OAuthCallbackRequest,
    db: Session = Depends(get_db)
):
    """Handle OAuth callback and complete authentication"""
    try:
        # Verify state and get user info
        user_info = await oauth_service.handle_callback(
            provider=request.provider,
            code=request.code,
            state=request.state
        )
        
        # Create or get user
        user = await oauth_service.create_or_get_user(db, user_info)
        
        # Create access token
        security_service = SecurityService(db)
        access_token = security_service.create_access_token(data={"sub": str(user.id), "email": user.email})
        
        # Update user login timestamp
        user.last_login_at = datetime.utcnow()
        db.commit()
        
        logger.info(f"OAuth authentication successful for user {user.email}")
        
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "user": {
                "id": str(user.id),
                "email": user.email,
                "username": user.username,
                "full_name": user.full_name
            }
        }
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"OAuth callback failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="OAuth authentication failed"
        )

# =============================================================================
# MFA ENDPOINTS
# =============================================================================

@router.post("/login/mfa")
async def login_with_mfa(
    request: LoginWithMFARequest,
    db: Session = Depends(get_db)
):
    """Login with MFA support"""
    try:
        # First authenticate with username/password
        security_service = SecurityService(db)
        user = await security_service.authenticate_user(request.email, request.password)
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid credentials"
            )
        
        # Check if user has MFA enabled
        mfa_devices = await mfa_service.get_user_devices(user.id)
        
        if mfa_devices and not request.mfa_token:
            # MFA required but not provided
            return {
                "mfa_required": True,
                "message": "MFA token required",
                "available_devices": [
                    {
                        "id": device.id,
                        "name": device.device_name,
                        "type": device.device_type
                    }
                    for device in mfa_devices
                ]
            }
        
        if mfa_devices and request.mfa_token:
            # Verify MFA token
            mfa_valid = await mfa_service.verify_mfa_token(user.id, request.mfa_token)
            if not mfa_valid:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid MFA token"
                )
        
        # Create access token
        security_service = SecurityService(db)
        access_token = security_service.create_access_token(data={"sub": str(user.id), "email": user.email})
        
        # Update user login timestamp
        user.last_login_at = datetime.utcnow()
        db.commit()
        
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "mfa_required": False
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"MFA login failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )

@router.post("/mfa/setup/totp")
async def setup_totp_mfa(
    request: MFASetupTOTPRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Setup TOTP (authenticator app) MFA device"""
    try:
        setup_data = await mfa_service.setup_totp_device(
            user_id=current_user.id,
            device_name=request.device_name
        )
        
        return {
            "device_id": setup_data["device_id"],
            "secret_key": setup_data["secret_key"],
            "qr_code": setup_data["qr_code"],
            "backup_codes": setup_data["backup_codes"],
            "setup_instructions": "Scan the QR code with your authenticator app and verify with a code"
        }
        
    except Exception as e:
        logger.error(f"TOTP setup failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="TOTP setup failed"
        )

@router.post("/mfa/setup/sms")
async def setup_sms_mfa(
    request: MFASetupSMSRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Setup SMS MFA device"""
    try:
        device_id = await mfa_service.setup_sms_device(
            user_id=current_user.id,
            device_name=request.device_name,
            phone_number=request.phone_number
        )
        
        # Send verification code
        verification_sent = await mfa_service.send_sms_verification(device_id)
        
        return {
            "device_id": device_id,
            "message": "Verification code sent to your phone",
            "verification_sent": verification_sent
        }
        
    except Exception as e:
        logger.error(f"SMS MFA setup failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="SMS MFA setup failed"
        )

@router.post("/mfa/verify/setup")
async def verify_mfa_setup(
    request: MFAVerifyRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Verify and complete MFA device setup"""
    try:
        success = await mfa_service.verify_device_setup(
            device_id=request.device_id,
            verification_code=request.code,
            user_id=current_user.id
        )
        
        if success:
            return {"message": "MFA device setup completed successfully"}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid verification code"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"MFA verification failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="MFA verification failed"
        )

@router.get("/mfa/devices")
async def get_mfa_devices(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user's MFA devices"""
    try:
        devices = await mfa_service.get_user_devices(current_user.id)
        
        return {
            "devices": [
                {
                    "id": device.id,
                    "name": device.device_name,
                    "type": device.device_type,
                    "is_verified": device.is_verified,
                    "created_at": device.created_at,
                    "last_used": device.last_used_at
                }
                for device in devices
            ]
        }
        
    except Exception as e:
        logger.error(f"Failed to get MFA devices: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve MFA devices"
        )

@router.delete("/mfa/devices/{device_id}")
async def remove_mfa_device(
    device_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Remove MFA device"""
    try:
        success = await mfa_service.remove_device(device_id, current_user.id)
        
        if success:
            return {"message": "MFA device removed successfully"}
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="MFA device not found"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to remove MFA device: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to remove MFA device"
        )

@router.post("/mfa/send-sms")
async def send_sms_code(
    request: MFATokenRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Send SMS verification code"""
    try:
        success = await mfa_service.send_sms_code(
            user_id=current_user.id,
            device_token=request.token
        )
        
        if success:
            return {"message": "SMS code sent successfully"}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to send SMS code"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to send SMS code: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to send SMS code"
        )

# =============================================================================
# RBAC ENDPOINTS
# =============================================================================

@router.get("/permissions", response_model=UserPermissionsResponse)
async def get_user_permissions(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get current user's permissions and roles"""
    try:
        permissions = await rbac_service.get_user_permissions(current_user.id)
        roles = await rbac_service.get_user_roles(current_user.id)
        
        return UserPermissionsResponse(
            permissions=permissions,
            roles=[
                {
                    "name": role.name,
                    "display_name": role.display_name,
                    "description": role.description,
                    "expires_at": role.expires_at.isoformat() if role.expires_at else None
                }
                for role in roles
            ]
        )
        
    except Exception as e:
        logger.error(f"Failed to get user permissions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve permissions"
        )

@router.get("/roles")
async def get_all_roles(
    current_user: User = Depends(get_current_superuser),
    db: Session = Depends(get_db)
):
    """Get all available roles (admin only)"""
    try:
        roles = await rbac_service.get_all_roles()
        
        return {
            "roles": [
                {
                    "name": role.name,
                    "display_name": role.display_name,
                    "description": role.description,
                    "permissions": role.permissions,
                    "created_at": role.created_at
                }
                for role in roles
            ]
        }
        
    except Exception as e:
        logger.error(f"Failed to get roles: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve roles"
        )

@router.get("/permissions/all")
async def get_all_permissions(
    current_user: User = Depends(get_current_superuser),
    db: Session = Depends(get_db)
):
    """Get all available permissions (admin only)"""
    try:
        permissions = await rbac_service.get_all_permissions()
        
        return {"permissions": permissions}
        
    except Exception as e:
        logger.error(f"Failed to get permissions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve permissions"
        )

@router.post("/roles/create")
async def create_role(
    request: CreateRoleRequest,
    current_user: User = Depends(get_current_superuser),
    db: Session = Depends(get_db)
):
    """Create new role (admin only)"""
    try:
        role = await rbac_service.create_role(
            name=request.name,
            display_name=request.display_name,
            description=request.description,
            permissions=request.permissions
        )
        
        return {
            "message": "Role created successfully",
            "role": {
                "name": role.name,
                "display_name": role.display_name,
                "description": role.description,
                "permissions": role.permissions
            }
        }
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Failed to create role: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create role"
        )

@router.post("/roles/assign")
async def assign_role(
    request: RoleAssignmentRequest,
    current_user: User = Depends(get_current_superuser),
    db: Session = Depends(get_db)
):
    """Assign role to user (admin only)"""
    try:
        success = await rbac_service.assign_role_to_user(
            user_id=request.user_id,
            role_name=request.role_name,
            expires_at=request.expires_at
        )
        
        if success:
            return {"message": "Role assigned successfully"}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to assign role"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to assign role: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to assign role"
        )

@router.delete("/roles/remove")
async def remove_role(
    request: RoleAssignmentRequest,
    current_user: User = Depends(get_current_superuser),
    db: Session = Depends(get_db)
):
    """Remove role from user (admin only)"""
    try:
        success = await rbac_service.remove_role_from_user(
            user_id=request.user_id,
            role_name=request.role_name
        )
        
        if success:
            return {"message": "Role removed successfully"}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to remove role"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to remove role: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to remove role"
        )

# =============================================================================
# SECURITY MONITORING ENDPOINTS
# =============================================================================

@router.get("/security/login-attempts")
async def get_login_attempts(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user's recent login attempts"""
    try:
        attempts = db.query(LoginAttempt).filter(
            LoginAttempt.user_id == current_user.id
        ).order_by(LoginAttempt.attempted_at.desc()).limit(10).all()
        
        return {
            "login_attempts": [
                {
                    "ip_address": attempt.ip_address,
                    "user_agent": attempt.user_agent,
                    "success": attempt.success,
                    "attempted_at": attempt.attempted_at,
                    "failure_reason": attempt.failure_reason
                }
                for attempt in attempts
            ]
        }
        
    except Exception as e:
        logger.error(f"Failed to get login attempts: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve login attempts"
        )