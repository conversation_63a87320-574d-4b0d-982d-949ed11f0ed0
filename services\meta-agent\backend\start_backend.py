#!/usr/bin/env python3
"""
Simple script to start the AI Agent Platform backend server
"""
import os
import sys
import subprocess

# Add the backend src directory to Python path
backend_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(backend_dir, 'src')
sys.path.insert(0, src_dir)

# Change to backend directory
os.chdir(backend_dir)

# Set environment variables
os.environ.setdefault('DATABASE_URL', 'sqlite:///ai_agent_platform.db')
os.environ.setdefault('ENVIRONMENT', 'development')
os.environ.setdefault('DEBUG', 'true')
os.environ.setdefault('SECRET_KEY', 'dev-secret-key')

# Try to start the server
try:
    print("Starting AI Agent Platform backend server...")
    print("Backend directory:", backend_dir)
    print("Python path:", sys.path[0])
    
    # Check if we have the virtual environment
    venv_python = os.path.join(backend_dir, 'venv', 'bin', 'python')
    if os.path.exists(venv_python):
        print(f"Using virtual environment: {venv_python}")
        subprocess.run([
            venv_python, '-m', 'uvicorn', 'src.main:app',
            '--host', '0.0.0.0',
            '--port', '8000',
            '--reload'
        ])
    else:
        print("Using system python")
        subprocess.run([
            sys.executable, '-m', 'uvicorn', 'src.main:app',
            '--host', '0.0.0.0',
            '--port', '8000',
            '--reload'
        ])
        
except KeyboardInterrupt:
    print("\nShutting down server...")
except Exception as e:
    print(f"Failed to start server: {e}")
    sys.exit(1)