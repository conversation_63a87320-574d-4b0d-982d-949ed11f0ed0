# Database Module Outputs

output "instance_name" {
  description = "Name of the Cloud SQL instance"
  value       = google_sql_database_instance.postgres.name
}

output "instance_connection_name" {
  description = "Connection name of the Cloud SQL instance"
  value       = google_sql_database_instance.postgres.connection_name
}

output "instance_self_link" {
  description = "Self link of the Cloud SQL instance"
  value       = google_sql_database_instance.postgres.self_link
}

output "instance_private_ip" {
  description = "Private IP address of the Cloud SQL instance"
  value       = google_sql_database_instance.postgres.private_ip_address
}

output "database_name" {
  description = "Name of the application database"
  value       = google_sql_database.app_database.name
}

output "database_user" {
  description = "Username for database access"
  value       = google_sql_user.app_user.name
}

output "database_password_secret_name" {
  description = "Secret Manager secret name containing the database password"
  value       = google_secret_manager_secret.db_password.secret_id
}

output "database_password_secret_version" {
  description = "Latest version of the database password secret"
  value       = google_secret_manager_secret_version.db_password.name
}

output "connection_string" {
  description = "PostgreSQL connection string (without password)"
  value       = "postgresql://${google_sql_user.app_user.name}@${google_sql_database_instance.postgres.private_ip_address}:5432/${google_sql_database.app_database.name}"
  sensitive   = false
}

output "jdbc_url" {
  description = "JDBC URL for database connection (without password)"
  value       = "jdbc:postgresql://${google_sql_database_instance.postgres.private_ip_address}:5432/${google_sql_database.app_database.name}"
  sensitive   = false
}

# Migration automation outputs
output "migration_trigger_name" {
  description = "Name of the Cloud Build trigger for migrations"
  value       = var.enable_migration_automation ? google_cloudbuild_trigger.db_migration[0].name : ""
}

output "migration_trigger_id" {
  description = "ID of the Cloud Build trigger for migrations"
  value       = var.enable_migration_automation ? google_cloudbuild_trigger.db_migration[0].trigger_id : ""
}

# Private networking outputs
output "private_ip_range_name" {
  description = "Name of the private IP range for VPC peering"
  value       = google_compute_global_address.private_ip_range.name
}

output "vpc_peering_connection" {
  description = "VPC peering connection for private access"
  value       = google_service_networking_connection.private_vpc_connection.network
}