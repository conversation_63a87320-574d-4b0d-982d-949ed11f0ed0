/* tslint:disable */
/* eslint-disable */
/**
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { DealDetailsAllOfDealStage } from './DealDetailsAllOfDealStage';
import {
    DealDetailsAllOfDealStageFromJSON,
    DealDetailsAllOfDealStageFromJSONTyped,
    DealDetailsAllOfDealStageToJSON,
    DealDetailsAllOfDealStageToJSONTyped,
} from './DealDetailsAllOfDealStage';
import type { DealDetailsAllOfCompany } from './DealDetailsAllOfCompany';
import {
    DealDetailsAllOfCompanyFromJSON,
    DealDetailsAllOfCompanyFromJSONTyped,
    DealDetailsAllOfCompanyToJSON,
    DealDetailsAllOfCompanyToJSONTyped,
} from './DealDetailsAllOfCompany';

/**
 * 
 * @export
 * @interface DealDetails
 */
export interface DealDetails {
    /**
     * 
     * @type {string}
     * @memberof DealDetails
     */
    id?: string;
    /**
     * 
     * @type {string}
     * @memberof DealDetails
     */
    title?: string;
    /**
     * 
     * @type {string}
     * @memberof DealDetails
     */
    description?: string;
    /**
     * 
     * @type {number}
     * @memberof DealDetails
     */
    estimatedValue?: number;
    /**
     * 
     * @type {string}
     * @memberof DealDetails
     */
    companyId?: string;
    /**
     * 
     * @type {string}
     * @memberof DealDetails
     */
    dealStageId?: string;
    /**
     * 
     * @type {Date}
     * @memberof DealDetails
     */
    expectedCloseDate?: Date;
    /**
     * 
     * @type {string}
     * @memberof DealDetails
     */
    createdBy?: string;
    /**
     * 
     * @type {Date}
     * @memberof DealDetails
     */
    createdAt?: Date;
    /**
     * 
     * @type {Date}
     * @memberof DealDetails
     */
    updatedAt?: Date;
    /**
     * 
     * @type {DealDetailsAllOfCompany}
     * @memberof DealDetails
     */
    company?: DealDetailsAllOfCompany;
    /**
     * 
     * @type {DealDetailsAllOfDealStage}
     * @memberof DealDetails
     */
    dealStage?: DealDetailsAllOfDealStage;
}

/**
 * Check if a given object implements the DealDetails interface.
 */
export function instanceOfDealDetails(value: object): value is DealDetails {
    return true;
}

export function DealDetailsFromJSON(json: any): DealDetails {
    return DealDetailsFromJSONTyped(json, false);
}

export function DealDetailsFromJSONTyped(json: any, ignoreDiscriminator: boolean): DealDetails {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'] == null ? undefined : json['id'],
        'title': json['title'] == null ? undefined : json['title'],
        'description': json['description'] == null ? undefined : json['description'],
        'estimatedValue': json['estimated_value'] == null ? undefined : json['estimated_value'],
        'companyId': json['company_id'] == null ? undefined : json['company_id'],
        'dealStageId': json['deal_stage_id'] == null ? undefined : json['deal_stage_id'],
        'expectedCloseDate': json['expected_close_date'] == null ? undefined : (new Date(json['expected_close_date'])),
        'createdBy': json['created_by'] == null ? undefined : json['created_by'],
        'createdAt': json['created_at'] == null ? undefined : (new Date(json['created_at'])),
        'updatedAt': json['updated_at'] == null ? undefined : (new Date(json['updated_at'])),
        'company': json['company'] == null ? undefined : DealDetailsAllOfCompanyFromJSON(json['company']),
        'dealStage': json['deal_stage'] == null ? undefined : DealDetailsAllOfDealStageFromJSON(json['deal_stage']),
    };
}

  export function DealDetailsToJSON(json: any): DealDetails {
      return DealDetailsToJSONTyped(json, false);
  }

  export function DealDetailsToJSONTyped(value?: DealDetails | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'title': value['title'],
        'description': value['description'],
        'estimated_value': value['estimatedValue'],
        'company_id': value['companyId'],
        'deal_stage_id': value['dealStageId'],
        'expected_close_date': value['expectedCloseDate'] == null ? undefined : ((value['expectedCloseDate']).toISOString().substring(0,10)),
        'created_by': value['createdBy'],
        'created_at': value['createdAt'] == null ? undefined : ((value['createdAt']).toISOString()),
        'updated_at': value['updatedAt'] == null ? undefined : ((value['updatedAt']).toISOString()),
        'company': DealDetailsAllOfCompanyToJSON(value['company']),
        'deal_stage': DealDetailsAllOfDealStageToJSON(value['dealStage']),
    };
}

