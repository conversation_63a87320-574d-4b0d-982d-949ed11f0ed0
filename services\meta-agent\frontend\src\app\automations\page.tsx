/**
 * AI Agent Platform - Unified Automations Page
 * Combines workflows and orchestrations into a single powerful automation system
 */

'use client';

import React, { useState, useEffect } from 'react';
import { AppLayout } from '@/components/Layout/AppLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Plus,
  Play,
  Pause,
  Square,
  Edit,
  Trash2,
  Copy,
  Search,
  Filter,
  MoreHorizontal,
  Zap,
  Network,
  Workflow,
  Users,
  Clock,
  CheckCircle2,
  XCircle,
  AlertCircle,
  GitBranch,
  Settings,
  Eye,
  Activity,
  Bot,
  Layers
} from 'lucide-react';
import Link from 'next/link';

// Unified automation types
const automationTypes = {
  workflow: { 
    label: 'Workflow', 
    icon: Workflow, 
    color: 'bg-blue-50 text-blue-700',
    description: 'Node-based automation with triggers and actions'
  },
  orchestration: { 
    label: 'Agent Orchestration', 
    icon: Network, 
    color: 'bg-purple-50 text-purple-700',
    description: 'Multi-agent coordination and collaboration'
  },
  hybrid: { 
    label: 'Hybrid Automation', 
    icon: Layers, 
    color: 'bg-green-50 text-green-700',
    description: 'Combined workflows with agent orchestration'
  }
};

// Mock unified automations data
const mockAutomations = [
  {
    id: '1',
    name: 'Customer Support Pipeline',
    description: 'Automated customer support with sentiment analysis, agent routing, and response generation',
    type: 'hybrid',
    status: 'running',
    progress: 75,
    pattern: 'sequential',
    components: {
      nodes: 8,
      agents: 3,
      connections: 12
    },
    agents: ['Support Classifier', 'Sentiment Analyzer', 'Response Generator'],
    lastRun: '2024-01-15T10:30:00Z',
    totalRuns: 1247,
    successRate: 94.2,
    tags: ['customer-support', 'ai', 'automation']
  },
  {
    id: '2',
    name: 'Data Processing Workflow',
    description: 'ETL workflow for processing customer data and generating insights',
    type: 'workflow',
    status: 'completed',
    progress: 100,
    pattern: 'parallel',
    components: {
      nodes: 12,
      agents: 0,
      connections: 18
    },
    agents: [],
    lastRun: '2024-01-15T11:45:00Z',
    totalRuns: 892,
    successRate: 98.7,
    tags: ['data-processing', 'etl', 'analytics']
  },
  {
    id: '3',
    name: 'Multi-Agent Content Review',
    description: 'Coordinated content moderation with multiple specialized AI agents',
    type: 'orchestration',
    status: 'running',
    progress: 60,
    pattern: 'parallel',
    components: {
      nodes: 0,
      agents: 4,
      connections: 8
    },
    agents: ['Text Moderator', 'Image Analyzer', 'Sentiment Checker', 'Policy Validator'],
    lastRun: '2024-01-15T12:00:00Z',
    totalRuns: 2156,
    successRate: 91.8,
    tags: ['content-moderation', 'ai', 'parallel']
  },
  {
    id: '4',
    name: 'Smart Lead Qualification',
    description: 'Intelligent lead scoring with workflow automation and agent analysis',
    type: 'hybrid',
    status: 'paused',
    progress: 45,
    pattern: 'hierarchical',
    components: {
      nodes: 6,
      agents: 2,
      connections: 10
    },
    agents: ['Lead Scorer', 'Market Analyzer'],
    lastRun: '2024-01-15T09:15:00Z',
    totalRuns: 543,
    successRate: 87.3,
    tags: ['sales', 'lead-generation', 'ai']
  }
];

const statusConfig = {
  running: { color: 'bg-blue-100 text-blue-800', icon: Play },
  completed: { color: 'bg-green-100 text-green-800', icon: CheckCircle2 },
  paused: { color: 'bg-yellow-100 text-yellow-800', icon: Pause },
  error: { color: 'bg-red-100 text-red-800', icon: XCircle },
  draft: { color: 'bg-gray-100 text-gray-800', icon: Edit }
};

const patternConfig = {
  sequential: { label: 'Sequential', icon: GitBranch, color: 'bg-blue-50 text-blue-700' },
  parallel: { label: 'Parallel', icon: Network, color: 'bg-green-50 text-green-700' },
  hierarchical: { label: 'Hierarchical', icon: Users, color: 'bg-purple-50 text-purple-700' },
  event_driven: { label: 'Event Driven', icon: Zap, color: 'bg-orange-50 text-orange-700' }
};

export default function AutomationsPage() {
  const [automations, setAutomations] = useState(mockAutomations);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [loading, setLoading] = useState(false);

  const filteredAutomations = automations.filter(automation => {
    const matchesSearch = automation.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         automation.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || automation.status === statusFilter;
    const matchesType = typeFilter === 'all' || automation.type === typeFilter;
    return matchesSearch && matchesStatus && matchesType;
  });

  const handleAutomationAction = async (automationId: string, action: string) => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setAutomations(prev => prev.map(automation => {
        if (automation.id === automationId) {
          switch (action) {
            case 'start':
              return { ...automation, status: 'running' };
            case 'pause':
              return { ...automation, status: 'paused' };
            case 'stop':
              return { ...automation, status: 'completed' };
            default:
              return automation;
          }
        }
        return automation;
      }));
    } catch (error) {
      console.error('Failed to perform automation action:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <AppLayout>
      <div className="container mx-auto py-8 px-4 max-w-7xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-3">
              <Zap className="h-8 w-8 text-primary" />
              Automations
            </h1>
            <p className="text-muted-foreground mt-2">
              Unified workflows, agent orchestrations, and hybrid automations
            </p>
          </div>
          <div className="flex gap-3">
            <Button variant="outline">
              <Activity className="h-4 w-4 mr-2" />
              Monitor All
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Automation
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Automations</p>
                  <p className="text-2xl font-bold">{automations.length}</p>
                </div>
                <Zap className="h-8 w-8 text-muted-foreground" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Workflows</p>
                  <p className="text-2xl font-bold text-blue-600">
                    {automations.filter(a => a.type === 'workflow').length}
                  </p>
                </div>
                <Workflow className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Orchestrations</p>
                  <p className="text-2xl font-bold text-purple-600">
                    {automations.filter(a => a.type === 'orchestration').length}
                  </p>
                </div>
                <Network className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Hybrid</p>
                  <p className="text-2xl font-bold text-green-600">
                    {automations.filter(a => a.type === 'hybrid').length}
                  </p>
                </div>
                <Layers className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Active</p>
                  <p className="text-2xl font-bold text-orange-600">
                    {automations.filter(a => a.status === 'running').length}
                  </p>
                </div>
                <Play className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search automations..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <select
                  value={typeFilter}
                  onChange={(e) => setTypeFilter(e.target.value)}
                  className="flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                >
                  <option value="all">All Types</option>
                  <option value="workflow">Workflows</option>
                  <option value="orchestration">Orchestrations</option>
                  <option value="hybrid">Hybrid</option>
                </select>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                >
                  <option value="all">All Status</option>
                  <option value="running">Running</option>
                  <option value="completed">Completed</option>
                  <option value="paused">Paused</option>
                  <option value="error">Error</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Automations List */}
        <div className="space-y-6">
          {filteredAutomations.map((automation) => {
            const StatusIcon = statusConfig[automation.status as keyof typeof statusConfig]?.icon || AlertCircle;
            const statusColor = statusConfig[automation.status as keyof typeof statusConfig]?.color || 'bg-gray-100 text-gray-800';
            const typeInfo = automationTypes[automation.type as keyof typeof automationTypes];
            const TypeIcon = typeInfo?.icon || Zap;
            const patternInfo = patternConfig[automation.pattern as keyof typeof patternConfig];
            const PatternIcon = patternInfo?.icon || Network;
            
            return (
              <Card key={automation.id} className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <CardTitle className="flex items-center gap-2">
                          <Link href={`/automations/${automation.id}`} className="hover:underline">
                            {automation.name}
                          </Link>
                        </CardTitle>
                        <Badge className={statusColor}>
                          <StatusIcon className="h-3 w-3 mr-1" />
                          {automation.status}
                        </Badge>
                        <Badge className={typeInfo?.color || 'bg-gray-50 text-gray-700'}>
                          <TypeIcon className="h-3 w-3 mr-1" />
                          {typeInfo?.label || automation.type}
                        </Badge>
                        <Badge className={patternInfo?.color || 'bg-gray-50 text-gray-700'}>
                          <PatternIcon className="h-3 w-3 mr-1" />
                          {patternInfo?.label || automation.pattern}
                        </Badge>
                      </div>
                      <CardDescription>
                        {automation.description}
                      </CardDescription>
                    </div>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* Progress */}
                    {automation.status === 'running' && (
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Progress</span>
                          <span>{automation.progress}%</span>
                        </div>
                        <Progress value={automation.progress} className="h-2" />
                      </div>
                    )}

                    {/* Components */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <p className="text-muted-foreground">Nodes</p>
                        <p className="font-medium">{automation.components.nodes}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Agents</p>
                        <p className="font-medium">{automation.components.agents}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Total Runs</p>
                        <p className="font-medium">{automation.totalRuns.toLocaleString()}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Success Rate</p>
                        <p className="font-medium text-green-600">{automation.successRate}%</p>
                      </div>
                    </div>

                    {/* Agents */}
                    {automation.agents.length > 0 && (
                      <div>
                        <p className="text-sm font-medium mb-2">Participating Agents:</p>
                        <div className="flex flex-wrap gap-2">
                          {automation.agents.map((agent, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              <Bot className="h-3 w-3 mr-1" />
                              {agent}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Tags */}
                    <div className="flex flex-wrap gap-2">
                      {automation.tags.map((tag, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>

                    {/* Timing */}
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Clock className="h-4 w-4" />
                      Last run: {new Date(automation.lastRun).toLocaleString()}
                    </div>

                    {/* Actions */}
                    <div className="flex gap-2 pt-2 border-t">
                      {automation.status === 'paused' ? (
                        <Button
                          size="sm"
                          onClick={() => handleAutomationAction(automation.id, 'start')}
                          disabled={loading}
                        >
                          <Play className="h-4 w-4 mr-1" />
                          Resume
                        </Button>
                      ) : automation.status === 'running' ? (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleAutomationAction(automation.id, 'pause')}
                          disabled={loading}
                        >
                          <Pause className="h-4 w-4 mr-1" />
                          Pause
                        </Button>
                      ) : (
                        <Button
                          size="sm"
                          onClick={() => handleAutomationAction(automation.id, 'start')}
                          disabled={loading}
                        >
                          <Play className="h-4 w-4 mr-1" />
                          Start
                        </Button>
                      )}
                      
                      <Link href={`/automations/${automation.id}`}>
                        <Button size="sm" variant="outline">
                          <Eye className="h-4 w-4 mr-1" />
                          View Details
                        </Button>
                      </Link>
                      
                      <Link href={`/automations/${automation.id}/edit`}>
                        <Button size="sm" variant="outline">
                          <Edit className="h-4 w-4 mr-1" />
                          Edit
                        </Button>
                      </Link>
                      
                      <Button size="sm" variant="outline">
                        <Copy className="h-4 w-4 mr-1" />
                        Clone
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {filteredAutomations.length === 0 && (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center py-8">
                <Zap className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">No automations found</h3>
                <p className="text-muted-foreground mb-4">
                  {searchTerm || statusFilter !== 'all' || typeFilter !== 'all'
                    ? 'Try adjusting your search or filters'
                    : 'Get started by creating your first automation'
                  }
                </p>
                {!searchTerm && statusFilter === 'all' && typeFilter === 'all' && (
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Your First Automation
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </AppLayout>
  );
}
