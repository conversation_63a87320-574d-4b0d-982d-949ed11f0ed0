package(default_visibility = ["//visibility:public"])

# Automatically download OpenAPI Generator JAR
genrule(
    name = "openapi_generator_jar",
    outs = ["openapi-generator-cli.jar"],
    cmd = """
        curl -L -o $@ https://repo1.maven.org/maven2/org/openapitools/openapi-generator-cli/7.2.0/openapi-generator-cli-7.2.0.jar
    """,
    visibility = ["//visibility:public"],
)

# Download script for the JAR (legacy - kept for manual use)
sh_binary(
    name = "download_openapi_generator",
    srcs = ["download-openapi-generator.sh"],
)

# Placeholder lint target (can be expanded later)
sh_binary(
    name = "lint",
    srcs = ["lint.sh"],
)
