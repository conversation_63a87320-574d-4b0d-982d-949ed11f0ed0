"""Base AI Provider interface for multi-provider abstraction."""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, AsyncIterator
from pydantic import BaseModel
from enum import Enum

class ModelCapability(str, Enum):
    """AI model capabilities."""
    TEXT_GENERATION = "text_generation"
    CHAT_COMPLETION = "chat_completion"
    CODE_GENERATION = "code_generation"
    FUNCTION_CALLING = "function_calling"
    EMBEDDING = "embedding"
    IMAGE_ANALYSIS = "image_analysis"
    SPEECH_TO_TEXT = "speech_to_text"
    TEXT_TO_SPEECH = "text_to_speech"

class AIMessage(BaseModel):
    """Standard AI message format."""
    role: str
    content: str
    metadata: Optional[Dict[str, Any]] = None
    timestamp: Optional[str] = None

class AIResponse(BaseModel):
    """Standard AI response format."""
    content: str
    model: str
    usage: Optional[Dict[str, int]] = None
    metadata: Optional[Dict[str, Any]] = None
    finish_reason: Optional[str] = None

class ModelInfo(BaseModel):
    """AI model information."""
    name: str
    provider: str
    capabilities: List[ModelCapability]
    max_tokens: Optional[int] = None
    cost_per_token: Optional[float] = None
    supports_streaming: bool = False
    supports_functions: bool = False

class BaseAIProvider(ABC):
    """Base class for AI providers."""
    
    def __init__(self, api_key: str, **kwargs):
        self.api_key = api_key
        self.config = kwargs
        
    @abstractmethod
    async def initialize(self) -> bool:
        """Initialize the provider connection."""
        pass
    
    @abstractmethod
    async def chat_completion(
        self, 
        messages: List[AIMessage], 
        model: str,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        stream: bool = False,
        **kwargs
    ) -> AIResponse:
        """Generate chat completion."""
        pass
    
    @abstractmethod
    async def stream_completion(
        self,
        messages: List[AIMessage],
        model: str,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        **kwargs
    ) -> AsyncIterator[str]:
        """Stream chat completion."""
        pass
    
    @abstractmethod
    async def generate_embedding(
        self,
        text: str,
        model: Optional[str] = None
    ) -> List[float]:
        """Generate text embeddings."""
        pass
    
    @abstractmethod
    async def analyze_image(
        self,
        image_data: bytes,
        prompt: str,
        model: Optional[str] = None
    ) -> AIResponse:
        """Analyze image with AI."""
        pass
    
    @abstractmethod
    async def function_calling(
        self,
        messages: List[AIMessage],
        functions: List[Dict[str, Any]],
        model: str,
        **kwargs
    ) -> AIResponse:
        """Execute function calling."""
        pass
    
    @abstractmethod
    def get_available_models(self) -> List[ModelInfo]:
        """Get list of available models."""
        pass
    
    @abstractmethod
    def estimate_cost(
        self,
        input_tokens: int,
        output_tokens: int,
        model: str
    ) -> float:
        """Estimate API cost for token usage."""
        pass
    
    @property
    @abstractmethod
    def provider_name(self) -> str:
        """Provider identifier."""
        pass
    
    async def health_check(self) -> bool:
        """Check provider health."""
        try:
            models = self.get_available_models()
            return len(models) > 0
        except Exception:
            return False