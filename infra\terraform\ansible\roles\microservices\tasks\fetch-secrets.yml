---
- name: Get G<PERSON> access token
  uri:
    url: "{{ gcp_metadata_url }}/instance/service-accounts/default/token"
    headers: "{{ gcp_metadata_headers }}"
  register: gcp_token
  no_log: true
  tags: ['secrets']

- name: Fetch database password from Secret Manager
  uri:
    url: "https://secretmanager.googleapis.com/v1/projects/{{ gcp_project_id }}/secrets/db-password/versions/latest:access"
    headers:
      Authorization: "Bearer {{ gcp_token.json.access_token }}"
  register: db_password_secret
  no_log: true
  tags: ['secrets']

- name: Fetch JWT secret from Secret Manager
  uri:
    url: "https://secretmanager.googleapis.com/v1/projects/{{ gcp_project_id }}/secrets/jwt-secret/versions/latest:access"
    headers:
      Authorization: "Bearer {{ gcp_token.json.access_token }}"
  register: jwt_secret_secret
  no_log: true
  tags: ['secrets']

- name: Fetch OAuth client ID from Secret Manager
  uri:
    url: "https://secretmanager.googleapis.com/v1/projects/{{ gcp_project_id }}/secrets/oauth-client-id/versions/latest:access"
    headers:
      Authorization: "Bearer {{ gcp_token.json.access_token }}"
  register: oauth_client_secret
  no_log: true
  tags: ['secrets']

- name: Fetch OAuth client secret from Secret Manager
  uri:
    url: "https://secretmanager.googleapis.com/v1/projects/{{ gcp_project_id }}/secrets/oauth-client-secret/versions/latest:access"
    headers:
      Authorization: "Bearer {{ gcp_token.json.access_token }}"
  register: oauth_secret_secret
  no_log: true
  tags: ['secrets']

- name: Set secret facts
  set_fact:
    database_password: "{{ (db_password_secret.json.payload.data | b64decode) if db_password_secret.json.payload.data is defined else '' }}"
    jwt_secret: "{{ (jwt_secret_secret.json.payload.data | b64decode) if jwt_secret_secret.json.payload.data is defined else '' }}"
    oauth_client_id: "{{ (oauth_client_secret.json.payload.data | b64decode) if oauth_client_secret.json.payload.data is defined else '' }}"
    oauth_client_secret: "{{ (oauth_secret_secret.json.payload.data | b64decode) if oauth_secret_secret.json.payload.data is defined else '' }}"
    database_user: "postgres"
  no_log: true
  tags: ['secrets']