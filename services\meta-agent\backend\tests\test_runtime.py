"""
AI Agent Platform - Runtime Management Tests
"""

import pytest
import uuid
from unittest.mock import AsyncMock, patch


class TestRuntimeAPI:
    """Test runtime management API endpoints"""
    
    def test_get_system_status(self, client, authenticated_user):
        """Test getting system status"""
        response = client.get(
            "/api/v1/runtime/status",
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert "system_health" in data
        assert "active_agents" in data
        assert "total_agents" in data
        assert "resource_usage" in data
        assert "uptime" in data
    
    def test_get_system_status_without_auth(self, client):
        """Test getting system status without authentication"""
        response = client.get("/api/v1/runtime/status")
        assert response.status_code == 401
    
    def test_get_system_metrics(self, client, authenticated_user):
        """Test getting system metrics"""
        response = client.get(
            "/api/v1/runtime/metrics",
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert "cpu_usage" in data
        assert "memory_usage" in data
        assert "agent_performance" in data
        assert "task_throughput" in data
    
    def test_get_runtime_logs(self, client, authenticated_user):
        """Test getting runtime logs"""
        response = client.get(
            "/api/v1/runtime/logs",
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert "logs" in data
        assert isinstance(data["logs"], list)
        assert "total" in data
    
    def test_get_runtime_logs_with_filter(self, client, authenticated_user):
        """Test getting runtime logs with level filter"""
        response = client.get(
            "/api/v1/runtime/logs?level=ERROR",
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # All returned logs should be ERROR level
        for log in data["logs"]:
            assert log.get("level") == "ERROR"
    
    @patch('src.agents.manager.agent_manager.get_agent_resources')
    def test_get_agent_resources(self, mock_get_resources, client, authenticated_user, test_agent):
        """Test getting agent resource usage"""
        agent_id = test_agent["id"]
        mock_get_resources.return_value = {
            "cpu_usage": 25.5,
            "memory_usage": 128.0,
            "task_count": 5
        }
        
        response = client.get(
            f"/api/v1/runtime/agents/{agent_id}/resources",
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["cpu_usage"] == 25.5
        assert data["memory_usage"] == 128.0
        assert data["task_count"] == 5
    
    @patch('src.agents.manager.agent_manager.get_agent_performance')
    def test_get_agent_performance(self, mock_get_performance, client, authenticated_user, test_agent):
        """Test getting agent performance metrics"""
        agent_id = test_agent["id"]
        mock_get_performance.return_value = {
            "tasks_completed": 100,
            "success_rate": 0.95,
            "average_execution_time": 5.2,
            "error_count": 5
        }
        
        response = client.get(
            f"/api/v1/runtime/agents/{agent_id}/performance",
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["tasks_completed"] == 100
        assert data["success_rate"] == 0.95
        assert data["average_execution_time"] == 5.2
        assert data["error_count"] == 5
    
    def test_get_nonexistent_agent_resources(self, client, authenticated_user):
        """Test getting resources for nonexistent agent"""
        fake_id = str(uuid.uuid4())
        
        response = client.get(
            f"/api/v1/runtime/agents/{fake_id}/resources",
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 404
    
    @patch('src.agents.manager.agent_manager.restart_agent')
    def test_restart_agent(self, mock_restart, client, authenticated_user, test_agent):
        """Test restarting agent"""
        mock_restart.return_value = True
        agent_id = test_agent["id"]
        
        response = client.post(
            f"/api/v1/runtime/agents/{agent_id}/restart",
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] == True
        mock_restart.assert_called_once()
    
    @patch('src.agents.manager.agent_manager.get_active_agents')
    def test_get_active_agents(self, mock_get_active, client, authenticated_user):
        """Test getting list of active agents"""
        mock_get_active.return_value = [
            {"id": str(uuid.uuid4()), "name": "Agent 1", "status": "running"},
            {"id": str(uuid.uuid4()), "name": "Agent 2", "status": "running"}
        ]
        
        response = client.get(
            "/api/v1/runtime/agents/active",
            headers=authenticated_user["headers"]
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert len(data["agents"]) == 2
        for agent in data["agents"]:
            assert agent["status"] == "running"
    
    def test_system_shutdown(self, client, authenticated_user):
        """Test system shutdown endpoint (should be restricted)"""
        response = client.post(
            "/api/v1/runtime/shutdown",
            headers=authenticated_user["headers"]
        )
        
        # Should require admin privileges
        assert response.status_code in [403, 401]


class TestRuntimeService:
    """Test runtime service layer"""
    
    @pytest.mark.asyncio
    async def test_get_system_status_service(self):
        """Test getting system status via service"""
        from src.services.runtime import RuntimeService
        
        runtime_service = RuntimeService()
        status = await runtime_service.get_system_status()
        
        assert "system_health" in status
        assert "active_agents" in status
        assert "resource_usage" in status
        assert isinstance(status["active_agents"], int)
    
    @pytest.mark.asyncio
    async def test_get_system_metrics_service(self):
        """Test getting system metrics via service"""
        from src.services.runtime import RuntimeService
        
        runtime_service = RuntimeService()
        metrics = await runtime_service.get_system_metrics()
        
        assert "cpu_usage" in metrics
        assert "memory_usage" in metrics
        assert "agent_performance" in metrics
        assert isinstance(metrics["cpu_usage"], (int, float))
    
    @pytest.mark.asyncio
    @patch('src.services.runtime.RuntimeService.get_logs')
    async def test_get_runtime_logs_service(self, mock_get_logs):
        """Test getting runtime logs via service"""
        from src.services.runtime import RuntimeService
        
        mock_get_logs.return_value = [
            {"timestamp": "2025-07-22T10:00:00Z", "level": "INFO", "message": "Test log"},
            {"timestamp": "2025-07-22T10:01:00Z", "level": "ERROR", "message": "Error log"}
        ]
        
        runtime_service = RuntimeService()
        logs = await runtime_service.get_logs()
        
        assert len(logs) == 2
        assert logs[0]["level"] == "INFO"
        assert logs[1]["level"] == "ERROR"
    
    @pytest.mark.asyncio
    @patch('src.services.runtime.RuntimeService.get_logs')
    async def test_get_filtered_logs_service(self, mock_get_logs):
        """Test getting filtered runtime logs via service"""
        from src.services.runtime import RuntimeService
        
        mock_get_logs.return_value = [
            {"timestamp": "2025-07-22T10:01:00Z", "level": "ERROR", "message": "Error log"}
        ]
        
        runtime_service = RuntimeService()
        logs = await runtime_service.get_logs(level="ERROR")
        
        assert len(logs) == 1
        assert logs[0]["level"] == "ERROR"


class TestAgentRuntime:
    """Test agent runtime engine"""
    
    @pytest.mark.asyncio
    async def test_runtime_initialization(self):
        """Test agent runtime initialization"""
        from src.agents.runtime import AgentRuntime
        
        agent_id = uuid.uuid4()
        config = {"max_concurrent_tasks": 5, "timeout_seconds": 300}
        
        runtime = AgentRuntime(agent_id, config)
        
        assert runtime.agent_id == agent_id
        assert runtime.config == config
        assert runtime.status.value == "stopped"
        assert runtime.current_tasks == 0
    
    @pytest.mark.asyncio
    async def test_runtime_start_stop(self):
        """Test agent runtime start/stop lifecycle"""
        from src.agents.runtime import AgentRuntime
        
        runtime = AgentRuntime(uuid.uuid4(), {})
        
        # Test start
        await runtime.start()
        assert runtime.status.value == "running"
        
        # Test stop
        await runtime.stop()
        assert runtime.status.value == "stopped"
    
    @pytest.mark.asyncio
    async def test_runtime_task_execution(self):
        """Test runtime task execution"""
        from src.agents.runtime import AgentRuntime
        
        runtime = AgentRuntime(uuid.uuid4(), {"max_concurrent_tasks": 2})
        await runtime.start()
        
        task_data = {
            "title": "Test Task",
            "type": "test",
            "input_data": {"test": "data"}
        }
        
        # Execute task
        result = await runtime.execute_task(task_data)
        
        assert result is not None
        assert "task_id" in result
        assert "status" in result
        assert "result" in result
    
    @pytest.mark.asyncio
    async def test_runtime_resource_monitoring(self):
        """Test runtime resource monitoring"""
        from src.agents.runtime import AgentRuntime
        
        runtime = AgentRuntime(uuid.uuid4(), {})
        await runtime.start()
        
        resources = await runtime.get_resource_usage()
        
        assert "cpu_usage" in resources
        assert "memory_usage" in resources
        assert "active_tasks" in resources
        assert isinstance(resources["cpu_usage"], (int, float))
    
    @pytest.mark.asyncio
    async def test_runtime_pause_resume(self):
        """Test runtime pause/resume functionality"""
        from src.agents.runtime import AgentRuntime
        
        runtime = AgentRuntime(uuid.uuid4(), {})
        await runtime.start()
        
        # Test pause
        await runtime.pause()
        assert runtime.status.value == "paused"
        
        # Test resume
        await runtime.resume()
        assert runtime.status.value == "running"
    
    @pytest.mark.asyncio
    async def test_runtime_task_queue_limit(self):
        """Test runtime task queue limit"""
        from src.agents.runtime import AgentRuntime
        
        runtime = AgentRuntime(uuid.uuid4(), {"max_concurrent_tasks": 1})
        await runtime.start()
        
        # Add task up to limit
        task_data = {"title": "Test Task", "type": "test"}
        result1 = await runtime.execute_task(task_data)
        assert result1 is not None
        
        # Adding another task should handle the queue properly
        result2 = await runtime.execute_task(task_data)
        assert result2 is not None