/* tslint:disable */
/* eslint-disable */
/**
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  Deal,
  DealCreate,
  DealDetails,
  DealStage,
  DealUpdate,
  DealWithDetails,
  ErrorResponse,
  ValidationError,
} from '../models/index';
import {
    DealFromJSON,
    DealToJSON,
    DealCreateFromJSON,
    DealCreateToJSON,
    DealDetailsFromJSON,
    DealDetailsToJSON,
    DealStageFromJSON,
    DealStageToJSON,
    DealUpdateFromJSON,
    DealUpdateToJSON,
    DealWithDetailsFromJSON,
    DealWithDetailsToJSON,
    ErrorResponseFromJSON,
    ErrorResponseToJSON,
    ValidationErrorFromJSON,
    ValidationErrorToJSON,
} from '../models/index';

export interface DealsGetRequest {
    stageId?: string;
    companyId?: string;
}

export interface DealsIdDeleteRequest {
    id: string;
}

export interface DealsIdGetRequest {
    id: string;
}

export interface DealsIdPutRequest {
    id: string;
    dealUpdate: DealUpdate;
}

export interface DealsPostRequest {
    dealCreate: DealCreate;
}

/**
 * 
 */
export class DealsApi extends runtime.BaseAPI {

    /**
     * Retrieve all deal stages ordered by pipeline order
     * List deal stages
     */
    async dealStagesGetRaw(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Array<DealStage>>> {
        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("bearerAuth", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/deal-stages`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => jsonValue.map(DealStageFromJSON));
    }

    /**
     * Retrieve all deal stages ordered by pipeline order
     * List deal stages
     */
    async dealStagesGet(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Array<DealStage>> {
        const response = await this.dealStagesGetRaw(initOverrides);
        return await response.value();
    }

    /**
     * Retrieve a list of all deals with company and stage information
     * List deals
     */
    async dealsGetRaw(requestParameters: DealsGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Array<DealWithDetails>>> {
        const queryParameters: any = {};

        if (requestParameters['stageId'] != null) {
            queryParameters['stage_id'] = requestParameters['stageId'];
        }

        if (requestParameters['companyId'] != null) {
            queryParameters['company_id'] = requestParameters['companyId'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("bearerAuth", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/deals`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => jsonValue.map(DealWithDetailsFromJSON));
    }

    /**
     * Retrieve a list of all deals with company and stage information
     * List deals
     */
    async dealsGet(requestParameters: DealsGetRequest = {}, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Array<DealWithDetails>> {
        const response = await this.dealsGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Delete a deal
     * Delete deal
     */
    async dealsIdDeleteRaw(requestParameters: DealsIdDeleteRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<void>> {
        if (requestParameters['id'] == null) {
            throw new runtime.RequiredError(
                'id',
                'Required parameter "id" was null or undefined when calling dealsIdDelete().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("bearerAuth", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/deals/{id}`.replace(`{${"id"}}`, encodeURIComponent(String(requestParameters['id']))),
            method: 'DELETE',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.VoidApiResponse(response);
    }

    /**
     * Delete a deal
     * Delete deal
     */
    async dealsIdDelete(requestParameters: DealsIdDeleteRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void> {
        await this.dealsIdDeleteRaw(requestParameters, initOverrides);
    }

    /**
     * Retrieve a specific deal by ID
     * Get deal
     */
    async dealsIdGetRaw(requestParameters: DealsIdGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<DealDetails>> {
        if (requestParameters['id'] == null) {
            throw new runtime.RequiredError(
                'id',
                'Required parameter "id" was null or undefined when calling dealsIdGet().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("bearerAuth", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/deals/{id}`.replace(`{${"id"}}`, encodeURIComponent(String(requestParameters['id']))),
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => DealDetailsFromJSON(jsonValue));
    }

    /**
     * Retrieve a specific deal by ID
     * Get deal
     */
    async dealsIdGet(requestParameters: DealsIdGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<DealDetails> {
        const response = await this.dealsIdGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Update an existing deal
     * Update deal
     */
    async dealsIdPutRaw(requestParameters: DealsIdPutRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Deal>> {
        if (requestParameters['id'] == null) {
            throw new runtime.RequiredError(
                'id',
                'Required parameter "id" was null or undefined when calling dealsIdPut().'
            );
        }

        if (requestParameters['dealUpdate'] == null) {
            throw new runtime.RequiredError(
                'dealUpdate',
                'Required parameter "dealUpdate" was null or undefined when calling dealsIdPut().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("bearerAuth", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/deals/{id}`.replace(`{${"id"}}`, encodeURIComponent(String(requestParameters['id']))),
            method: 'PUT',
            headers: headerParameters,
            query: queryParameters,
            body: DealUpdateToJSON(requestParameters['dealUpdate']),
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => DealFromJSON(jsonValue));
    }

    /**
     * Update an existing deal
     * Update deal
     */
    async dealsIdPut(requestParameters: DealsIdPutRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Deal> {
        const response = await this.dealsIdPutRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Create a new deal
     * Create deal
     */
    async dealsPostRaw(requestParameters: DealsPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Deal>> {
        if (requestParameters['dealCreate'] == null) {
            throw new runtime.RequiredError(
                'dealCreate',
                'Required parameter "dealCreate" was null or undefined when calling dealsPost().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("bearerAuth", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/deals`,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: DealCreateToJSON(requestParameters['dealCreate']),
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => DealFromJSON(jsonValue));
    }

    /**
     * Create a new deal
     * Create deal
     */
    async dealsPost(requestParameters: DealsPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Deal> {
        const response = await this.dealsPostRaw(requestParameters, initOverrides);
        return await response.value();
    }

}
