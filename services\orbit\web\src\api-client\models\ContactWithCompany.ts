/* tslint:disable */
/* eslint-disable */
/**
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { CompanyInfo } from './CompanyInfo';
import {
    CompanyInfoFromJSON,
    CompanyInfoFromJSONTyped,
    CompanyInfoToJSON,
    CompanyInfoToJSONTyped,
} from './CompanyInfo';

/**
 * 
 * @export
 * @interface ContactWithCompany
 */
export interface ContactWithCompany {
    /**
     * 
     * @type {string}
     * @memberof ContactWithCompany
     */
    id?: string;
    /**
     * 
     * @type {string}
     * @memberof ContactWithCompany
     */
    firstName?: string;
    /**
     * 
     * @type {string}
     * @memberof ContactWithCompany
     */
    lastName?: string;
    /**
     * 
     * @type {string}
     * @memberof ContactWithCompany
     */
    email?: string;
    /**
     * 
     * @type {string}
     * @memberof ContactWithCompany
     */
    phone?: string;
    /**
     * 
     * @type {string}
     * @memberof ContactWithCompany
     */
    jobTitle?: string;
    /**
     * 
     * @type {string}
     * @memberof ContactWithCompany
     */
    companyId?: string | null;
    /**
     * 
     * @type {string}
     * @memberof ContactWithCompany
     */
    createdBy?: string;
    /**
     * 
     * @type {boolean}
     * @memberof ContactWithCompany
     */
    isDeleted?: boolean;
    /**
     * 
     * @type {Date}
     * @memberof ContactWithCompany
     */
    createdAt?: Date;
    /**
     * 
     * @type {Date}
     * @memberof ContactWithCompany
     */
    updatedAt?: Date;
    /**
     * 
     * @type {CompanyInfo}
     * @memberof ContactWithCompany
     */
    company?: CompanyInfo | null;
}

/**
 * Check if a given object implements the ContactWithCompany interface.
 */
export function instanceOfContactWithCompany(value: object): value is ContactWithCompany {
    return true;
}

export function ContactWithCompanyFromJSON(json: any): ContactWithCompany {
    return ContactWithCompanyFromJSONTyped(json, false);
}

export function ContactWithCompanyFromJSONTyped(json: any, ignoreDiscriminator: boolean): ContactWithCompany {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'] == null ? undefined : json['id'],
        'firstName': json['first_name'] == null ? undefined : json['first_name'],
        'lastName': json['last_name'] == null ? undefined : json['last_name'],
        'email': json['email'] == null ? undefined : json['email'],
        'phone': json['phone'] == null ? undefined : json['phone'],
        'jobTitle': json['job_title'] == null ? undefined : json['job_title'],
        'companyId': json['company_id'] == null ? undefined : json['company_id'],
        'createdBy': json['created_by'] == null ? undefined : json['created_by'],
        'isDeleted': json['is_deleted'] == null ? undefined : json['is_deleted'],
        'createdAt': json['created_at'] == null ? undefined : (new Date(json['created_at'])),
        'updatedAt': json['updated_at'] == null ? undefined : (new Date(json['updated_at'])),
        'company': json['company'] == null ? undefined : CompanyInfoFromJSON(json['company']),
    };
}

  export function ContactWithCompanyToJSON(json: any): ContactWithCompany {
      return ContactWithCompanyToJSONTyped(json, false);
  }

  export function ContactWithCompanyToJSONTyped(value?: ContactWithCompany | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'first_name': value['firstName'],
        'last_name': value['lastName'],
        'email': value['email'],
        'phone': value['phone'],
        'job_title': value['jobTitle'],
        'company_id': value['companyId'],
        'created_by': value['createdBy'],
        'is_deleted': value['isDeleted'],
        'created_at': value['createdAt'] == null ? undefined : ((value['createdAt']).toISOString()),
        'updated_at': value['updatedAt'] == null ? undefined : ((value['updatedAt']).toISOString()),
        'company': CompanyInfoToJSON(value['company']),
    };
}

