# Database Module Variables

variable "project_name" {
  description = "Name of the project (used for resource naming)"
  type        = string
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
  default     = "dev"
  
  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "Environment must be one of: dev, staging, prod."
  }
}

variable "region" {
  description = "GCP region for the database"
  type        = string
  default     = "australia-southeast1"
}

variable "vpc_network" {
  description = "VPC network self-link for private IP configuration"
  type        = string
}

variable "postgres_version" {
  description = "PostgreSQL version for Cloud SQL instance"
  type        = string
  default     = "POSTGRES_15"
  
  validation {
    condition = contains([
      "POSTGRES_13", "POSTGRES_14", "POSTGRES_15", "POSTGRES_16"
    ], var.postgres_version)
    error_message = "PostgreSQL version must be a supported Cloud SQL version."
  }
}

variable "instance_tier" {
  description = "Machine type for the Cloud SQL instance"
  type        = string
  default     = "db-f1-micro"
  
  validation {
    condition = can(regex("^db-", var.instance_tier))
    error_message = "Instance tier must be a valid Cloud SQL machine type (starting with 'db-')."
  }
}

variable "availability_type" {
  description = "Availability type for the instance (ZONAL or REGIONAL)"
  type        = string
  default     = "ZONAL"
  
  validation {
    condition     = contains(["ZONAL", "REGIONAL"], var.availability_type)
    error_message = "Availability type must be either ZONAL or REGIONAL."
  }
}

variable "disk_size_gb" {
  description = "Initial disk size in GB"
  type        = number
  default     = 20
  
  validation {
    condition     = var.disk_size_gb >= 10 && var.disk_size_gb <= 4000
    error_message = "Disk size must be between 10 and 4000 GB."
  }
}

variable "max_disk_size_gb" {
  description = "Maximum disk size in GB for autoresize"
  type        = number
  default     = 100
  
  validation {
    condition     = var.max_disk_size_gb >= 10 && var.max_disk_size_gb <= 4000
    error_message = "Maximum disk size must be between 10 and 4000 GB."
  }
}

variable "backup_retention_days" {
  description = "Number of backup snapshots to retain"
  type        = number
  default     = 7
  
  validation {
    condition     = var.backup_retention_days >= 1 && var.backup_retention_days <= 365
    error_message = "Backup retention must be between 1 and 365 days."
  }
}

variable "deletion_protection" {
  description = "Whether to enable deletion protection for the instance"
  type        = bool
  default     = true
}

variable "database_name" {
  description = "Name of the application database"
  type        = string
  default     = "platform_db"
  
  validation {
    condition     = can(regex("^[a-zA-Z][a-zA-Z0-9_]*$", var.database_name))
    error_message = "Database name must start with a letter and contain only letters, numbers, and underscores."
  }
}

variable "database_user" {
  description = "Name of the application database user"
  type        = string
  default     = "platform_user"
  
  validation {
    condition     = can(regex("^[a-zA-Z][a-zA-Z0-9_]*$", var.database_user))
    error_message = "Database user must start with a letter and contain only letters, numbers, and underscores."
  }
}

variable "authorized_networks" {
  description = "List of authorized networks for database access"
  type = list(object({
    name = string
    cidr = string
  }))
  default = []
}

# Migration automation variables
variable "enable_migration_automation" {
  description = "Whether to enable automated database migrations via Cloud Build"
  type        = bool
  default     = false
}

variable "github_repo_owner" {
  description = "GitHub repository owner for migration automation"
  type        = string
  default     = ""
}

variable "github_repo_name" {
  description = "GitHub repository name for migration automation"
  type        = string
  default     = ""
}

variable "migration_branch" {
  description = "Git branch to trigger migrations from"
  type        = string
  default     = "main"
}

variable "migration_cloudbuild_file" {
  description = "Path to Cloud Build configuration file for migrations"
  type        = string
  default     = "platform/db/cloudbuild.yaml"
}