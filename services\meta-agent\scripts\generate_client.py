#!/usr/bin/env python3
"""
TypeScript Client Generator

This script generates a fully typed TypeScript client from the OpenAPI specification.
"""

import json
import os
import subprocess
import sys
from pathlib import Path
from typing import Dict, Any, List

class TypeScriptClientGenerator:
    """Generates TypeScript client code from OpenAPI specifications."""
    
    def __init__(self, openapi_spec_path: str, output_dir: str):
        self.openapi_spec_path = Path(openapi_spec_path)
        self.output_dir = Path(output_dir)
        self.client_dir = self.output_dir / "generated"
        
    def generate_client(self) -> None:
        """Generate TypeScript client using openapi-generator."""
        
        print("🚀 Generating TypeScript client...")
        
        # Ensure output directory exists
        self.client_dir.mkdir(parents=True, exist_ok=True)
        
        # Generate client using openapi-generator-cli
        cmd = [
            "npx", "@openapitools/openapi-generator-cli", "generate",
            "-i", str(self.openapi_spec_path),
            "-g", "typescript-axios",
            "-o", str(self.client_dir),
            "--additional-properties",
            "supportsES6=true,npmName=@ai-platform/api-client,withInterfaces=true,typescriptThreePlus=true"
        ]
        
        try:
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            print("✅ TypeScript client generated successfully!")
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Error generating client: {e}")
            print(f"stdout: {e.stdout}")
            print(f"stderr: {e.stderr}")
            
            # Fallback to manual generation
            print("🔄 Falling back to manual client generation...")
            self._generate_manual_client()
    
    def _generate_manual_client(self) -> None:
        """Generate TypeScript client manually when openapi-generator fails."""
        
        # Load OpenAPI spec
        with open(self.openapi_spec_path, 'r') as f:
            spec = json.load(f)
        
        # Generate types
        self._generate_types(spec)
        
        # Generate API client
        self._generate_api_client(spec)
        
        # Generate index file
        self._generate_index_file()
        
        # Generate package.json
        self._generate_package_json()
        
        print("✅ Manual TypeScript client generated successfully!")
    
    def _generate_types(self, spec: Dict[str, Any]) -> None:
        """Generate TypeScript type definitions."""
        
        types_content = """// Auto-generated TypeScript types from OpenAPI specification
// Do not edit this file manually

"""
        
        # Generate schema types
        schemas = spec.get("components", {}).get("schemas", {})
        
        for schema_name, schema_def in schemas.items():
            types_content += self._generate_interface(schema_name, schema_def)
            types_content += "\n"
        
        # Generate common types
        types_content += """
// Common API types
export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: PaginationInfo;
}

export interface ApiError {
  error: string;
  error_code?: string;
  details?: Record<string, any>;
  timestamp: string;
  request_id?: string;
}

// Request configuration
export interface RequestConfig {
  timeout?: number;
  headers?: Record<string, string>;
  params?: Record<string, any>;
}

// HTTP methods
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

// API client configuration
export interface ApiClientConfig {
  baseURL?: string;
  timeout?: number;
  headers?: Record<string, string>;
  apiKey?: string;
  bearerToken?: string;
}
"""
        
        # Write types file
        with open(self.client_dir / "types.ts", "w") as f:
            f.write(types_content)
    
    def _generate_interface(self, name: str, schema: Dict[str, Any]) -> str:
        """Generate TypeScript interface from JSON schema."""
        
        if schema.get("type") != "object":
            return f"export type {name} = any; // Complex type - define manually\n"
        
        interface_content = f"export interface {name} {{\n"
        
        properties = schema.get("properties", {})
        required = schema.get("required", [])
        
        for prop_name, prop_def in properties.items():
            optional = "" if prop_name in required else "?"
            type_annotation = self._get_typescript_type(prop_def)
            description = prop_def.get("description", "")
            
            if description:
                interface_content += f"  /** {description} */\n"
            
            interface_content += f"  {prop_name}{optional}: {type_annotation};\n"
        
        interface_content += "}\n"
        
        return interface_content
    
    def _get_typescript_type(self, prop_def: Dict[str, Any]) -> str:
        """Convert JSON schema type to TypeScript type."""
        
        prop_type = prop_def.get("type", "any")
        
        if prop_type == "string":
            if "enum" in prop_def:
                enum_values = prop_def["enum"]
                return " | ".join(f'"{value}"' for value in enum_values)
            elif prop_def.get("format") == "date-time":
                return "string" # Could be Date, but string is more common for APIs
            elif prop_def.get("format") == "uuid":
                return "string"
            else:
                return "string"
        elif prop_type == "integer" or prop_type == "number":
            return "number"
        elif prop_type == "boolean":
            return "boolean"
        elif prop_type == "array":
            items = prop_def.get("items", {})
            item_type = self._get_typescript_type(items)
            return f"{item_type}[]"
        elif prop_type == "object":
            if "additionalProperties" in prop_def:
                additional_type = self._get_typescript_type(prop_def["additionalProperties"])
                return f"Record<string, {additional_type}>"
            else:
                return "Record<string, any>"
        elif "$ref" in prop_def:
            ref = prop_def["$ref"]
            return ref.split("/")[-1]  # Extract type name from #/components/schemas/TypeName
        else:
            return "any"
    
    def _generate_api_client(self, spec: Dict[str, Any]) -> None:
        """Generate main API client class."""
        
        client_content = """// Auto-generated API client from OpenAPI specification
// Do not edit this file manually

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import * as Types from './types';

export class ApiClient {
  private client: AxiosInstance;
  
  constructor(config: Types.ApiClientConfig = {}) {
    const {
      baseURL = 'http://localhost:8000',
      timeout = 30000,
      headers = {},
      apiKey,
      bearerToken,
    } = config;
    
    this.client = axios.create({
      baseURL,
      timeout,
      headers: {
        'Content-Type': 'application/json',
        ...headers,
      },
    });
    
    // Add auth interceptor
    this.client.interceptors.request.use((config) => {
      if (bearerToken) {
        config.headers.Authorization = `Bearer ${bearerToken}`;
      } else if (apiKey) {
        config.headers['X-API-Key'] = apiKey;
      }
      return config;
    });
    
    // Add response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.data) {
          throw new ApiError(error.response.data);
        }
        throw error;
      }
    );
  }
  
  // Generic request method
  private async request<T>(
    method: Types.HttpMethod,
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const response: AxiosResponse<T> = await this.client.request({
      method,
      url,
      data,
      ...config,
    });
    
    return response.data;
  }

"""
        
        # Generate API methods from paths
        paths = spec.get("paths", {})
        
        for path, methods in paths.items():
            for method, operation in methods.items():
                if method.upper() in ["GET", "POST", "PUT", "DELETE", "PATCH"]:
                    client_content += self._generate_api_method(path, method.upper(), operation)
        
        # Add error class
        client_content += """
}

export class ApiError extends Error {
  public readonly error_code?: string;
  public readonly details?: Record<string, any>;
  public readonly timestamp: string;
  public readonly request_id?: string;
  
  constructor(errorData: Types.ApiError) {
    super(errorData.error);
    this.name = 'ApiError';
    this.error_code = errorData.error_code;
    this.details = errorData.details;
    this.timestamp = errorData.timestamp;
    this.request_id = errorData.request_id;
  }
}

// Export types for convenience
export * from './types';
"""
        
        # Write client file
        with open(self.client_dir / "client.ts", "w") as f:
            f.write(client_content)
    
    def _generate_api_method(self, path: str, method: str, operation: Dict[str, Any]) -> str:
        """Generate TypeScript method for API endpoint."""
        
        operation_id = operation.get("operationId", f"{method.lower()}{path.replace('/', '_')}")
        summary = operation.get("summary", "")
        parameters = operation.get("parameters", [])
        request_body = operation.get("requestBody")
        responses = operation.get("responses", {})
        
        # Generate method signature
        method_signature = f"  /**\n   * {summary}\n   */\n"
        method_signature += f"  async {operation_id}("
        
        # Add parameters
        param_parts = []
        path_params = [p for p in parameters if p["in"] == "path"]
        query_params = [p for p in parameters if p["in"] == "query"]
        
        # Path parameters
        for param in path_params:
            param_name = param["name"]
            param_type = self._get_param_type(param["schema"])
            param_parts.append(f"{param_name}: {param_type}")
        
        # Request body
        if request_body:
            param_parts.append("data: any") # Simplified for now
        
        # Query parameters as options
        if query_params:
            param_parts.append("params?: Record<string, any>")
        
        # Config parameter
        param_parts.append("config?: AxiosRequestConfig")
        
        method_signature += ", ".join(param_parts)
        method_signature += "): Promise<any> {\n"
        
        # Generate method body
        method_body = ""
        
        # Replace path parameters
        api_path = path
        for param in path_params:
            param_name = param["name"]
            api_path = api_path.replace(f"{{{param_name}}}", f"${{{param_name}}}")
        
        # Build request config
        request_config = "{ ...config }"
        if query_params:
            request_config = "{ params, ...config }"
        
        # Make request
        if request_body:
            method_body += f"    return this.request<any>('{method}', `{api_path}`, data, {request_config});\n"
        else:
            method_body += f"    return this.request<any>('{method}', `{api_path}`, undefined, {request_config});\n"
        
        method_body += "  }\n\n"
        
        return method_signature + method_body
    
    def _get_param_type(self, schema: Dict[str, Any]) -> str:
        """Get TypeScript type for parameter schema."""
        return self._get_typescript_type(schema)
    
    def _generate_index_file(self) -> None:
        """Generate index.ts file."""
        
        index_content = """// AI Agent Platform API Client
// Auto-generated from OpenAPI specification

export { ApiClient, ApiError } from './client';
export * from './types';

// Default client instance
import { ApiClient } from './client';

let defaultClient: ApiClient | null = null;

export function createClient(config?: Parameters<typeof ApiClient>[0]): ApiClient {
  return new ApiClient(config);
}

export function getDefaultClient(): ApiClient {
  if (!defaultClient) {
    defaultClient = new ApiClient();
  }
  return defaultClient;
}

export function setDefaultClient(client: ApiClient): void {
  defaultClient = client;
}

// Convenience methods using default client
export const api = {
  get client() {
    return getDefaultClient();
  },
  
  configure(config: Parameters<typeof ApiClient>[0]) {
    defaultClient = new ApiClient(config);
  },
};
"""
        
        with open(self.client_dir / "index.ts", "w") as f:
            f.write(index_content)
    
    def _generate_package_json(self) -> None:
        """Generate package.json for the client."""
        
        package_json = {
            "name": "@ai-platform/api-client",
            "version": "2.0.0",
            "description": "TypeScript client for AI Agent Platform API",
            "main": "dist/index.js",
            "types": "dist/index.d.ts",
            "files": ["dist"],
            "scripts": {
                "build": "tsc",
                "prepublishOnly": "npm run build"
            },
            "dependencies": {
                "axios": "^1.6.2"
            },
            "devDependencies": {
                "typescript": "^5.3.0",
                "@types/node": "^20.10.0"
            },
            "keywords": [
                "ai-agent-platform",
                "api-client",
                "typescript",
                "openapi"
            ],
            "author": "AI Agent Platform Team",
            "license": "MIT"
        }
        
        with open(self.client_dir / "package.json", "w") as f:
            json.dump(package_json, f, indent=2)
    
    def integrate_with_frontend(self) -> None:
        """Integrate generated client with frontend project."""
        
        print("🔗 Integrating client with frontend...")
        
        frontend_lib_dir = Path("../frontend/src/lib/api")
        frontend_lib_dir.mkdir(parents=True, exist_ok=True)
        
        # Copy generated files
        import shutil
        
        for file_name in ["types.ts", "client.ts", "index.ts"]:
            src_file = self.client_dir / file_name
            dst_file = frontend_lib_dir / file_name
            
            if src_file.exists():
                shutil.copy2(src_file, dst_file)
                print(f"✅ Copied {file_name} to frontend")
        
        # Generate frontend-specific wrapper
        self._generate_frontend_wrapper(frontend_lib_dir)
        
        print("✅ Client integration completed!")
    
    def _generate_frontend_wrapper(self, output_dir: Path) -> None:
        """Generate frontend-specific API wrapper."""
        
        wrapper_content = """// Frontend API wrapper with React Query integration
import { QueryClient } from '@tanstack/react-query';
import { ApiClient, ApiClientConfig } from './client';

// Create query client
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      retry: 1,
      refetchOnWindowFocus: false,
    },
    mutations: {
      retry: 1,
    },
  },
});

// API client instance
export const apiClient = new ApiClient({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000',
  timeout: 30000,
});

// Configure client based on environment
if (typeof window !== 'undefined') {
  // Client-side configuration
  const token = localStorage.getItem('auth_token');
  if (token) {
    apiClient.client.defaults.headers.Authorization = `Bearer ${token}`;
  }
}

// Query keys factory
export const queryKeys = {
  agents: {
    all: ['agents'] as const,
    lists: () => [...queryKeys.agents.all, 'list'] as const,
    list: (filters: Record<string, any>) => [...queryKeys.agents.lists(), filters] as const,
    details: () => [...queryKeys.agents.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.agents.details(), id] as const,
  },
  tasks: {
    all: ['tasks'] as const,
    lists: () => [...queryKeys.tasks.all, 'list'] as const,
    list: (filters: Record<string, any>) => [...queryKeys.tasks.lists(), filters] as const,
    details: () => [...queryKeys.tasks.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.tasks.details(), id] as const,
  },
  migration: {
    all: ['migration'] as const,
    analyses: () => [...queryKeys.migration.all, 'analyses'] as const,
    analysis: (id: string) => [...queryKeys.migration.analyses(), id] as const,
    plans: () => [...queryKeys.migration.all, 'plans'] as const,
    plan: (id: string) => [...queryKeys.migration.plans(), id] as const,
  },
  system: {
    all: ['system'] as const,
    stats: () => [...queryKeys.system.all, 'stats'] as const,
    health: () => [...queryKeys.system.all, 'health'] as const,
  },
} as const;

// Export everything from the generated client
export * from './client';
export * from './types';
"""
        
        with open(output_dir / "wrapper.ts", "w") as f:
            f.write(wrapper_content)

def main():
    """Main function to generate TypeScript client."""
    
    print("🎯 AI Agent Platform - TypeScript Client Generator")
    print("=" * 50)
    
    # First, generate OpenAPI spec
    print("1. Generating OpenAPI specification...")
    
    # Import and run OpenAPI generator
    sys.path.append(str(Path(__file__).parent.parent / "backend"))
    
    try:
        from openapi_generator import OpenAPIGenerator
        
        openapi_gen = OpenAPIGenerator()
        spec_dir = openapi_gen.save_specifications("openapi")
        spec_file = Path(spec_dir) / "openapi.json"
        
        print(f"✅ OpenAPI spec generated: {spec_file}")
        
    except ImportError:
        print("❌ Could not import OpenAPI generator")
        return
    
    # Generate TypeScript client
    print("\n2. Generating TypeScript client...")
    
    client_gen = TypeScriptClientGenerator(
        openapi_spec_path=str(spec_file),
        output_dir="frontend/src/lib/api"
    )
    
    client_gen.generate_client()
    
    # Integrate with frontend
    print("\n3. Integrating with frontend...")
    client_gen.integrate_with_frontend()
    
    print("\n🎉 TypeScript client generation completed!")
    print("\nNext steps:")
    print("1. Review generated client code")
    print("2. Update frontend components to use typed client")
    print("3. Test API integration")
    print("4. Run frontend build to verify types")

if __name__ == "__main__":
    main()