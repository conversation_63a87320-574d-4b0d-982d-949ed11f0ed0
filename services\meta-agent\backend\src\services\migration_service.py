"""Migration Service Integration.

This service integrates the application migration system with the platform's
core services and provides API endpoints for migration operations.
"""

from typing import Dict, Any, List, Optional
from dataclasses import asdict
import asyncio
from migration import (
    ApplicationAnalyzer,
    MigrationPlanner,
    ApplicationMigrator,
    ApplicationAnalysis,
    MigrationPlan,
    MigrationProject
)
from intelligence.gateway import AIGateway
from generation.ai_workflow_generator import AgentWorkflowGenerator
from utils.logging import get_logger, LoggerMixin
from utils.exceptions import Agent<PERSON>rror, ValidationError

logger = get_logger(__name__)

class MigrationService(LoggerMixin):
    """Service for managing application migrations."""
    
    def __init__(
        self,
        ai_gateway: AIGateway,
        workflow_generator: AgentWorkflowGenerator
    ):
        self.ai_gateway = ai_gateway
        self.workflow_generator = workflow_generator
        
        # Core migration components
        self.analyzer = ApplicationAnalyzer(ai_gateway)
        self.planner = MigrationPlanner(ai_gateway)
        self.migrator = ApplicationMigrator(ai_gateway, workflow_generator)
        
        # State tracking
        self.analyses: Dict[str, ApplicationAnalysis] = {}
        self.plans: Dict[str, MigrationPlan] = {}
        self.projects: Dict[str, MigrationProject] = {}
    
    async def analyze_application(self, app_path: str) -> Dict[str, Any]:
        """Analyze an application for migration."""
        
        try:
            analysis = await self.analyzer.analyze_application(app_path)
            self.analyses[analysis.id] = analysis
            
            self.log_operation("application_analyzed_via_service",
                             analysis_id=analysis.id,
                             app_path=app_path,
                             app_type=analysis.app_type.value)
            
            return {
                "analysis_id": analysis.id,
                "app_type": analysis.app_type.value,
                "complexity_score": analysis.complexity_score,
                "migration_feasibility": analysis.migration_feasibility,
                "recommended_strategy": analysis.recommended_strategy.value,
                "estimated_effort_hours": analysis.estimated_effort,
                "technology_stack": analysis.technology_stack,
                "endpoints_count": len(analysis.endpoints),
                "models_count": len(analysis.data_models),
                "dependencies_count": len(analysis.dependencies),
                "external_integrations_count": len(analysis.external_integrations)
            }
            
        except Exception as e:
            self.log_error("application_analysis_service_error", e, app_path=app_path)
            raise AgentError(f"Application analysis failed: {e}")
    
    async def get_analysis_details(self, analysis_id: str) -> Dict[str, Any]:
        """Get detailed analysis results."""
        
        analysis = self.analyses.get(analysis_id)
        if not analysis:
            raise ValidationError(f"Analysis {analysis_id} not found")
        
        return {
            "id": analysis.id,
            "app_path": analysis.app_path,
            "app_type": analysis.app_type.value,
            "technology_stack": analysis.technology_stack,
            "complexity_score": analysis.complexity_score,
            "migration_feasibility": analysis.migration_feasibility,
            "recommended_strategy": analysis.recommended_strategy.value,
            "estimated_effort_hours": analysis.estimated_effort,
            "entry_points": analysis.entry_points,
            "endpoints": analysis.endpoints[:10],  # Limit for response size
            "data_models": analysis.data_models[:10],
            "business_logic": analysis.business_logic[:10],
            "dependencies": dict(list(analysis.dependencies.items())[:20]),
            "external_integrations": analysis.external_integrations,
            "database_schemas": analysis.database_schemas,
            "environment_vars": analysis.environment_vars,
            "created_at": analysis.created_at.isoformat()
        }
    
    async def create_migration_plan(self, analysis_id: str, custom_strategy: Optional[str] = None) -> Dict[str, Any]:
        """Create a migration plan for an analyzed application."""
        
        analysis = self.analyses.get(analysis_id)
        if not analysis:
            raise ValidationError(f"Analysis {analysis_id} not found")
        
        # Override strategy if provided
        if custom_strategy:
            try:
                from migration.app_to_agent import MigrationStrategy
                analysis.recommended_strategy = MigrationStrategy(custom_strategy)
            except ValueError:
                raise ValidationError(f"Invalid migration strategy: {custom_strategy}")
        
        try:
            plan = await self.planner.create_migration_plan(analysis)
            self.plans[plan.id] = plan
            
            self.log_operation("migration_plan_created_via_service",
                             plan_id=plan.id,
                             analysis_id=analysis_id,
                             strategy=plan.strategy.value)
            
            return {
                "plan_id": plan.id,
                "analysis_id": plan.analysis_id,
                "strategy": plan.strategy.value,
                "estimated_timeline_days": plan.estimated_timeline,
                "phases_count": len(plan.phases),
                "agents_to_generate": len(plan.agent_specifications),
                "phases": plan.phases,
                "success_criteria": plan.success_criteria,
                "risk_assessment": plan.risk_assessment,
                "resource_requirements": plan.resource_requirements
            }
            
        except Exception as e:
            self.log_error("migration_plan_service_error", e, analysis_id=analysis_id)
            raise AgentError(f"Migration plan creation failed: {e}")
    
    async def get_migration_plan_details(self, plan_id: str) -> Dict[str, Any]:
        """Get detailed migration plan."""
        
        plan = self.plans.get(plan_id)
        if not plan:
            raise ValidationError(f"Migration plan {plan_id} not found")
        
        return {
            "id": plan.id,
            "analysis_id": plan.analysis_id,
            "strategy": plan.strategy.value,
            "phases": plan.phases,
            "agent_specifications": plan.agent_specifications,
            "data_migration_plan": plan.data_migration_plan,
            "testing_strategy": plan.testing_strategy,
            "rollback_plan": plan.rollback_plan,
            "risk_assessment": plan.risk_assessment,
            "success_criteria": plan.success_criteria,
            "estimated_timeline_days": plan.estimated_timeline,
            "resource_requirements": plan.resource_requirements
        }
    
    async def start_migration(self, plan_id: str) -> Dict[str, Any]:
        """Start executing a migration plan."""
        
        plan = self.plans.get(plan_id)
        if not plan:
            raise ValidationError(f"Migration plan {plan_id} not found")
        
        analysis = self.analyses.get(plan.analysis_id)
        if not analysis:
            raise ValidationError(f"Analysis {plan.analysis_id} not found")
        
        try:
            project_id = await self.migrator.start_migration(analysis, plan)
            
            # Get project details
            project = self.migrator.get_migration_project(project_id)
            if project:
                self.projects[project_id] = project
            
            self.log_operation("migration_started_via_service",
                             project_id=project_id,
                             plan_id=plan_id,
                             analysis_id=plan.analysis_id)
            
            return {
                "project_id": project_id,
                "name": project.name if project else "Migration Project",
                "status": project.status.value if project else "started",
                "estimated_timeline_days": plan.estimated_timeline,
                "phases_count": len(plan.phases)
            }
            
        except Exception as e:
            self.log_error("migration_start_service_error", e, plan_id=plan_id)
            raise AgentError(f"Migration start failed: {e}")
    
    async def get_migration_status(self, project_id: str) -> Dict[str, Any]:
        """Get migration project status."""
        
        project = self.migrator.get_migration_project(project_id)
        if not project:
            project = self.projects.get(project_id)
        
        if not project:
            raise ValidationError(f"Migration project {project_id} not found")
        
        return {
            "project_id": project.id,
            "name": project.name,
            "description": project.description,
            "status": project.status.value,
            "progress": project.progress,
            "current_phase": project.current_phase,
            "generated_agents": project.generated_agents,
            "created_at": project.created_at.isoformat(),
            "started_at": project.started_at.isoformat() if project.started_at else None,
            "completed_at": project.completed_at.isoformat() if project.completed_at else None,
            "error": project.error,
            "recent_logs": project.logs[-10:] if hasattr(project, 'logs') else []  # Last 10 logs
        }
    
    async def get_migration_logs(self, project_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Get migration project logs."""
        
        project = self.migrator.get_migration_project(project_id)
        if not project:
            project = self.projects.get(project_id)
        
        if not project:
            raise ValidationError(f"Migration project {project_id} not found")
        
        logs = getattr(project, 'logs', [])
        
        # Convert logs to serializable format
        serializable_logs = []
        for log in logs[-limit:]:
            serializable_log = {
                "timestamp": log["timestamp"].isoformat(),
                "event_type": log["event_type"],
                "details": log["details"]
            }
            serializable_logs.append(serializable_log)
        
        return serializable_logs
    
    def list_analyses(self) -> List[Dict[str, Any]]:
        """List all application analyses."""
        
        return [
            {
                "id": analysis.id,
                "app_path": analysis.app_path,
                "app_type": analysis.app_type.value,
                "complexity_score": analysis.complexity_score,
                "migration_feasibility": analysis.migration_feasibility,
                "recommended_strategy": analysis.recommended_strategy.value,
                "created_at": analysis.created_at.isoformat()
            }
            for analysis in self.analyses.values()
        ]
    
    def list_migration_plans(self) -> List[Dict[str, Any]]:
        """List all migration plans."""
        
        return [
            {
                "id": plan.id,
                "analysis_id": plan.analysis_id,
                "strategy": plan.strategy.value,
                "estimated_timeline_days": plan.estimated_timeline,
                "phases_count": len(plan.phases),
                "agents_count": len(plan.agent_specifications)
            }
            for plan in self.plans.values()
        ]
    
    def list_migration_projects(self) -> List[Dict[str, Any]]:
        """List all migration projects."""
        
        all_projects = list(self.projects.values()) + self.migrator.list_migration_projects()
        
        return [
            {
                "id": project.id,
                "name": project.name,
                "status": project.status.value,
                "progress": project.progress,
                "created_at": project.created_at.isoformat(),
                "started_at": project.started_at.isoformat() if project.started_at else None,
                "completed_at": project.completed_at.isoformat() if project.completed_at else None,
                "generated_agents_count": len(project.generated_agents)
            }
            for project in all_projects
        ]
    
    async def get_migration_summary(self) -> Dict[str, Any]:
        """Get overall migration system summary."""
        
        projects = self.list_migration_projects()
        
        # Calculate status counts
        status_counts = {}
        for project in projects:
            status = project["status"]
            status_counts[status] = status_counts.get(status, 0) + 1
        
        # Calculate success rate for completed projects
        completed_projects = [p for p in projects if p["status"] == "completed"]
        failed_projects = [p for p in projects if p["status"] == "failed"]
        success_rate = (
            len(completed_projects) / (len(completed_projects) + len(failed_projects))
            if (completed_projects or failed_projects) else 1.0
        )
        
        return {
            "total_analyses": len(self.analyses),
            "total_migration_plans": len(self.plans),
            "total_projects": len(projects),
            "active_migrations": len([p for p in projects if p["status"] in ["planning", "migrating", "testing"]]),
            "completed_migrations": len(completed_projects),
            "failed_migrations": len(failed_projects),
            "success_rate": success_rate,
            "status_distribution": status_counts,
            "most_common_strategies": self._get_strategy_stats(),
            "average_complexity": self._get_average_complexity()
        }
    
    def _get_strategy_stats(self) -> Dict[str, int]:
        """Get statistics on migration strategies."""
        
        strategy_counts = {}
        for plan in self.plans.values():
            strategy = plan.strategy.value
            strategy_counts[strategy] = strategy_counts.get(strategy, 0) + 1
        
        return strategy_counts
    
    def _get_average_complexity(self) -> float:
        """Get average complexity score."""
        
        if not self.analyses:
            return 0.0
        
        total_complexity = sum(analysis.complexity_score for analysis in self.analyses.values())
        return total_complexity / len(self.analyses)
    
    async def cleanup_completed_projects(self, days_old: int = 30) -> int:
        """Clean up old completed projects."""
        
        from datetime import datetime, timedelta
        cutoff_date = datetime.now() - timedelta(days=days_old)
        
        cleaned_count = 0
        project_ids_to_remove = []
        
        for project_id, project in self.projects.items():
            if (project.status.value in ["completed", "failed"] and
                project.completed_at and
                project.completed_at < cutoff_date):
                project_ids_to_remove.append(project_id)
        
        for project_id in project_ids_to_remove:
            del self.projects[project_id]
            cleaned_count += 1
        
        self.log_operation("migration_cleanup_completed", cleaned_projects=cleaned_count)
        
        return cleaned_count