# Software Version Information

This file documents all software versions used in the CRM monorepo project.

## Core Build System

| Software | Version | Purpose |
|----------|---------|---------|
| Bazel | 8.3.1 | Build system |
| Bazelisk | 1.26.0 | Bazel version manager |
| Bzlmod | Enabled | Modern Bazel module system |

## Languages & Runtimes

| Language | Version | Configuration File |
|----------|---------|-------------------|
| Node.js | 24.3.0 | - |
| npm | 11.4.2 | - |
| Python | 3.12+ | `pyproject.toml` |
| Go | 1.21 | `go.mod` |
| Java | 21 | Bazel remote JDK |

## Frontend Stack (CRM Web App)

| Package | Version | Purpose |
|---------|---------|---------|
| React | 18.3.1 | UI Framework |
| TypeScript | 5.5.3 | Type system |
| Vite | 5.4.1 | Build tool |
| Tailwind CSS | 3.4.11 | Styling |
| React Query | 5.56.2 | Data fetching |
| React Router | 6.26.2 | Routing |
| Radix UI | 1.1.x-1.2.x | UI components |

## Backend Dependencies

| Package | Version | Language | Purpose |
|---------|---------|----------|---------|
| FastAPI | 0.104.0+ | Python | Web framework |
| Gin | 1.9.1 | Go | Web framework |
| PostgreSQL | 17 | - | Database |
| Supabase | 2.49.3 | - | Backend service (legacy) |

## Development Tools

| Tool | Version | Purpose |
|------|---------|---------|
| ESLint | 9.9.0 | Linting |
| OpenAPI Generator | 7.2.0 | API client generation |
| Lovable Tagger | 1.1.7 | Development tool |

## Key Dependencies by Service

### CRM Web App (`services/orbit/web/`)
- React 18.3.1
- TypeScript 5.5.3  
- Vite 5.4.1
- Tailwind CSS 3.4.11

### Python Services (`services/examples/python/`)
- FastAPI 0.104.0+
- Uvicorn 0.24.0+
- Pydantic 2.0.0+

### Go Services (`services/examples/go/`, `services/orbit/crm_backend/`)
- Gin 1.9.1
- UUID 1.6.0
- JWT 5.2.0

### Java Services (`services/examples/java/`)
- Spring Boot (configured)
- Maven (build system)

## Version Management

- **Bazel**: Managed via Bazelisk
- **Node.js**: Managed via nvm or system package manager
- **Python**: Managed via pyproject.toml and UV
- **Go**: Managed via go.mod
- **Java**: Managed via Bazel remote JDK

## Compatibility Notes

- **Bazel 8.3.1**: Uses Bzlmod (modern module system), WORKSPACE deprecated
- **Node.js 24.3.0**: Latest LTS features supported
- **React 18.3.1**: Concurrent features enabled
- **TypeScript 5.5.3**: Latest strict type checking
- **Python 3.12+**: Modern Python features required

## Last Updated

**Date**: July 10, 2025
**By**: Claude Code
**Bazel Version**: 8.3.1