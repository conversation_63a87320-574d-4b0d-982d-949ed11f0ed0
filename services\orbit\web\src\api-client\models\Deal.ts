/* tslint:disable */
/* eslint-disable */
/**
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface Deal
 */
export interface Deal {
    /**
     * 
     * @type {string}
     * @memberof Deal
     */
    id?: string;
    /**
     * 
     * @type {string}
     * @memberof Deal
     */
    title?: string;
    /**
     * 
     * @type {string}
     * @memberof Deal
     */
    description?: string;
    /**
     * 
     * @type {number}
     * @memberof Deal
     */
    estimatedValue?: number;
    /**
     * 
     * @type {string}
     * @memberof Deal
     */
    companyId?: string;
    /**
     * 
     * @type {string}
     * @memberof Deal
     */
    dealStageId?: string;
    /**
     * 
     * @type {Date}
     * @memberof Deal
     */
    expectedCloseDate?: Date;
    /**
     * 
     * @type {string}
     * @memberof Deal
     */
    createdBy?: string;
    /**
     * 
     * @type {Date}
     * @memberof Deal
     */
    createdAt?: Date;
    /**
     * 
     * @type {Date}
     * @memberof Deal
     */
    updatedAt?: Date;
}

/**
 * Check if a given object implements the Deal interface.
 */
export function instanceOfDeal(value: object): value is Deal {
    return true;
}

export function DealFromJSON(json: any): Deal {
    return DealFromJSONTyped(json, false);
}

export function DealFromJSONTyped(json: any, ignoreDiscriminator: boolean): Deal {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'] == null ? undefined : json['id'],
        'title': json['title'] == null ? undefined : json['title'],
        'description': json['description'] == null ? undefined : json['description'],
        'estimatedValue': json['estimated_value'] == null ? undefined : json['estimated_value'],
        'companyId': json['company_id'] == null ? undefined : json['company_id'],
        'dealStageId': json['deal_stage_id'] == null ? undefined : json['deal_stage_id'],
        'expectedCloseDate': json['expected_close_date'] == null ? undefined : (new Date(json['expected_close_date'])),
        'createdBy': json['created_by'] == null ? undefined : json['created_by'],
        'createdAt': json['created_at'] == null ? undefined : (new Date(json['created_at'])),
        'updatedAt': json['updated_at'] == null ? undefined : (new Date(json['updated_at'])),
    };
}

  export function DealToJSON(json: any): Deal {
      return DealToJSONTyped(json, false);
  }

  export function DealToJSONTyped(value?: Deal | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'title': value['title'],
        'description': value['description'],
        'estimated_value': value['estimatedValue'],
        'company_id': value['companyId'],
        'deal_stage_id': value['dealStageId'],
        'expected_close_date': value['expectedCloseDate'] == null ? undefined : ((value['expectedCloseDate']).toISOString().substring(0,10)),
        'created_by': value['createdBy'],
        'created_at': value['createdAt'] == null ? undefined : ((value['createdAt']).toISOString()),
        'updated_at': value['updatedAt'] == null ? undefined : ((value['updatedAt']).toISOString()),
    };
}

