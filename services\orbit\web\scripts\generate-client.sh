#!/bin/bash
# Generate and install API client for CRM web app

set -e

echo "Generating TypeScript API client for Orbit..."

# Get the workspace root
WORKSPACE_ROOT="/Users/<USER>/Projects/multi/mono"
GENERATED_DIR="$WORKSPACE_ROOT/generated/orbit/typescript"
DEST_DIR="src/api-client"

# Generate the client using <PERSON><PERSON> (only if not already in Bazel context)
cd "$(dirname "$0")/.."
if [ -z "$BUILD_WORKSPACE_DIRECTORY" ]; then
    # First generate the client and update the generated folder
    bazel build //services/orbit/web/api-client:generate_client
    bazel run //services/orbit/rest-api:update_generated
else
    echo "Running in Bazel context, assuming client is already generated..."
fi

# Copy generated files from the centralized generated folder to src/api-client
echo "Copying generated client files from $GENERATED_DIR to $DEST_DIR..."
rm -rf "$DEST_DIR"
mkdir -p "$DEST_DIR"

# Check if the TypeScript client was generated
if [ -f "$GENERATED_DIR/client.ts" ]; then
    # For openapi-typescript, we get a single client.ts file
    cp "$GENERATED_DIR/client.ts" "$DEST_DIR/"
    echo "Copied TypeScript client from generated folder"
elif [ -d "$GENERATED_DIR/src" ]; then
    # For openapi-generator, we get a src directory structure
    cp -r "$GENERATED_DIR/src"/* "$DEST_DIR/"
    echo "Copied TypeScript client source files from generated folder"
else
    # Fallback: try the old api-client location
    CLIENT_SRC="bazel-bin/services/orbit/web/api-client"
    if [ -d "$CLIENT_SRC/src" ]; then
        cp -r "$CLIENT_SRC/src"/* "$DEST_DIR/"
        echo "Copied from bazel-bin location"
    else
        echo "Warning: Generated client files not found"
        echo "Expected location: $GENERATED_DIR"
        ls -la "$GENERATED_DIR" 2>/dev/null || echo "Generated directory not found"
    fi
fi

echo "API client generation complete!"
echo "Generated files are now available in $DEST_DIR"