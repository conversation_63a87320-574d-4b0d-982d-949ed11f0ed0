#!/bin/bash
# Quick setup for PROD environment
# Usage: source setup.sh (must be sourced to set environment variables)

echo "🎯 Setting up PROD environment..."

# Source the main environment setup script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"

if [[ -f "$PROJECT_ROOT/terraform/ansible/setup-env.sh" ]]; then
    source "$PROJECT_ROOT/terraform/ansible/setup-env.sh"
else
    echo "❌ Could not find setup-env.sh script"
    return 1
fi

# Production-specific overrides
export ENVIRONMENT="prod"
export GCP_PROJECT_ID="${GCP_PROJECT_ID:-crm-platform-prod}"
export GCP_REGION="us-east1"
export GCP_ZONE="us-east1-a"

# Production-specific settings
export LOG_LEVEL="warn"
export ENABLE_DEBUG="false"
export ENABLE_SWAGGER="false"
export CORS_ALLOWED_ORIGINS="https://app.example.com"

# Production safety checks
echo ""
echo "⚠️  PRODUCTION ENVIRONMENT DETECTED"
echo "🔒 Extra safety checks enabled"
echo ""

# Confirmation prompt for production
read -p "Are you sure you want to work with PRODUCTION? (yes/no): " confirm
if [[ "$confirm" != "yes" ]]; then
    echo "❌ Production setup cancelled"
    return 1
fi

echo ""
echo "🚀 PROD environment ready!"
echo "💡 Quick commands:"
echo "   terraform init"
echo "   terraform plan"
echo "   terraform apply --auto-approve=false"  # Never auto-approve in prod
echo ""
echo "⚠️  Remember: Always run 'terraform plan' first in production!"
echo ""