# Artifact Registry Module Variables

variable "project_id" {
  description = "GCP project ID"
  type        = string
}

variable "project_name" {
  description = "Name of the project (used for resource naming)"
  type        = string
}

variable "region" {
  description = "GCP region for the Artifact Registry"
  type        = string
  default     = "australia-southeast1"
}

variable "immutable_tags" {
  description = "Whether tags are immutable (prevents tag overwriting)"
  type        = bool
  default     = false
}

# Cleanup policy variables
variable "cleanup_tag_prefixes" {
  description = "Tag prefixes to apply cleanup policies to"
  type        = list(string)
  default     = ["dev-", "feature-", "pr-"]
}

variable "cleanup_older_than_days" {
  description = "Delete tagged images older than this many days"
  type        = number
  default     = 30
  
  validation {
    condition     = var.cleanup_older_than_days >= 1 && var.cleanup_older_than_days <= 365
    error_message = "Cleanup period must be between 1 and 365 days."
  }
}

variable "cleanup_untagged_older_than_days" {
  description = "Delete untagged images older than this many days"
  type        = number
  default     = 7
  
  validation {
    condition     = var.cleanup_untagged_older_than_days >= 1 && var.cleanup_untagged_older_than_days <= 365
    error_message = "Untagged cleanup period must be between 1 and 365 days."
  }
}

# Access control variables
variable "enable_cloud_build_access" {
  description = "Whether to grant Cloud Build write access to the registry"
  type        = bool
  default     = true
}

variable "enable_compute_access" {
  description = "Whether to grant Compute Engine read access to the registry"
  type        = bool
  default     = true
}

variable "additional_readers" {
  description = "Additional service accounts or users with read access"
  type        = map(list(string))
  default     = {}
  
  # Example:
  # additional_readers = {
  #   "staging" = ["serviceAccount:<EMAIL>"]
  #   "devs"    = ["user:<EMAIL>", "user:<EMAIL>"]
  # }
}

variable "additional_writers" {
  description = "Additional service accounts or users with write access"
  type        = map(list(string))
  default     = {}
  
  # Example:
  # additional_writers = {
  #   "ci-cd" = ["serviceAccount:<EMAIL>"]
  # }
}

# Automated build variables
variable "enable_automated_builds" {
  description = "Whether to enable automated Docker builds via Cloud Build"
  type        = bool
  default     = false
}

variable "github_repo_owner" {
  description = "GitHub repository owner for automated builds"
  type        = string
  default     = ""
}

variable "github_repo_name" {
  description = "GitHub repository name for automated builds"
  type        = string
  default     = ""
}

variable "build_branch_pattern" {
  description = "Git branch pattern to trigger builds from"
  type        = string
  default     = "main"
}

variable "cloudbuild_file_path" {
  description = "Path to Cloud Build configuration file"
  type        = string
  default     = "cloudbuild.yaml"
}