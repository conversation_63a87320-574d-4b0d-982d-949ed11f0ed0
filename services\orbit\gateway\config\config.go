package config

import (
	"os"
	
	"github.com/TwoDotAi/mono/shared/go/logging"
)

type Config struct {
	GatewayPort  string
	Services     map[string]ServiceConfig
	Environment  string
	LogLevel     string
}

type ServiceConfig struct {
	BaseURL string
	Port    string
	Timeout int // timeout in seconds
}

func Load() *Config {
	cfg := &Config{
		GatewayPort: getEnv("GATEWAY_PORT", "8085"),
		Environment: getEnv("ENVIRONMENT", "development"),
		LogLevel:    getEnv("LOG_LEVEL", "info"),
		Services:    make(map[string]ServiceConfig),
	}

	// Configure services
	cfg.Services["crm_backend"] = ServiceConfig{
		BaseURL: getEnv("CRM_BACKEND_URL", "http://localhost:8003"),
		Port:    getEnv("CRM_BACKEND_PORT", "8003"),
		Timeout: 30,
	}

	// Future auth service configuration
	cfg.Services["auth"] = ServiceConfig{
		BaseURL: getEnv("AUTH_SERVICE_URL", "http://localhost:8004"),
		Port:    getEnv("AUTH_SERVICE_PORT", "8004"),
		Timeout: 30,
	}

	logging.ConfigMessage("GATEWAY", cfg.Environment)
	return cfg
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}