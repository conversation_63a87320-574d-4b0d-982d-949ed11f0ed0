#!/bin/bash
# Build CRM web app with API client generation

set -e

# Get the workspace root
if [ -n "$BUILD_WORKSPACE_DIRECTORY" ]; then
    cd "$BUILD_WORKSPACE_DIRECTORY"
elif [ -n "$TEST_WORKSPACE" ]; then
    cd "$TEST_WORKSPACE/.."
else
    # Find the workspace root by looking for WORKSPACE file
    CURRENT_DIR="$PWD"
    while [ "$CURRENT_DIR" != "/" ]; do
        if [ -f "$CURRENT_DIR/WORKSPACE" ]; then
            cd "$CURRENT_DIR"
            break
        fi
        CURRENT_DIR="$(dirname "$CURRENT_DIR")"
    done
fi

echo "Building CRM web app with API client..."

# First generate the API client
echo "Step 1: Generating API client..."
bazel run //services/orbit/web:generate_client

# Then build the web app
echo "Step 2: Building web application..."
cd services/orbit/web
npm install
npm run build

echo "Build complete! The web app is ready with generated API client."