/* tslint:disable */
/* eslint-disable */
/**
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface InteractionUpdate
 */
export interface InteractionUpdate {
    /**
     * 
     * @type {string}
     * @memberof InteractionUpdate
     */
    interactionType?: InteractionUpdateInteractionTypeEnum;
    /**
     * 
     * @type {string}
     * @memberof InteractionUpdate
     */
    notes?: string;
    /**
     * 
     * @type {Date}
     * @memberof InteractionUpdate
     */
    interactionDatetime?: Date;
    /**
     * 
     * @type {string}
     * @memberof InteractionUpdate
     */
    companyId?: string;
    /**
     * 
     * @type {string}
     * @memberof InteractionUpdate
     */
    contactId?: string;
}

/**
* @export
* @enum {string}
*/
export enum InteractionUpdateInteractionTypeEnum {
    Email = 'email',
    Phone = 'phone',
    Meeting = 'meeting',
    Demo = 'demo',
    Proposal = 'proposal',
    FollowUp = 'follow-up',
    Other = 'other'
}


/**
 * Check if a given object implements the InteractionUpdate interface.
 */
export function instanceOfInteractionUpdate(value: object): value is InteractionUpdate {
    return true;
}

export function InteractionUpdateFromJSON(json: any): InteractionUpdate {
    return InteractionUpdateFromJSONTyped(json, false);
}

export function InteractionUpdateFromJSONTyped(json: any, ignoreDiscriminator: boolean): InteractionUpdate {
    if (json == null) {
        return json;
    }
    return {
        
        'interactionType': json['interaction_type'] == null ? undefined : json['interaction_type'],
        'notes': json['notes'] == null ? undefined : json['notes'],
        'interactionDatetime': json['interaction_datetime'] == null ? undefined : (new Date(json['interaction_datetime'])),
        'companyId': json['company_id'] == null ? undefined : json['company_id'],
        'contactId': json['contact_id'] == null ? undefined : json['contact_id'],
    };
}

  export function InteractionUpdateToJSON(json: any): InteractionUpdate {
      return InteractionUpdateToJSONTyped(json, false);
  }

  export function InteractionUpdateToJSONTyped(value?: InteractionUpdate | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'interaction_type': value['interactionType'],
        'notes': value['notes'],
        'interaction_datetime': value['interactionDatetime'] == null ? undefined : ((value['interactionDatetime']).toISOString()),
        'company_id': value['companyId'],
        'contact_id': value['contactId'],
    };
}

