load("@rules_go//go:def.bzl", "go_library")

# Server library (oapi-codegen generated types and gin handlers)
go_library(
    name = "server",
    srcs = ["server/server.go"],
    importpath = "github.com/TwoDotAi/mono/generated/sprouta-glance/go/server",
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_gin_gonic_gin//:gin",
        "@com_github_oapi_codegen_runtime//:runtime",
        "@com_github_oapi_codegen_runtime//types:types",
    ],
)

# Client library (oapi-codegen generated client)
go_library(
    name = "client",
    srcs = ["client/client.go"],
    importpath = "github.com/TwoDotAi/mono/generated/sprouta-glance/go/client",
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_oapi_codegen_runtime//:runtime",
        "@com_github_oapi_codegen_runtime//types:types",
    ],
)