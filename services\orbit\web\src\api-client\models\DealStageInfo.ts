/* tslint:disable */
/* eslint-disable */
/**
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface DealStageInfo
 */
export interface DealStageInfo {
    /**
     * 
     * @type {string}
     * @memberof DealStageInfo
     */
    id?: string;
    /**
     * 
     * @type {string}
     * @memberof DealStageInfo
     */
    name?: string;
    /**
     * 
     * @type {number}
     * @memberof DealStageInfo
     */
    pipelineOrder?: number;
}

/**
 * Check if a given object implements the DealStageInfo interface.
 */
export function instanceOfDealStageInfo(value: object): value is DealStageInfo {
    return true;
}

export function DealStageInfoFromJSON(json: any): DealStageInfo {
    return DealStageInfoFromJSONTyped(json, false);
}

export function DealStageInfoFromJSONTyped(json: any, ignoreDiscriminator: boolean): DealStageInfo {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'] == null ? undefined : json['id'],
        'name': json['name'] == null ? undefined : json['name'],
        'pipelineOrder': json['pipeline_order'] == null ? undefined : json['pipeline_order'],
    };
}

  export function DealStageInfoToJSON(json: any): DealStageInfo {
      return DealStageInfoToJSONTyped(json, false);
  }

  export function DealStageInfoToJSONTyped(value?: DealStageInfo | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'name': value['name'],
        'pipeline_order': value['pipelineOrder'],
    };
}

