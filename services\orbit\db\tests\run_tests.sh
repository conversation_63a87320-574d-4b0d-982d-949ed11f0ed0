#!/bin/bash

# Database migration tests runner
# This script sets up a test database, runs migrations, and executes tests

set -e

# Configuration
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"
DB_USER="${DB_USER:-postgres}"
DB_PASSWORD="${DB_PASSWORD:-password}"
DB_NAME="crm_db_test"
DB_URL="postgres://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}?sslmode=disable"

echo "🔧 Setting up test database..."

# Drop and recreate test database
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres <<EOF
DROP DATABASE IF EXISTS ${DB_NAME};
CREATE DATABASE ${DB_NAME};
EOF

echo "📦 Running migrations..."

# Run migrations using Flyway
cd ../..
docker run --rm \
  -v "$PWD/db/migrations:/flyway/sql" \
  -v "$PWD/db/flyway.conf:/flyway/conf/flyway.conf" \
  flyway/flyway:latest \
  -url="jdbc:postgresql://${DB_HOST}:${DB_PORT}/${DB_NAME}" \
  -user=$DB_USER \
  -password=$DB_PASSWORD \
  -connectRetries=60 \
  migrate

echo "🧪 Running tests..."

# Run the Go tests
cd db/tests
go test -v ./... -tags=integration

echo "✅ All tests passed!"