/* tslint:disable */
/* eslint-disable */
/**
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface InteractionDetailsAllOfCompany
 */
export interface InteractionDetailsAllOfCompany {
    /**
     * 
     * @type {string}
     * @memberof InteractionDetailsAllOfCompany
     */
    id?: string;
    /**
     * 
     * @type {string}
     * @memberof InteractionDetailsAllOfCompany
     */
    name?: string;
    /**
     * 
     * @type {string}
     * @memberof InteractionDetailsAllOfCompany
     */
    website?: string;
    /**
     * 
     * @type {string}
     * @memberof InteractionDetailsAllOfCompany
     */
    phone?: string;
}

/**
 * Check if a given object implements the InteractionDetailsAllOfCompany interface.
 */
export function instanceOfInteractionDetailsAllOfCompany(value: object): value is InteractionDetailsAllOfCompany {
    return true;
}

export function InteractionDetailsAllOfCompanyFromJSON(json: any): InteractionDetailsAllOfCompany {
    return InteractionDetailsAllOfCompanyFromJSONTyped(json, false);
}

export function InteractionDetailsAllOfCompanyFromJSONTyped(json: any, ignoreDiscriminator: boolean): InteractionDetailsAllOfCompany {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'] == null ? undefined : json['id'],
        'name': json['name'] == null ? undefined : json['name'],
        'website': json['website'] == null ? undefined : json['website'],
        'phone': json['phone'] == null ? undefined : json['phone'],
    };
}

  export function InteractionDetailsAllOfCompanyToJSON(json: any): InteractionDetailsAllOfCompany {
      return InteractionDetailsAllOfCompanyToJSONTyped(json, false);
  }

  export function InteractionDetailsAllOfCompanyToJSONTyped(value?: InteractionDetailsAllOfCompany | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'name': value['name'],
        'website': value['website'],
        'phone': value['phone'],
    };
}

