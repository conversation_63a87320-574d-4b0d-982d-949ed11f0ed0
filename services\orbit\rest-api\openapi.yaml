openapi: 3.0.3
info:
  title: CRM API
  description: Complete API specification for the CRM system
  version: 1.0.0
  contact:
    name: CRM API Support
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://api.crm.example.com/v1
    description: Production server
  - url: https://staging-api.crm.example.com/v1
    description: Staging server
  - url: http://localhost:8003/api/v1
    description: Development server

security:
  - bearerAuth: []

tags:
  - name: Authentication
    description: User authentication and session management
  - name: Companies
    description: Company management operations
  - name: Contacts
    description: Contact management operations
  - name: Deals
    description: Deal pipeline management
  - name: Interactions
    description: Communication tracking
  - name: Arli
    description: AI-powered document processing
  - name: User Management
    description: User profile and settings

paths:
  # =============================================================================
  # AUTHENTICATION ENDPOINTS
  # =============================================================================
  /auth/session:
    get:
      tags: [Authentication]
      summary: Get current session
      description: Retrieve the current user session
      security: []
      responses:
        '200':
          description: Session information
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Session'
        '401':
          description: Not authenticated

  /auth/signin:
    post:
      tags: [Authentication]
      summary: Sign in with email/password
      description: Authenticate user with email and password
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              title: SignInRequest
              properties:
                email:
                  type: string
                  format: email
                password:
                  type: string
                  minLength: 6
              required:
                - email
                - password
      responses:
        '200':
          description: Authentication successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '401':
          description: Invalid credentials

  /auth/signin/oauth:
    post:
      tags: [Authentication]
      summary: Sign in with OAuth
      description: Authenticate user with OAuth provider (Google)
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              title: OAuthSignInRequest
              properties:
                provider:
                  type: string
                  enum: [google]
                redirectTo:
                  type: string
                  format: uri
              required:
                - provider
      responses:
        '200':
          description: OAuth redirect URL
          content:
            application/json:
              schema:
                type: object
                title: OAuthRedirectResponse
                properties:
                  url:
                    type: string
                    format: uri

  /auth/callback/{provider}:
    get:
      tags: [Authentication]
      summary: OAuth callback
      description: Handle OAuth provider callback with authorization code
      security: []
      parameters:
        - name: provider
          in: path
          required: true
          schema:
            type: string
            enum: [google]
        - name: code
          in: query
          required: true
          schema:
            type: string
        - name: state
          in: query
          required: true
          schema:
            type: string
        - name: error
          in: query
          required: false
          schema:
            type: string
      responses:
        '200':
          description: OAuth authentication successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '400':
          description: Invalid request or OAuth error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Authentication failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /auth/signout:
    post:
      tags: [Authentication]
      summary: Sign out
      description: End the current user session
      responses:
        '200':
          description: Successfully signed out
        '401':
          description: Not authenticated

  /auth/user:
    get:
      tags: [Authentication]
      summary: Get current user
      description: Retrieve current authenticated user information
      responses:
        '200':
          description: User information
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '401':
          description: Not authenticated

  # =============================================================================
  # USER PROFILE ENDPOINTS  
  # =============================================================================
  /users/profile:
    get:
      tags: [User Management]
      summary: Get user profile
      description: Retrieve the current user's profile information
      responses:
        '200':
          description: User profile
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfile'
        '401':
          description: Not authenticated

    put:
      tags: [User Management]
      summary: Update user profile
      description: Update the current user's profile information
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserProfileUpdate'
      responses:
        '200':
          description: Profile updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfile'
        '400':
          description: Invalid input

  # =============================================================================
  # COMPANY ENDPOINTS
  # =============================================================================
  /companies:
    get:
      tags: [Companies]
      summary: List companies
      description: Retrieve a list of all active companies
      parameters:
        - name: status_id
          in: query
          schema:
            type: string
            format: uuid
          description: Filter by company status
        - name: include_deleted
          in: query
          schema:
            type: boolean
            default: false
          description: Include soft-deleted companies
      responses:
        '200':
          description: List of companies
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Company'

    post:
      tags: [Companies]
      summary: Create company
      description: Create a new company
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CompanyCreate'
      responses:
        '201':
          description: Company created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Company'
        '400':
          description: Invalid input

  /companies/{id}:
    get:
      tags: [Companies]
      summary: Get company
      description: Retrieve a specific company by ID
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Company details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanyDetails'
        '404':
          description: Company not found

    put:
      tags: [Companies]
      summary: Update company
      description: Update an existing company
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CompanyUpdate'
      responses:
        '200':
          description: Company updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Company'
        '404':
          description: Company not found

    delete:
      tags: [Companies]
      summary: Delete company
      description: Soft delete a company (sets is_deleted to true)
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '204':
          description: Company deleted successfully
        '404':
          description: Company not found

  /companies/{id}/status:
    put:
      tags: [Companies]
      summary: Update company status
      description: Update the status of a company
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              title: CompanyStatusUpdateRequest
              properties:
                company_status_id:
                  type: string
                  format: uuid
              required:
                - company_status_id
      responses:
        '200':
          description: Company status updated successfully
        '404':
          description: Company not found

  /company-statuses:
    get:
      tags: [Companies]
      summary: List company statuses
      description: Retrieve all company statuses ordered by pipeline order
      responses:
        '200':
          description: List of company statuses
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CompanyStatus'

  # =============================================================================
  # CONTACT ENDPOINTS  
  # =============================================================================
  /contacts:
    $ref: './crm/contacts/paths.yaml#/contacts'
  /contacts/{id}:
    $ref: './crm/contacts/paths.yaml#/contacts-by-id'
  /contacts/{id}/company:
    $ref: './crm/contacts/paths.yaml#/contacts-company'

  # =============================================================================
  # DEAL ENDPOINTS
  # =============================================================================
  /deals:
    $ref: './crm/deals/paths.yaml#/deals'
  /deals/{id}:
    $ref: './crm/deals/paths.yaml#/deals-by-id'
  /deal-stages:
    $ref: './crm/deals/paths.yaml#/deal-stages'

  # =============================================================================
  # INTERACTION ENDPOINTS
  # =============================================================================
  /interactions:
    $ref: './crm/interactions/paths.yaml#/interactions'
  /interactions/{id}:
    $ref: './crm/interactions/paths.yaml#/interactions-by-id'

  # =============================================================================
  # ARLI DOCUMENT PROCESSING ENDPOINTS  
  # =============================================================================
  /arli/documents:
    $ref: './crm/arli/paths.yaml#/arli-documents'
  /arli/documents/{id}:
    $ref: './crm/arli/paths.yaml#/arli-documents-by-id'
  /arli/documents/vectorize:
    $ref: './crm/arli/paths.yaml#/arli-vectorize'
  /arli/filter-tags:
    $ref: './crm/arli/paths.yaml#/filter-tags'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    # =============================================================================
    # AUTHENTICATION SCHEMAS
    # =============================================================================
    Session:
      $ref: './auth/schemas.yaml#/Session'
    AuthResponse:
      $ref: './auth/schemas.yaml#/AuthResponse'
    User:
      $ref: './auth/schemas.yaml#/User'
    
    # =============================================================================
    # USER PROFILE SCHEMAS
    # =============================================================================
    UserProfile:
      $ref: './crm/users/schemas.yaml#/UserProfile'
    UserProfileUpdate:
      $ref: './crm/users/schemas.yaml#/UserProfileUpdate'

    # =============================================================================
    # COMPANY SCHEMAS
    # =============================================================================
    Company:
      $ref: './crm/companies/schemas.yaml#/Company'
    CompanyDetails:
      $ref: './crm/companies/schemas.yaml#/CompanyDetails'
    CompanyCreate:
      $ref: './crm/companies/schemas.yaml#/CompanyCreate'
    CompanyUpdate:
      $ref: './crm/companies/schemas.yaml#/CompanyUpdate'
    CompanyStatus:
      $ref: './crm/companies/schemas.yaml#/CompanyStatus'
    CompanyInfo:
      $ref: './crm/companies/schemas.yaml#/CompanyInfo'
    CompanyBasicInfo:
      $ref: './crm/companies/schemas.yaml#/CompanyBasicInfo'
    
    # =============================================================================
    # CONTACT SCHEMAS
    # =============================================================================
    Contact:
      $ref: './crm/contacts/schemas.yaml#/Contact'
    ContactWithCompany:
      $ref: './crm/contacts/schemas.yaml#/ContactWithCompany'
    ContactDetails:
      $ref: './crm/contacts/schemas.yaml#/ContactDetails'
    ContactCreate:
      $ref: './crm/contacts/schemas.yaml#/ContactCreate'
    ContactUpdate:
      $ref: './crm/contacts/schemas.yaml#/ContactUpdate'
    
    # =============================================================================
    # DEAL SCHEMAS
    # =============================================================================
    Deal:
      $ref: './crm/deals/schemas.yaml#/Deal'
    DealWithDetails:
      $ref: './crm/deals/schemas.yaml#/DealWithDetails'
    DealCompanyInfo:
      $ref: './crm/deals/schemas.yaml#/DealCompanyInfo'
    DealStageInfo:
      $ref: './crm/deals/schemas.yaml#/DealStageInfo'
    DealDetails:
      $ref: './crm/deals/schemas.yaml#/DealDetails'
    DealCreate:
      $ref: './crm/deals/schemas.yaml#/DealCreate'
    DealUpdate:
      $ref: './crm/deals/schemas.yaml#/DealUpdate'
    DealStage:
      $ref: './crm/deals/schemas.yaml#/DealStage'
    
    # =============================================================================
    # INTERACTION SCHEMAS
    # =============================================================================
    Interaction:
      $ref: './crm/interactions/schemas.yaml#/Interaction'
    InteractionWithDetails:
      $ref: './crm/interactions/schemas.yaml#/InteractionWithDetails'
    InteractionDetails:
      $ref: './crm/interactions/schemas.yaml#/InteractionDetails'
    InteractionCreate:
      $ref: './crm/interactions/schemas.yaml#/InteractionCreate'
    InteractionUpdate:
      $ref: './crm/interactions/schemas.yaml#/InteractionUpdate'
    
    # =============================================================================
    # DOCUMENT/ARLI SCHEMAS
    # =============================================================================
    Document:
      $ref: './crm/arli/schemas.yaml#/Document'
    DocumentDetails:
      $ref: './crm/arli/schemas.yaml#/DocumentDetails'
    DocumentCreate:
      $ref: './crm/arli/schemas.yaml#/DocumentCreate'
    DocumentUpdate:
      $ref: './crm/arli/schemas.yaml#/DocumentUpdate'
    FilterTag:
      $ref: './crm/arli/schemas.yaml#/FilterTag'
    FilterTagCreate:
      $ref: './crm/arli/schemas.yaml#/FilterTagCreate'
    
    # =============================================================================
    # SHARED ERROR SCHEMAS
    # =============================================================================
    Error:
      $ref: './shared/schemas.yaml#/Error'
    ValidationError:
      $ref: './shared/schemas.yaml#/ValidationError'