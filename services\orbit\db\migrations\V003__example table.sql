-- Auto-generated migration from schema/example_table.sql
-- Generated on Tue 15 Jul 2025 22:29:05 AEST
-- V003__example table.sql

-- Example schema file
-- Place your SQL schema definitions here
-- This file will be automatically converted to a migration

CREATE TABLE IF NOT EXISTS example_table (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes
CREATE INDEX IF NOT EXISTS idx_example_table_name ON example_table(name);
