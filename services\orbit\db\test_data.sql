-- Create test data for CRM system
-- This includes users, companies, contacts, deals, etc.

-- First ensure the users table exists with the proper structure
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    password_hash VARCHAR(255),
    email_confirmed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_deleted BOOLEAN DEFAULT FALSE
);

-- Create user profiles table
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID PRIMARY KEY,
    full_name <PERSON><PERSON><PERSON><PERSON>(255),
    avatar_url TEXT,
    timezone VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Insert test users
INSERT INTO users (email, password_hash, email_confirmed_at)
VALUES 
    -- <EMAIL> with password "TestPassword"
    ('<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewNhBuOPZe.nWnTO', NOW()),
    -- <EMAIL> with password "password123" 
    ('<EMAIL>', '$2a$10$X4kv7j5ZcG39WgogSl16yuprcEzV6evwQCXVnAq4DSbCKcSrqTX9i', NOW()),
    -- <EMAIL> with password "admin123"
    ('<EMAIL>', '$2a$12$YQjGxrT8WIBg4GotKQJOde/SBjGnKFXQ9e6zP0r3K1PY2QvCJGZ6W', NOW())
ON CONFLICT (email) DO UPDATE SET
    password_hash = EXCLUDED.password_hash,
    email_confirmed_at = EXCLUDED.email_confirmed_at;

-- Insert user profiles
INSERT INTO user_profiles (id, full_name)
SELECT u.id, 
    CASE 
        WHEN u.email = '<EMAIL>' THEN 'Will Smith'
        WHEN u.email = '<EMAIL>' THEN 'Test User'
        WHEN u.email = '<EMAIL>' THEN 'Admin User'
    END
FROM users u
WHERE u.email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>')
ON CONFLICT (id) DO UPDATE SET
    full_name = EXCLUDED.full_name;

-- Create company statuses if they don't exist
CREATE TABLE IF NOT EXISTS company_statuses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    pipeline_order INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

INSERT INTO company_statuses (name, pipeline_order) VALUES
('Lead', 1),
('Prospect', 2),
('Customer', 3),
('Former Customer', 4)
ON CONFLICT DO NOTHING;

-- Create companies table
CREATE TABLE IF NOT EXISTS companies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    website TEXT,
    phone VARCHAR(50),
    address TEXT,
    notes TEXT,
    company_status_id UUID REFERENCES company_statuses(id),
    is_deleted BOOLEAN DEFAULT FALSE,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Insert test companies
WITH will_user AS (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
     lead_status AS (SELECT id FROM company_statuses WHERE name = 'Lead' LIMIT 1),
     prospect_status AS (SELECT id FROM company_statuses WHERE name = 'Prospect' LIMIT 1),
     customer_status AS (SELECT id FROM company_statuses WHERE name = 'Customer' LIMIT 1)
INSERT INTO companies (name, website, phone, address, notes, company_status_id, created_by)
SELECT * FROM (VALUES
    ('Acme Corporation', 'https://acme.com', '******-0123', '123 Business St, San Francisco, CA', 'Large enterprise client', (SELECT id FROM prospect_status), (SELECT id FROM will_user)),
    ('TechStart Inc', 'https://techstart.io', '******-0456', '456 Startup Ave, Austin, TX', 'Growing startup, high potential', (SELECT id FROM lead_status), (SELECT id FROM will_user)),
    ('Global Industries', 'https://globalind.com', '******-0789', '789 Corporate Blvd, New York, NY', 'Existing customer, renewal coming up', (SELECT id FROM customer_status), (SELECT id FROM will_user)),
    ('Innovation Labs', 'https://innovlabs.com', '******-0321', '321 Research Dr, Seattle, WA', 'R&D focused company', (SELECT id FROM lead_status), (SELECT id FROM will_user)),
    ('Two Dot AI', 'https://twodot.ai', '******-0654', '654 AI Way, Palo Alto, CA', 'Our own company!', (SELECT id FROM customer_status), (SELECT id FROM will_user))
) AS t(name, website, phone, address, notes, company_status_id, created_by)
ON CONFLICT DO NOTHING;

-- Create contacts table
CREATE TABLE IF NOT EXISTS contacts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(50),
    job_title VARCHAR(255),
    company_id UUID REFERENCES companies(id) ON DELETE SET NULL,
    created_by UUID NOT NULL REFERENCES users(id),
    is_deleted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Insert test contacts
WITH will_user AS (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
     acme_company AS (SELECT id FROM companies WHERE name = 'Acme Corporation' LIMIT 1),
     techstart_company AS (SELECT id FROM companies WHERE name = 'TechStart Inc' LIMIT 1),
     global_company AS (SELECT id FROM companies WHERE name = 'Global Industries' LIMIT 1),
     innovation_company AS (SELECT id FROM companies WHERE name = 'Innovation Labs' LIMIT 1)
INSERT INTO contacts (first_name, last_name, email, phone, job_title, company_id, created_by)
SELECT * FROM (VALUES
    ('John', 'Smith', '<EMAIL>', '******-1001', 'CEO', (SELECT id FROM acme_company), (SELECT id FROM will_user)),
    ('Sarah', 'Johnson', '<EMAIL>', '******-1002', 'CTO', (SELECT id FROM acme_company), (SELECT id FROM will_user)),
    ('Mike', 'Davis', '<EMAIL>', '******-2001', 'Founder', (SELECT id FROM techstart_company), (SELECT id FROM will_user)),
    ('Emily', 'Chen', '<EMAIL>', '******-3001', 'VP Engineering', (SELECT id FROM global_company), (SELECT id FROM will_user)),
    ('Alex', 'Rodriguez', '<EMAIL>', '******-4001', 'Research Director', (SELECT id FROM innovation_company), (SELECT id FROM will_user)),
    ('Lisa', 'Wang', '<EMAIL>', '******-3002', 'Product Manager', (SELECT id FROM global_company), (SELECT id FROM will_user))
) AS t(first_name, last_name, email, phone, job_title, company_id, created_by)
ON CONFLICT DO NOTHING;

-- Create deal stages table
CREATE TABLE IF NOT EXISTS deal_stages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    pipeline_order INTEGER NOT NULL,
    is_closed_won BOOLEAN DEFAULT FALSE,
    is_closed_lost BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

INSERT INTO deal_stages (name, pipeline_order, is_closed_won, is_closed_lost) VALUES
('Prospecting', 1, FALSE, FALSE),
('Qualification', 2, FALSE, FALSE),
('Proposal', 3, FALSE, FALSE),
('Negotiation', 4, FALSE, FALSE),
('Closed Won', 5, TRUE, FALSE),
('Closed Lost', 6, FALSE, TRUE)
ON CONFLICT DO NOTHING;

-- Create deals table
CREATE TABLE IF NOT EXISTS deals (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    estimated_value DECIMAL(15,2),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    deal_stage_id UUID NOT NULL REFERENCES deal_stages(id),
    expected_close_date DATE,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Insert test deals
WITH will_user AS (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
     acme_company AS (SELECT id FROM companies WHERE name = 'Acme Corporation' LIMIT 1),
     techstart_company AS (SELECT id FROM companies WHERE name = 'TechStart Inc' LIMIT 1),
     global_company AS (SELECT id FROM companies WHERE name = 'Global Industries' LIMIT 1),
     prospecting_stage AS (SELECT id FROM deal_stages WHERE name = 'Prospecting' LIMIT 1),
     qualification_stage AS (SELECT id FROM deal_stages WHERE name = 'Qualification' LIMIT 1),
     proposal_stage AS (SELECT id FROM deal_stages WHERE name = 'Proposal' LIMIT 1),
     negotiation_stage AS (SELECT id FROM deal_stages WHERE name = 'Negotiation' LIMIT 1)
INSERT INTO deals (title, description, estimated_value, company_id, deal_stage_id, expected_close_date, created_by)
SELECT * FROM (VALUES
    ('Enterprise CRM License', 'Annual license for 500 users', 150000.00, (SELECT id FROM acme_company), (SELECT id FROM proposal_stage), '2025-08-15', (SELECT id FROM will_user)),
    ('Startup Package', 'Complete CRM setup for growing startup', 25000.00, (SELECT id FROM techstart_company), (SELECT id FROM qualification_stage), '2025-07-30', (SELECT id FROM will_user)),
    ('License Renewal', 'Annual renewal of existing contract', 75000.00, (SELECT id FROM global_company), (SELECT id FROM negotiation_stage), '2025-09-01', (SELECT id FROM will_user)),
    ('Custom Integration', 'Custom API integration project', 50000.00, (SELECT id FROM acme_company), (SELECT id FROM prospecting_stage), '2025-10-15', (SELECT id FROM will_user))
) AS t(title, description, estimated_value, company_id, deal_stage_id, expected_close_date, created_by)
ON CONFLICT DO NOTHING;

-- Display created data summary
SELECT 'USERS CREATED' as table_name, COUNT(*) as count FROM users
UNION ALL
SELECT 'USER PROFILES CREATED', COUNT(*) FROM user_profiles
UNION ALL 
SELECT 'COMPANIES CREATED', COUNT(*) FROM companies
UNION ALL
SELECT 'CONTACTS CREATED', COUNT(*) FROM contacts
UNION ALL
SELECT 'DEALS CREATED', COUNT(*) FROM deals;

-- Show the test users that were created
SELECT 'Test Users:' as info, email, full_name
FROM users u
JOIN user_profiles up ON u.id = up.id
WHERE u.email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>');