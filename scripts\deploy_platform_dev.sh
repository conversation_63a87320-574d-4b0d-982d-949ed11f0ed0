#!/bin/bash
# Deploy platform to dev environment - wrapper for terraform deploy
set -e

echo "🚀 Deploying platform to dev environment..."

# Change to workspace directory
if [ -n "$BUILD_WORKSPACE_DIRECTORY" ]; then
    cd "$BUILD_WORKSPACE_DIRECTORY"
elif [ -n "$TEST_WORKSPACE" ]; then
    cd "$TEST_WORKSPACE"
else
    # Find the workspace root by looking for WORKSPACE file
    CURRENT_DIR="$PWD"
    while [ "$CURRENT_DIR" != "/" ]; do
        if [ -f "$CURRENT_DIR/WORKSPACE" ]; then
            cd "$CURRENT_DIR"
            break
        fi
        CURRENT_DIR="$(dirname "$CURRENT_DIR")"
    done
fi

# Run the terraform deployment
bazel run //terraform:deploy_full_dev

echo "✅ Platform deployment completed!"