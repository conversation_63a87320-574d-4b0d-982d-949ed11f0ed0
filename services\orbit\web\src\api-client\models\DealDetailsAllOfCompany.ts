/* tslint:disable */
/* eslint-disable */
/**
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface DealDetailsAllOfCompany
 */
export interface DealDetailsAllOfCompany {
    /**
     * 
     * @type {string}
     * @memberof DealDetailsAllOfCompany
     */
    id?: string;
    /**
     * 
     * @type {string}
     * @memberof DealDetailsAllOfCompany
     */
    name?: string;
    /**
     * 
     * @type {string}
     * @memberof DealDetailsAllOfCompany
     */
    website?: string;
    /**
     * 
     * @type {string}
     * @memberof DealDetailsAllOfCompany
     */
    phone?: string;
}

/**
 * Check if a given object implements the DealDetailsAllOfCompany interface.
 */
export function instanceOfDealDetailsAllOfCompany(value: object): value is DealDetailsAllOfCompany {
    return true;
}

export function DealDetailsAllOfCompanyFromJSON(json: any): DealDetailsAllOfCompany {
    return DealDetailsAllOfCompanyFromJSONTyped(json, false);
}

export function DealDetailsAllOfCompanyFromJSONTyped(json: any, ignoreDiscriminator: boolean): DealDetailsAllOfCompany {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'] == null ? undefined : json['id'],
        'name': json['name'] == null ? undefined : json['name'],
        'website': json['website'] == null ? undefined : json['website'],
        'phone': json['phone'] == null ? undefined : json['phone'],
    };
}

  export function DealDetailsAllOfCompanyToJSON(json: any): DealDetailsAllOfCompany {
      return DealDetailsAllOfCompanyToJSONTyped(json, false);
  }

  export function DealDetailsAllOfCompanyToJSONTyped(value?: DealDetailsAllOfCompany | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'name': value['name'],
        'website': value['website'],
        'phone': value['phone'],
    };
}

