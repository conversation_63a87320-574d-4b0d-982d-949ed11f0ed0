load("@rules_go//go:def.bzl", "go_test")

# SQL integration test
sh_test(
    name = "sql_integration_test",
    srcs = ["sql_integration_test.sh"],
    data = [
        "integration_test.sql",
        "//services/orbit/db:cleanup_test_db",
        "//services/orbit/db:setup_test_db",
    ],
    tags = [
        "integration",
        "requires-docker",
    ],
)

# Go migration tests
go_test(
    name = "migration_test",
    srcs = ["migration_test.go"],
    env = {
        "DATABASE_URL": "postgres://postgres:testpassword@localhost:5433/crm_db_test?sslmode=disable",
    },
    tags = [
        "integration",
        "requires-database",
    ],
    deps = [
        "@com_github_lib_pq//:pq",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
    ],
)

# Full database test suite
sh_test(
    name = "database_test_suite",
    srcs = ["database_test_suite.sh"],
    data = [
        ":migration_test",
        ":sql_integration_test",
        "//services/orbit/db:cleanup_test_db",
        "//services/orbit/db:setup_test_db",
    ],
    tags = [
        "integration",
        "requires-docker",
    ],
)

# Test with clean database setup
sh_test(
    name = "migration_with_setup",
    srcs = ["migration_with_setup.sh"],
    data = [
        "integration_test.sql",
        "migration_test.go",
        "//services/orbit/db:cleanup_test_db",
        "//services/orbit/db:setup_test_db",
    ],
    tags = [
        "integration",
        "requires-docker",
    ],
)

# File group for all test scripts
filegroup(
    name = "test_scripts",
    srcs = [
        "database_test_suite.sh",
        "migration_with_setup.sh",
        "sql_integration_test.sh",
    ],
    visibility = ["//visibility:public"],
)

go_test(
    name = "tests_test",
    srcs = ["migration_test.go"],
    deps = [
        "@com_github_lib_pq//:pq",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
    ],
)
