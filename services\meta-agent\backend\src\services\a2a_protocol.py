"""
Agent-to-Agent (A2A) Protocol Service
Handles communication between agents in the platform
"""

import asyncio
import json
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from uuid import UUID, uuid4
import structlog
import aiohttp
from sqlalchemy.ext.asyncio import AsyncSession

from api.schemas import (
    A2AMessage, A2AAgentInfo, A2ACapability, A2ADiscoveryRequest, 
    A2ADiscoveryResponse, A2AMessageRequest, A2AMessageResponse
)
from services.agents import AgentService

logger = structlog.get_logger()

class A2AProtocolService:
    """Service for Agent-to-Agent communication protocol"""
    
    def __init__(self):
        # In-memory registry for active agents (in production, use Redis or database)
        self.agent_registry: Dict[UUID, A2AAgentInfo] = {}
        self.message_queue: Dict[UUID, List[A2AMessage]] = {}
        self.pending_responses: Dict[UUID, A2AMessage] = {}
        
    async def register_agent(self, agent_info: A2AAgentInfo) -> bool:
        """Register an agent in the A2A network"""
        try:
            logger.info("Registering agent for A2A", agent_id=str(agent_info.agent_id))
            
            # Update heartbeat
            agent_info.last_heartbeat = datetime.utcnow()
            
            # Store in registry
            self.agent_registry[agent_info.agent_id] = agent_info
            
            # Initialize message queue if not exists
            if agent_info.agent_id not in self.message_queue:
                self.message_queue[agent_info.agent_id] = []
            
            logger.info("Agent registered successfully", agent_id=str(agent_info.agent_id))
            return True
            
        except Exception as e:
            logger.error("Failed to register agent", agent_id=str(agent_info.agent_id), error=str(e))
            return False
    
    async def unregister_agent(self, agent_id: UUID) -> bool:
        """Unregister an agent from the A2A network"""
        try:
            if agent_id in self.agent_registry:
                del self.agent_registry[agent_id]
                
            if agent_id in self.message_queue:
                del self.message_queue[agent_id]
                
            logger.info("Agent unregistered", agent_id=str(agent_id))
            return True
            
        except Exception as e:
            logger.error("Failed to unregister agent", agent_id=str(agent_id), error=str(e))
            return False
    
    async def discover_agents(self, request: A2ADiscoveryRequest) -> A2ADiscoveryResponse:
        """Discover available agents based on filters"""
        try:
            agents = list(self.agent_registry.values())
            
            # Apply filters
            if request.capability_filter:
                agents = [
                    agent for agent in agents 
                    if any(request.capability_filter.lower() in cap.name.lower() 
                          for cap in agent.capabilities)
                ]
            
            if request.agent_type_filter:
                agents = [
                    agent for agent in agents 
                    if agent.type.lower() == request.agent_type_filter.lower()
                ]
            
            # Remove stale agents (no heartbeat in last 5 minutes)
            cutoff_time = datetime.utcnow() - timedelta(minutes=5)
            active_agents = [
                agent for agent in agents 
                if agent.last_heartbeat > cutoff_time
            ]
            
            return A2ADiscoveryResponse(
                agents=active_agents,
                total_count=len(active_agents)
            )
            
        except Exception as e:
            logger.error("Failed to discover agents", error=str(e))
            return A2ADiscoveryResponse(agents=[], total_count=0)
    
    async def send_message(self, sender_id: UUID, request: A2AMessageRequest) -> A2AMessageResponse:
        """Send a message from one agent to another"""
        try:
            # Check if receiver exists
            if request.receiver_id not in self.agent_registry:
                return A2AMessageResponse(
                    message_id=uuid4(),
                    status="failed",
                    error_message="Receiver agent not found"
                )
            
            receiver_info = self.agent_registry[request.receiver_id]
            
            # Create message
            message = A2AMessage(
                sender_id=sender_id,
                receiver_id=request.receiver_id,
                message_type=request.message_type,
                action=request.action,
                payload=request.payload,
                correlation_id=request.correlation_id,
                ttl_seconds=request.ttl_seconds
            )
            
            # Try direct HTTP delivery first
            try:
                response_payload = await self._deliver_message_http(receiver_info, message)
                
                return A2AMessageResponse(
                    message_id=message.id,
                    status="delivered",
                    response_payload=response_payload
                )
                
            except Exception as http_error:
                logger.warning("HTTP delivery failed, queuing message", 
                             receiver_id=str(request.receiver_id), 
                             error=str(http_error))
                
                # Fallback to message queue
                self.message_queue[request.receiver_id].append(message)
                
                return A2AMessageResponse(
                    message_id=message.id,
                    status="sent"
                )
                
        except Exception as e:
            logger.error("Failed to send message", 
                        sender_id=str(sender_id), 
                        receiver_id=str(request.receiver_id), 
                        error=str(e))
            return A2AMessageResponse(
                message_id=uuid4(),
                status="failed",
                error_message=str(e)
            )
    
    async def _deliver_message_http(self, receiver_info: A2AAgentInfo, message: A2AMessage) -> Optional[Dict[str, Any]]:
        """Deliver message via HTTP to agent endpoint"""
        try:
            endpoint_url = f"{receiver_info.endpoint_url}/a2a/message"
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    endpoint_url,
                    json=message.dict(),
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        raise Exception(f"HTTP {response.status}: {await response.text()}")
                        
        except Exception as e:
            logger.error("HTTP message delivery failed", 
                        receiver_id=str(message.receiver_id), 
                        error=str(e))
            raise
    
    async def get_messages(self, agent_id: UUID, limit: int = 10) -> List[A2AMessage]:
        """Get pending messages for an agent"""
        try:
            if agent_id not in self.message_queue:
                return []
            
            messages = self.message_queue[agent_id][:limit]
            
            # Remove expired messages
            current_time = datetime.utcnow()
            valid_messages = []
            
            for message in messages:
                if message.ttl_seconds:
                    expiry_time = message.timestamp + timedelta(seconds=message.ttl_seconds)
                    if current_time < expiry_time:
                        valid_messages.append(message)
                else:
                    valid_messages.append(message)
            
            # Update queue
            self.message_queue[agent_id] = self.message_queue[agent_id][limit:]
            
            return valid_messages
            
        except Exception as e:
            logger.error("Failed to get messages", agent_id=str(agent_id), error=str(e))
            return []
    
    async def send_heartbeat(self, agent_id: UUID) -> bool:
        """Update agent heartbeat"""
        try:
            if agent_id in self.agent_registry:
                self.agent_registry[agent_id].last_heartbeat = datetime.utcnow()
                return True
            return False
            
        except Exception as e:
            logger.error("Failed to send heartbeat", agent_id=str(agent_id), error=str(e))
            return False
    
    async def get_agent_info(self, agent_id: UUID) -> Optional[A2AAgentInfo]:
        """Get information about a specific agent"""
        return self.agent_registry.get(agent_id)
    
    async def cleanup_stale_agents(self):
        """Remove agents that haven't sent heartbeat in a while"""
        try:
            cutoff_time = datetime.utcnow() - timedelta(minutes=10)
            stale_agents = [
                agent_id for agent_id, agent_info in self.agent_registry.items()
                if agent_info.last_heartbeat < cutoff_time
            ]
            
            for agent_id in stale_agents:
                await self.unregister_agent(agent_id)
                logger.info("Removed stale agent", agent_id=str(agent_id))
                
        except Exception as e:
            logger.error("Failed to cleanup stale agents", error=str(e))

# Global instance
a2a_protocol = A2AProtocolService()

# Background task to cleanup stale agents
async def cleanup_stale_agents_task():
    """Background task to periodically cleanup stale agents"""
    while True:
        try:
            await a2a_protocol.cleanup_stale_agents()
            await asyncio.sleep(300)  # Run every 5 minutes
        except Exception as e:
            logger.error("Error in cleanup task", error=str(e))
            await asyncio.sleep(60)  # Wait 1 minute before retrying
