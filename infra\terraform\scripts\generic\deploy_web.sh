#!/bin/bash
# Generic web deployment script
# Builds and deploys web application to any environment

# Source common utilities
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/../common/utils.sh"
source "$SCRIPT_DIR/../common/environment.sh"

# Script configuration
SCRIPT_NAME="deploy_web.sh"
SCRIPT_DESCRIPTION="Build and deploy web application for specified environment"

# Required tools
REQUIRED_TOOLS=("terraform" "gcloud" "gsutil" "bazel")

# Web deployment function
deploy_web() {
    local env="$1"
    
    print_step "Step 1: Environment setup and validation"
    if ! setup_environment "$env"; then
        return 1
    fi
    
    print_environment_summary "$env"
    production_safety_check
    
    print_step "Step 2: Get deployment configuration"
    cd "$TERRAFORM_DIR"
    
    # Get bucket name from Terraform outputs
    local bucket_name
    if ! bucket_name=$(terraform output -raw web_bucket_name 2>/dev/null); then
        print_error "Could not get web bucket name from Terraform outputs"
        print_info "Make sure infrastructure is deployed first"
        return 1
    fi
    
    print_info "Web bucket: $bucket_name"
    
    # Go back to workspace root for build commands
    cd - > /dev/null
    
    print_step "Step 3: Build web application"
    
    # Generate API client first
    print_info "Generating API client from OpenAPI specs..."
    if ! execute bazel run //services/orbit/web:generate_client; then
        print_error "API client generation failed"
        return 1
    fi
    
    print_success "API client generated"
    
    # Build the web application
    print_info "Building web application..."
    if ! execute bazel run //services/orbit/web:build_dist_complete; then
        print_error "Web application build failed"
        return 1
    fi
    
    print_success "Web application built"
    
    # Verify build output exists
    if [[ ! -d "services/orbit/web/dist" ]]; then
        print_error "Build output directory not found: services/orbit/web/dist"
        return 1
    fi
    
    # Check if build output has files
    if [[ ! -f "services/orbit/web/dist/index.html" ]]; then
        print_error "Build output missing index.html"
        return 1
    fi
    
    print_info "Build output verified"
    
    print_step "Step 4: Deploy to Cloud Storage"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_info "[DRY RUN] Would deploy to bucket: $bucket_name"
        return 0
    fi
    
    # Backup existing deployment (for rollback)
    print_info "Creating backup of existing deployment..."
    local backup_dir="gs://$bucket_name/backups/$(date +%Y%m%d-%H%M%S)"
    
    # Check if bucket has existing content
    if gsutil ls "gs://$bucket_name/" > /dev/null 2>&1; then
        # Create backup
        gsutil -m cp -r "gs://$bucket_name/*" "$backup_dir/" 2>/dev/null || true
        print_info "Backup created: $backup_dir"
    else
        print_info "No existing content to backup"
    fi
    
    # Deploy new content
    print_info "Deploying web application to bucket..."
    
    # Set proper cache headers for different file types
    local cache_headers_html="-h Cache-Control:no-cache,no-store,must-revalidate"
    local cache_headers_assets="-h Cache-Control:public,max-age=31536000"
    local cache_headers_api="-h Cache-Control:no-cache,no-store,must-revalidate"
    
    # Deploy HTML files (no cache)
    if ! gsutil -m rsync -r -d $cache_headers_html \
        -x ".*\\.(js|css|png|jpg|jpeg|gif|svg|woff|woff2|ttf|eot|ico|webp|pdf)$" \
        services/orbit/web/dist/ "gs://$bucket_name/"; then
        print_error "Failed to deploy HTML files"
        return 1
    fi
    
    # Deploy static assets (long cache)
    if ! gsutil -m rsync -r -d $cache_headers_assets \
        -x ".*\\.(html|json|txt|xml)$" \
        services/orbit/web/dist/ "gs://$bucket_name/"; then
        print_error "Failed to deploy static assets"
        return 1
    fi
    
    # Deploy API-related files (no cache)
    if ! gsutil -m rsync -r -d $cache_headers_api \
        -x ".*\\.(js|css|png|jpg|jpeg|gif|svg|woff|woff2|ttf|eot|ico|webp|pdf|html)$" \
        services/orbit/web/dist/ "gs://$bucket_name/"; then
        print_error "Failed to deploy API files"
        return 1
    fi
    
    print_success "Web application deployed to bucket"
    
    print_step "Step 5: Set up SPA routing"
    
    # For Single Page Applications, we need to serve index.html for all routes
    print_info "Configuring SPA routing..."
    
    # Create a not-found page that serves index.html
    gsutil cp services/orbit/web/dist/index.html "gs://$bucket_name/404.html" $cache_headers_html
    
    # Set bucket website configuration
    gsutil web set -m index.html -e 404.html "gs://$bucket_name/"
    
    print_success "SPA routing configured"
    
    print_step "Step 6: Create deployment metadata"
    
    # Create deployment metadata file
    local deployment_info=$(cat <<EOF
{
    "deployment_time": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
    "environment": "$env",
    "bucket": "$bucket_name",
    "git_commit": "$(git rev-parse HEAD 2>/dev/null || echo 'unknown')",
    "git_branch": "$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo 'unknown')",
    "deployed_by": "$(whoami)",
    "backup_location": "$backup_dir"
}
EOF
)
    
    # Upload deployment metadata
    echo "$deployment_info" | gsutil cp - "gs://$bucket_name/deployment-info.json" $cache_headers_api
    
    print_success "Deployment metadata created"
    
    print_step "Step 7: Verify deployment"
    
    # Check if key files exist in bucket
    local key_files=("index.html" "deployment-info.json")
    for file in "${key_files[@]}"; do
        if ! gsutil ls "gs://$bucket_name/$file" > /dev/null 2>&1; then
            print_error "Key file missing after deployment: $file"
            return 1
        fi
    done
    
    print_success "Deployment verification passed"
    
    print_step "Step 8: Get access URLs"
    
    # Get load balancer IP from terraform
    local lb_ip
    if lb_ip=$(terraform output -raw load_balancer_ip 2>/dev/null); then
        print_info "Load Balancer IP: $lb_ip"
    fi
    
    # Get SSL domains
    local ssl_domains
    if ssl_domains=$(terraform output -raw ssl_domains 2>/dev/null); then
        print_info "SSL Domains: $ssl_domains"
    fi
    
    print_success "Web deployment completed successfully!"
    
    echo ""
    print_info "🌐 Your web application is now available at:"
    
    # Show different access methods
    if [[ -n "$lb_ip" ]]; then
        echo "  🔗 Load Balancer: http://$lb_ip"
        if [[ -n "$ssl_domains" ]]; then
            # Parse SSL domains (comma-separated)
            IFS=',' read -ra DOMAIN_ARRAY <<< "$ssl_domains"
            for domain in "${DOMAIN_ARRAY[@]}"; do
                domain=$(echo "$domain" | xargs)  # trim whitespace
                echo "  🔒 HTTPS: https://$domain"
            done
        fi
    fi
    
    echo "  💾 Direct Bucket: https://storage.googleapis.com/$bucket_name/index.html"
    echo "  📊 Deployment Info: https://storage.googleapis.com/$bucket_name/deployment-info.json"
    echo ""
    echo "  🔄 Backup Location: $backup_dir"
    echo ""
    
    return 0
}

# Build only (no deployment)
build_web() {
    local env="$1"
    
    print_step "Building web application for: $env"
    
    if ! setup_environment "$env"; then
        return 1
    fi
    
    # Generate API client
    print_info "Generating API client..."
    if ! execute bazel run //services/orbit/web:generate_client; then
        print_error "API client generation failed"
        return 1
    fi
    
    # Build web application
    print_info "Building web application..."
    if ! execute bazel run //services/orbit/web:build_dist_complete; then
        print_error "Web application build failed"
        return 1
    fi
    
    print_success "Web application built successfully"
    return 0
}

# Deploy only (no build)
deploy_only() {
    local env="$1"
    
    print_step "Deploying existing build to: $env"
    
    if ! setup_environment "$env"; then
        return 1
    fi
    
    # Check if build exists
    if [[ ! -d "services/orbit/web/dist" || ! -f "services/orbit/web/dist/index.html" ]]; then
        print_error "Build output not found. Run build first."
        return 1
    fi
    
    # Get bucket name
    cd "$TERRAFORM_DIR"
    local bucket_name
    if ! bucket_name=$(terraform output -raw web_bucket_name 2>/dev/null); then
        print_error "Could not get web bucket name from Terraform outputs"
        return 1
    fi
    
    cd - > /dev/null
    
    # Deploy to bucket (same logic as in deploy_web)
    print_info "Deploying to bucket: $bucket_name"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_info "[DRY RUN] Would deploy to bucket: $bucket_name"
        return 0
    fi
    
    # Deploy with proper cache headers
    gsutil -m rsync -r -d \
        -h "Cache-Control:no-cache,no-store,must-revalidate" \
        services/orbit/web/dist/ "gs://$bucket_name/"
    
    print_success "Deployment completed"
    return 0
}

# Main script execution
main() {
    setup_error_handling
    
    # Parse command line arguments
    parse_args "$SCRIPT_NAME" "$@"
    
    # Print banner
    print_banner "$SCRIPT_NAME" "$SCRIPT_DESCRIPTION"
    
    # Check required tools
    if ! check_required_tools "${REQUIRED_TOOLS[@]}"; then
        exit 1
    fi
    
    # Change to workspace directory
    if ! change_to_workspace; then
        exit 1
    fi
    
    # Validate environment
    if ! validate_environment "$ENVIRONMENT"; then
        exit 1
    fi
    
    # Handle special commands
    case "$1" in
        "build")
            if build_web "$ENVIRONMENT"; then
                print_success "Web application build completed! 🎉"
                exit 0
            else
                print_error "Web application build failed!"
                exit 1
            fi
            ;;
        "deploy-only")
            if deploy_only "$ENVIRONMENT"; then
                print_success "Web application deployment completed! 🎉"
                exit 0
            else
                print_error "Web application deployment failed!"
                exit 1
            fi
            ;;
        *)
            # Full build and deploy
            if deploy_web "$ENVIRONMENT"; then
                print_success "Web application deployment completed! 🎉"
                exit 0
            else
                print_error "Web application deployment failed!"
                exit 1
            fi
            ;;
    esac
}

# Show usage if no arguments provided
if [[ $# -eq 0 ]]; then
    print_usage "$SCRIPT_NAME"
    echo ""
    echo "Additional commands:"
    echo "  build ENVIRONMENT        Build web application only"
    echo "  deploy-only ENVIRONMENT  Deploy existing build only"
    exit 1
fi

# Execute main function with all arguments
main "$@"