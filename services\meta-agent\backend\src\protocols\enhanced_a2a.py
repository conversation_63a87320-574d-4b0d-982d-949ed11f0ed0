"""Enhanced Agent-to-Agent (A2A) Communication Protocol.

This module provides multiple communication protocols for agent-to-agent
communication including REST, gRPC, WebSocket, and message queue patterns.
"""

from typing import Dict, Any, List, Optional, Union, AsyncIterator, Callable
from dataclasses import dataclass, field
from enum import Enum
import asyncio
import json
import uuid
from datetime import datetime, timedelta
import aiohttp
import websockets
import grpc
from concurrent import futures
import ssl
from utils.logging import get_logger, LoggerMixin
from utils.exceptions import AgentError, ExternalServiceError
from intelligence.gateway import AIGateway

logger = get_logger(__name__)

class ProtocolType(str, Enum):
    """Available communication protocols."""
    REST = "rest"
    GRPC = "grpc"
    WEBSOCKET = "websocket"
    MESSAGE_QUEUE = "message_queue"
    TCP = "tcp"
    UDP = "udp"

class MessagePattern(str, Enum):
    """Message exchange patterns."""
    REQUEST_RESPONSE = "request_response"
    PUBLISH_SUBSCRIBE = "publish_subscribe"
    PUSH_PULL = "push_pull"
    STREAMING = "streaming"
    FIRE_AND_FORGET = "fire_and_forget"
    BROADCAST = "broadcast"

class SecurityMode(str, Enum):
    """Security modes for communication."""
    NONE = "none"
    TLS = "tls"
    MUTUAL_TLS = "mutual_tls"
    JWT = "jwt"
    OAUTH2 = "oauth2"
    API_KEY = "api_key"

@dataclass
class A2AMessage:
    """Enhanced A2A message with protocol support."""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    from_agent: str = ""
    to_agent: str = ""
    message_type: str = "request"
    content: Any = None
    headers: Dict[str, str] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    priority: str = "medium"
    timestamp: datetime = field(default_factory=datetime.now)
    ttl: Optional[int] = None  # Time to live in seconds
    correlation_id: Optional[str] = None
    reply_to: Optional[str] = None
    protocol: ProtocolType = ProtocolType.REST
    pattern: MessagePattern = MessagePattern.REQUEST_RESPONSE
    security_context: Dict[str, Any] = field(default_factory=dict)

@dataclass
class AgentEndpoint:
    """Agent communication endpoint configuration."""
    agent_id: str = ""
    protocol: ProtocolType = ProtocolType.REST
    host: str = "localhost"
    port: int = 8080
    path: str = ""
    security: SecurityMode = SecurityMode.NONE
    credentials: Dict[str, Any] = field(default_factory=dict)
    capabilities: List[str] = field(default_factory=list)
    health_check_url: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    last_seen: Optional[datetime] = None
    status: str = "unknown"

@dataclass
class ConversationContext:
    """Multi-message conversation context."""
    conversation_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    participants: List[str] = field(default_factory=list)
    messages: List[A2AMessage] = field(default_factory=list)
    context: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    expires_at: Optional[datetime] = None
    status: str = "active"

class EnhancedA2AProtocol(LoggerMixin):
    """Enhanced Agent-to-Agent communication protocol handler."""
    
    def __init__(self, ai_gateway: AIGateway, agent_id: str):
        self.ai_gateway = ai_gateway
        self.agent_id = agent_id
        
        # Registry and routing
        self.agent_registry: Dict[str, AgentEndpoint] = {}
        self.protocol_handlers: Dict[ProtocolType, Any] = {}
        self.conversation_contexts: Dict[str, ConversationContext] = {}
        self.message_handlers: Dict[str, Callable] = {}
        
        # Communication state
        self.active_connections: Dict[str, Any] = {}
        self.pending_requests: Dict[str, asyncio.Future] = {}
        self.subscriptions: Dict[str, List[Callable]] = {}
        
        # Configuration
        self.default_timeout = 30
        self.max_retries = 3
        self.compression_enabled = True
        
        # Initialize protocol handlers
        self._initialize_protocol_handlers()
    
    def _initialize_protocol_handlers(self) -> None:
        """Initialize handlers for different protocols."""
        
        self.protocol_handlers = {
            ProtocolType.REST: self._create_rest_handler(),
            ProtocolType.WEBSOCKET: self._create_websocket_handler(),
            ProtocolType.GRPC: self._create_grpc_handler(),
            ProtocolType.MESSAGE_QUEUE: self._create_mq_handler(),
            ProtocolType.TCP: self._create_tcp_handler(),
            ProtocolType.UDP: self._create_udp_handler()
        }
    
    def _create_rest_handler(self) -> Dict[str, Any]:
        """Create REST protocol handler."""
        return {
            "name": "REST",
            "send": self._send_rest_message,
            "receive": self._receive_rest_message,
            "stream": self._stream_rest_messages,
            "setup": self._setup_rest_server
        }
    
    def _create_websocket_handler(self) -> Dict[str, Any]:
        """Create WebSocket protocol handler."""
        return {
            "name": "WebSocket",
            "send": self._send_websocket_message,
            "receive": self._receive_websocket_message,
            "stream": self._stream_websocket_messages,
            "setup": self._setup_websocket_server
        }
    
    def _create_grpc_handler(self) -> Dict[str, Any]:
        """Create gRPC protocol handler."""
        return {
            "name": "gRPC",
            "send": self._send_grpc_message,
            "receive": self._receive_grpc_message,
            "stream": self._stream_grpc_messages,
            "setup": self._setup_grpc_server
        }
    
    def _create_mq_handler(self) -> Dict[str, Any]:
        """Create message queue protocol handler."""
        return {
            "name": "Message Queue",
            "send": self._send_mq_message,
            "receive": self._receive_mq_message,
            "stream": self._stream_mq_messages,
            "setup": self._setup_mq_connection
        }
    
    def _create_tcp_handler(self) -> Dict[str, Any]:
        """Create TCP protocol handler."""
        return {
            "name": "TCP",
            "send": self._send_tcp_message,
            "receive": self._receive_tcp_message,
            "stream": self._stream_tcp_messages,
            "setup": self._setup_tcp_server
        }
    
    def _create_udp_handler(self) -> Dict[str, Any]:
        """Create UDP protocol handler."""
        return {
            "name": "UDP",
            "send": self._send_udp_message,
            "receive": self._receive_udp_message,
            "stream": self._stream_udp_messages,
            "setup": self._setup_udp_server
        }
    
    async def register_agent(self, endpoint: AgentEndpoint) -> bool:
        """Register an agent endpoint for communication."""
        
        try:
            # Validate endpoint
            if not endpoint.agent_id:
                raise AgentError("Agent ID is required")
            
            # Test connectivity
            if await self._test_agent_connectivity(endpoint):
                endpoint.status = "online"
                endpoint.last_seen = datetime.now()
            else:
                endpoint.status = "offline"
            
            self.agent_registry[endpoint.agent_id] = endpoint
            
            self.log_operation(
                "agent_registered",
                agent_id=endpoint.agent_id,
                protocol=endpoint.protocol.value,
                endpoint=f"{endpoint.host}:{endpoint.port}"
            )
            
            return True
            
        except Exception as e:
            self.log_error("agent_registration_failed", e, agent_id=endpoint.agent_id)
            return False
    
    async def _test_agent_connectivity(self, endpoint: AgentEndpoint) -> bool:
        """Test connectivity to an agent endpoint."""
        
        try:
            if endpoint.protocol == ProtocolType.REST:
                health_url = endpoint.health_check_url or f"http://{endpoint.host}:{endpoint.port}/health"
                
                async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=5)) as session:
                    async with session.get(health_url) as response:
                        return response.status == 200
                        
            elif endpoint.protocol == ProtocolType.WEBSOCKET:
                ws_url = f"ws://{endpoint.host}:{endpoint.port}{endpoint.path or '/ws'}"
                
                try:
                    websocket = await websockets.connect(ws_url, timeout=5)
                    await websocket.ping()
                    await websocket.close()
                    return True
                except:
                    return False
            
            elif endpoint.protocol == ProtocolType.TCP:
                try:
                    reader, writer = await asyncio.wait_for(
                        asyncio.open_connection(endpoint.host, endpoint.port),
                        timeout=5
                    )
                    writer.close()
                    await writer.wait_closed()
                    return True
                except:
                    return False
            
            # For other protocols, assume online for now
            return True
            
        except Exception:
            return False
    
    async def send_message(
        self,
        message: A2AMessage,
        timeout: Optional[int] = None,
        retries: Optional[int] = None
    ) -> Optional[A2AMessage]:
        """Send message to another agent using appropriate protocol."""
        
        timeout = timeout or self.default_timeout
        retries = retries or self.max_retries
        
        # Find target agent endpoint
        endpoint = self.agent_registry.get(message.to_agent)
        if not endpoint:
            raise AgentError(f"Agent {message.to_agent} not registered")
        
        # Set protocol if not specified
        if not message.protocol:
            message.protocol = endpoint.protocol
        
        # Set message metadata
        message.from_agent = self.agent_id
        message.timestamp = datetime.now()
        
        # Get appropriate handler
        handler = self.protocol_handlers.get(message.protocol)
        if not handler:
            raise AgentError(f"Protocol {message.protocol} not supported")
        
        # Attempt to send with retries
        last_error = None
        for attempt in range(retries + 1):
            try:
                result = await handler["send"](message, endpoint, timeout)
                
                self.log_operation(
                    "message_sent",
                    message_id=message.id,
                    from_agent=message.from_agent,
                    to_agent=message.to_agent,
                    protocol=message.protocol.value,
                    attempt=attempt + 1
                )
                
                return result
                
            except Exception as e:
                last_error = e
                
                if attempt < retries:
                    wait_time = 2 ** attempt  # Exponential backoff
                    await asyncio.sleep(wait_time)
                    self.logger.warning(f"Retry {attempt + 1} for message {message.id}: {e}")
        
        # All retries failed
        self.log_error("message_send_failed", last_error, message_id=message.id)
        raise ExternalServiceError(f"Failed to send message after {retries + 1} attempts: {last_error}")
    
    async def _send_rest_message(
        self,
        message: A2AMessage,
        endpoint: AgentEndpoint,
        timeout: int
    ) -> Optional[A2AMessage]:
        """Send message via REST."""
        
        url = f"http://{endpoint.host}:{endpoint.port}{endpoint.path or '/messages'}"
        
        headers = {
            "Content-Type": "application/json",
            "X-Agent-ID": self.agent_id,
            "X-Message-ID": message.id,
            **message.headers
        }
        
        # Add authentication if configured
        if endpoint.security == SecurityMode.API_KEY:
            headers["Authorization"] = f"Bearer {endpoint.credentials.get('api_key')}"
        elif endpoint.security == SecurityMode.JWT:
            headers["Authorization"] = f"Bearer {endpoint.credentials.get('jwt_token')}"
        
        payload = {
            "id": message.id,
            "from_agent": message.from_agent,
            "to_agent": message.to_agent,
            "message_type": message.message_type,
            "content": message.content,
            "headers": message.headers,
            "metadata": message.metadata,
            "timestamp": message.timestamp.isoformat(),
            "correlation_id": message.correlation_id,
            "reply_to": message.reply_to
        }
        
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=timeout)) as session:
            async with session.post(url, headers=headers, json=payload) as response:
                if response.status == 200:
                    response_data = await response.json()
                    
                    # Return response message if provided
                    if response_data.get("response"):
                        return self._dict_to_message(response_data["response"])
                
                elif response.status != 202:  # 202 = Accepted (fire and forget)
                    raise ExternalServiceError(f"REST request failed: {response.status}")
        
        return None
    
    async def _send_websocket_message(
        self,
        message: A2AMessage,
        endpoint: AgentEndpoint,
        timeout: int
    ) -> Optional[A2AMessage]:
        """Send message via WebSocket."""
        
        ws_url = f"ws://{endpoint.host}:{endpoint.port}{endpoint.path or '/ws'}"
        
        # Check if we have an active connection
        connection_key = f"{endpoint.agent_id}:websocket"
        websocket = self.active_connections.get(connection_key)
        
        if not websocket or websocket.closed:
            # Create new connection
            websocket = await websockets.connect(ws_url, timeout=timeout)
            self.active_connections[connection_key] = websocket
        
        # Prepare message
        payload = {
            "id": message.id,
            "from_agent": message.from_agent,
            "to_agent": message.to_agent,
            "message_type": message.message_type,
            "content": message.content,
            "timestamp": message.timestamp.isoformat(),
            "correlation_id": message.correlation_id
        }
        
        # Send message
        await websocket.send(json.dumps(payload))
        
        # Wait for response if request-response pattern
        if message.pattern == MessagePattern.REQUEST_RESPONSE:
            try:
                response_data = await asyncio.wait_for(websocket.recv(), timeout=timeout)
                response_json = json.loads(response_data)
                return self._dict_to_message(response_json)
            except asyncio.TimeoutError:
                raise ExternalServiceError("WebSocket response timeout")
        
        return None
    
    async def _send_grpc_message(
        self,
        message: A2AMessage,
        endpoint: AgentEndpoint,
        timeout: int
    ) -> Optional[A2AMessage]:
        """Send message via gRPC."""
        
        # gRPC implementation would require generated proto files
        # This is a simplified placeholder
        
        try:
            # Create gRPC channel
            channel_options = [
                ('grpc.keepalive_time_ms', 30000),
                ('grpc.keepalive_timeout_ms', 5000),
                ('grpc.keepalive_permit_without_calls', True)
            ]
            
            if endpoint.security == SecurityMode.TLS:
                channel = grpc.aio.secure_channel(
                    f"{endpoint.host}:{endpoint.port}",
                    grpc.ssl_channel_credentials(),
                    options=channel_options
                )
            else:
                channel = grpc.aio.insecure_channel(
                    f"{endpoint.host}:{endpoint.port}",
                    options=channel_options
                )
            
            # This would use generated gRPC stubs
            # For now, return placeholder
            await channel.close()
            
            return None
            
        except Exception as e:
            raise ExternalServiceError(f"gRPC send failed: {e}")
    
    async def _send_mq_message(
        self,
        message: A2AMessage,
        endpoint: AgentEndpoint,
        timeout: int
    ) -> Optional[A2AMessage]:
        """Send message via message queue."""
        
        # Message queue implementation (would integrate with Kafka, RabbitMQ, etc.)
        # This is a placeholder
        
        try:
            # Publish to message queue
            queue_name = f"agent.{endpoint.agent_id}"
            
            payload = {
                "id": message.id,
                "from_agent": message.from_agent,
                "to_agent": message.to_agent,
                "message_type": message.message_type,
                "content": message.content,
                "timestamp": message.timestamp.isoformat()
            }
            
            # Would use actual MQ client here
            # For now, simulate success
            await asyncio.sleep(0.1)
            
            return None
            
        except Exception as e:
            raise ExternalServiceError(f"Message queue send failed: {e}")
    
    async def _send_tcp_message(
        self,
        message: A2AMessage,
        endpoint: AgentEndpoint,
        timeout: int
    ) -> Optional[A2AMessage]:
        """Send message via TCP."""
        
        try:
            reader, writer = await asyncio.wait_for(
                asyncio.open_connection(endpoint.host, endpoint.port),
                timeout=timeout
            )
            
            # Prepare message with length prefix
            payload = json.dumps({
                "id": message.id,
                "from_agent": message.from_agent,
                "to_agent": message.to_agent,
                "message_type": message.message_type,
                "content": message.content,
                "timestamp": message.timestamp.isoformat()
            })
            
            # Send length-prefixed message
            message_bytes = payload.encode('utf-8')
            length_prefix = len(message_bytes).to_bytes(4, byteorder='big')
            
            writer.write(length_prefix + message_bytes)
            await writer.drain()
            
            # Read response if expected
            if message.pattern == MessagePattern.REQUEST_RESPONSE:
                response_length_data = await asyncio.wait_for(reader.read(4), timeout=timeout)
                response_length = int.from_bytes(response_length_data, byteorder='big')
                
                response_data = await asyncio.wait_for(reader.read(response_length), timeout=timeout)
                response_json = json.loads(response_data.decode('utf-8'))
                
                writer.close()
                await writer.wait_closed()
                
                return self._dict_to_message(response_json)
            
            writer.close()
            await writer.wait_closed()
            
            return None
            
        except Exception as e:
            raise ExternalServiceError(f"TCP send failed: {e}")
    
    async def _send_udp_message(
        self,
        message: A2AMessage,
        endpoint: AgentEndpoint,
        timeout: int
    ) -> Optional[A2AMessage]:
        """Send message via UDP."""
        
        try:
            # Create UDP socket
            transport, protocol = await asyncio.get_event_loop().create_datagram_endpoint(
                asyncio.DatagramProtocol,
                remote_addr=(endpoint.host, endpoint.port)
            )
            
            # Prepare message
            payload = json.dumps({
                "id": message.id,
                "from_agent": message.from_agent,
                "to_agent": message.to_agent,
                "message_type": message.message_type,
                "content": message.content,
                "timestamp": message.timestamp.isoformat()
            })
            
            # Send message
            transport.sendto(payload.encode('utf-8'))
            
            # UDP is fire-and-forget
            transport.close()
            
            return None
            
        except Exception as e:
            raise ExternalServiceError(f"UDP send failed: {e}")
    
    def _dict_to_message(self, data: Dict[str, Any]) -> A2AMessage:
        """Convert dictionary to A2AMessage."""
        
        return A2AMessage(
            id=data.get("id", str(uuid.uuid4())),
            from_agent=data.get("from_agent", ""),
            to_agent=data.get("to_agent", ""),
            message_type=data.get("message_type", "response"),
            content=data.get("content"),
            headers=data.get("headers", {}),
            metadata=data.get("metadata", {}),
            correlation_id=data.get("correlation_id"),
            reply_to=data.get("reply_to"),
            timestamp=datetime.fromisoformat(data.get("timestamp", datetime.now().isoformat()))
        )
    
    async def start_conversation(
        self,
        participant_ids: List[str],
        initial_message: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> str:
        """Start a multi-agent conversation."""
        
        conversation = ConversationContext(
            participants=participant_ids,
            context=context or {},
            expires_at=datetime.now() + timedelta(hours=24)  # Default 24h expiry
        )
        
        self.conversation_contexts[conversation.conversation_id] = conversation
        
        # Send initial message if provided
        if initial_message and len(participant_ids) > 0:
            message = A2AMessage(
                to_agent=participant_ids[0],
                content=initial_message,
                metadata={"conversation_id": conversation.conversation_id}
            )
            
            await self.send_message(message)
        
        self.log_operation(
            "conversation_started",
            conversation_id=conversation.conversation_id,
            participants=len(participant_ids)
        )
        
        return conversation.conversation_id
    
    def add_message_handler(self, message_type: str, handler: Callable[[A2AMessage], Any]) -> None:
        """Add handler for specific message type."""
        self.message_handlers[message_type] = handler
    
    def subscribe_to_topic(self, topic: str, handler: Callable[[A2AMessage], Any]) -> None:
        """Subscribe to a topic for pub/sub messaging."""
        if topic not in self.subscriptions:
            self.subscriptions[topic] = []
        self.subscriptions[topic].append(handler)
    
    async def broadcast_message(
        self,
        message: A2AMessage,
        agent_filter: Optional[Callable[[AgentEndpoint], bool]] = None
    ) -> List[str]:
        """Broadcast message to multiple agents."""
        
        # Get target agents
        target_agents = list(self.agent_registry.values())
        
        if agent_filter:
            target_agents = [agent for agent in target_agents if agent_filter(agent)]
        
        # Send to all targets
        tasks = []
        for agent in target_agents:
            if agent.agent_id != self.agent_id:  # Don't send to self
                msg_copy = A2AMessage(
                    to_agent=agent.agent_id,
                    message_type=message.message_type,
                    content=message.content,
                    headers=message.headers,
                    metadata=message.metadata
                )
                tasks.append(self.send_message(msg_copy))
        
        # Execute broadcasts
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Count successes
        successful_sends = []
        for i, result in enumerate(results):
            if not isinstance(result, Exception):
                successful_sends.append(target_agents[i].agent_id)
        
        self.log_operation("message_broadcast", targets=len(target_agents), successful=len(successful_sends))
        
        return successful_sends
    
    async def setup_protocol_server(self, protocol: ProtocolType, port: int) -> bool:
        """Setup server for a specific protocol."""
        
        handler = self.protocol_handlers.get(protocol)
        if not handler:
            return False
        
        try:
            await handler["setup"](port)
            self.log_operation("protocol_server_started", protocol=protocol.value, port=port)
            return True
            
        except Exception as e:
            self.log_error("protocol_server_setup_failed", e, protocol=protocol.value, port=port)
            return False
    
    async def _setup_rest_server(self, port: int) -> None:
        """Setup REST API server."""
        from aiohttp import web
        
        app = web.Application()
        
        # Add routes
        app.router.add_post('/messages', self._handle_rest_message)
        app.router.add_get('/health', self._handle_health_check)
        app.router.add_get('/agents', self._handle_agent_list)
        
        # Start server
        runner = web.AppRunner(app)
        await runner.setup()
        
        site = web.TCPSite(runner, 'localhost', port)
        await site.start()
    
    async def _handle_rest_message(self, request) -> web.Response:
        """Handle incoming REST message."""
        try:
            data = await request.json()
            message = self._dict_to_message(data)
            
            # Process message
            response = await self._process_incoming_message(message)
            
            if response:
                return web.json_response({"response": response})
            else:
                return web.Response(status=202)  # Accepted
                
        except Exception as e:
            return web.json_response({"error": str(e)}, status=400)
    
    async def _handle_health_check(self, request) -> web.Response:
        """Handle health check request."""
        return web.json_response({
            "status": "healthy",
            "agent_id": self.agent_id,
            "timestamp": datetime.now().isoformat()
        })
    
    async def _handle_agent_list(self, request) -> web.Response:
        """Handle agent list request."""
        agents = [
            {
                "agent_id": agent.agent_id,
                "protocol": agent.protocol.value,
                "status": agent.status,
                "capabilities": agent.capabilities
            }
            for agent in self.agent_registry.values()
        ]
        
        return web.json_response({"agents": agents})
    
    async def _setup_websocket_server(self, port: int) -> None:
        """Setup WebSocket server."""
        async def handle_websocket(websocket, path):
            try:
                async for message_data in websocket:
                    data = json.loads(message_data)
                    message = self._dict_to_message(data)
                    
                    response = await self._process_incoming_message(message)
                    
                    if response:
                        await websocket.send(json.dumps(response))
                        
            except Exception as e:
                await websocket.send(json.dumps({"error": str(e)}))
        
        start_server = websockets.serve(handle_websocket, "localhost", port)
        await start_server
    
    async def _setup_grpc_server(self, port: int) -> None:
        """Setup gRPC server."""
        # gRPC server setup would require proto definitions
        # This is a placeholder
        pass
    
    async def _setup_mq_connection(self, port: int) -> None:
        """Setup message queue connection."""
        # Message queue connection setup
        # This is a placeholder
        pass
    
    async def _setup_tcp_server(self, port: int) -> None:
        """Setup TCP server."""
        async def handle_tcp_client(reader, writer):
            try:
                while True:
                    # Read length prefix
                    length_data = await reader.read(4)
                    if not length_data:
                        break
                    
                    message_length = int.from_bytes(length_data, byteorder='big')
                    
                    # Read message
                    message_data = await reader.read(message_length)
                    data = json.loads(message_data.decode('utf-8'))
                    message = self._dict_to_message(data)
                    
                    response = await self._process_incoming_message(message)
                    
                    if response:
                        response_data = json.dumps(response).encode('utf-8')
                        response_length = len(response_data).to_bytes(4, byteorder='big')
                        
                        writer.write(response_length + response_data)
                        await writer.drain()
                        
            except Exception as e:
                self.logger.error(f"TCP client error: {e}")
            finally:
                writer.close()
                await writer.wait_closed()
        
        server = await asyncio.start_server(handle_tcp_client, 'localhost', port)
        async with server:
            await server.serve_forever()
    
    async def _setup_udp_server(self, port: int) -> None:
        """Setup UDP server."""
        class UDPProtocol(asyncio.DatagramProtocol):
            def __init__(self, a2a_handler):
                self.a2a_handler = a2a_handler
            
            def datagram_received(self, data, addr):
                try:
                    message_data = json.loads(data.decode('utf-8'))
                    message = self.a2a_handler._dict_to_message(message_data)
                    
                    # Process message (fire and forget for UDP)
                    asyncio.create_task(self.a2a_handler._process_incoming_message(message))
                    
                except Exception as e:
                    self.a2a_handler.logger.error(f"UDP message processing error: {e}")
        
        transport, protocol = await asyncio.get_event_loop().create_datagram_endpoint(
            lambda: UDPProtocol(self),
            local_addr=('localhost', port)
        )
    
    async def _process_incoming_message(self, message: A2AMessage) -> Optional[Dict[str, Any]]:
        """Process incoming message and return response if needed."""
        
        # Check if message is part of conversation
        conversation_id = message.metadata.get("conversation_id")
        if conversation_id and conversation_id in self.conversation_contexts:
            conversation = self.conversation_contexts[conversation_id]
            conversation.messages.append(message)
            conversation.updated_at = datetime.now()
        
        # Find appropriate handler
        handler = self.message_handlers.get(message.message_type)
        if handler:
            try:
                result = await handler(message)
                
                if result:
                    return {
                        "id": str(uuid.uuid4()),
                        "from_agent": self.agent_id,
                        "to_agent": message.from_agent,
                        "message_type": "response",
                        "content": result,
                        "correlation_id": message.id,
                        "timestamp": datetime.now().isoformat()
                    }
                    
            except Exception as e:
                self.log_error("message_handler_failed", e, message_id=message.id)
                return {
                    "id": str(uuid.uuid4()),
                    "from_agent": self.agent_id,
                    "to_agent": message.from_agent,
                    "message_type": "error",
                    "content": {"error": str(e)},
                    "correlation_id": message.id,
                    "timestamp": datetime.now().isoformat()
                }
        
        return None
    
    def get_agent_registry(self) -> Dict[str, AgentEndpoint]:
        """Get copy of agent registry."""
        return self.agent_registry.copy()
    
    def get_conversation(self, conversation_id: str) -> Optional[ConversationContext]:
        """Get conversation context."""
        return self.conversation_contexts.get(conversation_id)
    
    def list_conversations(self) -> List[ConversationContext]:
        """List all active conversations."""
        return list(self.conversation_contexts.values())
    
    def get_protocol_stats(self) -> Dict[str, Any]:
        """Get protocol usage statistics."""
        
        protocol_usage = {}
        for agent in self.agent_registry.values():
            protocol = agent.protocol.value
            protocol_usage[protocol] = protocol_usage.get(protocol, 0) + 1
        
        return {
            "total_agents": len(self.agent_registry),
            "active_conversations": len(self.conversation_contexts),
            "active_connections": len(self.active_connections),
            "protocol_usage": protocol_usage,
            "message_handlers": len(self.message_handlers),
            "subscriptions": len(self.subscriptions)
        }