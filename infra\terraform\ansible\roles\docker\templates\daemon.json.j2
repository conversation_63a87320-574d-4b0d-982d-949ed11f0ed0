{"log-driver": "{{ docker_log_driver | default('json-file') }}", "log-opts": {"max-size": "{{ docker_log_options['max-size'] | default('10m') }}", "max-file": "{{ docker_log_options['max-file'] | default('3') }}"}, "storage-driver": "overlay2", "default-runtime": "runc", "insecure-registries": [], "registry-mirrors": [], "bip": "**********/16", "default-address-pools": [{"base": "**********/16", "size": 24}], "metrics-addr": "0.0.0.0:{{ metrics_port | default(9323) }}", "experimental": true, "features": {"buildkit": true}}