#!/bin/bash

# Test database setup script for <PERSON><PERSON>
# This script sets up a test database, runs migrations, and prepares for testing

set -e

# Configuration
DB_CONTAINER_NAME="crm_test_db"
DB_NAME="crm_db_test"
DB_USER="postgres"
DB_PASSWORD="testpassword"
DB_PORT="5433"

echo "🐳 Setting up test database container..."

# Stop and remove existing container if it exists
docker stop $DB_CONTAINER_NAME 2>/dev/null || true
docker rm $DB_CONTAINER_NAME 2>/dev/null || true

# Start PostgreSQL container
docker run -d \
    --name $DB_CONTAINER_NAME \
    -e POSTGRES_DB=$DB_NAME \
    -e POSTGRES_USER=$DB_USER \
    -e POSTGRES_PASSWORD=$DB_PASSWORD \
    -p $DB_PORT:5432 \
    postgres:17-alpine

echo "⏳ Waiting for PostgreSQL to be ready..."
sleep 5

# Wait for PostgreSQL to be ready
until docker exec $DB_CONTAINER_NAME pg_isready -U $DB_USER -d $DB_NAME 2>/dev/null; do
    echo "Waiting for PostgreSQL..."
    sleep 2
done

echo "📦 Running migrations..."

# Get the directory of this script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DB_DIR="$(dirname "$SCRIPT_DIR")"

# Apply migrations
if [ -f "$DB_DIR/migrations/V001__initial_schema.sql" ]; then
    docker exec -i $DB_CONTAINER_NAME psql -U $DB_USER -d $DB_NAME < "$DB_DIR/migrations/V001__initial_schema.sql"
fi

if [ -f "$DB_DIR/migrations/V002__crm_schema.sql" ]; then
    docker exec -i $DB_CONTAINER_NAME psql -U $DB_USER -d $DB_NAME < "$DB_DIR/migrations/V002__crm_schema.sql"
fi

# Export connection details for tests
export TEST_DATABASE_URL="postgres://$DB_USER:$DB_PASSWORD@localhost:$DB_PORT/$DB_NAME?sslmode=disable"

echo "✅ Test database ready!"
echo "Connection: $TEST_DATABASE_URL"
echo "Container: $DB_CONTAINER_NAME"