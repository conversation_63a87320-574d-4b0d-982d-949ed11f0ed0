version: '3.8'

services:
{% for service_name, service_config in services.items() %}
  {{ service_name }}{% if deployment_strategy == 'blue-green' %}-{{ deployment_color | default('blue') }}{% endif %}:
    image: {{ service_config.image }}
    container_name: {{ service_name }}{% if deployment_strategy == 'blue-green' %}-{{ deployment_color | default('blue') }}{% endif %}
    restart: {{ docker_restart_policy | default('unless-stopped') }}
    env_file:
      - {{ microservices_root }}/env/{{ service_name }}.env
    {% if service_config.ports is defined %}
    ports:
      {% for port in service_config.ports %}
      - "{{ port }}"
      {% endfor %}
    {% elif deployment_strategy != 'blue-green' %}
    ports:
      - "{{ service_config.port }}:{{ service_config.port }}"
    {% endif %}
    {% if deployment_strategy == 'blue-green' %}
    expose:
      - "{{ service_config.port }}"
    {% endif %}
    networks:
      - {{ docker_network_name | default('microservices') }}
    {% if service_config.volumes is defined %}
    volumes:
      {% for volume in service_config.volumes %}
      - {{ volume }}
      {% endfor %}
    {% endif %}
    {% if service_config.depends_on is defined %}
    depends_on:
      {% for dep in service_config.depends_on %}
      - {{ dep }}{% if deployment_strategy == 'blue-green' %}-{{ deployment_color | default('blue') }}{% endif %}
      {% endfor %}
    {% endif %}
    {% if service_config.health_check is defined %}
    healthcheck:
      test: {{ service_config.health_check.test | default(['CMD', 'curl', '-f', 'http://localhost:' + service_config.port|string + service_config.health_check.path]) | to_json }}
      interval: {{ service_config.health_check.interval | default('30s') }}
      timeout: {{ service_config.health_check.timeout | default('10s') }}
      retries: {{ service_config.health_check.retries | default(3) }}
      start_period: {{ service_config.health_check.start_period | default('40s') }}
    {% endif %}
    deploy:
      resources:
        limits:
          memory: {{ resource_limits[service_name].memory | default(docker_default_memory_limit) }}
          cpus: "{{ resource_limits[service_name].cpu | default(docker_default_cpu_limit) }}"
    labels:
      com.microservices.service: "{{ service_name }}"
      com.microservices.version: "{{ service_config.image.split(':')[-1] }}"
      com.microservices.environment: "{{ environment }}"
      {% if deployment_strategy == 'blue-green' %}
      com.microservices.deployment-color: "{{ deployment_color | default('blue') }}"
      {% endif %}
    logging:
      driver: "{{ docker_log_driver | default('json-file') }}"
      options:
        max-size: "{{ docker_log_options['max-size'] | default('10m') }}"
        max-file: "{{ docker_log_options['max-file'] | default('3') }}"
        labels: "com.microservices.service,com.microservices.environment"

{% endfor %}

networks:
  {{ docker_network_name | default('microservices') }}:
    external: true

{% if services | selectattr('volumes', 'defined') | list | length > 0 %}
volumes:
  {% for service_name, service_config in services.items() %}
  {% if service_config.volumes is defined %}
  {% for volume in service_config.volumes %}
  {% if ':' in volume and not volume.startswith('/') %}
  {{ volume.split(':')[0] }}:
    driver: local
  {% endif %}
  {% endfor %}
  {% endif %}
  {% endfor %}
{% endif %}