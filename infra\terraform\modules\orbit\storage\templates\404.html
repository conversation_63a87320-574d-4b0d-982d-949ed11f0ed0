<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page Not Found - ${project_name}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 40px 20px;
            text-align: center;
            line-height: 1.6;
            color: #333;
        }
        .error-code {
            font-size: 8rem;
            font-weight: bold;
            color: #e74c3c;
            margin: 0;
            line-height: 1;
        }
        .error-message {
            font-size: 1.5rem;
            margin: 20px 0;
            color: #555;
        }
        .description {
            color: #777;
            margin: 30px 0;
        }
        .actions {
            margin: 40px 0;
        }
        .button {
            display: inline-block;
            background: #2196f3;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            margin: 0 10px;
            transition: background 0.3s;
        }
        .button:hover {
            background: #1976d2;
        }
        .button.secondary {
            background: #f5f5f5;
            color: #333;
            border: 1px solid #ddd;
        }
        .button.secondary:hover {
            background: #e8e8e8;
        }
        .footer {
            margin-top: 60px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="error-code">404</div>
    <div class="error-message">Page Not Found</div>
    
    <div class="description">
        <p>The page you're looking for doesn't exist or has been moved.</p>
        <p>This might happen when your React app is still being deployed or if you're accessing a direct URL that requires client-side routing.</p>
    </div>

    <div class="actions">
        <a href="/" class="button">Go Home</a>
        <a href="javascript:history.back()" class="button secondary">Go Back</a>
    </div>

    <div class="footer">
        <p><strong>${project_name}</strong> - ${environment} environment</p>
        <p>If this error persists, please contact your system administrator.</p>
    </div>

    <script>
        // Auto-redirect to home for SPA routes after 5 seconds
        setTimeout(function() {
            if (window.location.pathname !== '/') {
                const redirect = confirm('This might be a single-page app route. Redirect to home page?');
                if (redirect) {
                    window.location.href = '/';
                }
            }
        }, 5000);
    </script>
</body>
</html>