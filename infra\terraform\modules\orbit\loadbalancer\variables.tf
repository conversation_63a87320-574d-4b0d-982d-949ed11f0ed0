# Load Balancer Mo<PERSON>le Variables

variable "project_name" {
  description = "Name of the project (used for resource naming)"
  type        = string
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
  default     = "dev"
  
  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "Environment must be one of: dev, staging, prod."
  }
}

# Backend configuration
variable "storage_bucket_name" {
  description = "Name of the Cloud Storage bucket for static files"
  type        = string
}

variable "api_backend_service" {
  description = "Self-link of the backend service for API traffic"
  type        = string
}

# SSL/TLS configuration
variable "enable_ssl" {
  description = "Whether to enable SSL/TLS termination"
  type        = bool
  default     = true
}

variable "use_managed_ssl" {
  description = "Whether to use Google-managed SSL certificates"
  type        = bool
  default     = true
}

variable "ssl_domains" {
  description = "List of domains for SSL certificate"
  type        = list(string)
  default     = []
}

variable "ssl_certificate_ids" {
  description = "List of self-managed SSL certificate IDs (if not using managed SSL)"
  type        = list(string)
  default     = []
}

variable "create_ssl_policy" {
  description = "Whether to create an SSL policy"
  type        = bool
  default     = true
}

variable "ssl_policy_name" {
  description = "Name of existing SSL policy to use (if not creating new one)"
  type        = string
  default     = ""
}

variable "ssl_policy_profile" {
  description = "SSL policy profile"
  type        = string
  default     = "MODERN"
  
  validation {
    condition     = contains(["COMPATIBLE", "MODERN", "RESTRICTED"], var.ssl_policy_profile)
    error_message = "SSL policy profile must be one of: COMPATIBLE, MODERN, RESTRICTED."
  }
}

variable "ssl_min_tls_version" {
  description = "Minimum TLS version for SSL policy"
  type        = string
  default     = "TLS_1_2"
  
  validation {
    condition     = contains(["TLS_1_0", "TLS_1_1", "TLS_1_2", "TLS_1_3"], var.ssl_min_tls_version)
    error_message = "Minimum TLS version must be one of: TLS_1_0, TLS_1_1, TLS_1_2, TLS_1_3."
  }
}

variable "enable_http_redirect" {
  description = "Whether to redirect HTTP to HTTPS (independent of SSL)"
  type        = bool
  default     = true
}

# CDN configuration
variable "enable_cdn" {
  description = "Whether to enable Cloud CDN"
  type        = bool
  default     = true
}

variable "cdn_default_ttl" {
  description = "Default TTL for CDN cache in seconds"
  type        = number
  default     = 3600
  
  validation {
    condition     = var.cdn_default_ttl >= 0 && var.cdn_default_ttl <= 86400
    error_message = "CDN default TTL must be between 0 and 86400 seconds."
  }
}

variable "cdn_max_ttl" {
  description = "Maximum TTL for CDN cache in seconds"
  type        = number
  default     = 86400
  
  validation {
    condition     = var.cdn_max_ttl >= 0 && var.cdn_max_ttl <= 31536000
    error_message = "CDN max TTL must be between 0 and 31536000 seconds."
  }
}

variable "cdn_client_ttl" {
  description = "Client TTL for CDN cache in seconds"
  type        = number
  default     = 3600
  
  validation {
    condition     = var.cdn_client_ttl >= 0 && var.cdn_client_ttl <= 86400
    error_message = "CDN client TTL must be between 0 and 86400 seconds."
  }
}

variable "cdn_serve_while_stale" {
  description = "How long to serve stale content while fetching fresh content"
  type        = number
  default     = 86400
  
  validation {
    condition     = var.cdn_serve_while_stale >= 0 && var.cdn_serve_while_stale <= 604800
    error_message = "CDN serve while stale must be between 0 and 604800 seconds."
  }
}

# Cloud Armor security configuration
variable "enable_cloud_armor" {
  description = "Whether to enable Cloud Armor security policies"
  type        = bool
  default     = false
}

variable "rate_limit_requests_per_minute" {
  description = "Rate limit requests per minute (0 to disable)"
  type        = number
  default     = 0
  
  validation {
    condition     = var.rate_limit_requests_per_minute >= 0 && var.rate_limit_requests_per_minute <= 10000
    error_message = "Rate limit must be between 0 and 10000 requests per minute."
  }
}

variable "blocked_regions" {
  description = "List of region codes to block"
  type        = list(string)
  default     = []
  
  # Example: ["CN", "RU", "KP"] to block China, Russia, North Korea
}

variable "blocked_ip_ranges" {
  description = "List of IP ranges to block"
  type        = list(string)
  default     = []
  
  validation {
    condition = alltrue([
      for cidr in var.blocked_ip_ranges : can(cidrhost(cidr, 0))
    ])
    error_message = "All blocked IP ranges must be valid IPv4 CIDR blocks."
  }
}