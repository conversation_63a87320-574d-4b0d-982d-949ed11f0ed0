---
# Terraform-generated Ansible inventory for dev environment
all:
  hosts:
    platform-platform-dev:
      ansible_host: **************
      ansible_user: ansible
      ansible_ssh_private_key_file: "{{ lookup('env', 'ANSIBLE_SSH_KEY') }}"
      ansible_ssh_common_args: '-o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null'
      
      # GCP metadata
      gcp_project_id: agent-dev-459718
      gcp_zone: australia-southeast1-a
      gcp_instance_name: platform-platform-dev
      environment: dev
      
      # Network information
      internal_ip: ********
      external_ip: **************
      
  children:
    microservices:
      hosts:
        platform-platform-dev:
    dev:
      hosts:
        platform-platform-dev: