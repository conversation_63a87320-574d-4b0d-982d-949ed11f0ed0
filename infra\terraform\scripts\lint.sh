#!/bin/bash
set -euo pipefail

# Terraform linting script using tflint

echo "Running Terraform linting..."

# Check if tflint is installed
if ! command -v tflint &> /dev/null; then
    echo "WARNING: tflint not found. Installing..."
    # Install tflint if not present
    if command -v brew &> /dev/null; then
        brew install tflint
    elif command -v curl &> /dev/null; then
        curl -s https://raw.githubusercontent.com/terraform-linters/tflint/master/install_linux.sh | bash
    else
        echo "ERROR: Cannot install tflint. Please install it manually."
        exit 1
    fi
fi

# Find all Terraform directories
terraform_dirs=$(find . -name "*.tf" -exec dirname {} \; | sort -u)

exit_code=0

for dir in $terraform_dirs; do
    echo "Linting Terraform configuration in: $dir"
    
    # Change to directory
    cd "$dir"
    
    # Initialize tflint
    tflint --init || true
    
    # Run tflint
    if ! tflint --format=compact; then
        echo "ERROR: Terraform linting failed in $dir"
        exit_code=1
    else
        echo "SUCCESS: Terraform linting passed in $dir"
    fi
    
    # Return to original directory
    cd - > /dev/null
done

if [ $exit_code -eq 0 ]; then
    echo "All Terraform configurations passed linting!"
else
    echo "Some Terraform configurations failed linting!"
fi

exit $exit_code