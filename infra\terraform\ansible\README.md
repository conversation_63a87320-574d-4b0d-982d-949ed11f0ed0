# Ansible Integration for Microservices Deployment

This directory contains the Ansible automation for deploying and managing microservices on GCP infrastructure created by Terraform.

## 🚀 Quick Start

### Method 1: Environment Auto-Detection (Recommended)

Navigate to any Terraform environment directory and source the setup script:

```bash
# For DEV environment
cd terraform/environments/dev
source setup.sh

# For STAGING environment  
cd terraform/environments/staging
source setup.sh

# For PROD environment
cd terraform/environments/prod
source setup.sh
```

### Method 2: Manual Environment Setup

From anywhere in the project:

```bash
cd terraform/ansible
source setup-env.sh
```

This will auto-detect your environment based on your current directory.

## 🎯 Environment Detection

The setup scripts automatically detect your target environment based on:

1. **Current directory path** - If you're in `terraform/environments/dev`, it sets DEV environment
2. **terraform.tfvars file** - Reads `environment` variable from the file
3. **Fallback to DEV** - If detection fails, defaults to development environment

### Supported Environments

| Environment | GCP Project ID (default) | Region | Zone |
|-------------|-------------------------|--------|------|
| `dev` | `crm-platform-dev` | `us-central1` | `us-central1-a` |
| `staging` | `crm-platform-staging` | `us-central1` | `us-central1-b` |
| `prod` | `crm-platform-prod` | `us-east1` | `us-east1-a` |

## 🛠️ Available Commands

After sourcing the setup script, you get these convenient aliases:

### Terraform Commands
```bash
tf-plan         # Run terraform plan
tf-apply        # Run terraform apply  
tf-destroy      # Run terraform destroy
```

### Ansible Commands
```bash
ansible-deploy    # Deploy all services
ansible-update    # Update service images
ansible-config    # Update service configuration
ansible-rollback  # Rollback to previous deployment
ansible-ping      # Test connectivity to instances
ansible-test      # Run integration tests
```

## 🧪 Testing the Integration

### Test Everything
```bash
ansible-test
```

### Test Specific Components
```bash
# Test dynamic inventory
ansible-inventory -i inventory/gcp.yml --list

# Test connectivity
ansible microservices -i inventory/gcp.yml -m ping

# Validate playbook syntax
ansible-playbook --syntax-check playbooks/deploy-services.yml
```

## 📋 Prerequisites

### Required Tools
- **Terraform** (>= 1.6)
- **Ansible** (>= 2.14)
- **gcloud CLI** (authenticated)
- **Python 3** with pip

### GCP Authentication
Ensure you're authenticated with GCP:
```bash
gcloud auth login
gcloud auth application-default login
```

### Install Ansible Dependencies
```bash
cd terraform/ansible
ansible-galaxy install -r requirements.yml
```

## 🔧 Configuration

### Environment Variables

The setup scripts automatically configure these variables:

| Variable | Description | Auto-set |
|----------|-------------|----------|
| `ENVIRONMENT` | Target environment (dev/staging/prod) | ✅ |
| `GCP_PROJECT_ID` | GCP project identifier | ✅ |
| `GCP_REGION` | GCP region for resources | ✅ |
| `GCP_ZONE` | GCP zone for compute instances | ✅ |
| `ANSIBLE_CONFIG` | Path to Ansible configuration | ✅ |
| `ANSIBLE_SSH_KEY` | SSH key for instance access | ✅ |
| `PROJECT_ROOT` | Project root directory | ✅ |

### Service Versions

Control service versions through Terraform variables:

```hcl
# In terraform.tfvars
service_versions = {
  gateway = "v1.2.3"
  auth    = "v1.2.3" 
  backend = "v1.2.3"
}
```

## 🚀 Deployment Workflow

### 1. Initial Setup
```bash
# Navigate to your target environment
cd terraform/environments/dev

# Source environment setup
source setup.sh

# Initialize Terraform
terraform init
```

### 2. Deploy Infrastructure + Services
```bash
# Plan deployment
tf-plan

# Apply (this will run Ansible automatically)
tf-apply
```

### 3. Ongoing Management
```bash
# Update service images
ansible-update

# Update configuration
ansible-config

# Rollback if needed
ansible-rollback
```

## 🔄 Blue-Green Deployments

The system supports zero-downtime blue-green deployments:

1. **Deploy to inactive color** - New services start alongside existing ones
2. **Health check** - Verify new services are healthy
3. **Switch traffic** - Nginx routes traffic to new services
4. **Clean up** - Remove old services

### Manual Blue-Green Deployment
```bash
ansible-playbook -i inventory/gcp.yml playbooks/update-images.yml \
  --extra-vars "deployment_color=green"
```

## 📁 Directory Structure

```
terraform/ansible/
├── playbooks/           # Deployment playbooks
├── roles/              # Ansible roles  
├── inventory/          # Dynamic and static inventories
├── templates/          # Jinja2 templates
├── vars/              # Environment-specific variables
├── ansible.cfg        # Ansible configuration
├── requirements.yml   # Galaxy requirements
├── setup-env.sh      # Environment auto-detection
├── test-integration.sh # Integration tests
└── README.md         # This file
```

## 🐛 Troubleshooting

### Common Issues

#### 1. "GCP_PROJECT_ID not set"
```bash
# Ensure you've sourced the setup script
source setup.sh

# Or manually set the project ID
export GCP_PROJECT_ID="your-project-id"
```

#### 2. "Cannot connect to instances"
```bash
# Check if instances exist
gcloud compute instances list --project=$GCP_PROJECT_ID

# Test SSH connectivity
gcloud compute ssh instance-name --zone=$GCP_ZONE
```

#### 3. "Ansible SSH key not found"
```bash
# The SSH key is created by Terraform
# Run terraform apply first
terraform apply
```

#### 4. "Permission denied"
```bash
# Ensure you're authenticated with GCP
gcloud auth login
gcloud auth list

# Check project permissions
gcloud projects get-iam-policy $GCP_PROJECT_ID
```

### Debug Mode

Enable verbose output:
```bash
export ANSIBLE_VERBOSITY=2
ansible-deploy
```

## 📚 Advanced Usage

### Custom Ansible Variables
```bash
ansible-playbook -i inventory/gcp.yml playbooks/deploy-services.yml \
  --extra-vars "log_level=debug enable_monitoring=true"
```

### Target Specific Hosts
```bash
ansible-playbook -i inventory/gcp.yml playbooks/deploy-services.yml \
  --limit "dev"
```

### Dry Run Mode
```bash
ansible-playbook -i inventory/gcp.yml playbooks/deploy-services.yml \
  --check --diff
```

## 🔒 Security Considerations

- SSH keys are auto-generated and stored securely
- Secrets are managed via GCP Secret Manager
- Service accounts follow principle of least privilege
- All communication uses private IPs where possible

## 📈 Monitoring

After deployment, services provide these endpoints:

- **Health checks**: `http://instance-ip/health`
- **Metrics**: `http://instance-ip:9090/metrics` (if enabled)
- **Service status**: Check via `docker ps` on instances

## 🤝 Contributing

When adding new services or modifying configurations:

1. Update `vars/{environment}.yml` with new service definitions
2. Add health checks to service configurations
3. Test with `ansible-test` before deployment
4. Update this README if adding new features

## 📞 Support

For issues with:
- **Terraform**: Check `terraform plan` output
- **Ansible**: Use `ansible-test` for diagnostics  
- **GCP**: Verify authentication and permissions
- **Services**: Check service logs via Docker commands