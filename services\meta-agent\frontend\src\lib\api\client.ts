// Auto-generated API client from OpenAPI specification
// Enhanced with full type safety and error handling

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import type { components } from './types';

// Type aliases for existing schemas
type Agent = components["schemas"]["AgentResponse"];
type Task = components["schemas"]["TaskResponse"];
type SystemStats = components["schemas"]["SystemStatsResponse"];
type SystemHealth = components["schemas"]["SystemHealthResponse"];
type GenerationRequest = components["schemas"]["AgentCreate"];
type DeploymentConfig = components["schemas"]["DeploymentConfig"];

// Placeholder types for missing schemas (will be properly typed when schemas are added)
type ApplicationAnalysis = any;
type MigrationPlan = any;
type MigrationProject = any;

export interface ApiClientConfig {
  baseURL?: string;
  timeout?: number;
  headers?: Record<string, string>;
  apiKey?: string;
  bearerToken?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: any; // Will be properly typed when PaginationInfo schema is available
}

export interface AgentListResponse {
  agents: Agent[];
  total: number;
  limit: number;
  offset: number;
}

export interface TaskListResponse {
  tasks: Task[];
  total: number;
  limit: number;
  offset: number;
}

export interface ApiError {
  error: string;
  error_code?: string;
  details?: Record<string, any>;
  timestamp: string;
  request_id?: string;
}

export class ApiClient {
  public client: AxiosInstance;
  
  constructor(config: ApiClientConfig = {}) {
    const {
      baseURL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000',
      timeout = 30000,
      headers = {},
      apiKey,
      bearerToken,
    } = config;
    
    this.client = axios.create({
      baseURL: `${baseURL}/api/v1`,
      timeout,
      headers: {
        'Content-Type': 'application/json',
        ...headers,
      },
    });
    
    // Add auth interceptor
    this.client.interceptors.request.use((config) => {
      if (bearerToken) {
        config.headers.Authorization = `Bearer ${bearerToken}`;
      } else if (apiKey) {
        config.headers['X-API-Key'] = apiKey;
      }
      return config;
    });
    
    // Add response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.data) {
          throw new ApiError({
            ...error.response.data,
            status: error.response.status
          });
        }
        throw error;
      }
    );
  }
  
  // Generic request method
  private async request<T>(
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH',
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const response: AxiosResponse<T> = await this.client.request({
      method,
      url,
      data,
      ...config,
    });
    
    return response.data;
  }

  // Generic HTTP methods for custom endpoints
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>('GET', url, undefined, config);
  }

  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>('POST', url, data, config);
  }

  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>('PUT', url, data, config);
  }

  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>('DELETE', url, undefined, config);
  }

  async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>('PATCH', url, data, config);
  }

  /**
   * List all agents
   */
  async getAgents(params?: {
    page?: number;
    size?: number;
    status?: Agent['status'];
    type?: Agent['type'];
    search?: string;
  }, config?: AxiosRequestConfig): Promise<AgentListResponse> {
    return this.request<AgentListResponse>('GET', '/agents', undefined, { params, ...config });
  }

  /**
   * Create a new agent
   */
  async createAgent(data: Types.GenerationRequest, config?: AxiosRequestConfig): Promise<{
    agent: Types.Agent;
    task_id: string;
  }> {
    return this.request<{
      agent: Types.Agent;
      task_id: string;
    }>('POST', '/agents', data, config);
  }

  /**
   * Get agent details
   */
  async getAgentById(agent_id: string, config?: AxiosRequestConfig): Promise<Types.Agent> {
    return this.request<Types.Agent>('GET', `/agents/${agent_id}`, undefined, config);
  }

  /**
   * Update agent
   */
  async updateAgent(agent_id: string, data: {
    name?: string;
    description?: string;
    configuration?: Record<string, any>;
    deployment_config?: Types.DeploymentConfig;
  }, config?: AxiosRequestConfig): Promise<Types.Agent> {
    return this.request<Types.Agent>('PUT', `/agents/${agent_id}`, data, config);
  }

  /**
   * Delete agent
   */
  async deleteAgent(agent_id: string, config?: AxiosRequestConfig): Promise<void> {
    return this.request<void>('DELETE', `/agents/${agent_id}`, undefined, config);
  }

  /**
   * Start agent
   */
  async startAgent(agent_id: string, config?: AxiosRequestConfig): Promise<{
    status: string;
    port: number;
    process_id: number;
  }> {
    return this.request<{
      status: string;
      port: number;
      process_id: number;
    }>('POST', `/agents/${agent_id}/start`, undefined, config);
  }

  /**
   * Stop agent
   */
  async stopAgent(agent_id: string, config?: AxiosRequestConfig): Promise<{
    status: string;
    message: string;
  }> {
    return this.request<{
      status: string;
      message: string;
    }>('POST', `/agents/${agent_id}/stop`, undefined, config);
  }

  /**
   * List tasks
   */
  async getTasks(params?: {
    page?: number;
    size?: number;
    status?: Task['status'];
    type?: Task['type'];
    agent_id?: string;
  }, config?: AxiosRequestConfig): Promise<TaskListResponse> {
    return this.request<TaskListResponse>('GET', '/tasks', undefined, { params, ...config });
  }

  /**
   * Get task details
   */
  async getTaskById(task_id: string, config?: AxiosRequestConfig): Promise<Types.Task> {
    return this.request<Types.Task>('GET', `/tasks/${task_id}`, undefined, config);
  }

  /**
   * Analyze application for migration
   */
  async analyzeMigration(data: {
    app_path: string;
  }, config?: AxiosRequestConfig): Promise<Types.ApplicationAnalysis> {
    return this.request<Types.ApplicationAnalysis>('POST', '/migration/analyze', data, config);
  }

  /**
   * Get analysis details
   */
  async getMigrationAnalysis(analysis_id: string, config?: AxiosRequestConfig): Promise<Types.ApplicationAnalysis> {
    return this.request<Types.ApplicationAnalysis>('GET', `/migration/analysis/${analysis_id}`, undefined, config);
  }

  /**
   * Create migration plan
   */
  async createMigrationPlan(data: {
    analysis_id: string;
    custom_strategy?: Types.MigrationPlan['strategy'];
  }, config?: AxiosRequestConfig): Promise<Types.MigrationPlan> {
    return this.request<Types.MigrationPlan>('POST', '/migration/plan', data, config);
  }

  /**
   * Get migration plan details
   */
  async getMigrationPlan(plan_id: string, config?: AxiosRequestConfig): Promise<Types.MigrationPlan> {
    return this.request<Types.MigrationPlan>('GET', `/migration/plan/${plan_id}`, undefined, config);
  }

  /**
   * Start migration
   */
  async startMigration(data: {
    plan_id: string;
  }, config?: AxiosRequestConfig): Promise<{
    project_id: string;
    status: string;
    estimated_timeline_days: number;
  }> {
    return this.request<{
      project_id: string;
      status: string;
      estimated_timeline_days: number;
    }>('POST', '/migration/start', data, config);
  }

  /**
   * Get migration project status
   */
  async getMigrationStatus(project_id: string, config?: AxiosRequestConfig): Promise<Types.MigrationProject> {
    return this.request<Types.MigrationProject>('GET', `/migration/status/${project_id}`, undefined, config);
  }

  /**
   * List migration analyses
   */
  async listMigrationAnalyses(config?: AxiosRequestConfig): Promise<Types.ApplicationAnalysis[]> {
    return this.request<Types.ApplicationAnalysis[]>('GET', '/migration/analyses', undefined, config);
  }

  /**
   * List migration plans
   */
  async listMigrationPlans(config?: AxiosRequestConfig): Promise<Types.MigrationPlan[]> {
    return this.request<Types.MigrationPlan[]>('GET', '/migration/plans', undefined, config);
  }

  /**
   * List migration projects
   */
  async listMigrationProjects(config?: AxiosRequestConfig): Promise<Types.MigrationProject[]> {
    return this.request<Types.MigrationProject[]>('GET', '/migration/projects', undefined, config);
  }

  /**
   * Get system statistics
   */
  async getSystemStats(config?: AxiosRequestConfig): Promise<Types.SystemStats> {
    return this.request<Types.SystemStats>('GET', '/runtime/system/stats', undefined, config);
  }

  /**
   * Health check
   */
  async getSystemHealth(config?: AxiosRequestConfig): Promise<Types.SystemHealth> {
    return this.request<Types.SystemHealth>('GET', '/runtime/system/health', undefined, config);
  }

  /**
   * Get migration summary
   */
  async getMigrationSummary(config?: AxiosRequestConfig): Promise<{
    total_analyses: number;
    total_migration_plans: number;
    total_projects: number;
    active_migrations: number;
    completed_migrations: number;
    failed_migrations: number;
    success_rate: number;
    status_distribution: Record<string, number>;
    most_common_strategies: Record<string, number>;
    average_complexity: number;
  }> {
    return this.request('GET', '/migration/summary', undefined, config);
  }

  /**
   * Parse requirements using AI
   */
  async parseRequirements(data: {
    requirements: string;
    context?: Record<string, any>;
  }, config?: AxiosRequestConfig): Promise<{
    name: string;
    type: string;
    language: string;
    framework: string;
    capabilities: string[];
    deployment: Record<string, any>;
    advanced_options: Record<string, any>;
    reasoning: string;
    confidence: number;
  }> {
    return this.request('POST', '/ai/parse-requirements', data, config);
  }

  /**
   * Analyze code with AI
   */
  async analyzeCode(data: {
    code: string;
    language: string;
    analysis_type?: string;
  }, config?: AxiosRequestConfig): Promise<{
    quality_score: number;
    issues: string[];
    suggestions: string[];
    complexity: string;
    maintainability: string;
    security_concerns: string[];
    performance_notes: string[];
  }> {
    return this.request('POST', '/ai/analyze-code', data, config);
  }

  /**
   * Generate documentation with AI
   */
  async generateDocumentation(data: {
    code: string;
    language: string;
    doc_type?: string;
  }, config?: AxiosRequestConfig): Promise<{
    documentation: string;
    format: string;
  }> {
    return this.request('POST', '/ai/generate-documentation', data, config);
  }

  /**
   * Generate AI content
   */
  async generateAIContent(data: {
    prompt: string;
    capability: string;
    max_tokens?: number;
    temperature?: number;
    system_message?: string;
    preferred_provider?: string;
  }, config?: AxiosRequestConfig): Promise<{
    content: string;
    provider: string;
    model: string;
    usage: Record<string, any>;
    metadata: Record<string, any>;
  }> {
    return this.request('POST', '/ai/generate', data, config);
  }

  /**
   * Get available AI providers
   */
  async getAIProviders(config?: AxiosRequestConfig): Promise<{
    providers: string[];
    total: number;
    status: string;
  }> {
    return this.request('GET', '/ai/providers', undefined, config);
  }

  /**
   * Deploy an existing agent
   */
  async deployAgent(agent_id: string, config?: AxiosRequestConfig): Promise<{
    message: string;
    deployment_url?: string;
    status: string;
  }> {
    return this.request('POST', `/agents/${agent_id}/deploy`, undefined, {
      timeout: 60000, // 60 seconds timeout for deployment
      ...config
    });
  }

  /**
   * Get agent deployment status
   */
  async getAgentDeployment(agent_id: string, config?: AxiosRequestConfig): Promise<{
    deployed: boolean;
    status: string;
    url?: string;
    health_status?: string;
    port?: number;
  }> {
    return this.request('GET', `/agents/${agent_id}/deployment`, undefined, config);
  }

  /**
   * Stop agent deployment
   */
  async stopAgentDeployment(agent_id: string, config?: AxiosRequestConfig): Promise<{
    message: string;
  }> {
    return this.request('DELETE', `/agents/${agent_id}/deployment`, undefined, config);
  }

  /**
   * Get AI capabilities
   */
  async getAICapabilities(config?: AxiosRequestConfig): Promise<{
    capabilities: Array<{name: string, description: string}>;
  }> {
    return this.request('GET', '/ai/capabilities', undefined, config);
  }

  /**
   * Check AI service health
   */
  async getAIHealth(config?: AxiosRequestConfig): Promise<{
    status: string;
    providers: Record<string, any>;
    total_providers: number;
    healthy_providers: number;
  }> {
    return this.request('GET', '/ai/health', undefined, config);
  }

  /**
   * Update client configuration
   */
  updateConfig(config: Partial<Types.ApiClientConfig>): void {
    if (config.baseURL) {
      this.client.defaults.baseURL = config.baseURL;
    }
    
    if (config.timeout) {
      this.client.defaults.timeout = config.timeout;
    }
    
    if (config.headers) {
      Object.assign(this.client.defaults.headers, config.headers);
    }
    
    if (config.bearerToken) {
      this.client.defaults.headers.Authorization = `Bearer ${config.bearerToken}`;
    } else if (config.apiKey) {
      this.client.defaults.headers['X-API-Key'] = config.apiKey;
    }
  }

  /**
   * Set authentication token
   */
  setAuthToken(token: string): void {
    this.client.defaults.headers.Authorization = `Bearer ${token}`;
  }

  /**
   * Clear authentication
   */
  clearAuth(): void {
    delete this.client.defaults.headers.Authorization;
    delete this.client.defaults.headers['X-API-Key'];
  }

}

export class ApiError extends Error {
  public readonly error_code?: string;
  public readonly details?: Record<string, any>;
  public readonly timestamp: string;
  public readonly request_id?: string;
  public readonly status?: number;
  
  constructor(errorData: Types.ApiError & { status?: number }) {
    // Handle both FastAPI (detail) and custom error formats
    const message = errorData.error || errorData.detail || 'Unknown error';
    super(message);
    this.name = 'ApiError';
    this.error_code = errorData.error_code;
    this.details = errorData.details;
    this.timestamp = errorData.timestamp;
    this.request_id = errorData.request_id;
    this.status = errorData.status;
  }
}

// Export types for convenience
export * from './types';