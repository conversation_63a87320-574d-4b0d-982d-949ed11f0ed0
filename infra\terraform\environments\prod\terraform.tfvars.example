# Production Environment Configuration Example
# Copy this file to terraform.tfvars and customize the values

# GCP Project Configuration
project_id   = "twodot-agent-prod"  # Production project ID
project_name = "platform"
region       = "australia-southeast1"
zone         = "australia-southeast1-a"

# GitHub Repository (for CI/CD automation)
github_repo_owner = "your-github-organization"
github_repo_name  = "your-repo-name"

# SSL/TLS Configuration (REQUIRED for production)
ssl_domains = [
  "internal.twodot.ai",
  "api.twodot.ai"
]

# Security Configuration
admin_ip_ranges = [
  "***********/24",    # Your office IP range
  "************/24",   # Your VPN IP range
  # DO NOT use "0.0.0.0/0" in production!
]

# Cloud Armor Security Settings
rate_limit_requests_per_minute = 1000
blocked_regions = [
  # "CN",  # Example: Block China
  # "RU",  # Example: Block Russia
]
blocked_ip_ranges = [
  # "*********/24",  # Example: Block specific IP ranges
]

# Database Migration Settings
enable_scheduled_migrations = false  # Require manual approval for production
migration_schedule         = "0 2 * * 0"  # Sundays at 2 AM UTC
migration_timezone         = "UTC"

# Notification Settings
notification_email = "<EMAIL>"
slack_webhook_url  = "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK"

# High Availability Settings
enable_multi_region = false  # Set to true for multi-region deployment
backup_regions = [
  # "us-east1",
  # "europe-west1"
]

# Monitoring and Alerting
enable_advanced_monitoring = true
alert_email_addresses = [
  "<EMAIL>",
  "<EMAIL>"
]

# Performance and Scaling
min_instance_count = 2
max_instance_count = 10
enable_auto_scaling = true

# Compliance and Audit
enable_audit_logging = true
data_retention_days  = 365  # 1 year retention

# Cost Optimization
enable_committed_use_discounts = false  # Consider enabling for long-term savings
enable_preemptible_instances   = false  # Not recommended for production

# Production Notes:
# - All security features are enabled
# - SSL/TLS is required with valid domains
# - Access is restricted to specified IP ranges
# - Database deletion protection is enabled
# - Regional availability for high availability
# - Comprehensive monitoring and alerting
# - Automated backups with long retention
# - Cloud Armor protection against attacks