# Build stage
FROM golang:1.24-alpine AS builder

WORKDIR /app

# Copy go mod files from root
COPY go.mod go.sum ./

# Copy shared dependencies first
COPY shared/go/ ./shared/go/

# Copy service dependencies
COPY services/orbit/rest-api/ ./services/orbit/rest-api/

RUN go mod download

# Copy source code
COPY services/orbit/gateway/ ./services/orbit/gateway/

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -o gateway ./services/orbit/gateway

# Final stage
FROM alpine:latest

RUN apk --no-cache add ca-certificates wget

WORKDIR /root/

# Copy the binary from builder
COPY --from=builder /app/gateway .

# Expose port
EXPOSE 8085

# Run the binary
CMD ["./gateway"]