# Load Balancer Module Outputs

output "load_balancer_ip" {
  description = "IP address of the load balancer"
  value       = google_compute_global_address.lb_ip.address
}

output "load_balancer_ip_name" {
  description = "Name of the load balancer IP address"
  value       = google_compute_global_address.lb_ip.name
}

output "load_balancer_ip_self_link" {
  description = "Self link of the load balancer IP address"
  value       = google_compute_global_address.lb_ip.self_link
}

# URL map outputs
output "url_map_name" {
  description = "Name of the URL map"
  value       = google_compute_url_map.lb_url_map.name
}

output "url_map_self_link" {
  description = "Self link of the URL map"
  value       = google_compute_url_map.lb_url_map.self_link
}

# SSL certificate outputs
output "managed_ssl_certificate_name" {
  description = "Name of the managed SSL certificate"
  value       = var.enable_ssl && var.use_managed_ssl ? google_compute_managed_ssl_certificate.ssl_cert[0].name : null
}

output "managed_ssl_certificate_status" {
  description = "Status of the managed SSL certificate"
  value       = var.enable_ssl && var.use_managed_ssl ? "PENDING" : "Not enabled"
}

output "ssl_certificate_domains" {
  description = "Domains covered by the SSL certificate"
  value       = var.enable_ssl && var.use_managed_ssl ? var.ssl_domains : []
}

# Proxy outputs
output "https_proxy_name" {
  description = "Name of the HTTPS target proxy"
  value       = var.enable_ssl ? google_compute_target_https_proxy.https_proxy[0].name : null
}

output "http_proxy_name" {
  description = "Name of the HTTP target proxy"
  value       = var.enable_ssl && var.enable_http_redirect ? google_compute_target_http_proxy.http_proxy_redirect[0].name : (var.enable_ssl ? google_compute_target_http_proxy.http_proxy_direct[0].name : google_compute_target_http_proxy.http_proxy_main[0].name)
}

# Forwarding rule outputs
output "https_forwarding_rule_name" {
  description = "Name of the HTTPS forwarding rule"
  value       = var.enable_ssl ? google_compute_global_forwarding_rule.https_rule[0].name : null
}

output "http_forwarding_rule_name" {
  description = "Name of the HTTP forwarding rule"
  value       = google_compute_global_forwarding_rule.http_rule.name
}

# Backend outputs
output "static_backend_name" {
  description = "Name of the static files backend bucket"
  value       = google_compute_backend_bucket.static_backend.name
}

output "static_backend_self_link" {
  description = "Self link of the static files backend bucket"
  value       = google_compute_backend_bucket.static_backend.self_link
}

# SSL policy outputs
output "ssl_policy_name" {
  description = "Name of the SSL policy"
  value       = var.enable_ssl && var.create_ssl_policy ? google_compute_ssl_policy.ssl_policy[0].name : var.ssl_policy_name
}

output "ssl_policy_self_link" {
  description = "Self link of the SSL policy"
  value       = var.enable_ssl && var.create_ssl_policy ? google_compute_ssl_policy.ssl_policy[0].self_link : null
}

# Cloud Armor outputs
output "security_policy_name" {
  description = "Name of the Cloud Armor security policy"
  value       = var.enable_cloud_armor ? google_compute_security_policy.security_policy[0].name : null
}

output "security_policy_self_link" {
  description = "Self link of the Cloud Armor security policy"
  value       = var.enable_cloud_armor ? google_compute_security_policy.security_policy[0].self_link : null
}

# Website URLs
output "website_urls" {
  description = "URLs to access the website"
  value = {
    http  = "http://${google_compute_global_address.lb_ip.address}"
    https = var.enable_ssl ? "https://${google_compute_global_address.lb_ip.address}" : null
  }
}

output "domain_urls" {
  description = "URLs using custom domains (if SSL is enabled)"
  value = var.enable_ssl && length(var.ssl_domains) > 0 ? {
    for domain in var.ssl_domains :
    domain => "https://${domain}"
  } : {}
}

# API endpoints
output "api_endpoints" {
  description = "API endpoint URLs"
  value = {
    base_url = var.enable_ssl ? "https://${google_compute_global_address.lb_ip.address}/api" : "http://${google_compute_global_address.lb_ip.address}/api"
    auth     = var.enable_ssl ? "https://${google_compute_global_address.lb_ip.address}/api/v1/auth" : "http://${google_compute_global_address.lb_ip.address}/api/v1/auth"
    crm      = var.enable_ssl ? "https://${google_compute_global_address.lb_ip.address}/api/v1" : "http://${google_compute_global_address.lb_ip.address}/api/v1"
    health   = var.enable_ssl ? "https://${google_compute_global_address.lb_ip.address}/health" : "http://${google_compute_global_address.lb_ip.address}/health"
  }
}

# DNS configuration
output "dns_configuration" {
  description = "DNS configuration for custom domains"
  value = length(var.ssl_domains) > 0 ? {
    ip_address = google_compute_global_address.lb_ip.address
    domains    = var.ssl_domains
    a_records  = [for domain in var.ssl_domains : "${domain} A ${google_compute_global_address.lb_ip.address}"]
    instructions = "Create A records pointing your domains to ${google_compute_global_address.lb_ip.address}"
  } : null
}

# CDN configuration
output "cdn_configuration" {
  description = "Cloud CDN configuration details"
  value = var.enable_cdn ? {
    enabled            = true
    type              = "Cloud CDN"
    cache_mode        = "CACHE_ALL_STATIC"
    default_ttl       = var.cdn_default_ttl
    max_ttl          = var.cdn_max_ttl
    client_ttl       = var.cdn_client_ttl
    serve_while_stale = var.cdn_serve_while_stale
    origin_type      = "Cloud Storage"
  } : { enabled = false }
}

# Monitoring and troubleshooting
output "monitoring_urls" {
  description = "URLs for monitoring and troubleshooting"
  value = {
    load_balancer_console = "https://console.cloud.google.com/net-services/loadbalancing/list/loadBalancers"
    ssl_certificates      = "https://console.cloud.google.com/net-security/sslcertificates/list"
    cloud_armor          = var.enable_cloud_armor ? "https://console.cloud.google.com/net-security/securitypolicies/list" : null
    cdn_cache            = var.enable_cdn ? "https://console.cloud.google.com/net-services/cdn/list" : null
  }
}

# Health check information
output "health_check_info" {
  description = "Information about health checks"
  value = {
    backend_health_check = "Check the health of your backend service at /health endpoint"
    ssl_certificate_status = var.enable_ssl && var.use_managed_ssl ? "Monitor SSL certificate provisioning status in GCP Console" : null
    troubleshooting = {
      check_dns       = "Verify DNS A records point to ${google_compute_global_address.lb_ip.address}"
      check_firewall  = "Ensure firewall rules allow HTTP (80) and HTTPS (443) traffic"
      check_backend   = "Verify backend service is healthy and responding"
      check_ssl       = var.enable_ssl ? "SSL certificate may take 10-20 minutes to provision" : null
    }
  }
}