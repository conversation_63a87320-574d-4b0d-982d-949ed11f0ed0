#!/bin/bash
set -e

echo "Stopping PostgreSQL database..."

# Check if container is running
if docker ps | grep -q platform_postgres; then
    # Try docker compose first (newer syntax), then docker-compose (older syntax)
    if docker compose version &> /dev/null 2>&1; then
        echo "Using 'docker compose' to stop..."
        docker compose down --remove-orphans 2>/dev/null || docker stop platform_postgres
    elif command -v docker-compose &> /dev/null 2>&1; then
        echo "Using 'docker-compose' to stop..."
        docker-compose down --remove-orphans 2>/dev/null || docker stop platform_postgres
    else
        echo "Using docker directly to stop..."
        docker stop platform_postgres 2>/dev/null || true
    fi
    
    # Remove container if it exists
    docker rm platform_postgres 2>/dev/null || true
    
    echo "PostgreSQL stopped."
else
    echo "PostgreSQL container is not running."
fi