"""AI Gateway - Unified interface for multiple AI providers with intelligent routing."""

import asyncio
from typing import Dict, List, Optional, Any, AsyncIterator
from .providers.base import BaseAIProvider, AIMessage, AIResponse, ModelInfo, ModelCapability
from .providers.openai import OpenAIProvider
from .providers.anthropic import <PERSON><PERSON><PERSON><PERSON><PERSON>ider
from .providers.google import GoogleA<PERSON>rovider
import logging
from enum import Enum
from dataclasses import dataclass
import time

logger = logging.getLogger(__name__)

class RoutingStrategy(str, Enum):
    """AI routing strategies."""
    COST_OPTIMIZED = "cost_optimized"
    PERFORMANCE_OPTIMIZED = "performance_optimized" 
    CAPABILITY_BASED = "capability_based"
    LOAD_BALANCED = "load_balanced"
    REDUNDANCY = "redundancy"

@dataclass
class ProviderConfig:
    """Provider configuration."""
    name: str
    api_key: str
    enabled: bool = True
    priority: int = 1
    rate_limit: int = 100
    kwargs: Dict[str, Any] = None

class AIGateway:
    """Unified AI Gateway with intelligent routing and fallback."""
    
    def __init__(self):
        self.providers: Dict[str, BaseAIProvider] = {}
        self.provider_configs: Dict[str, ProviderConfig] = {}
        self.usage_stats: Dict[str, Dict[str, Any]] = {}
        self.routing_strategy = RoutingStrategy.CAPABILITY_BASED
        
    async def initialize(self, provider_configs: List[ProviderConfig]) -> bool:
        """Initialize all providers."""
        initialization_results = {}
        
        for config in provider_configs:
            try:
                provider = self._create_provider(config)
                if provider:
                    success = await provider.initialize()
                    if success:
                        self.providers[config.name] = provider
                        self.provider_configs[config.name] = config
                        self.usage_stats[config.name] = {
                            "requests": 0,
                            "tokens": 0,
                            "cost": 0.0,
                            "errors": 0,
                            "avg_response_time": 0.0
                        }
                        initialization_results[config.name] = True
                        logger.info(f"Provider {config.name} initialized successfully")
                    else:
                        initialization_results[config.name] = False
                        logger.error(f"Provider {config.name} initialization failed")
            except Exception as e:
                initialization_results[config.name] = False
                logger.error(f"Provider {config.name} initialization error: {e}")
        
        return len(self.providers) > 0
    
    def _create_provider(self, config: ProviderConfig) -> Optional[BaseAIProvider]:
        """Create provider instance based on configuration."""
        kwargs = config.kwargs or {}
        
        if config.name == "openai":
            return OpenAIProvider(config.api_key, **kwargs)
        elif config.name == "anthropic":
            return AnthropicProvider(config.api_key, **kwargs)
        elif config.name == "google":
            return GoogleAIProvider(config.api_key, **kwargs)
        else:
            logger.error(f"Unknown provider: {config.name}")
            return None
    
    async def chat_completion(
        self,
        messages: List[AIMessage],
        model: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        stream: bool = False,
        preferred_provider: Optional[str] = None,
        **kwargs
    ) -> AIResponse:
        """Generate chat completion with intelligent provider selection."""
        
        # Select provider and model
        provider_name, selected_model = await self._select_provider_and_model(
            task_type=ModelCapability.CHAT_COMPLETION,
            model_preference=model,
            provider_preference=preferred_provider
        )
        
        if not provider_name:
            raise Exception("No suitable provider available for chat completion")
        
        provider = self.providers[provider_name]
        start_time = time.time()
        
        try:
            response = await provider.chat_completion(
                messages=messages,
                model=selected_model,
                temperature=temperature,
                max_tokens=max_tokens,
                stream=stream,
                **kwargs
            )
            
            # Update usage statistics
            self._update_usage_stats(
                provider_name, 
                response.usage or {},
                time.time() - start_time,
                success=True
            )
            
            return response
            
        except Exception as e:
            self._update_usage_stats(provider_name, {}, 0, success=False)
            
            # Try fallback provider
            fallback_response = await self._try_fallback(
                "chat_completion",
                exclude_provider=provider_name,
                messages=messages,
                model=model,
                temperature=temperature,
                max_tokens=max_tokens,
                stream=stream,
                **kwargs
            )
            
            if fallback_response:
                return fallback_response
            
            raise Exception(f"Chat completion failed: {e}")
    
    async def stream_completion(
        self,
        messages: List[AIMessage],
        model: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        preferred_provider: Optional[str] = None,
        **kwargs
    ) -> AsyncIterator[str]:
        """Stream chat completion with provider selection."""
        
        provider_name, selected_model = await self._select_provider_and_model(
            task_type=ModelCapability.CHAT_COMPLETION,
            model_preference=model,
            provider_preference=preferred_provider
        )
        
        if not provider_name:
            raise Exception("No suitable provider available for streaming")
        
        provider = self.providers[provider_name]
        
        try:
            async for chunk in provider.stream_completion(
                messages=messages,
                model=selected_model,
                temperature=temperature,
                max_tokens=max_tokens,
                **kwargs
            ):
                yield chunk
                
        except Exception as e:
            self._update_usage_stats(provider_name, {}, 0, success=False)
            logger.error(f"Streaming failed for {provider_name}: {e}")
            raise
    
    async def generate_embedding(
        self,
        text: str,
        model: Optional[str] = None,
        preferred_provider: Optional[str] = None
    ) -> List[float]:
        """Generate text embeddings."""
        
        provider_name, selected_model = await self._select_provider_and_model(
            task_type=ModelCapability.EMBEDDING,
            model_preference=model,
            provider_preference=preferred_provider
        )
        
        if not provider_name:
            raise Exception("No suitable provider available for embeddings")
        
        provider = self.providers[provider_name]
        start_time = time.time()
        
        try:
            embedding = await provider.generate_embedding(text, selected_model)
            self._update_usage_stats(provider_name, {"tokens": len(text.split())}, 
                                   time.time() - start_time, success=True)
            return embedding
            
        except Exception as e:
            self._update_usage_stats(provider_name, {}, 0, success=False)
            
            # Try fallback
            fallback_result = await self._try_fallback(
                "generate_embedding",
                exclude_provider=provider_name,
                text=text,
                model=model
            )
            
            if fallback_result:
                return fallback_result
            
            raise Exception(f"Embedding generation failed: {e}")
    
    async def analyze_image(
        self,
        image_data: bytes,
        prompt: str,
        model: Optional[str] = None,
        preferred_provider: Optional[str] = None
    ) -> AIResponse:
        """Analyze image with AI."""
        
        provider_name, selected_model = await self._select_provider_and_model(
            task_type=ModelCapability.IMAGE_ANALYSIS,
            model_preference=model,
            provider_preference=preferred_provider
        )
        
        if not provider_name:
            raise Exception("No suitable provider available for image analysis")
        
        provider = self.providers[provider_name]
        start_time = time.time()
        
        try:
            response = await provider.analyze_image(image_data, prompt, selected_model)
            self._update_usage_stats(provider_name, response.usage or {}, 
                                   time.time() - start_time, success=True)
            return response
            
        except Exception as e:
            self._update_usage_stats(provider_name, {}, 0, success=False)
            raise Exception(f"Image analysis failed: {e}")
    
    async def function_calling(
        self,
        messages: List[AIMessage],
        functions: List[Dict[str, Any]],
        model: Optional[str] = None,
        preferred_provider: Optional[str] = None,
        **kwargs
    ) -> AIResponse:
        """Execute function calling."""
        
        provider_name, selected_model = await self._select_provider_and_model(
            task_type=ModelCapability.FUNCTION_CALLING,
            model_preference=model,
            provider_preference=preferred_provider
        )
        
        if not provider_name:
            raise Exception("No suitable provider available for function calling")
        
        provider = self.providers[provider_name]
        start_time = time.time()
        
        try:
            response = await provider.function_calling(
                messages, functions, selected_model, **kwargs
            )
            self._update_usage_stats(provider_name, response.usage or {},
                                   time.time() - start_time, success=True)
            return response
            
        except Exception as e:
            self._update_usage_stats(provider_name, {}, 0, success=False)
            raise Exception(f"Function calling failed: {e}")
    
    async def _select_provider_and_model(
        self,
        task_type: ModelCapability,
        model_preference: Optional[str] = None,
        provider_preference: Optional[str] = None
    ) -> tuple[Optional[str], Optional[str]]:
        """Intelligently select provider and model based on strategy."""
        
        suitable_providers = {}
        
        # Filter providers by capability
        for provider_name, provider in self.providers.items():
            if not self.provider_configs[provider_name].enabled:
                continue
                
            available_models = provider.get_available_models()
            suitable_models = [
                model for model in available_models
                if task_type in model.capabilities
            ]
            
            if suitable_models:
                suitable_providers[provider_name] = suitable_models
        
        if not suitable_providers:
            return None, None
        
        # If specific provider requested
        if provider_preference and provider_preference in suitable_providers:
            models = suitable_providers[provider_preference]
            if model_preference:
                matching_models = [m for m in models if m.name == model_preference]
                if matching_models:
                    return provider_preference, model_preference
            return provider_preference, models[0].name
        
        # If specific model requested
        if model_preference:
            for provider_name, models in suitable_providers.items():
                matching_models = [m for m in models if m.name == model_preference]
                if matching_models:
                    return provider_name, model_preference
        
        # Apply routing strategy
        return self._apply_routing_strategy(suitable_providers, task_type)
    
    def _apply_routing_strategy(
        self,
        suitable_providers: Dict[str, List[ModelInfo]],
        task_type: ModelCapability
    ) -> tuple[Optional[str], Optional[str]]:
        """Apply routing strategy to select provider and model."""
        
        if self.routing_strategy == RoutingStrategy.COST_OPTIMIZED:
            # Select cheapest option
            best_provider = None
            best_model = None
            best_cost = float('inf')
            
            for provider_name, models in suitable_providers.items():
                for model in models:
                    if model.cost_per_token and model.cost_per_token < best_cost:
                        best_cost = model.cost_per_token
                        best_provider = provider_name
                        best_model = model.name
            
            return best_provider, best_model
        
        elif self.routing_strategy == RoutingStrategy.PERFORMANCE_OPTIMIZED:
            # Select based on historical performance
            best_provider = None
            best_model = None
            best_score = 0
            
            for provider_name, models in suitable_providers.items():
                stats = self.usage_stats.get(provider_name, {})
                error_rate = stats.get('errors', 0) / max(stats.get('requests', 1), 1)
                avg_time = stats.get('avg_response_time', 1.0)
                
                # Score = success_rate / response_time
                score = (1 - error_rate) / max(avg_time, 0.1)
                
                if score > best_score:
                    best_score = score
                    best_provider = provider_name
                    best_model = models[0].name  # Select first suitable model
            
            return best_provider, best_model
        
        elif self.routing_strategy == RoutingStrategy.LOAD_BALANCED:
            # Select provider with least recent usage
            min_requests = float('inf')
            best_provider = None
            
            for provider_name in suitable_providers.keys():
                requests = self.usage_stats.get(provider_name, {}).get('requests', 0)
                if requests < min_requests:
                    min_requests = requests
                    best_provider = provider_name
            
            if best_provider:
                models = suitable_providers[best_provider]
                return best_provider, models[0].name
        
        # Default: capability-based (first available)
        provider_name = list(suitable_providers.keys())[0]
        models = suitable_providers[provider_name]
        return provider_name, models[0].name
    
    async def _try_fallback(
        self,
        method_name: str,
        exclude_provider: str,
        **kwargs
    ) -> Optional[Any]:
        """Try fallback providers for failed requests."""
        
        for provider_name, provider in self.providers.items():
            if provider_name == exclude_provider:
                continue
            
            if not self.provider_configs[provider_name].enabled:
                continue
            
            try:
                method = getattr(provider, method_name)
                result = await method(**kwargs)
                logger.info(f"Fallback successful with provider: {provider_name}")
                return result
            except Exception as e:
                logger.warning(f"Fallback failed for {provider_name}: {e}")
                continue
        
        return None
    
    def _update_usage_stats(
        self,
        provider_name: str,
        usage: Dict[str, int],
        response_time: float,
        success: bool
    ):
        """Update provider usage statistics."""
        stats = self.usage_stats[provider_name]
        
        stats["requests"] += 1
        if not success:
            stats["errors"] += 1
        
        if "total_tokens" in usage:
            stats["tokens"] += usage["total_tokens"]
        
        # Update average response time
        current_avg = stats["avg_response_time"]
        request_count = stats["requests"]
        stats["avg_response_time"] = (current_avg * (request_count - 1) + response_time) / request_count
    
    def get_provider_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get usage statistics for all providers."""
        return self.usage_stats.copy()
    
    def get_available_models(self) -> Dict[str, List[ModelInfo]]:
        """Get all available models from all providers."""
        all_models = {}
        for provider_name, provider in self.providers.items():
            all_models[provider_name] = provider.get_available_models()
        return all_models
    
    def set_routing_strategy(self, strategy: RoutingStrategy):
        """Set routing strategy."""
        self.routing_strategy = strategy
        logger.info(f"Routing strategy changed to: {strategy}")
    
    async def health_check(self) -> Dict[str, bool]:
        """Check health of all providers."""
        health_status = {}
        
        for provider_name, provider in self.providers.items():
            try:
                health_status[provider_name] = await provider.health_check()
            except Exception:
                health_status[provider_name] = False
        
        return health_status