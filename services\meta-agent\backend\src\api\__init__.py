"""
AI Agent Platform - API Package
"""

from fastapi import APIRouter
from .agents import router as agents_router
from .tasks import router as tasks_router
from .auth import router as auth_router
from .runtime import router as runtime_router
from .orchestration import router as orchestration_router
from .ai import router as ai_router
from .generation import router as generation_router
from .vector_db import router as vector_db_router
from .a2a import router as a2a_router
from .google_adk import router as google_adk_router
from .deployment import router as deployment_router
from .evolution import router as evolution_router
from .workflows import router as workflows_router
from .ai_assistant import router as ai_assistant_router

# Create main API router
api_router = APIRouter()

# Include all sub-routers
api_router.include_router(auth_router)  # Now includes OAuth, MFA, and RBAC
api_router.include_router(agents_router)
api_router.include_router(tasks_router)
api_router.include_router(orchestration_router)
api_router.include_router(runtime_router)
api_router.include_router(ai_router)
api_router.include_router(generation_router)
api_router.include_router(vector_db_router)
api_router.include_router(a2a_router)
api_router.include_router(google_adk_router)
api_router.include_router(deployment_router)
api_router.include_router(evolution_router)
api_router.include_router(workflows_router)
api_router.include_router(ai_assistant_router)

# TODO: Add other routers as they are implemented
# api_router.include_router(intelligence_router)