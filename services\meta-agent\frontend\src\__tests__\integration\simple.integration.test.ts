/**
 * Simple Integration Test
 * Basic smoke test to verify API connectivity
 */

import { apiService } from '../../services/api.test';
import {
  User,
  LoginRequest,
  RegisterRequest,
  TokenResponse,
} from '@/types/api';

class AuthServiceTest {
  async login(credentials: LoginRequest): Promise<User> {
    const params = new URLSearchParams();
    params.append('username', credentials.username);
    params.append('password', credentials.password);

    const response = await apiService.post<TokenResponse>(
      '/auth/login',
      params
    );
    apiService.setTokens(response.data);

    // Get user data after login
    const userResponse = await apiService.get<User>('/auth/me');
    return userResponse.data;
  }

  async register(data: RegisterRequest): Promise<User> {
    const response = await apiService.post<User>('/auth/register', data);
    return response.data;
  }

  async getCurrentUser(): Promise<User> {
    const response = await apiService.get<User>('/auth/me');
    return response.data;
  }

  logout() {
    apiService.logout();
  }

  isAuthenticated(): boolean {
    return !!apiService.getToken();
  }
}

const authService = new AuthServiceTest();

describe('Simple Integration Test', () => {
  test('should be able to connect to backend', async () => {
    // Simple test to check if backend is accessible
    try {
      const response = await fetch('http://localhost:8000/');
      const data = await response.json();
      
      expect(response.ok).toBe(true);
      expect(data.message).toBe('Welcome to AI Agent Platform');
      expect(data.status).toBe('running');
    } catch (error) {
      console.error('Backend connection failed:', error);
      throw error;
    }
  });

  test('should handle user registration and login flow (basic)', async () => {
    const testUser = {
      username: `simpletest_${Date.now()}`,
      email: `simpletest_${Date.now()}@example.com`,
      password: 'SimpleTestPassword123!',
      full_name: 'Simple Test User'
    };

    try {
      // Register user
      console.log('🔥 Registering user:', testUser.username);
      const user = await authService.register(testUser);
      
      expect(user).toBeDefined();
      expect(user.username).toBe(testUser.username);
      expect(user.email).toBe(testUser.email);
      
      console.log('✅ User registered successfully');

      // Login user  
      console.log('🔥 Logging in user:', testUser.username);
      const loginResponse = await authService.login({
        username: testUser.username,
        password: testUser.password
      });

      expect(loginResponse).toBeDefined();
      expect(loginResponse.username).toBe(testUser.username);
      expect(authService.isAuthenticated()).toBe(true);
      
      console.log('✅ User logged in successfully');

      // Get current user
      console.log('🔥 Getting current user');
      const currentUser = await authService.getCurrentUser();
      
      expect(currentUser).toBeDefined();
      expect(currentUser.username).toBe(testUser.username);
      
      console.log('✅ Got current user successfully');

      // Logout
      authService.logout();
      expect(authService.isAuthenticated()).toBe(false);
      
      console.log('✅ User logged out successfully');

    } catch (error) {
      console.error('❌ Test failed:', error);
      throw error;
    }
  }, 60000); // 60 second timeout for this test
});