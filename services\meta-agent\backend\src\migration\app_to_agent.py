"""Application-to-Agent Migration System.

This module provides tools to analyze existing applications and convert them
into AI-powered agents that can run on the platform.
"""

from typing import Dict, Any, List, Optional, Tuple, Union
from dataclasses import dataclass, field
from enum import Enum
import asyncio
import json
import uuid
import os
import ast
import subprocess
from datetime import datetime
from pathlib import Path
import yaml
import re
from utils.logging import get_logger, LoggerMixin
from utils.exceptions import AgentError, ValidationError
from intelligence.gateway import AIGateway
from intelligence.providers.base import AIMessage
from generation.ai_workflow_generator import AgentWorkflowGenerator

logger = get_logger(__name__)

class ApplicationType(str, Enum):
    """Supported application types for migration."""
    WEB_APP = "web_app"
    API_SERVICE = "api_service"
    CLI_TOOL = "cli_tool"
    MICROSERVICE = "microservice"
    MONOLITH = "monolith"
    SCRIPT = "script"
    DESKTOP_APP = "desktop_app"
    MOBILE_APP = "mobile_app"

class MigrationStrategy(str, Enum):
    """Migration strategies."""
    WRAPPER = "wrapper"          # Wrap existing app
    REWRITE = "rewrite"         # Complete rewrite
    HYBRID = "hybrid"           # Partial migration
    INTERFACE = "interface"     # Add AI interface layer
    DECOMPOSE = "decompose"     # Break into microservices

class MigrationStatus(str, Enum):
    """Migration status."""
    ANALYZING = "analyzing"
    PLANNING = "planning"
    MIGRATING = "migrating"
    TESTING = "testing"
    COMPLETED = "completed"
    FAILED = "failed"

@dataclass
class ApplicationAnalysis:
    """Analysis results of an existing application."""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    app_path: str = ""
    app_type: ApplicationType = ApplicationType.WEB_APP
    technology_stack: List[str] = field(default_factory=list)
    dependencies: Dict[str, str] = field(default_factory=dict)
    entry_points: List[str] = field(default_factory=list)
    endpoints: List[Dict[str, Any]] = field(default_factory=list)
    data_models: List[Dict[str, Any]] = field(default_factory=list)
    business_logic: List[Dict[str, Any]] = field(default_factory=list)
    configurations: Dict[str, Any] = field(default_factory=dict)
    environment_vars: List[str] = field(default_factory=list)
    database_schemas: List[Dict[str, Any]] = field(default_factory=list)
    external_integrations: List[Dict[str, Any]] = field(default_factory=list)
    complexity_score: float = 0.0
    migration_feasibility: float = 0.0
    recommended_strategy: MigrationStrategy = MigrationStrategy.WRAPPER
    estimated_effort: int = 0  # hours
    created_at: datetime = field(default_factory=datetime.now)

@dataclass
class MigrationPlan:
    """Detailed migration plan."""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    analysis_id: str = ""
    strategy: MigrationStrategy = MigrationStrategy.WRAPPER
    phases: List[Dict[str, Any]] = field(default_factory=list)
    agent_specifications: List[Dict[str, Any]] = field(default_factory=list)
    data_migration_plan: Dict[str, Any] = field(default_factory=dict)
    testing_strategy: Dict[str, Any] = field(default_factory=dict)
    rollback_plan: Dict[str, Any] = field(default_factory=dict)
    risk_assessment: List[Dict[str, Any]] = field(default_factory=list)
    success_criteria: List[str] = field(default_factory=list)
    estimated_timeline: int = 0  # days
    resource_requirements: Dict[str, Any] = field(default_factory=dict)

@dataclass
class MigrationProject:
    """Active migration project."""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    analysis: ApplicationAnalysis = field(default_factory=ApplicationAnalysis)
    plan: MigrationPlan = field(default_factory=MigrationPlan)
    status: MigrationStatus = MigrationStatus.ANALYZING
    progress: float = 0.0
    current_phase: str = ""
    generated_agents: List[str] = field(default_factory=list)
    migration_artifacts: Dict[str, Any] = field(default_factory=dict)
    logs: List[Dict[str, Any]] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error: Optional[str] = None

class ApplicationAnalyzer(LoggerMixin):
    """Analyzer for existing applications."""
    
    def __init__(self, ai_gateway: AIGateway):
        self.ai_gateway = ai_gateway
        self.supported_languages = {
            ".py": "python",
            ".js": "javascript", 
            ".ts": "typescript",
            ".java": "java",
            ".go": "go",
            ".rs": "rust",
            ".rb": "ruby",
            ".php": "php",
            ".cs": "csharp",
            ".cpp": "cpp",
            ".c": "c"
        }
    
    async def analyze_application(self, app_path: str) -> ApplicationAnalysis:
        """Analyze an existing application."""
        
        if not os.path.exists(app_path):
            raise ValidationError(f"Application path does not exist: {app_path}")
        
        analysis = ApplicationAnalysis(app_path=app_path)
        
        try:
            # Basic file system analysis
            await self._analyze_file_structure(analysis)
            
            # Technology stack detection
            await self._detect_technology_stack(analysis)
            
            # Dependency analysis
            await self._analyze_dependencies(analysis)
            
            # Code analysis
            await self._analyze_code_structure(analysis)
            
            # Configuration analysis
            await self._analyze_configurations(analysis)
            
            # Database schema analysis
            await self._analyze_database_schemas(analysis)
            
            # AI-powered analysis
            await self._ai_analyze_application(analysis)
            
            # Calculate scores
            analysis.complexity_score = self._calculate_complexity_score(analysis)
            analysis.migration_feasibility = self._calculate_feasibility_score(analysis)
            analysis.recommended_strategy = self._recommend_strategy(analysis)
            analysis.estimated_effort = self._estimate_effort(analysis)
            
            self.log_operation("application_analyzed", 
                             app_path=app_path,
                             app_type=analysis.app_type.value,
                             complexity=analysis.complexity_score,
                             feasibility=analysis.migration_feasibility)
            
            return analysis
            
        except Exception as e:
            self.log_error("application_analysis_failed", e, app_path=app_path)
            raise AgentError(f"Application analysis failed: {e}")
    
    async def _analyze_file_structure(self, analysis: ApplicationAnalysis) -> None:
        """Analyze file and directory structure."""
        
        app_path = Path(analysis.app_path)
        
        # Count files by type
        file_counts = {}
        total_files = 0
        
        for file_path in app_path.rglob("*"):
            if file_path.is_file():
                total_files += 1
                suffix = file_path.suffix.lower()
                file_counts[suffix] = file_counts.get(suffix, 0) + 1
        
        # Identify main language
        main_language = None
        max_count = 0
        
        for suffix, count in file_counts.items():
            if suffix in self.supported_languages and count > max_count:
                max_count = count
                main_language = self.supported_languages[suffix]
        
        if main_language:
            analysis.technology_stack.append(main_language)
        
        # Detect application type based on structure
        analysis.app_type = self._detect_app_type(app_path, file_counts)
        
        # Find entry points
        analysis.entry_points = self._find_entry_points(app_path)
    
    def _detect_app_type(self, app_path: Path, file_counts: Dict[str, int]) -> ApplicationType:
        """Detect application type from structure."""
        
        # Check for common patterns
        if (app_path / "package.json").exists():
            package_json = json.loads((app_path / "package.json").read_text())
            
            if "next" in package_json.get("dependencies", {}):
                return ApplicationType.WEB_APP
            elif "express" in package_json.get("dependencies", {}):
                return ApplicationType.API_SERVICE
            elif package_json.get("bin"):
                return ApplicationType.CLI_TOOL
        
        if (app_path / "requirements.txt").exists() or (app_path / "pyproject.toml").exists():
            if (app_path / "manage.py").exists():
                return ApplicationType.WEB_APP
            elif (app_path / "app.py").exists() or (app_path / "main.py").exists():
                return ApplicationType.API_SERVICE
        
        if (app_path / "Cargo.toml").exists():
            cargo_toml = (app_path / "Cargo.toml").read_text()
            if "[[bin]]" in cargo_toml:
                return ApplicationType.CLI_TOOL
            else:
                return ApplicationType.API_SERVICE
        
        if (app_path / "pom.xml").exists() or (app_path / "build.gradle").exists():
            if (app_path / "src" / "main" / "webapp").exists():
                return ApplicationType.WEB_APP
            else:
                return ApplicationType.API_SERVICE
        
        # Default based on file types
        if file_counts.get(".html", 0) > 0 or file_counts.get(".css", 0) > 0:
            return ApplicationType.WEB_APP
        
        return ApplicationType.API_SERVICE
    
    def _find_entry_points(self, app_path: Path) -> List[str]:
        """Find application entry points."""
        
        entry_points = []
        
        # Common entry point files
        common_entries = [
            "main.py", "app.py", "server.py", "index.py",
            "main.js", "index.js", "server.js", "app.js",
            "main.go", "main.rs", "Main.java",
            "manage.py"  # Django
        ]
        
        for entry in common_entries:
            if (app_path / entry).exists():
                entry_points.append(str(app_path / entry))
        
        # Look in src directory
        src_path = app_path / "src"
        if src_path.exists():
            for entry in common_entries:
                if (src_path / entry).exists():
                    entry_points.append(str(src_path / entry))
        
        return entry_points
    
    async def _detect_technology_stack(self, analysis: ApplicationAnalysis) -> None:
        """Detect technology stack."""
        
        app_path = Path(analysis.app_path)
        
        # Package managers and their files
        package_files = {
            "package.json": ["nodejs", "npm"],
            "yarn.lock": ["nodejs", "yarn"],
            "requirements.txt": ["python", "pip"],
            "Pipfile": ["python", "pipenv"],
            "poetry.lock": ["python", "poetry"],
            "Cargo.toml": ["rust", "cargo"],
            "go.mod": ["golang", "go-modules"],
            "pom.xml": ["java", "maven"],
            "build.gradle": ["java", "gradle"],
            "composer.json": ["php", "composer"],
            "Gemfile": ["ruby", "bundler"]
        }
        
        for file_name, technologies in package_files.items():
            if (app_path / file_name).exists():
                analysis.technology_stack.extend(technologies)
        
        # Docker
        if (app_path / "Dockerfile").exists():
            analysis.technology_stack.append("docker")
        
        if (app_path / "docker-compose.yml").exists():
            analysis.technology_stack.append("docker-compose")
        
        # CI/CD
        if (app_path / ".github" / "workflows").exists():
            analysis.technology_stack.append("github-actions")
        
        if (app_path / ".gitlab-ci.yml").exists():
            analysis.technology_stack.append("gitlab-ci")
        
        # Frameworks (based on dependencies)
        framework_patterns = {
            "react": ["react"],
            "vue": ["vue"],
            "angular": ["angular"],
            "express": ["express"],
            "fastapi": ["fastapi"],
            "django": ["django"],
            "flask": ["flask"],
            "spring": ["spring", "spring-boot"],
            "laravel": ["laravel"],
            "rails": ["rails"]
        }
        
        # This would be enhanced with actual dependency parsing
        analysis.technology_stack = list(set(analysis.technology_stack))
    
    async def _analyze_dependencies(self, analysis: ApplicationAnalysis) -> None:
        """Analyze application dependencies."""
        
        app_path = Path(analysis.app_path)
        
        # Python dependencies
        if (app_path / "requirements.txt").exists():
            requirements = (app_path / "requirements.txt").read_text().strip().split('\n')
            for req in requirements:
                if req and not req.startswith('#'):
                    parts = req.split('==')
                    package = parts[0].strip()
                    version = parts[1].strip() if len(parts) > 1 else "latest"
                    analysis.dependencies[package] = version
        
        # Node.js dependencies
        if (app_path / "package.json").exists():
            package_json = json.loads((app_path / "package.json").read_text())
            deps = package_json.get("dependencies", {})
            dev_deps = package_json.get("devDependencies", {})
            analysis.dependencies.update(deps)
            analysis.dependencies.update(dev_deps)
        
        # Go dependencies
        if (app_path / "go.mod").exists():
            go_mod = (app_path / "go.mod").read_text()
            # Simple parsing - would be enhanced with proper go.mod parser
            for line in go_mod.split('\n'):
                if line.strip().startswith('require'):
                    parts = line.strip().split()
                    if len(parts) >= 2:
                        analysis.dependencies[parts[1]] = parts[2] if len(parts) > 2 else "latest"
    
    async def _analyze_code_structure(self, analysis: ApplicationAnalysis) -> None:
        """Analyze code structure and extract business logic."""
        
        app_path = Path(analysis.app_path)
        
        # Find all code files
        code_files = []
        for suffix in self.supported_languages:
            code_files.extend(app_path.rglob(f"*{suffix}"))
        
        # Analyze up to 20 main files to avoid performance issues
        main_files = sorted(code_files, key=lambda x: x.stat().st_size, reverse=True)[:20]
        
        for file_path in main_files:
            try:
                await self._analyze_code_file(file_path, analysis)
            except Exception as e:
                self.logger.warning(f"Failed to analyze {file_path}: {e}")
    
    async def _analyze_code_file(self, file_path: Path, analysis: ApplicationAnalysis) -> None:
        """Analyze a single code file."""
        
        try:
            content = file_path.read_text(encoding='utf-8')
        except:
            return  # Skip files that can't be read
        
        # Python AST analysis
        if file_path.suffix == ".py":
            try:
                tree = ast.parse(content)
                
                # Extract classes and functions
                for node in ast.walk(tree):
                    if isinstance(node, ast.ClassDef):
                        analysis.data_models.append({
                            "type": "class",
                            "name": node.name,
                            "file": str(file_path),
                            "methods": [n.name for n in node.body if isinstance(n, ast.FunctionDef)]
                        })
                    elif isinstance(node, ast.FunctionDef):
                        analysis.business_logic.append({
                            "type": "function",
                            "name": node.name,
                            "file": str(file_path),
                            "args": [arg.arg for arg in node.args.args]
                        })
                        
            except:
                pass  # Skip invalid Python files
        
        # Look for API endpoints (simple pattern matching)
        endpoint_patterns = [
            r'@app\.route\(["\']([^"\']+)["\']',  # Flask
            r'@router\.(get|post|put|delete)\(["\']([^"\']+)["\']',  # FastAPI
            r'app\.(get|post|put|delete)\(["\']([^"\']+)["\']',  # Express
            r'Route::(get|post|put|delete)\(["\']([^"\']+)["\']',  # Laravel
        ]
        
        for pattern in endpoint_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                if isinstance(match, tuple):
                    method = match[0] if len(match) > 1 else "GET"
                    path = match[1] if len(match) > 1 else match[0]
                else:
                    method = "GET"
                    path = match
                
                analysis.endpoints.append({
                    "method": method.upper(),
                    "path": path,
                    "file": str(file_path)
                })
    
    async def _analyze_configurations(self, analysis: ApplicationAnalysis) -> None:
        """Analyze configuration files."""
        
        app_path = Path(analysis.app_path)
        
        # Common config files
        config_files = [
            "config.json", "config.yaml", "config.yml",
            ".env", ".env.example", ".env.local",
            "settings.py", "config.py",
            "application.properties", "application.yml"
        ]
        
        for config_file in config_files:
            file_path = app_path / config_file
            if file_path.exists():
                try:
                    if config_file.endswith(('.yml', '.yaml')):
                        config_data = yaml.safe_load(file_path.read_text())
                        analysis.configurations[config_file] = config_data
                    elif config_file.endswith('.json'):
                        config_data = json.loads(file_path.read_text())
                        analysis.configurations[config_file] = config_data
                    elif config_file.startswith('.env'):
                        env_vars = []
                        for line in file_path.read_text().split('\n'):
                            if '=' in line and not line.startswith('#'):
                                env_vars.append(line.split('=')[0].strip())
                        analysis.environment_vars.extend(env_vars)
                except Exception as e:
                    self.logger.warning(f"Failed to parse config file {config_file}: {e}")
    
    async def _analyze_database_schemas(self, analysis: ApplicationAnalysis) -> None:
        """Analyze database schemas."""
        
        app_path = Path(analysis.app_path)
        
        # Look for migration files
        migration_dirs = ["migrations", "db/migrate", "database/migrations"]
        
        for migration_dir in migration_dirs:
            migration_path = app_path / migration_dir
            if migration_path.exists():
                for migration_file in migration_path.rglob("*.sql"):
                    try:
                        content = migration_file.read_text()
                        # Extract CREATE TABLE statements
                        tables = re.findall(r'CREATE TABLE (\w+)', content, re.IGNORECASE)
                        for table in tables:
                            analysis.database_schemas.append({
                                "table": table,
                                "file": str(migration_file),
                                "type": "sql_migration"
                            })
                    except:
                        continue
        
        # Look for ORM models (Python)
        for model_file in app_path.rglob("models.py"):
            try:
                content = model_file.read_text()
                # Simple pattern for Django/SQLAlchemy models
                models = re.findall(r'class (\w+)\([^)]*Model[^)]*\):', content)
                for model in models:
                    analysis.database_schemas.append({
                        "model": model,
                        "file": str(model_file),
                        "type": "orm_model"
                    })
            except:
                continue
    
    async def _ai_analyze_application(self, analysis: ApplicationAnalysis) -> None:
        """Use AI to analyze application architecture and patterns."""
        
        # Create summary for AI analysis
        summary = {
            "app_type": analysis.app_type.value,
            "technology_stack": analysis.technology_stack,
            "entry_points": len(analysis.entry_points),
            "endpoints": len(analysis.endpoints),
            "models": len(analysis.data_models),
            "business_logic": len(analysis.business_logic),
            "dependencies": len(analysis.dependencies),
            "config_files": len(analysis.configurations)
        }
        
        prompt = f"""Analyze this application for AI agent migration:

Application Summary:
{json.dumps(summary, indent=2)}

Sample Endpoints:
{json.dumps(analysis.endpoints[:5], indent=2)}

Sample Business Logic:
{json.dumps(analysis.business_logic[:5], indent=2)}

Technology Stack: {', '.join(analysis.technology_stack)}

Please provide:
1. Architecture pattern identification (MVC, microservices, monolith, etc.)
2. Integration complexity assessment (1-10 scale)  
3. Data flow patterns
4. External service dependencies
5. Recommended migration approach
6. Potential challenges

Format as JSON with keys: architecture_pattern, integration_complexity, data_flow_patterns, external_dependencies, migration_approach, challenges."""
        
        try:
            messages = [
                AIMessage(role="system", content="You are a senior software architect specializing in application migration and modernization."),
                AIMessage(role="user", content=prompt)
            ]
            
            response = await self.ai_gateway.chat_completion(
                messages=messages,
                model="claude-3-5-sonnet-20241022",
                temperature=0.2,
                max_tokens=1500
            )
            
            ai_analysis = json.loads(response.content)
            
            # Store AI insights
            analysis.configurations["ai_analysis"] = ai_analysis
            
            # Extract external integrations
            external_deps = ai_analysis.get("external_dependencies", [])
            for dep in external_deps:
                analysis.external_integrations.append({
                    "name": dep,
                    "type": "external_service"
                })
                
        except Exception as e:
            self.logger.warning(f"AI analysis failed: {e}")
    
    def _calculate_complexity_score(self, analysis: ApplicationAnalysis) -> float:
        """Calculate application complexity score (0-10)."""
        
        score = 0.0
        
        # Base complexity from file counts
        total_files = len(analysis.entry_points) + len(analysis.endpoints) + len(analysis.data_models)
        score += min(total_files / 50.0, 3.0)  # Max 3 points
        
        # Technology stack diversity
        score += min(len(analysis.technology_stack) / 5.0, 2.0)  # Max 2 points
        
        # Dependency complexity
        score += min(len(analysis.dependencies) / 20.0, 2.0)  # Max 2 points
        
        # Database complexity
        score += min(len(analysis.database_schemas) / 10.0, 1.5)  # Max 1.5 points
        
        # External integrations
        score += min(len(analysis.external_integrations) / 5.0, 1.5)  # Max 1.5 points
        
        return min(score, 10.0)
    
    def _calculate_feasibility_score(self, analysis: ApplicationAnalysis) -> float:
        """Calculate migration feasibility score (0-10)."""
        
        score = 10.0  # Start optimistic
        
        # Penalize complexity
        score -= analysis.complexity_score * 0.5
        
        # Reward modern technology stack
        modern_techs = ["python", "nodejs", "golang", "rust", "docker"]
        modern_count = sum(1 for tech in analysis.technology_stack if tech in modern_techs)
        score += modern_count * 0.5
        
        # Penalize monolithic architecture
        if analysis.app_type == ApplicationType.MONOLITH:
            score -= 2.0
        
        # Reward API-based applications
        if analysis.app_type in [ApplicationType.API_SERVICE, ApplicationType.MICROSERVICE]:
            score += 1.0
        
        # Penalize too many external dependencies
        if len(analysis.external_integrations) > 10:
            score -= 1.0
        
        return max(min(score, 10.0), 0.0)
    
    def _recommend_strategy(self, analysis: ApplicationAnalysis) -> MigrationStrategy:
        """Recommend migration strategy based on analysis."""
        
        complexity = analysis.complexity_score
        feasibility = analysis.migration_feasibility
        
        if feasibility >= 8.0 and complexity <= 5.0:
            return MigrationStrategy.REWRITE
        elif feasibility >= 6.0 and analysis.app_type == ApplicationType.API_SERVICE:
            return MigrationStrategy.INTERFACE
        elif complexity >= 7.0:
            return MigrationStrategy.DECOMPOSE
        elif feasibility >= 5.0:
            return MigrationStrategy.HYBRID
        else:
            return MigrationStrategy.WRAPPER
    
    def _estimate_effort(self, analysis: ApplicationAnalysis) -> int:
        """Estimate migration effort in hours."""
        
        base_effort = 40  # Base 40 hours
        
        # Scale by complexity
        base_effort += int(analysis.complexity_score * 20)
        
        # Scale by code size
        code_size = len(analysis.endpoints) + len(analysis.data_models) + len(analysis.business_logic)
        base_effort += int(code_size * 2)
        
        # Adjust by strategy
        strategy_multipliers = {
            MigrationStrategy.WRAPPER: 0.5,
            MigrationStrategy.INTERFACE: 0.8,
            MigrationStrategy.HYBRID: 1.2,
            MigrationStrategy.REWRITE: 2.0,
            MigrationStrategy.DECOMPOSE: 2.5
        }
        
        multiplier = strategy_multipliers.get(analysis.recommended_strategy, 1.0)
        
        return int(base_effort * multiplier)

class MigrationPlanner(LoggerMixin):
    """Creates detailed migration plans."""
    
    def __init__(self, ai_gateway: AIGateway):
        self.ai_gateway = ai_gateway
    
    async def create_migration_plan(self, analysis: ApplicationAnalysis) -> MigrationPlan:
        """Create a detailed migration plan."""
        
        plan = MigrationPlan(
            analysis_id=analysis.id,
            strategy=analysis.recommended_strategy
        )
        
        try:
            # Generate phases based on strategy
            plan.phases = await self._generate_migration_phases(analysis, plan.strategy)
            
            # Generate agent specifications
            plan.agent_specifications = await self._generate_agent_specs(analysis)
            
            # Create data migration plan
            plan.data_migration_plan = await self._create_data_migration_plan(analysis)
            
            # Create testing strategy
            plan.testing_strategy = await self._create_testing_strategy(analysis)
            
            # Create rollback plan
            plan.rollback_plan = await self._create_rollback_plan(analysis)
            
            # Assess risks
            plan.risk_assessment = await self._assess_migration_risks(analysis)
            
            # Define success criteria
            plan.success_criteria = await self._define_success_criteria(analysis)
            
            # Calculate timeline
            plan.estimated_timeline = self._calculate_timeline(analysis, plan.phases)
            
            # Define resource requirements
            plan.resource_requirements = await self._define_resource_requirements(analysis)
            
            self.log_operation("migration_plan_created",
                             analysis_id=analysis.id,
                             strategy=plan.strategy.value,
                             phases=len(plan.phases),
                             timeline=plan.estimated_timeline)
            
            return plan
            
        except Exception as e:
            self.log_error("migration_plan_creation_failed", e, analysis_id=analysis.id)
            raise AgentError(f"Migration plan creation failed: {e}")
    
    async def _generate_migration_phases(self, analysis: ApplicationAnalysis, strategy: MigrationStrategy) -> List[Dict[str, Any]]:
        """Generate migration phases based on strategy."""
        
        if strategy == MigrationStrategy.WRAPPER:
            return [
                {
                    "phase": 1,
                    "name": "Wrapper Development",
                    "description": "Create AI wrapper around existing application",
                    "tasks": ["Analyze interfaces", "Create wrapper service", "Implement AI integration"],
                    "duration_days": 5
                },
                {
                    "phase": 2,
                    "name": "Integration Testing",
                    "description": "Test wrapper integration",
                    "tasks": ["Unit testing", "Integration testing", "Performance testing"],
                    "duration_days": 3
                },
                {
                    "phase": 3,
                    "name": "Deployment",
                    "description": "Deploy wrapped application",
                    "tasks": ["Setup deployment", "Configure monitoring", "Go live"],
                    "duration_days": 2
                }
            ]
        
        elif strategy == MigrationStrategy.REWRITE:
            return [
                {
                    "phase": 1,
                    "name": "Architecture Design",
                    "description": "Design new AI-native architecture",
                    "tasks": ["Design system architecture", "Define AI agent interfaces", "Plan data flow"],
                    "duration_days": 7
                },
                {
                    "phase": 2,
                    "name": "Core Development", 
                    "description": "Implement core functionality",
                    "tasks": ["Implement business logic", "Create AI agents", "Setup data layer"],
                    "duration_days": 21
                },
                {
                    "phase": 3,
                    "name": "Integration",
                    "description": "Integrate components and external services",
                    "tasks": ["Component integration", "External API integration", "Data migration"],
                    "duration_days": 10
                },
                {
                    "phase": 4,
                    "name": "Testing & Deployment",
                    "description": "Comprehensive testing and deployment",
                    "tasks": ["Testing", "Performance optimization", "Production deployment"],
                    "duration_days": 7
                }
            ]
        
        elif strategy == MigrationStrategy.INTERFACE:
            return [
                {
                    "phase": 1,
                    "name": "Interface Analysis",
                    "description": "Analyze existing interfaces and design AI layer",
                    "tasks": ["API analysis", "Design AI interface", "Plan integration points"],
                    "duration_days": 4
                },
                {
                    "phase": 2,
                    "name": "AI Layer Development",
                    "description": "Develop AI interface layer",
                    "tasks": ["Implement AI agents", "Create interface adapters", "Setup routing"],
                    "duration_days": 10
                },
                {
                    "phase": 3,
                    "name": "Integration & Testing",
                    "description": "Integrate AI layer with existing system",
                    "tasks": ["System integration", "End-to-end testing", "Performance testing"],
                    "duration_days": 5
                },
                {
                    "phase": 4,
                    "name": "Deployment",
                    "description": "Deploy AI-enhanced system",
                    "tasks": ["Gradual rollout", "Monitor performance", "Full deployment"],
                    "duration_days": 3
                }
            ]
        
        # Default phases for other strategies
        return [
            {
                "phase": 1,
                "name": "Planning & Design",
                "description": "Detailed planning and system design",
                "tasks": ["System analysis", "Architecture design", "Implementation planning"],
                "duration_days": 7
            },
            {
                "phase": 2,
                "name": "Development",
                "description": "Core development work",
                "tasks": ["Implement changes", "Create AI components", "Integration work"],
                "duration_days": 14
            },
            {
                "phase": 3,
                "name": "Testing & Deployment",
                "description": "Testing and production deployment",
                "tasks": ["Testing", "Bug fixes", "Production deployment"],
                "duration_days": 7
            }
        ]
    
    async def _generate_agent_specs(self, analysis: ApplicationAnalysis) -> List[Dict[str, Any]]:
        """Generate specifications for AI agents to be created."""
        
        agent_specs = []
        
        # API Gateway Agent (if has endpoints)
        if analysis.endpoints:
            agent_specs.append({
                "name": "API Gateway Agent",
                "type": "api_gateway",
                "description": "Handles incoming API requests and routes to appropriate handlers",
                "capabilities": ["request_routing", "authentication", "rate_limiting"],
                "endpoints": analysis.endpoints[:10]  # Sample endpoints
            })
        
        # Data Processing Agent (if has data models)
        if analysis.data_models:
            agent_specs.append({
                "name": "Data Processing Agent", 
                "type": "data_processor",
                "description": "Handles data operations and business logic",
                "capabilities": ["data_validation", "business_rules", "data_transformation"],
                "models": analysis.data_models[:5]
            })
        
        # Integration Agent (if has external integrations)
        if analysis.external_integrations:
            agent_specs.append({
                "name": "Integration Agent",
                "type": "integration",
                "description": "Manages external service integrations",
                "capabilities": ["external_api_calls", "data_sync", "error_handling"],
                "integrations": analysis.external_integrations
            })
        
        # Monitoring Agent (always included)
        agent_specs.append({
            "name": "Monitoring Agent",
            "type": "monitoring",
            "description": "Monitors system health and performance",
            "capabilities": ["health_checks", "performance_monitoring", "alerting"],
            "metrics": ["response_time", "error_rate", "resource_usage"]
        })
        
        return agent_specs
    
    async def _create_data_migration_plan(self, analysis: ApplicationAnalysis) -> Dict[str, Any]:
        """Create data migration plan."""
        
        return {
            "migration_type": "online" if len(analysis.database_schemas) < 10 else "offline",
            "backup_strategy": {
                "method": "full_backup",
                "frequency": "before_migration",
                "retention": "30_days"
            },
            "migration_steps": [
                "Create backup of existing data",
                "Set up new data structures",
                "Migrate data in batches",
                "Validate data integrity", 
                "Switch to new system",
                "Clean up old data"
            ],
            "rollback_strategy": "restore_from_backup",
            "estimated_downtime": "2-4 hours" if len(analysis.database_schemas) > 5 else "minimal"
        }
    
    async def _create_testing_strategy(self, analysis: ApplicationAnalysis) -> Dict[str, Any]:
        """Create comprehensive testing strategy."""
        
        return {
            "testing_phases": [
                {
                    "phase": "unit_testing",
                    "description": "Test individual components",
                    "coverage_target": 80
                },
                {
                    "phase": "integration_testing", 
                    "description": "Test component interactions",
                    "focus_areas": ["API endpoints", "database operations", "external integrations"]
                },
                {
                    "phase": "performance_testing",
                    "description": "Validate performance requirements",
                    "metrics": ["response_time", "throughput", "resource_usage"]
                },
                {
                    "phase": "acceptance_testing",
                    "description": "Validate business requirements",
                    "criteria": ["functional_requirements", "user_workflows", "edge_cases"]
                }
            ],
            "test_environments": ["development", "staging", "production"],
            "automated_testing": True,
            "load_testing": len(analysis.endpoints) > 5
        }
    
    async def _create_rollback_plan(self, analysis: ApplicationAnalysis) -> Dict[str, Any]:
        """Create rollback plan."""
        
        return {
            "rollback_triggers": [
                "Critical functionality failure",
                "Performance degradation > 50%",
                "Data corruption detected",
                "Security issues identified"
            ],
            "rollback_steps": [
                "Stop new system",
                "Restore previous version",
                "Restore data backup if needed",
                "Verify system functionality",
                "Notify stakeholders"
            ],
            "rollback_time": "1-2 hours",
            "data_recovery": {
                "method": "backup_restoration",
                "rpo": "1 hour",  # Recovery Point Objective
                "rto": "2 hours"  # Recovery Time Objective
            }
        }
    
    async def _assess_migration_risks(self, analysis: ApplicationAnalysis) -> List[Dict[str, Any]]:
        """Assess migration risks."""
        
        risks = []
        
        if analysis.complexity_score > 7:
            risks.append({
                "risk": "High complexity may lead to scope creep",
                "probability": "medium",
                "impact": "high",
                "mitigation": "Detailed planning and incremental development"
            })
        
        if len(analysis.external_integrations) > 5:
            risks.append({
                "risk": "External integration dependencies",
                "probability": "high", 
                "impact": "medium",
                "mitigation": "Implement circuit breakers and fallback mechanisms"
            })
        
        if analysis.migration_feasibility < 5:
            risks.append({
                "risk": "Low feasibility may cause project delays",
                "probability": "medium",
                "impact": "high", 
                "mitigation": "Consider alternative migration strategies"
            })
        
        # Always include common risks
        risks.extend([
            {
                "risk": "Data migration issues",
                "probability": "medium",
                "impact": "high",
                "mitigation": "Thorough testing and backup strategies"
            },
            {
                "risk": "Performance regression",
                "probability": "medium",
                "impact": "medium",
                "mitigation": "Performance testing and optimization"
            }
        ])
        
        return risks
    
    async def _define_success_criteria(self, analysis: ApplicationAnalysis) -> List[str]:
        """Define success criteria for migration."""
        
        criteria = [
            "All critical functionality migrated successfully",
            "System performance meets or exceeds baseline",
            "Zero data loss during migration",
            "All integration tests pass",
            "User acceptance criteria met"
        ]
        
        if analysis.endpoints:
            criteria.append("All API endpoints functioning correctly")
        
        if analysis.database_schemas:
            criteria.append("Data integrity maintained across all schemas")
        
        if analysis.external_integrations:
            criteria.append("All external integrations working properly")
        
        return criteria
    
    def _calculate_timeline(self, analysis: ApplicationAnalysis, phases: List[Dict[str, Any]]) -> int:
        """Calculate total timeline in days."""
        
        total_days = sum(phase.get("duration_days", 7) for phase in phases)
        
        # Add buffer based on complexity
        buffer_percentage = min(analysis.complexity_score / 10.0, 0.5)  # Up to 50% buffer
        buffer_days = int(total_days * buffer_percentage)
        
        return total_days + buffer_days
    
    async def _define_resource_requirements(self, analysis: ApplicationAnalysis) -> Dict[str, Any]:
        """Define resource requirements."""
        
        base_team_size = max(2, min(int(analysis.complexity_score), 8))
        
        return {
            "team_size": base_team_size,
            "roles_needed": [
                "Senior Developer",
                "AI/ML Engineer",
                "DevOps Engineer",
                "QA Engineer"
            ],
            "infrastructure": {
                "development_environment": True,
                "staging_environment": True,
                "ci_cd_pipeline": True,
                "monitoring_tools": True
            },
            "estimated_cost": {
                "development": f"${analysis.estimated_effort * 100:,}",
                "infrastructure": "$500-2000/month",
                "total_project": f"${analysis.estimated_effort * 120:,}"
            }
        }

class ApplicationMigrator(LoggerMixin):
    """Executes application migration."""
    
    def __init__(self, ai_gateway: AIGateway, workflow_generator: AgentWorkflowGenerator):
        self.ai_gateway = ai_gateway
        self.workflow_generator = workflow_generator
        self.active_projects: Dict[str, MigrationProject] = {}
    
    async def start_migration(self, analysis: ApplicationAnalysis, plan: MigrationPlan) -> str:
        """Start a migration project."""
        
        project = MigrationProject(
            name=f"Migration of {Path(analysis.app_path).name}",
            description=f"Migrate {analysis.app_type.value} application using {plan.strategy.value} strategy",
            analysis=analysis,
            plan=plan,
            status=MigrationStatus.PLANNING,
            started_at=datetime.now()
        )
        
        self.active_projects[project.id] = project
        
        # Start migration execution
        asyncio.create_task(self._execute_migration(project))
        
        self.log_operation("migration_started",
                         project_id=project.id,
                         app_path=analysis.app_path,
                         strategy=plan.strategy.value)
        
        return project.id
    
    async def _execute_migration(self, project: MigrationProject) -> None:
        """Execute migration project."""
        
        try:
            project.status = MigrationStatus.MIGRATING
            total_phases = len(project.plan.phases)
            
            for i, phase in enumerate(project.plan.phases):
                project.current_phase = phase["name"]
                
                self._log_project_event(project, "phase_started", phase=phase["name"])
                
                # Execute phase tasks
                await self._execute_phase(project, phase)
                
                # Update progress
                project.progress = (i + 1) / total_phases
                
                self._log_project_event(project, "phase_completed", 
                                      phase=phase["name"], 
                                      progress=project.progress)
            
            # Generate agents based on specifications
            await self._generate_migration_agents(project)
            
            # Run final testing
            project.status = MigrationStatus.TESTING
            await self._run_migration_tests(project)
            
            # Complete migration
            project.status = MigrationStatus.COMPLETED
            project.completed_at = datetime.now()
            project.progress = 1.0
            
            self.log_operation("migration_completed",
                             project_id=project.id,
                             duration=(project.completed_at - project.started_at).total_seconds())
            
        except Exception as e:
            project.status = MigrationStatus.FAILED
            project.error = str(e)
            self.log_error("migration_failed", e, project_id=project.id)
    
    async def _execute_phase(self, project: MigrationProject, phase: Dict[str, Any]) -> None:
        """Execute a migration phase."""
        
        phase_name = phase["name"]
        tasks = phase.get("tasks", [])
        duration_days = phase.get("duration_days", 1)
        
        # Simulate phase execution
        for i, task in enumerate(tasks):
            self._log_project_event(project, "task_started", task=task)
            
            # Simulate task duration (scaled for demo)
            task_duration = (duration_days / len(tasks)) * 24 * 3600 / 1000  # Scale to seconds
            await asyncio.sleep(min(task_duration, 10))  # Cap at 10 seconds
            
            self._log_project_event(project, "task_completed", task=task)
        
        # Store phase artifacts
        project.migration_artifacts[phase_name] = {
            "completed_at": datetime.now(),
            "tasks_completed": len(tasks),
            "artifacts_generated": self._generate_phase_artifacts(project, phase)
        }
    
    def _generate_phase_artifacts(self, project: MigrationProject, phase: Dict[str, Any]) -> List[str]:
        """Generate artifacts for a phase."""
        
        phase_name = phase["name"].lower()
        
        if "design" in phase_name:
            return ["system_architecture.md", "api_specifications.yaml", "database_schema.sql"]
        elif "development" in phase_name:
            return ["agent_implementations", "integration_code", "configuration_files"]
        elif "testing" in phase_name:
            return ["test_results.json", "performance_report.html", "coverage_report.html"]
        elif "deployment" in phase_name:
            return ["deployment_manifest.yaml", "monitoring_config.json", "rollback_script.sh"]
        else:
            return ["phase_documentation.md"]
    
    async def _generate_migration_agents(self, project: MigrationProject) -> None:
        """Generate AI agents based on migration specifications."""
        
        for agent_spec in project.plan.agent_specifications:
            try:
                # Create agent generation request
                generation_request = {
                    "name": agent_spec["name"],
                    "description": agent_spec["description"],
                    "type": agent_spec["type"],
                    "capabilities": agent_spec["capabilities"],
                    "framework": "fastapi",  # Default framework
                    "deployment": {
                        "strategy": "docker",
                        "auto_deploy": True
                    }
                }
                
                # Use workflow generator to create agent
                # This would integrate with the existing AgentWorkflowGenerator
                self._log_project_event(project, "agent_generation_started", agent=agent_spec["name"])
                
                # Simulate agent generation
                await asyncio.sleep(2)
                
                agent_id = str(uuid.uuid4())
                project.generated_agents.append(agent_id)
                
                self._log_project_event(project, "agent_generated", 
                                      agent=agent_spec["name"],
                                      agent_id=agent_id)
                
            except Exception as e:
                self._log_project_event(project, "agent_generation_failed",
                                      agent=agent_spec["name"],
                                      error=str(e))
    
    async def _run_migration_tests(self, project: MigrationProject) -> None:
        """Run migration testing."""
        
        test_phases = project.plan.testing_strategy.get("testing_phases", [])
        
        for test_phase in test_phases:
            phase_name = test_phase["phase"]
            
            self._log_project_event(project, "test_phase_started", phase=phase_name)
            
            # Simulate testing
            await asyncio.sleep(1)
            
            # Simulate test results
            success_rate = 0.95  # 95% success rate
            test_results = {
                "phase": phase_name,
                "total_tests": 100,
                "passed": 95,
                "failed": 5,
                "success_rate": success_rate
            }
            
            project.migration_artifacts[f"test_{phase_name}"] = test_results
            
            self._log_project_event(project, "test_phase_completed", 
                                  phase=phase_name,
                                  success_rate=success_rate)
    
    def _log_project_event(self, project: MigrationProject, event_type: str, **kwargs) -> None:
        """Log a project event."""
        
        event = {
            "timestamp": datetime.now(),
            "event_type": event_type,
            "details": kwargs
        }
        
        project.logs.append(event)
        
        self.log_operation(f"migration_{event_type}",
                         project_id=project.id,
                         **kwargs)
    
    def get_migration_project(self, project_id: str) -> Optional[MigrationProject]:
        """Get migration project by ID."""
        return self.active_projects.get(project_id)
    
    def list_migration_projects(self) -> List[MigrationProject]:
        """List all migration projects."""
        return list(self.active_projects.values())