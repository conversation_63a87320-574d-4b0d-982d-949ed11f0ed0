#!/bin/bash
# Setup script for automatic database migration system
set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_error() {
    echo -e "${RED}❌ Error: $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_step() {
    echo -e "${BLUE}🚀 $1${NC}"
}

# Get the workspace root
if [ -n "$BUILD_WORKSPACE_DIRECTORY" ]; then
    cd "$BUILD_WORKSPACE_DIRECTORY"
elif [ -n "$TEST_WORKSPACE" ]; then
    cd "$TEST_WORKSPACE"
else
    # Find the workspace root by looking for WORKSPACE file
    CURRENT_DIR="$PWD"
    while [ "$CURRENT_DIR" != "/" ]; do
        if [ -f "$CURRENT_DIR/WORKSPACE" ]; then
            cd "$CURRENT_DIR"
            break
        fi
        CURRENT_DIR="$(dirname "$CURRENT_DIR")"
    done
fi

echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "🛠️  AUTO-MIGRATION SYSTEM SETUP"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""
print_info "Setting up automatic database migration system..."
echo ""

# Step 1: Set up auto-migration directories and files
print_step "Step 1: Setting up auto-migration system"
echo ""

if bazel run //platform/db:auto_migrate -- setup; then
    print_success "Auto-migration system initialized"
else
    print_error "Failed to initialize auto-migration system"
    exit 1
fi

# Step 2: Install pre-commit hook
print_step "Step 2: Installing pre-commit hook"
echo ""

if bazel run //platform/db:pre_commit_migration -- install; then
    print_success "Pre-commit hook installed"
else
    print_warning "Pre-commit hook installation failed (may not be in a git repo)"
fi

# Step 3: Test the system
print_step "Step 3: Testing the auto-migration system"
echo ""

if bazel run //platform/db:pre_commit_migration -- test; then
    print_success "Auto-migration system test passed"
else
    print_error "Auto-migration system test failed"
    exit 1
fi

# Step 4: Instructions
print_step "Step 4: Setup complete - Usage instructions"
echo ""

echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
print_success "AUTO-MIGRATION SYSTEM SETUP COMPLETE!"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""
echo "🎉 Your automatic migration system is now ready!"
echo ""
echo "📁 Schema Directory:"
echo "  • Location: platform/db/schema/"
echo "  • Place your SQL schema files here"
echo "  • Files will be automatically converted to migrations"
echo ""
echo "🔧 Available Commands:"
echo "  • bazel run //platform/db:auto_migrate -- run          # Run once"
echo "  • bazel run //platform/db:migration_watcher -- start   # Watch for changes"
echo "  • bazel run //terraform:terraform_with_auto_migration  # Terraform with auto-migration"
echo ""
echo "🎯 Workflow:"
echo "  1. Create/modify SQL files in platform/db/schema/"
echo "  2. Commit your changes (pre-commit hook will auto-migrate)"
echo "  3. OR run bazel run //platform/db:auto_migrate manually"
echo "  4. OR start the watcher: bazel run //platform/db:migration_watcher"
echo ""
echo "🚀 Terraform Integration:"
echo "  • bazel run //terraform:terraform_with_auto_migration"
echo "  • Automatically detects schema changes and applies migrations"
echo "  • Runs terraform apply with migration support"
echo ""
echo "🛡️  Safety Features:"
echo "  • SQL validation before migration generation"
echo "  • Automatic versioning"
echo "  • Local testing before GCP deployment"
echo "  • Rollback protection"
echo ""
echo "📖 Documentation:"
echo "  • Schema examples: platform/db/schema/README.md"
echo "  • Migration logs: platform/db/migration_log.txt"
echo ""
print_success "Ready to use! 🎉"