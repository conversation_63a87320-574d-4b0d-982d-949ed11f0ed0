/* tslint:disable */
/* eslint-disable */
/**
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  Contact,
  ContactCompanyUpdateRequest,
  ContactCreate,
  ContactDetails,
  ContactUpdate,
  ContactWithCompany,
  ErrorResponse,
  ValidationError,
} from '../models/index';
import {
    ContactFromJSON,
    ContactToJSON,
    ContactCompanyUpdateRequestFromJSON,
    ContactCompanyUpdateRequestToJSON,
    ContactCreateFromJSON,
    ContactCreateToJSON,
    ContactDetailsFromJSON,
    ContactDetailsToJSON,
    ContactUpdateFromJSON,
    ContactUpdateToJSON,
    ContactWithCompanyFromJSON,
    ContactWithCompanyToJSON,
    ErrorResponseFromJSON,
    ErrorResponseToJSON,
    ValidationErrorFromJSON,
    ValidationErrorToJSON,
} from '../models/index';

export interface ContactsGetRequest {
    companyId?: string;
    unlinked?: boolean;
    includeDeleted?: boolean;
}

export interface ContactsIdCompanyDeleteRequest {
    id: string;
}

export interface ContactsIdCompanyPutRequest {
    id: string;
    contactCompanyUpdateRequest: ContactCompanyUpdateRequest;
}

export interface ContactsIdDeleteRequest {
    id: string;
}

export interface ContactsIdGetRequest {
    id: string;
}

export interface ContactsIdPutRequest {
    id: string;
    contactUpdate: ContactUpdate;
}

export interface ContactsPostRequest {
    contactCreate: ContactCreate;
}

/**
 * 
 */
export class ContactsApi extends runtime.BaseAPI {

    /**
     * Retrieve a list of all active contacts with their associated companies
     * List contacts
     */
    async contactsGetRaw(requestParameters: ContactsGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Array<ContactWithCompany>>> {
        const queryParameters: any = {};

        if (requestParameters['companyId'] != null) {
            queryParameters['company_id'] = requestParameters['companyId'];
        }

        if (requestParameters['unlinked'] != null) {
            queryParameters['unlinked'] = requestParameters['unlinked'];
        }

        if (requestParameters['includeDeleted'] != null) {
            queryParameters['include_deleted'] = requestParameters['includeDeleted'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("bearerAuth", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/contacts`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => jsonValue.map(ContactWithCompanyFromJSON));
    }

    /**
     * Retrieve a list of all active contacts with their associated companies
     * List contacts
     */
    async contactsGet(requestParameters: ContactsGetRequest = {}, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Array<ContactWithCompany>> {
        const response = await this.contactsGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Remove the association between a contact and company
     * Unlink contact from company
     */
    async contactsIdCompanyDeleteRaw(requestParameters: ContactsIdCompanyDeleteRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<void>> {
        if (requestParameters['id'] == null) {
            throw new runtime.RequiredError(
                'id',
                'Required parameter "id" was null or undefined when calling contactsIdCompanyDelete().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("bearerAuth", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/contacts/{id}/company`.replace(`{${"id"}}`, encodeURIComponent(String(requestParameters['id']))),
            method: 'DELETE',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.VoidApiResponse(response);
    }

    /**
     * Remove the association between a contact and company
     * Unlink contact from company
     */
    async contactsIdCompanyDelete(requestParameters: ContactsIdCompanyDeleteRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void> {
        await this.contactsIdCompanyDeleteRaw(requestParameters, initOverrides);
    }

    /**
     * Associate a contact with a company
     * Link contact to company
     */
    async contactsIdCompanyPutRaw(requestParameters: ContactsIdCompanyPutRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<void>> {
        if (requestParameters['id'] == null) {
            throw new runtime.RequiredError(
                'id',
                'Required parameter "id" was null or undefined when calling contactsIdCompanyPut().'
            );
        }

        if (requestParameters['contactCompanyUpdateRequest'] == null) {
            throw new runtime.RequiredError(
                'contactCompanyUpdateRequest',
                'Required parameter "contactCompanyUpdateRequest" was null or undefined when calling contactsIdCompanyPut().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("bearerAuth", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/contacts/{id}/company`.replace(`{${"id"}}`, encodeURIComponent(String(requestParameters['id']))),
            method: 'PUT',
            headers: headerParameters,
            query: queryParameters,
            body: ContactCompanyUpdateRequestToJSON(requestParameters['contactCompanyUpdateRequest']),
        }, initOverrides);

        return new runtime.VoidApiResponse(response);
    }

    /**
     * Associate a contact with a company
     * Link contact to company
     */
    async contactsIdCompanyPut(requestParameters: ContactsIdCompanyPutRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void> {
        await this.contactsIdCompanyPutRaw(requestParameters, initOverrides);
    }

    /**
     * Soft delete a contact (sets is_deleted to true)
     * Delete contact
     */
    async contactsIdDeleteRaw(requestParameters: ContactsIdDeleteRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<void>> {
        if (requestParameters['id'] == null) {
            throw new runtime.RequiredError(
                'id',
                'Required parameter "id" was null or undefined when calling contactsIdDelete().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("bearerAuth", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/contacts/{id}`.replace(`{${"id"}}`, encodeURIComponent(String(requestParameters['id']))),
            method: 'DELETE',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.VoidApiResponse(response);
    }

    /**
     * Soft delete a contact (sets is_deleted to true)
     * Delete contact
     */
    async contactsIdDelete(requestParameters: ContactsIdDeleteRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void> {
        await this.contactsIdDeleteRaw(requestParameters, initOverrides);
    }

    /**
     * Retrieve a specific contact by ID
     * Get contact
     */
    async contactsIdGetRaw(requestParameters: ContactsIdGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<ContactDetails>> {
        if (requestParameters['id'] == null) {
            throw new runtime.RequiredError(
                'id',
                'Required parameter "id" was null or undefined when calling contactsIdGet().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("bearerAuth", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/contacts/{id}`.replace(`{${"id"}}`, encodeURIComponent(String(requestParameters['id']))),
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => ContactDetailsFromJSON(jsonValue));
    }

    /**
     * Retrieve a specific contact by ID
     * Get contact
     */
    async contactsIdGet(requestParameters: ContactsIdGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<ContactDetails> {
        const response = await this.contactsIdGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Update an existing contact
     * Update contact
     */
    async contactsIdPutRaw(requestParameters: ContactsIdPutRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Contact>> {
        if (requestParameters['id'] == null) {
            throw new runtime.RequiredError(
                'id',
                'Required parameter "id" was null or undefined when calling contactsIdPut().'
            );
        }

        if (requestParameters['contactUpdate'] == null) {
            throw new runtime.RequiredError(
                'contactUpdate',
                'Required parameter "contactUpdate" was null or undefined when calling contactsIdPut().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("bearerAuth", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/contacts/{id}`.replace(`{${"id"}}`, encodeURIComponent(String(requestParameters['id']))),
            method: 'PUT',
            headers: headerParameters,
            query: queryParameters,
            body: ContactUpdateToJSON(requestParameters['contactUpdate']),
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => ContactFromJSON(jsonValue));
    }

    /**
     * Update an existing contact
     * Update contact
     */
    async contactsIdPut(requestParameters: ContactsIdPutRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Contact> {
        const response = await this.contactsIdPutRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Create a new contact
     * Create contact
     */
    async contactsPostRaw(requestParameters: ContactsPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Contact>> {
        if (requestParameters['contactCreate'] == null) {
            throw new runtime.RequiredError(
                'contactCreate',
                'Required parameter "contactCreate" was null or undefined when calling contactsPost().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("bearerAuth", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/contacts`,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: ContactCreateToJSON(requestParameters['contactCreate']),
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => ContactFromJSON(jsonValue));
    }

    /**
     * Create a new contact
     * Create contact
     */
    async contactsPost(requestParameters: ContactsPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Contact> {
        const response = await this.contactsPostRaw(requestParameters, initOverrides);
        return await response.value();
    }

}
