---
- name: Retrieve GCP access token from metadata
  uri:
    url: "{{ gcp_metadata_url }}/instance/service-accounts/default/token"
    headers: "{{ gcp_metadata_headers }}"
  register: gcp_token
  tags: ['docker', 'gcp-auth']

- name: Configure Docker for GCP Artifact Registry
  shell: |
    echo "{{ gcp_token.json.access_token }}" | docker login -u oauth2accesstoken --password-stdin https://{{ gcp_region }}-docker.pkg.dev
  no_log: true
  tags: ['docker', 'gcp-auth']

- name: Create Docker config directory for root
  file:
    path: /root/.docker
    state: directory
    mode: '0700'
  tags: ['docker', 'gcp-auth']

- name: Set up Docker credential helper for GCP
  copy:
    content: |
      {
        "credHelpers": {
          "{{ gcp_region }}-docker.pkg.dev": "gcloud"
        }
      }
    dest: /root/.docker/config.json
    mode: '0600'
  tags: ['docker', 'gcp-auth']

- name: Set up automatic token refresh
  cron:
    name: "Refresh GCP Docker token"
    minute: "*/45"
    job: "curl -s -H 'Metadata-Flavor: Google' '{{ gcp_metadata_url }}/instance/service-accounts/default/token' | jq -r .access_token | docker login -u oauth2accesstoken --password-stdin https://{{ gcp_region }}-docker.pkg.dev > /dev/null 2>&1"
  tags: ['docker', 'gcp-auth']