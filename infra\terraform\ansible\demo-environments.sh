#!/bin/bash
# Demo script showing environment auto-detection across different directories

echo "🎭 Environment Auto-Detection Demo"
echo "=================================="
echo ""

# Save original directory
ORIGINAL_DIR="$(pwd)"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Function to test environment detection
test_environment() {
    local test_dir="$1"
    local expected_env="$2"
    
    echo "📂 Testing directory: $test_dir"
    echo "🎯 Expected environment: $expected_env"
    
    if [[ -d "$test_dir" ]]; then
        cd "$test_dir"
        
        # Source the setup script in a subshell to avoid polluting current environment
        (
            source "$PROJECT_ROOT/terraform/ansible/setup-env.sh" 2>/dev/null | grep -E "(Detected environment|Found GCP Project|Found GCP Region|Active Environment)" | head -4
        )
        
        cd "$ORIGINAL_DIR"
    else
        echo "❌ Directory not found: $test_dir"
    fi
    
    echo ""
}

echo "🧪 Testing environment detection from different directories..."
echo ""

# Test DEV environment
test_environment "$PROJECT_ROOT/terraform/environments/dev" "dev"

# Test STAGING environment (if it exists)
test_environment "$PROJECT_ROOT/terraform/environments/staging" "staging"

# Test PROD environment (if it exists)
test_environment "$PROJECT_ROOT/terraform/environments/prod" "prod"

# Test from Terraform root
echo "📂 Testing from Terraform root directory"
cd "$PROJECT_ROOT/terraform"
(
    source "$PROJECT_ROOT/terraform/ansible/setup-env.sh" 2>/dev/null | grep -E "(Detected environment|Active Environment)" | head -2
)
cd "$ORIGINAL_DIR"
echo ""

# Test from project root
echo "📂 Testing from project root directory"
cd "$PROJECT_ROOT"
(
    source "$PROJECT_ROOT/terraform/ansible/setup-env.sh" 2>/dev/null | grep -E "(Detected environment|Active Environment)" | head -2
)
cd "$ORIGINAL_DIR"
echo ""

echo "✨ Demo complete!"
echo ""
echo "📚 Usage Instructions:"
echo "====================="
echo ""
echo "1. For DEV environment:"
echo "   cd terraform/environments/dev"
echo "   source setup.sh"
echo ""
echo "2. For STAGING environment:"
echo "   cd terraform/environments/staging"
echo "   source setup.sh"
echo ""
echo "3. For PROD environment:"
echo "   cd terraform/environments/prod"
echo "   source setup.sh"
echo ""
echo "4. Auto-detection from anywhere:"
echo "   source terraform/ansible/setup-env.sh"
echo ""
echo "🎯 Key Features:"
echo "- Auto-detects environment from directory path"
echo "- Reads configuration from terraform.tfvars"
echo "- Sets appropriate GCP project, region, and zone"
echo "- Provides convenient command aliases"
echo "- Validates GCP authentication and tools"
echo ""