package handlers

import (
	"crypto/rand"
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/TwoDotAi/mono/shared/go/logging"
	"github.com/TwoDotAi/mono/services/orbit/auth/config"
	"github.com/TwoDotAi/mono/services/orbit/auth/database"
	"github.com/TwoDotAi/mono/services/orbit/auth/middleware"
	openapi "github.com/TwoDotAi/mono/generated/orbit/go/server"
	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/oapi-codegen/runtime/types"
	"go.uber.org/zap"
	"golang.org/x/crypto/bcrypt"
)

// Use generated OpenAPI types
type SignInRequest = openapi.PostAuthSigninOauthJSONRequestBody
type OAuthSignInRequest = openapi.PostAuthSigninOauthJSONRequestBody
type AuthResponse = openapi.AuthResponse
type User = openapi.User

type ValidateTokenRequest struct {
	Token string `json:"token" binding:"required"`
}

type ValidateTokenResponse struct {
	Valid  bool   `json:"valid"`
	UserID string `json:"user_id,omitempty"`
	Email  string `json:"email,omitempty"`
	Error  string `json:"error,omitempty"`
}

// Google OAuth structures (using external API types, not OpenAPI generated)
type GoogleTokenResponse struct {
	AccessToken  string `json:"access_token"`
	TokenType    string `json:"token_type"`
	ExpiresIn    int    `json:"expires_in"`
	RefreshToken string `json:"refresh_token,omitempty"`
	Scope        string `json:"scope"`
	IDToken      string `json:"id_token"`
}

type GoogleUserInfo struct {
	ID            string `json:"id"`
	Email         string `json:"email"`
	VerifiedEmail bool   `json:"verified_email"`
	Name          string `json:"name"`
	GivenName     string `json:"given_name"`
	FamilyName    string `json:"family_name"`
	Picture       string `json:"picture"`
	Locale        string `json:"locale"`
}

// OAuth state structure for CSRF protection
type OAuthState struct {
	Provider   string    `json:"provider"`
	RedirectTo string    `json:"redirect_to"`
	Nonce      string    `json:"nonce"`
	CreatedAt  time.Time `json:"created_at"`
}

func GetSession(c *gin.Context) {
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Not authenticated"})
		return
	}

	// For now, return empty session - this would be enhanced with actual session logic
	c.JSON(http.StatusUnauthorized, gin.H{"error": "Not authenticated"})
}

func SignIn(c *gin.Context) {
	var req openapi.PostAuthSigninJSONBody
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	db := database.GetDB()

	// Check if user exists and password is correct
	var user openapi.User
	var hashedPassword string
	var emailConfirmedAt *time.Time
	err := db.QueryRow(
		"SELECT id, email, password_hash, email_confirmed_at, created_at, updated_at FROM users WHERE email = $1 AND is_deleted = false",
		req.Email,
	).Scan(&user.Id, &user.Email, &hashedPassword, &emailConfirmedAt, &user.CreatedAt, &user.UpdatedAt)

	// Convert nullable time to time.Time
	if emailConfirmedAt != nil {
		user.EmailConfirmedAt = emailConfirmedAt
	}

	if err == sql.ErrNoRows {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid credentials"})
		return
	}
	if err != nil {
		logging.LogError(err, "Failed to query user during signin",
			zap.String("service", "AUTH_SERVICE"),
			zap.String("operation", "signin"),
			zap.String("email", string(req.Email)),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
		return
	}

	// Check password
	if err := bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(req.Password)); err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid credentials"})
		return
	}

	// Generate JWT token
	token, err := generateJWTToken(user.Id.String(), string(*user.Email))
	if err != nil {
		logging.LogError(err, "Failed to generate JWT token",
			zap.String("service", "AUTH_SERVICE"),
			zap.String("operation", "signin"),
			zap.String("user_id", user.Id.String()),
			zap.String("email", string(*user.Email)),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token"})
		return
	}

	// TODO refresh token
	refresh := ""
	ExpiresIn := 86400 // 24 hours
	TokenType := "Bearer"

	response := openapi.AuthResponse{
		AccessToken:  &token,
		RefreshToken: &refresh,   // TODO: Implement refresh tokens
		ExpiresIn:    &ExpiresIn, // 24 hours
		TokenType:    &TokenType,
		User:         &user,
	}

	c.JSON(http.StatusOK, response)
}

func SignInOAuth(c *gin.Context) {
	var req openapi.PostAuthSigninOauthJSONBody
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if req.Provider != "google" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Unsupported OAuth provider"})
		return
	}

	cfg := config.Load()

	// Validate OAuth configuration
	if cfg.GoogleOAuthClientID == "" || cfg.GoogleOAuthClientSecret == "" {
		logging.LogError(fmt.Errorf("oauth not configured"), "OAuth configuration missing",
			zap.String("service", "AUTH_SERVICE"),
			zap.String("operation", "oauth_signin"),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "OAuth not configured"})
		return
	}

	// Generate state for CSRF protection
	state, err := generateOAuthState(string(req.Provider), req.RedirectTo)
	if err != nil {
		logging.LogError(err, "Failed to generate OAuth state",
			zap.String("service", "AUTH_SERVICE"),
			zap.String("operation", "oauth_signin"),
			zap.String("provider", string(req.Provider)),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate OAuth state"})
		return
	}

	// Build Google OAuth authorization URL
	authURL := buildGoogleOAuthURL(cfg, state)

	// Use gin.H since OAuthRedirectResponse wasn't generated properly
	c.JSON(http.StatusOK, gin.H{
		"url": authURL,
	})
}

func SignOut(c *gin.Context) {
	// In a real implementation, you would invalidate the JWT token
	// For now, just return success
	c.JSON(http.StatusOK, gin.H{"message": "Successfully signed out"})
}

func GetCurrentUser(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	db := database.GetDB()
	var user openapi.User
	var emailConfirmedAt *time.Time
	err := db.QueryRow(
		"SELECT id, email, email_confirmed_at, created_at, updated_at FROM users WHERE id = $1 AND is_deleted = false",
		userID,
	).Scan(&user.Id, &user.Email, &emailConfirmedAt, &user.CreatedAt, &user.UpdatedAt)

	// Convert nullable time to time.Time
	if emailConfirmedAt != nil {
		user.EmailConfirmedAt = emailConfirmedAt
	}

	if err == sql.ErrNoRows {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}
	if err != nil {
		logging.LogError(err, "Failed to query user in GetCurrentUser",
			zap.String("service", "AUTH_SERVICE"),
			zap.String("operation", "get_current_user"),
			zap.Any("user_id", userID),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
		return
	}

	c.JSON(http.StatusOK, user)
}

// ValidateToken validates a JWT token and returns user information
func ValidateToken(c *gin.Context) {
	var req ValidateTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	claims := &middleware.Claims{}
	cfg := config.Load()

	token, err := jwt.ParseWithClaims(req.Token, claims, func(token *jwt.Token) (interface{}, error) {
		return []byte(cfg.JWTSecret), nil
	})

	if err != nil || !token.Valid {
		logging.LogError(err, "Token validation failed",
			zap.String("service", "AUTH_SERVICE"),
			zap.String("operation", "validate_token"),
			zap.Bool("token_valid", token != nil && token.Valid),
		)
		c.JSON(http.StatusOK, ValidateTokenResponse{
			Valid: false,
			Error: "Invalid or expired token",
		})
		return
	}

	c.JSON(http.StatusOK, ValidateTokenResponse{
		Valid:  true,
		UserID: claims.UserID,
		Email:  claims.Email,
	})
}

func generateJWTToken(userID, email string) (string, error) {
	cfg := config.Load()

	claims := middleware.Claims{
		UserID: userID,
		Email:  email,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(24 * time.Hour)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(cfg.JWTSecret))
}

// OAuth utility functions

// generateOAuthState creates a signed state parameter for CSRF protection
func generateOAuthState(provider string, redirectTo *string) (string, error) {
	cfg := config.Load()

	// Generate random nonce
	nonce := make([]byte, 32)
	if _, err := rand.Read(nonce); err != nil {
		return "", fmt.Errorf("failed to generate nonce: %w", err)
	}

	state := OAuthState{
		Provider:   provider,
		RedirectTo: "",
		Nonce:      base64.URLEncoding.EncodeToString(nonce),
		CreatedAt:  time.Now(),
	}

	if redirectTo != nil {
		state.RedirectTo = *redirectTo
	}

	// Serialize state
	stateJSON, err := json.Marshal(state)
	if err != nil {
		return "", fmt.Errorf("failed to marshal state: %w", err)
	}

	// Sign the state with HMAC
	claims := jwt.MapClaims{
		"state": string(stateJSON),
		"exp":   time.Now().Add(15 * time.Minute).Unix(), // 15 minute expiry
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	signedState, err := token.SignedString([]byte(cfg.OAuthStateSecret))
	if err != nil {
		return "", fmt.Errorf("failed to sign state: %w", err)
	}

	return signedState, nil
}

// buildGoogleOAuthURL constructs the Google OAuth authorization URL
func buildGoogleOAuthURL(cfg *config.Config, state string) string {
	params := url.Values{}
	params.Set("client_id", cfg.GoogleOAuthClientID)
	params.Set("redirect_uri", cfg.GoogleOAuthRedirectURL)
	params.Set("response_type", "code")
	params.Set("scope", "openid email profile")
	params.Set("state", state)
	params.Set("access_type", "online") // We don't need refresh tokens for this use case

	return "https://accounts.google.com/o/oauth2/v2/auth?" + params.Encode()
}

// OAuth callback handler (manually implemented since not generated)
func OAuthCallback(c *gin.Context) {
	provider := c.Param("provider")
	if provider != "google" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Unsupported OAuth provider"})
		return
	}

	// Check for OAuth error
	if oauthError := c.Query("error"); oauthError != "" {
		logging.LogError(fmt.Errorf("oauth error: %s", oauthError), "OAuth authorization failed",
			zap.String("service", "AUTH_SERVICE"),
			zap.String("operation", "oauth_callback"),
			zap.String("provider", provider),
			zap.String("oauth_error", oauthError),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "OAuth authorization failed"})
		return
	}

	// Get authorization code and state
	code := c.Query("code")
	state := c.Query("state")

	if code == "" || state == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Missing code or state parameter"})
		return
	}

	// Verify and parse state
	// UNSUSED RN
	_, err := verifyOAuthState(state)
	if err != nil {
		logging.LogError(err, "Invalid OAuth state",
			zap.String("service", "AUTH_SERVICE"),
			zap.String("operation", "oauth_callback"),
			zap.String("provider", provider),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid state parameter"})
		return
	}

	// Exchange code for tokens
	tokens, err := exchangeGoogleOAuthCode(code)
	if err != nil {
		logging.LogError(err, "Failed to exchange OAuth code",
			zap.String("service", "AUTH_SERVICE"),
			zap.String("operation", "oauth_callback"),
			zap.String("provider", provider),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to exchange authorization code"})
		return
	}

	// Get user info from Google
	userInfo, err := getGoogleUserInfo(tokens.AccessToken)
	if err != nil {
		logging.LogError(err, "Failed to get user info from Google",
			zap.String("service", "AUTH_SERVICE"),
			zap.String("operation", "oauth_callback"),
			zap.String("provider", provider),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user information"})
		return
	}

	// Find or create user
	user, err := findOrCreateOAuthUser(userInfo)
	if err != nil {
		logging.LogError(err, "Failed to find or create OAuth user",
			zap.String("service", "AUTH_SERVICE"),
			zap.String("operation", "oauth_callback"),
			zap.String("provider", provider),
			zap.String("email", userInfo.Email),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create user session"})
		return
	}

	// Generate JWT token for our application
	token, err := generateJWTToken(user.Id.String(), string(*user.Email))
	if err != nil {
		logging.LogError(err, "Failed to generate JWT token for OAuth user",
			zap.String("service", "AUTH_SERVICE"),
			zap.String("operation", "oauth_callback"),
			zap.String("provider", provider),
			zap.String("user_id", user.Id.String()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate authentication token"})
		return
	}

	// Create auth response using generated type
	refresh := ""
	expiresIn := 86400 // 24 hours
	tokenType := "Bearer"

	response := openapi.AuthResponse{
		AccessToken:  &token,
		RefreshToken: &refresh,
		ExpiresIn:    &expiresIn,
		TokenType:    &tokenType,
		User:         user,
	}

	c.JSON(http.StatusOK, response)
}

// verifyOAuthState verifies and parses the OAuth state parameter
func verifyOAuthState(stateParam string) (*OAuthState, error) {
	cfg := config.Load()

	// Parse and verify the JWT state
	token, err := jwt.Parse(stateParam, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(cfg.OAuthStateSecret), nil
	})

	if err != nil || !token.Valid {
		return nil, fmt.Errorf("invalid state token: %w", err)
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, fmt.Errorf("invalid state claims")
	}

	stateJSON, ok := claims["state"].(string)
	if !ok {
		return nil, fmt.Errorf("missing state data")
	}

	var state OAuthState
	if err := json.Unmarshal([]byte(stateJSON), &state); err != nil {
		return nil, fmt.Errorf("failed to unmarshal state: %w", err)
	}

	// Check if state is expired (should not happen if JWT verification succeeds, but extra safety)
	if time.Since(state.CreatedAt) > 15*time.Minute {
		return nil, fmt.Errorf("state parameter expired")
	}

	return &state, nil
}

// exchangeGoogleOAuthCode exchanges authorization code for access token
func exchangeGoogleOAuthCode(code string) (*GoogleTokenResponse, error) {
	cfg := config.Load()

	data := url.Values{}
	data.Set("client_id", cfg.GoogleOAuthClientID)
	data.Set("client_secret", cfg.GoogleOAuthClientSecret)
	data.Set("code", code)
	data.Set("grant_type", "authorization_code")
	data.Set("redirect_uri", cfg.GoogleOAuthRedirectURL)

	resp, err := http.Post(
		"https://oauth2.googleapis.com/token",
		"application/x-www-form-urlencoded",
		strings.NewReader(data.Encode()),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to exchange code: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("token exchange failed: %s (status: %d)", string(body), resp.StatusCode)
	}

	var tokens GoogleTokenResponse
	if err := json.NewDecoder(resp.Body).Decode(&tokens); err != nil {
		return nil, fmt.Errorf("failed to decode token response: %w", err)
	}

	return &tokens, nil
}

// getGoogleUserInfo retrieves user information from Google using access token
func getGoogleUserInfo(accessToken string) (*GoogleUserInfo, error) {
	req, err := http.NewRequest("GET", "https://www.googleapis.com/oauth2/v2/userinfo", nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+accessToken)

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get user info: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("failed to get user info: %s (status: %d)", string(body), resp.StatusCode)
	}

	var userInfo GoogleUserInfo
	if err := json.NewDecoder(resp.Body).Decode(&userInfo); err != nil {
		return nil, fmt.Errorf("failed to decode user info: %w", err)
	}

	return &userInfo, nil
}

// findOrCreateOAuthUser finds existing user or creates new one from OAuth data
func findOrCreateOAuthUser(userInfo *GoogleUserInfo) (*openapi.User, error) {
	db := database.GetDB()

	// Try to find existing user by email
	var user openapi.User
	var emailConfirmedAt *time.Time
	err := db.QueryRow(
		"SELECT id, email, email_confirmed_at, created_at, updated_at FROM users WHERE email = $1 AND is_deleted = false",
		userInfo.Email,
	).Scan(&user.Id, &user.Email, &emailConfirmedAt, &user.CreatedAt, &user.UpdatedAt)

	if err == nil {
		// User exists, update email confirmation if needed
		if emailConfirmedAt != nil {
			user.EmailConfirmedAt = emailConfirmedAt
		} else {
			// OAuth email is verified, so mark as confirmed
			now := time.Now()
			_, updateErr := db.Exec(
				"UPDATE users SET email_confirmed_at = $1, updated_at = $1 WHERE id = $2",
				now, user.Id,
			)
			if updateErr == nil {
				user.EmailConfirmedAt = &now
				user.UpdatedAt = &now
			}
		}
		return &user, nil
	}

	if err != sql.ErrNoRows {
		return nil, fmt.Errorf("database error: %w", err)
	}

	// User doesn't exist, create new one
	newID := uuid.New()
	now := time.Now()
	email := userInfo.Email

	_, err = db.Exec(`
		INSERT INTO users (id, email, email_confirmed_at, created_at, updated_at, is_deleted)
		VALUES ($1, $2, $3, $4, $5, false)
	`, newID, email, now, now, now)

	if err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	// Return the new user
	user = openapi.User{
		Id:               &newID,
		Email:            (*types.Email)(&email),
		EmailConfirmedAt: &now,
		CreatedAt:        &now,
		UpdatedAt:        &now,
	}

	return &user, nil
}
