# Automatic Database Migration System

This system automatically detects SQL schema changes and applies database migrations to keep your database in sync with your code changes.

## 🚀 Quick Start

### 1. Setup the System
```bash
# Initialize the auto-migration system
bazel run //:setup_auto_migration
```

### 2. Create Schema Files
```bash
# Create your SQL schema files in the schema directory
echo "CREATE TABLE users (id UUID PRIMARY KEY, name VARCHAR(255));" > services/orbit/db/schema/users.sql
```

### 3. Auto-Apply Migrations
```bash
# Option 1: Manual run
bazel run //services/orbit/db:auto_migrate

# Option 2: Watch for changes
bazel run //services/orbit/db:migration_watcher -- start

# Option 3: Terraform with auto-migration
bazel run //:terraform_auto_deploy
```

## 📁 Directory Structure

```
services/orbit/db/
├── schema/                 # Your SQL schema files (create these)
│   ├── users.sql
│   ├── orders.sql
│   └── README.md
├── migrations/             # Auto-generated migration files
│   ├── V001__initial_schema.sql
│   ├── V002__crm_schema.sql
│   └── V003__users.sql     # Auto-generated from schema/users.sql
├── auto_migrations/        # Tracking files (don't edit)
└── migration_log.txt       # Log of all migrations
```

## 🔧 Available Commands

### Core Auto-Migration Commands
```bash
# Run auto-migration once
bazel run //services/orbit/db:auto_migrate -- run [local|dev]

# Set up the system
bazel run //services/orbit/db:auto_migrate -- setup

# Watch for schema changes
bazel run //services/orbit/db:migration_watcher -- start [local|dev]

# Stop the watcher
bazel run //services/orbit/db:migration_watcher -- stop
```

### Pre-commit Hook
```bash
# Install pre-commit hook
bazel run //services/orbit/db:pre_commit_migration -- install

# Test the system
bazel run //services/orbit/db:pre_commit_migration -- test

# Check hook status
bazel run //services/orbit/db:pre_commit_migration -- status
```

### Terraform Integration
```bash
# Terraform with auto-migration
bazel run //:terraform_auto_deploy

# Or directly
bazel run //terraform:terraform_with_auto_migration
```

## 🔄 Workflow Options

### 1. **Pre-commit Hook (Recommended)**
- Automatically runs on every commit
- Detects schema changes and applies migrations
- Adds generated migrations to your commit

```bash
# Install once
bazel run //services/orbit/db:pre_commit_migration -- install

# Now every commit will auto-migrate
git add services/orbit/db/schema/new_table.sql
git commit -m "Add new table"  # Auto-migration runs here
```

### 2. **File Watcher**
- Continuously monitors schema directory
- Applies migrations immediately when files change
- Great for active development

```bash
# Start watching
bazel run //services/orbit/db:migration_watcher -- start local

# In another terminal, create/modify schema files
echo "CREATE TABLE products (id UUID PRIMARY KEY);" > services/orbit/db/schema/products.sql
# Migration is applied automatically
```

### 3. **Manual Trigger**
- Run migrations on demand
- Good for testing or batch operations

```bash
# Run once
bazel run //services/orbit/db:auto_migrate -- run local

# For GCP dev environment
AUTO_MIGRATE_DEV=true bazel run //services/orbit/db:auto_migrate -- run dev
```

### 4. **Terraform Integration**
- Automatically detects schema changes during deployment
- Applies migrations as part of terraform apply
- Ensures database is always in sync with deployed code

```bash
# Deploy with auto-migration
bazel run //:terraform_auto_deploy

# Or with specific options
DEPLOY_WEB=true bazel run //:terraform_auto_deploy dev
```

## 📝 Schema File Guidelines

### File Naming
- Use descriptive names: `users.sql`, `order_system.sql`
- Files are processed alphabetically
- Each file generates a separate migration

### SQL Best Practices
```sql
-- ✅ Good: Use IF NOT EXISTS
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ✅ Good: Use IF NOT EXISTS for indexes
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);

-- ❌ Avoid: Destructive operations
DROP TABLE users;
TRUNCATE TABLE users;
DELETE FROM users WHERE 1=1;
```

### Schema File Structure
```sql
-- File: services/orbit/db/schema/user_management.sql
-- Description: User management tables and indexes

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- User profiles
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    full_name VARCHAR(255),
    avatar_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Indexes
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id ON user_profiles(id);
```

## 🛡️ Safety Features

### 1. **SQL Validation**
- Checks for dangerous operations (DROP, TRUNCATE, etc.)
- Validates SQL syntax
- Prevents destructive migrations

### 2. **Environment Controls**
- Local: Always allowed
- Dev: Requires `AUTO_MIGRATE_DEV=true`
- Prod: Disabled by default

### 3. **Automatic Versioning**
- Migrations are automatically numbered (V001, V002, etc.)
- No version conflicts
- Maintains migration history

### 4. **Rollback Protection**
- Logs all migrations
- Tracks processed files
- Prevents duplicate applications

## 🔍 Monitoring and Troubleshooting

### Check Migration Status
```bash
# View migration watcher status
bazel run //services/orbit/db:migration_watcher -- status

# View migration logs
bazel run //services/orbit/db:migration_watcher -- logs

# Check pre-commit hook status
bazel run //services/orbit/db:pre_commit_migration -- status
```

### Log Files
```bash
# Migration log
cat services/orbit/db/migration_log.txt

# Watcher log
cat services/orbit/db/migration_watcher.log
```

### Common Issues

1. **"No migrations detected"**
   - Check if schema files exist in `services/orbit/db/schema/`
   - Ensure files have `.sql` extension
   - Check if files have been processed (look in `auto_migrations/`)

2. **"Dangerous SQL operation detected"**
   - Review SQL for DROP, TRUNCATE, or DELETE operations
   - Use IF NOT EXISTS for CREATE statements
   - Avoid destructive operations

3. **"Auto-migration to dev disabled"**
   - Set environment variable: `AUTO_MIGRATE_DEV=true`
   - Or run: `AUTO_MIGRATE_DEV=true bazel run //services/orbit/db:auto_migrate -- run dev`

4. **"Migration watcher not starting"**
   - Install file watching tools: `brew install fswatch` (macOS)
   - Or use polling mode (automatic fallback)

## 📊 Integration Examples

### CI/CD Pipeline
```yaml
# .github/workflows/deploy.yml
name: Deploy with Auto-Migration
on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Bazel
        uses: bazelbuild/setup-bazelisk@v1
      - name: Deploy with Auto-Migration
        run: |
          export AUTO_MIGRATE_DEV=true
          bazel run //:terraform_auto_deploy
```

### Development Workflow
```bash
# Start local development with auto-migration
bazel run //services/orbit/db:migration_watcher -- start local &
bazel run //services/orbit/web:dev &

# Work on schema files
vim services/orbit/db/schema/new_feature.sql
# Migration is applied automatically

# When ready to deploy
bazel run //:terraform_auto_deploy
```

### Pre-commit Integration
```bash
# Install the hook once
bazel run //services/orbit/db:pre_commit_migration -- install

# Normal git workflow
git add services/orbit/db/schema/users.sql
git commit -m "Add users table"
# Pre-commit hook automatically:
# 1. Generates migration
# 2. Applies to local DB
# 3. Adds migration file to commit
```

## 🎯 Best Practices

1. **Use descriptive schema file names**
2. **Always use IF NOT EXISTS**
3. **Test locally before deploying**
4. **Use the pre-commit hook for consistency**
5. **Monitor migration logs**
6. **Keep schema files focused and atomic**
7. **Use the watcher during active development**

## 📚 Reference

### Environment Variables
- `AUTO_MIGRATE_DEV=true` - Enable auto-migration to dev environment
- `DEPLOY_WEB=true` - Deploy web app after terraform apply
- `FORCE_MIGRATION=true` - Force migration even if no changes detected

### File Locations
- Schema files: `services/orbit/db/schema/*.sql`
- Generated migrations: `services/orbit/db/migrations/V*.sql`
- Migration log: `services/orbit/db/migration_log.txt`
- Watcher log: `services/orbit/db/migration_watcher.log`

### Migration Targets
- `//services/orbit/db:auto_migrate` - Core auto-migration system
- `//services/orbit/db:migration_watcher` - File watcher service
- `//services/orbit/db:pre_commit_migration` - Pre-commit hook
- `//terraform:terraform_with_auto_migration` - Terraform with auto-migration
- `//:terraform_auto_deploy` - High-level deployment wrapper

This system ensures your database schema is always in sync with your code changes, automatically! 🚀