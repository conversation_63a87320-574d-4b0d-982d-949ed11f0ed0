/**
 * AI Agent Platform - Tasks Page
 * Comprehensive task management with full CRUD operations
 */

'use client';

import React, { useState, useEffect } from 'react';
import { AppLayout } from '@/components/Layout/AppLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';
import { 
  Search,
  Filter,
  Plus,
  Play,
  Pause,
  Square,
  MoreVertical,
  Clock,
  CheckCircle2,
  XCircle,
  AlertCircle,
  RefreshCw,
  Trash2,
  Eye
} from 'lucide-react';
import { cn } from '@/lib/utils';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { ApiClient } from '@/lib/api/client';
import type { components } from '@/lib/api/types';

type Task = components["schemas"]["TaskResponse"];

// Create API client instance
const apiClient = new ApiClient();

const statusColors = {
  pending: 'secondary',
  running: 'default',
  completed: 'default',
  failed: 'destructive',
  cancelled: 'outline',
} as const;

const priorityColors = {
  low: 'secondary',
  medium: 'default',
  high: 'outline',
  critical: 'destructive',
} as const;

const statusIcons = {
  pending: Clock,
  running: RefreshCw,
  completed: CheckCircle2,
  failed: XCircle,
  cancelled: AlertCircle,
};

const getStatusColor = (status: Task['status']) => {
  switch (status) {
    case 'pending': return 'bg-gray-400';
    case 'running': return 'bg-blue-500';
    case 'completed': return 'bg-green-500';
    case 'failed': return 'bg-red-500';
    case 'cancelled': return 'bg-orange-500';
    default: return 'bg-gray-400';
  }
};

export default function TasksPage() {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [filteredTasks, setFilteredTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [priorityFilter, setPriorityFilter] = useState<string>('');
  const [typeFilter, setTypeFilter] = useState<string>('');
  const router = useRouter();

  // Simple toast replacement
  const toast = (opts: any) => {
    alert(`${opts.title}: ${opts.description}`);
  };

  useEffect(() => {
    fetchTasks();
  }, []);

  useEffect(() => {
    filterTasks();
  }, [tasks, searchQuery, statusFilter, priorityFilter, typeFilter]);

  const fetchTasks = async () => {
    try {
      setLoading(true);
      
      // Fetch real tasks from API
      const response = await apiClient.getTasks({ 
        size: 50,
        status: statusFilter as any,
        type: typeFilter as any
      });
      
      setTasks(response.data || []);
    } catch (error) {
      console.error('Failed to fetch tasks:', error);
      toast({
        title: 'Error fetching tasks',
        description: error instanceof Error ? error.message : 'Failed to load tasks',
      });
      
      // Fallback to empty array if API fails
      setTasks([]);
    } finally {
      setLoading(false);
    }
  };

  const filterTasks = () => {
    let filtered = tasks;

    if (searchQuery) {
      filtered = filtered.filter(
        (task) =>
          task.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          task.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          task.type?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    if (statusFilter) {
      filtered = filtered.filter((task) => task.status === statusFilter);
    }

    if (priorityFilter) {
      filtered = filtered.filter((task) => task.priority === priorityFilter);
    }

    if (typeFilter) {
      filtered = filtered.filter((task) => task.type === typeFilter);
    }

    setFilteredTasks(filtered);
  };

  const handleStartTask = async (taskId: string) => {
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      toast({
        title: 'Task Started',
        description: 'Task execution has been initiated',
      });
      fetchTasks();
    } catch (error) {
      toast({
        title: 'Error starting task',
        description: error instanceof Error ? error.message : 'Failed to start task',
      });
    }
  };

  const handlePauseTask = async (taskId: string) => {
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      toast({
        title: 'Task Paused',
        description: 'Task execution has been paused',
      });
      fetchTasks();
    } catch (error) {
      toast({
        title: 'Error pausing task',
        description: error instanceof Error ? error.message : 'Failed to pause task',
      });
    }
  };

  const handleStopTask = async (taskId: string) => {
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      toast({
        title: 'Task Stopped',
        description: 'Task execution has been stopped',
      });
      fetchTasks();
    } catch (error) {
      toast({
        title: 'Error stopping task',
        description: error instanceof Error ? error.message : 'Failed to stop task',
      });
    }
  };

  const handleDeleteTask = async (taskId: string) => {
    if (window.confirm('Are you sure you want to delete this task?')) {
      try {
        // Mock API call
        await new Promise(resolve => setTimeout(resolve, 500));
        
        toast({
          title: 'Task Deleted',
          description: 'Task has been removed from the system',
        });
        fetchTasks();
      } catch (error) {
        toast({
          title: 'Error deleting task',
          description: error instanceof Error ? error.message : 'Failed to delete task',
        });
      }
    }
  };

  const formatDuration = (seconds?: number) => {
    if (!seconds) return 'N/A';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const getTaskActions = (task: Task) => {
    const actions = [];

    if (task.status === 'pending') {
      actions.push({
        label: 'Start',
        icon: Play,
        action: () => handleStartTask(task.id),
      });
    }

    if (task.status === 'running') {
      actions.push({
        label: 'Pause',
        icon: Pause,
        action: () => handlePauseTask(task.id),
      });
      actions.push({
        label: 'Stop',
        icon: Square,
        action: () => handleStopTask(task.id),
      });
    }

    if (['completed', 'failed', 'cancelled'].includes(task.status)) {
      actions.push({
        label: 'Delete',
        icon: Trash2,
        action: () => handleDeleteTask(task.id),
      });
    }

    return actions;
  };

  const formatRelativeTime = (date: string) => {
    const now = new Date();
    const then = new Date(date);
    const diff = now.getTime() - then.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  if (loading) {
    return (
      <AppLayout>
        <div className="space-y-6">
          <div className="h-8 bg-muted rounded w-1/4 animate-pulse"></div>
          <div className="h-16 bg-muted rounded animate-pulse"></div>
          <div className="h-96 bg-muted rounded animate-pulse"></div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Task Management</h1>
            <p className="text-muted-foreground">
              Monitor and manage task execution across your agents
            </p>
          </div>
          <Button asChild>
            <Link href="/tasks/create">
              <Plus className="h-4 w-4 mr-2" />
              Create Task
            </Link>
          </Button>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search tasks..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-9"
                />
              </div>

              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              >
                <option value="">Filter by status</option>
                <option value="pending">Pending</option>
                <option value="running">Running</option>
                <option value="completed">Completed</option>
                <option value="failed">Failed</option>
                <option value="cancelled">Cancelled</option>
              </select>

              <select
                value={priorityFilter}
                onChange={(e) => setPriorityFilter(e.target.value)}
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              >
                <option value="">Filter by priority</option>
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
                <option value="critical">Critical</option>
              </select>

              <select
                value={typeFilter}
                onChange={(e) => setTypeFilter(e.target.value)}
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              >
                <option value="">Filter by type</option>
                <option value="data_processing">Data Processing</option>
                <option value="analysis">Analysis</option>
                <option value="generation">Generation</option>
                <option value="validation">Validation</option>
                <option value="integration">Integration</option>
              </select>
            </div>

            {(searchQuery || statusFilter || priorityFilter || typeFilter) && (
              <div className="flex mt-4 gap-2 items-center">
                <span className="text-sm text-muted-foreground">
                  Filters active:
                </span>
                {searchQuery && (
                  <Badge variant="outline">
                    Search: {searchQuery}
                  </Badge>
                )}
                {statusFilter && (
                  <Badge variant="outline">
                    Status: {statusFilter}
                  </Badge>
                )}
                {priorityFilter && (
                  <Badge variant="outline">
                    Priority: {priorityFilter}
                  </Badge>
                )}
                {typeFilter && (
                  <Badge variant="outline">
                    Type: {typeFilter}
                  </Badge>
                )}
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => {
                    setSearchQuery('');
                    setStatusFilter('');
                    setPriorityFilter('');
                    setTypeFilter('');
                  }}
                >
                  Clear All
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Tasks Grid */}
        {filteredTasks.length === 0 ? (
          <Card>
            <CardContent className="py-8">
              <div className="flex flex-col items-center space-y-4 text-center">
                <AlertCircle className="h-12 w-12 text-muted-foreground" />
                <div>
                  <h3 className="text-lg font-medium">No tasks found</h3>
                  <p className="text-muted-foreground">
                    {tasks.length === 0
                      ? 'Create your first task to get started'
                      : 'Try adjusting your filters'}
                  </p>
                </div>
                {tasks.length === 0 && (
                  <Button asChild>
                    <Link href="/tasks/create">
                      <Plus className="h-4 w-4 mr-2" />
                      Create First Task
                    </Link>
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {filteredTasks.map((task) => {
              const StatusIcon = statusIcons[task.status];
              const actions = getTaskActions(task);

              return (
                <Card key={task.id} className="hover:shadow-md transition-shadow cursor-pointer">
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div className="space-y-2 flex-1">
                        <div className="flex items-center gap-2">
                          <StatusIcon className={cn("h-5 w-5", 
                            task.status === 'running' && "animate-spin",
                            task.status === 'completed' && "text-green-500",
                            task.status === 'failed' && "text-red-500",
                            task.status === 'pending' && "text-gray-500",
                            task.status === 'cancelled' && "text-orange-500"
                          )} />
                          <CardTitle className="text-lg line-clamp-1">
                            {task.name}
                          </CardTitle>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="flex items-center gap-1">
                            <div className={cn("h-2 w-2 rounded-full", getStatusColor(task.status))} />
                            <Badge variant={statusColors[task.status]}>
                              {task.status}
                            </Badge>
                          </div>
                          <Badge variant={(task.priority && priorityColors[task.priority]) || "default"}>
                            {task.priority}
                          </Badge>
                          <Badge variant="secondary">{task.type.replace('_', ' ')}</Badge>
                        </div>
                      </div>
                      <div className="flex items-center gap-1">
                        <Button variant="ghost" size="sm" asChild>
                          <Link href={`/tasks/${task.id}`}>
                            <Eye className="h-4 w-4" />
                          </Link>
                        </Button>
                        {actions.map((action) => (
                          <Button 
                            key={action.label}
                            variant="ghost" 
                            size="sm" 
                            onClick={action.action}
                            title={action.label}
                          >
                            <action.icon className="h-4 w-4" />
                          </Button>
                        ))}
                      </div>
                    </div>
                  </CardHeader>

                  <CardContent className="pt-0 space-y-4">
                    {task.description && (
                      <p className="text-muted-foreground text-sm line-clamp-2">
                        {task.description}
                      </p>
                    )}

                    {/* Progress Bar */}
                    {task.status === 'running' && task.progress && (
                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-muted-foreground">Progress</span>
                          <span className="text-muted-foreground">{task.progress}%</span>
                        </div>
                        <Progress value={task.progress} className="h-2" />
                      </div>
                    )}

                    {/* Task Info */}
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="text-xs text-muted-foreground mb-1">AGENT ID</p>
                        <p>{task.agent_id || 'Unassigned'}</p>
                      </div>
                      <div>
                        <p className="text-xs text-muted-foreground mb-1">TYPE</p>
                        <p className="capitalize">{task.type?.replace('_', ' ')}</p>
                      </div>
                    </div>

                    {/* Error Message */}
                    {task.status === 'failed' && task.error && (
                      <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-md">
                        <p className="text-sm text-destructive">
                          <strong>Error:</strong> {task.error}
                        </p>
                      </div>
                    )}

                    {/* Success Result */}
                    {task.status === 'completed' && task.result && (
                      <div className="p-3 bg-green-50 border border-green-200 rounded-md">
                        <p className="text-sm text-green-700">
                          <strong>Result:</strong> {JSON.stringify(task.result)}
                        </p>
                      </div>
                    )}

                    {/* Timestamps */}
                    <div className="pt-2 border-t border-border">
                      <div className="grid grid-cols-2 gap-4 text-xs text-muted-foreground">
                        <div>
                          Created: {task.created_at ? formatRelativeTime(task.created_at) : 'N/A'}
                        </div>
                        <div>
                          Started: {task.started_at ? formatRelativeTime(task.started_at) : 'N/A'}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}

        {/* Summary Stats */}
        <Card>
          <CardContent className="pt-6">
            <div className="grid grid-cols-2 md:grid-cols-5 gap-6 text-center">
              <div>
                <div className="text-2xl font-bold text-muted-foreground">
                  {tasks.filter(t => t.status === 'pending').length}
                </div>
                <div className="text-sm text-muted-foreground">Pending</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-blue-500">
                  {tasks.filter(t => t.status === 'running').length}
                </div>
                <div className="text-sm text-muted-foreground">Running</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-500">
                  {tasks.filter(t => t.status === 'completed').length}
                </div>
                <div className="text-sm text-muted-foreground">Completed</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-red-500">
                  {tasks.filter(t => t.status === 'failed').length}
                </div>
                <div className="text-sm text-muted-foreground">Failed</div>
              </div>
              <div>
                <div className="text-2xl font-bold">
                  {tasks.length}
                </div>
                <div className="text-sm text-muted-foreground">Total Tasks</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}