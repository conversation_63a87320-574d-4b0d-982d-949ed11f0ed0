"""
AI Agent Platform - Authentication and Security
"""

from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from uuid import UUID
from jose import JWTError, jwt
from passlib.context import CryptContext
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
import structlog

from config.settings import settings
from database.models import User

logger = structlog.get_logger()

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class SecurityService:
    """Service for handling authentication and security operations"""
    
    def __init__(self, db_session: AsyncSession):
        self.db = db_session
        self.settings = settings
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify a password against its hash"""
        return pwd_context.verify(plain_password, hashed_password)
    
    def get_password_hash(self, password: str) -> str:
        """Generate password hash"""
        return pwd_context.hash(password)
    
    def create_access_token(
        self, 
        data: Dict[str, Any], 
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """Create JWT access token"""
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(
                minutes=settings.security.access_token_expire_minutes
            )
        
        to_encode.update({"exp": expire})
        
        encoded_jwt = jwt.encode(
            to_encode,
            settings.security.secret_key,
            algorithm=settings.security.algorithm
        )
        
        return encoded_jwt
    
    def create_refresh_token(
        self,
        data: Dict[str, Any],
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """Create JWT refresh token"""
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(
                days=settings.security.refresh_token_expire_days
            )
        
        to_encode.update({"exp": expire, "type": "refresh"})
        
        encoded_jwt = jwt.encode(
            to_encode,
            settings.security.secret_key,
            algorithm=settings.security.algorithm
        )
        
        return encoded_jwt
    
    def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Verify and decode JWT token"""
        try:
            payload = jwt.decode(
                token,
                settings.security.secret_key,
                algorithms=[settings.security.algorithm]
            )
            return payload
        except JWTError as e:
            logger.warning("Token verification failed", error=str(e))
            return None
    
    async def authenticate_user(
        self, 
        username: str, 
        password: str
    ) -> Optional[User]:
        """Authenticate user with username and password"""
        try:
            # Get user from database
            result = await self.db.execute(
                select(User).where(
                    (User.username == username) | (User.email == username)
                )
            )
            user = result.scalar_one_or_none()
            
            if not user:
                logger.info("User not found during authentication", username=username)
                return None
            
            if not user.is_active:
                logger.info("Inactive user attempted login", username=username)
                return None
            
            if not self.verify_password(password, user.hashed_password):
                logger.info("Invalid password for user", username=username)
                return None
            
            # Update last login
            user.last_login = datetime.utcnow()
            await self.db.commit()
            
            logger.info("User authenticated successfully", 
                       user_id=str(user.id), username=username)
            
            return user
            
        except Exception as e:
            logger.error("Authentication error", error=str(e))
            return None
    
    async def get_user_by_token(self, token: str) -> Optional[User]:
        """Get user from JWT token"""
        try:
            payload = self.verify_token(token)
            if not payload:
                return None
            
            user_id = payload.get("sub")
            if not user_id:
                return None
            
            # Get user from database
            result = await self.db.execute(
                select(User).where(User.id == UUID(user_id))
            )
            user = result.scalar_one_or_none()
            
            if not user or not user.is_active:
                return None
            
            return user
            
        except Exception as e:
            logger.error("Token-based user lookup failed", error=str(e))
            return None
    
    async def create_user(
        self,
        username: str,
        email: str,
        password: str,
        full_name: Optional[str] = None,
        is_superuser: bool = False
    ) -> User:
        """Create a new user"""
        try:
            # Check if user already exists
            result = await self.db.execute(
                select(User).where(
                    (User.username == username) | (User.email == email)
                )
            )
            existing_user = result.scalar_one_or_none()
            
            if existing_user:
                raise ValueError("User with this username or email already exists")
            
            # Create new user
            hashed_password = self.get_password_hash(password)
            
            user = User(
                username=username,
                email=email,
                hashed_password=hashed_password,
                full_name=full_name,
                is_superuser=is_superuser
            )
            
            self.db.add(user)
            await self.db.commit()
            await self.db.refresh(user)
            
            logger.info("User created successfully", 
                       user_id=str(user.id), username=username)
            
            return user
            
        except Exception as e:
            await self.db.rollback()
            logger.error("User creation failed", error=str(e))
            raise
    
    async def change_password(
        self,
        user_id: UUID,
        current_password: str,
        new_password: str
    ) -> bool:
        """Change user password"""
        try:
            # Get user
            result = await self.db.execute(
                select(User).where(User.id == user_id)
            )
            user = result.scalar_one_or_none()
            
            if not user:
                return False
            
            # Verify current password
            if not self.verify_password(current_password, user.hashed_password):
                logger.warning("Invalid current password during password change",
                             user_id=str(user_id))
                return False
            
            # Update password
            user.hashed_password = self.get_password_hash(new_password)
            await self.db.commit()
            
            logger.info("Password changed successfully", user_id=str(user_id))
            return True
            
        except Exception as e:
            await self.db.rollback()
            logger.error("Password change failed", user_id=str(user_id), error=str(e))
            return False
    
    async def update_user(
        self,
        user_id: UUID,
        username: Optional[str] = None,
        email: Optional[str] = None,
        full_name: Optional[str] = None
    ) -> Optional[User]:
        """Update user information"""
        try:
            # Get user
            result = await self.db.execute(
                select(User).where(User.id == user_id)
            )
            user = result.scalar_one_or_none()
            
            if not user:
                return None
            
            # Check if new username/email already exists
            if username and username != user.username:
                existing = await self.db.execute(
                    select(User).where(User.username == username)
                )
                if existing.scalar_one_or_none():
                    raise ValueError("Username already exists")
                user.username = username
            
            if email and email != user.email:
                existing = await self.db.execute(
                    select(User).where(User.email == email)
                )
                if existing.scalar_one_or_none():
                    raise ValueError("Email already exists")
                user.email = email
            
            if full_name is not None:
                user.full_name = full_name
            
            user.updated_at = datetime.utcnow()
            await self.db.commit()
            await self.db.refresh(user)
            
            logger.info("User updated successfully", user_id=str(user_id))
            return user
            
        except Exception as e:
            await self.db.rollback()
            logger.error("User update failed", user_id=str(user_id), error=str(e))
            raise