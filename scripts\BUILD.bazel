package(default_visibility = ["//visibility:public"])

# Export the lint script
exports_files([
    "lint.sh",
])

# Create a test target that runs the linting script
sh_test(
    name = "go_lint_test",
    timeout = "long",
    srcs = ["lint_simple.sh"],
    data = [
        "//:go.mod",
        "//:golangci_config_file",
        "//shared/go/logging",
        "//shared/go/utils",
        "//services/orbit/auth",
        "//services/orbit/crm_backend",
        "//services/orbit/gateway",
    ],
    tags = ["lint"],
)

# Create a simple run target for linting
sh_binary(
    name = "lint",
    srcs = ["lint.sh"],
    data = [
        "//:go.mod",
        "//:golangci_config_file",
        "//shared/go/logging",
        "//shared/go/utils",
        "//services/orbit/auth",
        "//services/orbit/crm_backend",
        "//services/orbit/gateway",
    ],
)

# Platform deployment scripts
# Temporarily disabled due to terraform dependency
# sh_binary(
#     name = "deploy_platform_dev",
#     srcs = ["deploy_platform_dev.sh"],
#     data = [
#         "//services/orbit/db:migrate_gcp_simple",
#         "//infra/terraform:deploy_full",
#     ],
#     tags = [
#         "deploy",
#         "dev",
#         "platform",
#     ],
# )

# Temporarily disabled due to terraform dependency
# sh_binary(
#     name = "deploy_quick_dev",
#     srcs = ["deploy_quick_dev.sh"],
#     data = [
#         "//infra/terraform:deploy_web",
#         "//services/orbit/db:migrate_gcp_simple",
#     ],
#     tags = [
#         "deploy",
#         "dev",
#         "platform",
#         "quick",
#     ],
# )

sh_binary(
    name = "dev_setup",
    srcs = ["dev_setup.sh"],
    data = [
        "//services/orbit/db:migrate",
        "//services/orbit/db:start",
        "//services/orbit/db:test_data",
        "//services/orbit/web:dev",
    ],
    tags = [
        "dev",
        "platform",
        "setup",
    ],
)

sh_binary(
    name = "setup_auto_migration",
    srcs = ["setup_auto_migration.sh"],
    data = [
        "//services/orbit/db:auto_migrate",
        "//services/orbit/db:migration_watcher",
        "//services/orbit/db:pre_commit_migration",
    ],
    tags = [
        "auto-migration",
        "platform",
        "setup",
    ],
)

# Temporarily disabled due to terraform dependency
# sh_binary(
#     name = "terraform_auto_deploy",
#     srcs = ["terraform_auto_deploy.sh"],
#     data = [
#         "//infra/terraform:deploy_full",
#     ],
#     tags = [
#         "auto-migration",
#         "deploy",
#         "platform",
#         "terraform",
#     ],
# )
