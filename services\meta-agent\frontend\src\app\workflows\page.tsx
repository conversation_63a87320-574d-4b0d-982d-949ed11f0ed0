/**
 * AI Agent Platform - Workflows Page
 * Visual workflow builder and management
 */

'use client';

import React, { useState, useEffect } from 'react';
import { AppLayout } from '@/components/Layout/AppLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Plus,
  Play,
  Pause,
  Square,
  Edit,
  Trash2,
  Copy,
  Download,
  Upload,
  Search,
  Filter,
  MoreHorizontal,
  Workflow,
  Clock,
  CheckCircle2,
  XCircle,
  AlertCircle,
  Zap,
  GitBranch,
  Settings
} from 'lucide-react';
import Link from 'next/link';

// Mock data for workflows
const mockWorkflows = [
  {
    id: '1',
    name: 'Customer Support Automation',
    description: 'Automated customer support workflow with sentiment analysis and routing',
    status: 'active',
    lastRun: '2024-01-15T10:30:00Z',
    totalRuns: 1247,
    successRate: 94.2,
    nodes: 8,
    connections: 12,
    triggers: ['webhook', 'schedule'],
    tags: ['customer-support', 'automation']
  },
  {
    id: '2',
    name: 'Data Processing Pipeline',
    description: 'ETL workflow for processing customer data and generating insights',
    status: 'running',
    lastRun: '2024-01-15T11:45:00Z',
    totalRuns: 892,
    successRate: 98.7,
    nodes: 12,
    connections: 18,
    triggers: ['schedule', 'file-upload'],
    tags: ['data-processing', 'etl']
  },
  {
    id: '3',
    name: 'Content Moderation',
    description: 'AI-powered content moderation workflow for user-generated content',
    status: 'paused',
    lastRun: '2024-01-14T16:20:00Z',
    totalRuns: 2156,
    successRate: 91.8,
    nodes: 6,
    connections: 8,
    triggers: ['webhook'],
    tags: ['content-moderation', 'ai']
  },
  {
    id: '4',
    name: 'Lead Qualification',
    description: 'Automated lead scoring and qualification workflow',
    status: 'error',
    lastRun: '2024-01-15T09:15:00Z',
    totalRuns: 543,
    successRate: 87.3,
    nodes: 10,
    connections: 15,
    triggers: ['webhook', 'api'],
    tags: ['sales', 'lead-generation']
  }
];

const statusConfig = {
  active: { color: 'bg-green-100 text-green-800', icon: CheckCircle2 },
  running: { color: 'bg-blue-100 text-blue-800', icon: Play },
  paused: { color: 'bg-yellow-100 text-yellow-800', icon: Pause },
  error: { color: 'bg-red-100 text-red-800', icon: XCircle },
  draft: { color: 'bg-gray-100 text-gray-800', icon: Edit }
};

export default function WorkflowsPage() {
  const [workflows, setWorkflows] = useState(mockWorkflows);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [loading, setLoading] = useState(false);

  const filteredWorkflows = workflows.filter(workflow => {
    const matchesSearch = workflow.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         workflow.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || workflow.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const handleWorkflowAction = async (workflowId: string, action: string) => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setWorkflows(prev => prev.map(workflow => {
        if (workflow.id === workflowId) {
          switch (action) {
            case 'start':
              return { ...workflow, status: 'running' };
            case 'pause':
              return { ...workflow, status: 'paused' };
            case 'stop':
              return { ...workflow, status: 'active' };
            default:
              return workflow;
          }
        }
        return workflow;
      }));
    } catch (error) {
      console.error('Failed to perform workflow action:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <AppLayout>
      <div className="container mx-auto py-8 px-4 max-w-7xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-3">
              <Workflow className="h-8 w-8 text-primary" />
              Workflows
            </h1>
            <p className="text-muted-foreground mt-2">
              Create, manage, and monitor visual workflows for automation
            </p>
          </div>
          <div className="flex gap-3">
            <Button variant="outline">
              <Upload className="h-4 w-4 mr-2" />
              Import
            </Button>
            <Link href="/workflows/create">
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Create Workflow
              </Button>
            </Link>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Workflows</p>
                  <p className="text-2xl font-bold">{workflows.length}</p>
                </div>
                <Workflow className="h-8 w-8 text-muted-foreground" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Active</p>
                  <p className="text-2xl font-bold text-green-600">
                    {workflows.filter(w => w.status === 'active' || w.status === 'running').length}
                  </p>
                </div>
                <Play className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Runs</p>
                  <p className="text-2xl font-bold">
                    {workflows.reduce((sum, w) => sum + w.totalRuns, 0).toLocaleString()}
                  </p>
                </div>
                <Zap className="h-8 w-8 text-muted-foreground" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Avg Success Rate</p>
                  <p className="text-2xl font-bold text-blue-600">
                    {(workflows.reduce((sum, w) => sum + w.successRate, 0) / workflows.length).toFixed(1)}%
                  </p>
                </div>
                <CheckCircle2 className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search workflows..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                >
                  <option value="all">All Status</option>
                  <option value="active">Active</option>
                  <option value="running">Running</option>
                  <option value="paused">Paused</option>
                  <option value="error">Error</option>
                  <option value="draft">Draft</option>
                </select>
                <Button variant="outline" size="sm">
                  <Filter className="h-4 w-4 mr-2" />
                  More Filters
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Workflows List */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {filteredWorkflows.map((workflow) => {
            const StatusIcon = statusConfig[workflow.status as keyof typeof statusConfig]?.icon || AlertCircle;
            const statusColor = statusConfig[workflow.status as keyof typeof statusConfig]?.color || 'bg-gray-100 text-gray-800';
            
            return (
              <Card key={workflow.id} className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="flex items-center gap-2">
                        <Link href={`/workflows/${workflow.id}`} className="hover:underline">
                          {workflow.name}
                        </Link>
                        <Badge className={statusColor}>
                          <StatusIcon className="h-3 w-3 mr-1" />
                          {workflow.status}
                        </Badge>
                      </CardTitle>
                      <CardDescription className="mt-2">
                        {workflow.description}
                      </CardDescription>
                    </div>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* Workflow Stats */}
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="text-muted-foreground">Nodes</p>
                        <p className="font-medium">{workflow.nodes}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Connections</p>
                        <p className="font-medium">{workflow.connections}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Total Runs</p>
                        <p className="font-medium">{workflow.totalRuns.toLocaleString()}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Success Rate</p>
                        <p className="font-medium text-green-600">{workflow.successRate}%</p>
                      </div>
                    </div>

                    {/* Tags */}
                    <div className="flex flex-wrap gap-1">
                      {workflow.tags.map((tag, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>

                    {/* Triggers */}
                    <div>
                      <p className="text-sm text-muted-foreground mb-2">Triggers:</p>
                      <div className="flex gap-2">
                        {workflow.triggers.map((trigger, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {trigger}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    {/* Last Run */}
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Clock className="h-4 w-4" />
                      Last run: {new Date(workflow.lastRun).toLocaleString()}
                    </div>

                    {/* Actions */}
                    <div className="flex gap-2 pt-2 border-t">
                      {workflow.status === 'paused' || workflow.status === 'active' ? (
                        <Button
                          size="sm"
                          onClick={() => handleWorkflowAction(workflow.id, 'start')}
                          disabled={loading}
                        >
                          <Play className="h-4 w-4 mr-1" />
                          Start
                        </Button>
                      ) : workflow.status === 'running' ? (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleWorkflowAction(workflow.id, 'pause')}
                          disabled={loading}
                        >
                          <Pause className="h-4 w-4 mr-1" />
                          Pause
                        </Button>
                      ) : null}
                      
                      <Link href={`/workflows/${workflow.id}/edit`}>
                        <Button size="sm" variant="outline">
                          <Edit className="h-4 w-4 mr-1" />
                          Edit
                        </Button>
                      </Link>
                      
                      <Button size="sm" variant="outline">
                        <Copy className="h-4 w-4 mr-1" />
                        Clone
                      </Button>
                      
                      <Button size="sm" variant="outline">
                        <Download className="h-4 w-4 mr-1" />
                        Export
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {filteredWorkflows.length === 0 && (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center py-8">
                <Workflow className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">No workflows found</h3>
                <p className="text-muted-foreground mb-4">
                  {searchTerm || statusFilter !== 'all' 
                    ? 'Try adjusting your search or filters'
                    : 'Get started by creating your first workflow'
                  }
                </p>
                {!searchTerm && statusFilter === 'all' && (
                  <Link href="/workflows/create">
                    <Button>
                      <Plus className="h-4 w-4 mr-2" />
                      Create Your First Workflow
                    </Button>
                  </Link>
                )}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </AppLayout>
  );
}
