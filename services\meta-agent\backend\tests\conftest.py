"""
AI Agent Platform - Test Configuration
"""

import asyncio
import pytest
import pytest_asyncio
from typing import AsyncGenerator
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient
import uuid
import os
from dotenv import load_dotenv

# Load test environment variables  
load_dotenv('.env.test')

# Override DATABASE_URL for tests to use test PostgreSQL database
os.environ['DATABASE_URL'] = 'postgresql://ai_agent_user:ai_agent_password@localhost:5432/ai_agent_platform_test'

from main import app
from database.models import Base
from database.connection import get_db
from config.settings import settings

# Test database URL - use PostgreSQL test database
TEST_DATABASE_URL = "postgresql+asyncpg://ai_agent_user:ai_agent_password@localhost:5432/ai_agent_platform_test"

# Create test engine
test_engine = create_async_engine(
    TEST_DATABASE_URL,
    echo=False,  # Reduce noise in tests
    pool_pre_ping=True
)

# Test session factory
TestingSessionLocal = sessionmaker(
    test_engine,
    class_=AsyncSession,
    expire_on_commit=False
)


@pytest_asyncio.fixture
async def test_db() -> AsyncGenerator[AsyncSession, None]:
    """Create test database session"""
    # Create tables
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    # Create session
    async with TestingSessionLocal() as session:
        yield session
    
    # Drop tables
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)


@pytest.fixture
def client(test_db):
    """Create test client with dependency override"""
    async def override_get_db():
        yield test_db
    
    app.dependency_overrides[get_db] = override_get_db
    
    with TestClient(app) as test_client:
        yield test_client
    
    app.dependency_overrides.clear()


@pytest.fixture
def test_user_data():
    """Sample user data for testing"""
    return {
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "testpassword123",
        "full_name": "Test User"
    }


@pytest.fixture
def test_agent_data():
    """Sample agent data for testing"""
    return {
        "name": "Test Agent",
        "description": "A test agent for unit testing",
        "type": "assistant",
        "config": {
            "max_concurrent_tasks": 5,
            "timeout_seconds": 300
        },
        "capabilities": ["text_analysis", "data_processing"]
    }


@pytest.fixture
def test_orchestration_data():
    """Sample orchestration data for testing"""
    return {
        "name": "Test Orchestration",
        "description": "A test orchestration",
        "pattern": "sequential",
        "config": {
            "max_retries": 3,
            "timeout_minutes": 30
        },
        "execution_plan": {
            "steps": ["step1", "step2", "step3"]
        },
        "agent_ids": []
    }


@pytest.fixture(scope="session")
def event_loop():
    """Create event loop for async tests"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest_asyncio.fixture
async def authenticated_user(client, test_user_data):
    """Create and authenticate a test user"""
    # Register user
    response = client.post("/api/v1/auth/register", json=test_user_data)
    assert response.status_code == 201
    
    # Login user
    login_data = {
        "username": test_user_data["username"],
        "password": test_user_data["password"]
    }
    response = client.post("/api/v1/auth/login", data=login_data)
    assert response.status_code == 200
    
    token_data = response.json()
    auth_headers = {"Authorization": f"Bearer {token_data['access_token']}"}
    
    return {
        "user_data": test_user_data,
        "token_data": token_data,
        "headers": auth_headers
    }


@pytest_asyncio.fixture
async def test_agent(client, authenticated_user, test_agent_data):
    """Create a test agent"""
    response = client.post(
        "/api/v1/agents",
        json=test_agent_data,
        headers=authenticated_user["headers"]
    )
    assert response.status_code == 201
    return response.json()


@pytest_asyncio.fixture
async def test_orchestration(client, authenticated_user, test_orchestration_data):
    """Create a test orchestration"""
    response = client.post(
        "/api/v1/orchestrations",
        json=test_orchestration_data,
        headers=authenticated_user["headers"]
    )
    assert response.status_code == 201
    return response.json()