/**
 * AI Agent Platform - Connections Page
 * Unified integrations, APIs, and data sources management
 */

'use client';

import React, { useState } from 'react';
import { AppLayout } from '@/components/Layout/AppLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Puzzle,
  Database,
  Globe,
  Settings,
  Activity,
  CheckCircle2,
  XCircle,
  AlertCircle,
  Eye,
  Edit,
  Trash2,
  Play,
  Pause,
  Webhook,
  Server,
  Cloud,
  HardDrive,
  Brain
} from 'lucide-react';
import Link from 'next/link';

// Mock data for connections
const mockIntegrations = [
  {
    id: '1',
    name: 'Slack Integration',
    type: 'messaging',
    status: 'active',
    description: 'Send notifications and messages to Slack channels',
    provider: 'Slack',
    lastUsed: '2024-01-15T10:30:00Z',
    usage: 1247,
    config: { webhook_url: 'https://hooks.slack.com/...' }
  },
  {
    id: '2',
    name: 'OpenAI API',
    type: 'ai_service',
    status: 'active',
    description: 'Access to GPT models and embeddings',
    provider: 'OpenAI',
    lastUsed: '2024-01-15T11:45:00Z',
    usage: 15420,
    config: { api_key: 'sk-...', model: 'gpt-4' }
  },
  {
    id: '3',
    name: 'GitHub Webhooks',
    type: 'webhook',
    status: 'inactive',
    description: 'Receive repository events and trigger automations',
    provider: 'GitHub',
    lastUsed: '2024-01-10T16:20:00Z',
    usage: 89,
    config: { webhook_url: 'https://api.github.com/...' }
  }
];

const mockDataSources = [
  {
    id: '1',
    name: 'Customer Database',
    type: 'postgresql',
    status: 'active',
    description: 'Primary customer data and transaction history',
    host: 'db.company.com',
    database: 'customers',
    lastSync: '2024-01-15T12:00:00Z',
    records: 125000
  },
  {
    id: '2',
    name: 'Analytics Warehouse',
    type: 'bigquery',
    status: 'active',
    description: 'Data warehouse for analytics and reporting',
    host: 'bigquery.googleapis.com',
    database: 'analytics',
    lastSync: '2024-01-15T11:30:00Z',
    records: 2500000
  },
  {
    id: '3',
    name: 'File Storage',
    type: 's3',
    status: 'active',
    description: 'Document and media file storage',
    host: 's3.amazonaws.com',
    database: 'company-files',
    lastSync: '2024-01-15T10:15:00Z',
    records: 45000
  }
];

const statusConfig = {
  active: { color: 'bg-green-100 text-green-800', icon: CheckCircle2 },
  inactive: { color: 'bg-gray-100 text-gray-800', icon: XCircle },
  error: { color: 'bg-red-100 text-red-800', icon: AlertCircle }
};

const typeIcons = {
  messaging: Webhook,
  ai_service: Brain,
  webhook: Globe,
  api: Server,
  postgresql: Database,
  mysql: Database,
  mongodb: Database,
  bigquery: Cloud,
  s3: HardDrive,
  file_system: HardDrive
};

export default function ConnectionsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [loading, setLoading] = useState(false);

  const filteredIntegrations = mockIntegrations.filter(integration => {
    const matchesSearch = integration.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         integration.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || integration.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const filteredDataSources = mockDataSources.filter(source => {
    const matchesSearch = source.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         source.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || source.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  return (
    <AppLayout>
      <div className="container mx-auto py-8 px-4 max-w-7xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-3">
              <Puzzle className="h-8 w-8 text-primary" />
              Connections
            </h1>
            <p className="text-muted-foreground mt-2">
              Manage integrations, APIs, and data sources
            </p>
          </div>
          <div className="flex gap-3">
            <Button variant="outline">
              <Activity className="h-4 w-4 mr-2" />
              Test All
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Connection
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Connections</p>
                  <p className="text-2xl font-bold">{mockIntegrations.length + mockDataSources.length}</p>
                </div>
                <Puzzle className="h-8 w-8 text-muted-foreground" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Integrations</p>
                  <p className="text-2xl font-bold text-blue-600">{mockIntegrations.length}</p>
                </div>
                <Globe className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Data Sources</p>
                  <p className="text-2xl font-bold text-green-600">{mockDataSources.length}</p>
                </div>
                <Database className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Active</p>
                  <p className="text-2xl font-bold text-orange-600">
                    {[...mockIntegrations, ...mockDataSources].filter(c => c.status === 'active').length}
                  </p>
                </div>
                <CheckCircle2 className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="integrations" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="integrations">Integrations & APIs</TabsTrigger>
            <TabsTrigger value="data-sources">Data Sources</TabsTrigger>
          </TabsList>

          {/* Integrations Tab */}
          <TabsContent value="integrations" className="space-y-6">
            {/* Filters */}
            <Card>
              <CardContent className="pt-6">
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Search integrations..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                  >
                    <option value="all">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                    <option value="error">Error</option>
                  </select>
                </div>
              </CardContent>
            </Card>

            {/* Integrations List */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {filteredIntegrations.map((integration) => {
                const StatusIcon = statusConfig[integration.status as keyof typeof statusConfig]?.icon || AlertCircle;
                const statusColor = statusConfig[integration.status as keyof typeof statusConfig]?.color || 'bg-gray-100 text-gray-800';
                const TypeIcon = typeIcons[integration.type as keyof typeof typeIcons] || Globe;
                
                return (
                  <Card key={integration.id} className="hover:shadow-md transition-shadow">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <CardTitle className="flex items-center gap-2">
                            <TypeIcon className="h-5 w-5 text-primary" />
                            {integration.name}
                            <Badge className={statusColor}>
                              <StatusIcon className="h-3 w-3 mr-1" />
                              {integration.status}
                            </Badge>
                          </CardTitle>
                          <CardDescription className="mt-2">
                            {integration.description}
                          </CardDescription>
                        </div>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {/* Stats */}
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <p className="text-muted-foreground">Provider</p>
                            <p className="font-medium">{integration.provider}</p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">Usage</p>
                            <p className="font-medium">{integration.usage.toLocaleString()}</p>
                          </div>
                        </div>

                        {/* Last Used */}
                        <div className="text-sm text-muted-foreground">
                          Last used: {new Date(integration.lastUsed).toLocaleString()}
                        </div>

                        {/* Actions */}
                        <div className="flex gap-2 pt-2 border-t">
                          {integration.status === 'active' ? (
                            <Button size="sm" variant="outline">
                              <Pause className="h-4 w-4 mr-1" />
                              Disable
                            </Button>
                          ) : (
                            <Button size="sm">
                              <Play className="h-4 w-4 mr-1" />
                              Enable
                            </Button>
                          )}
                          <Button size="sm" variant="outline">
                            <Settings className="h-4 w-4 mr-1" />
                            Configure
                          </Button>
                          <Button size="sm" variant="outline">
                            <Activity className="h-4 w-4 mr-1" />
                            Test
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </TabsContent>

          {/* Data Sources Tab */}
          <TabsContent value="data-sources" className="space-y-6">
            <div className="space-y-4">
              {filteredDataSources.map((source) => {
                const StatusIcon = statusConfig[source.status as keyof typeof statusConfig]?.icon || AlertCircle;
                const statusColor = statusConfig[source.status as keyof typeof statusConfig]?.color || 'bg-gray-100 text-gray-800';
                const TypeIcon = typeIcons[source.type as keyof typeof typeIcons] || Database;
                
                return (
                  <Card key={source.id} className="hover:shadow-md transition-shadow">
                    <CardContent className="pt-6">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <TypeIcon className="h-5 w-5 text-primary" />
                            <h3 className="font-semibold text-lg">{source.name}</h3>
                            <Badge className={statusColor}>
                              <StatusIcon className="h-3 w-3 mr-1" />
                              {source.status}
                            </Badge>
                            <Badge variant="outline" className="capitalize">
                              {source.type}
                            </Badge>
                          </div>
                          <p className="text-muted-foreground">
                            {source.description}
                          </p>
                        </div>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </div>

                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-4">
                        <div>
                          <p className="text-muted-foreground">Host</p>
                          <p className="font-medium">{source.host}</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Database</p>
                          <p className="font-medium">{source.database}</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Records</p>
                          <p className="font-medium">{source.records.toLocaleString()}</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Last Sync</p>
                          <p className="font-medium">{new Date(source.lastSync).toLocaleDateString()}</p>
                        </div>
                      </div>

                      <div className="flex gap-2 pt-4 border-t">
                        <Button size="sm" variant="outline">
                          <Activity className="h-4 w-4 mr-1" />
                          Test Connection
                        </Button>
                        <Button size="sm" variant="outline">
                          <Settings className="h-4 w-4 mr-1" />
                          Configure
                        </Button>
                        <Button size="sm" variant="outline">
                          <Eye className="h-4 w-4 mr-1" />
                          View Schema
                        </Button>
                        <Button size="sm" variant="outline">
                          <Database className="h-4 w-4 mr-1" />
                          Sync Now
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </AppLayout>
  );
}
