#!/bin/bash
# Generate TypeScript client using npm package

set -e

SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"
WORKSPACE_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
OPENAPI_SPEC="$SCRIPT_DIR/openapi.yaml"
OUTPUT_DIR="$WORKSPACE_ROOT/services/orbit/web/src/api-client"

echo "Generating TypeScript API client using npm..."
echo "Input spec: $OPENAPI_SPEC"
echo "Output dir: $OUTPUT_DIR"

# Create a temporary directory for npm operations
TEMP_DIR=$(mktemp -d)
cd "$TEMP_DIR"

# Initialize npm and install openapi-generator-cli
npm init -y > /dev/null 2>&1
npm install --no-save @openapitools/openapi-generator-cli > /dev/null 2>&1

# Create output directory
mkdir -p "$OUTPUT_DIR"
rm -rf "$OUTPUT_DIR"/*

echo "Running OpenAPI generator..."

# Generate the TypeScript client
npx @openapitools/openapi-generator-cli generate \
    -i "$OPENAPI_SPEC" \
    -g typescript-fetch \
    -o "$OUTPUT_DIR" \
    --additional-properties=typescriptThreePlus=true,supportsES6=true,npmName=@crm/api-client,npmVersion=1.0.0,stringEnums=true,modelPropertyNaming=camelCase

# Clean up temp directory
cd "$WORKSPACE_ROOT"
rm -rf "$TEMP_DIR"

echo "TypeScript API client generated successfully!"
echo "Generated files:"
find "$OUTPUT_DIR" -name "*.ts" | head -10

# Create a simple index.ts that exports everything
cat > "$OUTPUT_DIR/index.ts" << 'EOF'
// Re-export all APIs and models
export * from './apis';
export * from './models';
export * from './runtime';
EOF

echo "Created index.ts with re-exports"