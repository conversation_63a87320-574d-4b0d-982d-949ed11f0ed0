openapi: 3.0.3
info:
  title: CRM Interactions API
  version: 1.0.0
  description: Communication tracking endpoints for the CRM system

paths:
  /interactions:
    get:
      summary: List interactions
      description: Retrieve a list of all interactions
      parameters:
        - name: company_id
          in: query
          schema:
            type: string
            format: uuid
          description: Filter by company ID
        - name: contact_id
          in: query
          schema:
            type: string
            format: uuid
          description: Filter by contact ID
        - name: interaction_type
          in: query
          schema:
            type: string
          description: Filter by interaction type
        - name: from_date
          in: query
          schema:
            type: string
            format: date-time
          description: Filter interactions from this date
        - name: to_date
          in: query
          schema:
            type: string
            format: date-time
          description: Filter interactions to this date
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
          description: Number of interactions to return
        - name: offset
          in: query
          schema:
            type: integer
            minimum: 0
            default: 0
          description: Number of interactions to skip
      responses:
        '200':
          description: List of interactions
          content:
            application/json:
              schema:
                type: object
                properties:
                  interactions:
                    type: array
                    items:
                      $ref: '#/components/schemas/InteractionWithDetails'
                  total_count:
                    type: integer
                  has_more:
                    type: boolean

    post:
      summary: Create interaction
      description: Create a new interaction record
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InteractionCreate'
      responses:
        '201':
          description: Interaction created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Interaction'
        '400':
          description: Invalid input

  /interactions/{id}:
    get:
      summary: Get interaction
      description: Retrieve a specific interaction by ID
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Interaction details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InteractionDetails'
        '404':
          description: Interaction not found

    put:
      summary: Update interaction
      description: Update an existing interaction
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InteractionUpdate'
      responses:
        '200':
          description: Interaction updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Interaction'
        '404':
          description: Interaction not found

    delete:
      summary: Delete interaction
      description: Delete an interaction record
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '204':
          description: Interaction deleted successfully
        '404':
          description: Interaction not found

  /companies/{company_id}/interactions:
    get:
      summary: Get company interactions
      description: Retrieve all interactions for a specific company
      parameters:
        - name: company_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: offset
          in: query
          schema:
            type: integer
            minimum: 0
            default: 0
      responses:
        '200':
          description: List of company interactions
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Interaction'

    post:
      summary: Create company interaction
      description: Create a new interaction for a company
      parameters:
        - name: company_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CompanyInteractionCreate'
      responses:
        '201':
          description: Company interaction created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Interaction'

  /contacts/{contact_id}/interactions:
    get:
      summary: Get contact interactions
      description: Retrieve all interactions for a specific contact
      parameters:
        - name: contact_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: offset
          in: query
          schema:
            type: integer
            minimum: 0
            default: 0
      responses:
        '200':
          description: List of contact interactions
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Interaction'

    post:
      summary: Create contact interaction
      description: Create a new interaction for a contact
      parameters:
        - name: contact_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ContactInteractionCreate'
      responses:
        '201':
          description: Contact interaction created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Interaction'

  /interaction-types:
    get:
      summary: Get interaction types
      description: Retrieve all available interaction types
      responses:
        '200':
          description: List of interaction types
          content:
            application/json:
              schema:
                type: array
                items:
                  type: string
                example: ["email", "phone", "meeting", "demo", "proposal", "follow-up"]

components:
  schemas:
    Interaction:
      type: object
      properties:
        id:
          type: string
          format: uuid
        company_id:
          type: string
          format: uuid
          nullable: true
        contact_id:
          type: string
          format: uuid
          nullable: true
        interaction_type:
          type: string
          enum: ["email", "phone", "meeting", "demo", "proposal", "follow-up", "other"]
        notes:
          type: string
        interaction_datetime:
          type: string
          format: date-time
        created_by:
          type: string
          format: uuid
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    InteractionWithDetails:
      allOf:
        - $ref: '#/components/schemas/Interaction'
        - type: object
          properties:
            company:
              type: object
              properties:
                id:
                  type: string
                  format: uuid
                name:
                  type: string
              nullable: true
            contact:
              type: object
              properties:
                id:
                  type: string
                  format: uuid
                first_name:
                  type: string
                last_name:
                  type: string
                email:
                  type: string
              nullable: true
            created_by_user:
              type: object
              properties:
                id:
                  type: string
                  format: uuid
                full_name:
                  type: string
                email:
                  type: string

    InteractionDetails:
      allOf:
        - $ref: '#/components/schemas/Interaction'
        - type: object
          properties:
            company:
              type: object
              properties:
                id:
                  type: string
                  format: uuid
                name:
                  type: string
                website:
                  type: string
                phone:
                  type: string
              nullable: true
            contact:
              type: object
              properties:
                id:
                  type: string
                  format: uuid
                first_name:
                  type: string
                last_name:
                  type: string
                email:
                  type: string
                phone:
                  type: string
                job_title:
                  type: string
              nullable: true

    InteractionCreate:
      type: object
      properties:
        company_id:
          type: string
          format: uuid
        contact_id:
          type: string
          format: uuid
        interaction_type:
          type: string
          enum: ["email", "phone", "meeting", "demo", "proposal", "follow-up", "other"]
        notes:
          type: string
          minLength: 1
        interaction_datetime:
          type: string
          format: date-time
      required:
        - interaction_type
        - notes
        - interaction_datetime
      anyOf:
        - required: ["company_id"]
        - required: ["contact_id"]

    CompanyInteractionCreate:
      type: object
      properties:
        interaction_type:
          type: string
          enum: ["email", "phone", "meeting", "demo", "proposal", "follow-up", "other"]
        notes:
          type: string
          minLength: 1
        interaction_datetime:
          type: string
          format: date-time
        contact_id:
          type: string
          format: uuid
          description: Optional contact within the company
      required:
        - interaction_type
        - notes
        - interaction_datetime

    ContactInteractionCreate:
      type: object
      properties:
        interaction_type:
          type: string
          enum: ["email", "phone", "meeting", "demo", "proposal", "follow-up", "other"]
        notes:
          type: string
          minLength: 1
        interaction_datetime:
          type: string
          format: date-time
      required:
        - interaction_type
        - notes
        - interaction_datetime

    InteractionUpdate:
      type: object
      properties:
        interaction_type:
          type: string
          enum: ["email", "phone", "meeting", "demo", "proposal", "follow-up", "other"]
        notes:
          type: string
          minLength: 1
        interaction_datetime:
          type: string
          format: date-time
        company_id:
          type: string
          format: uuid
        contact_id:
          type: string
          format: uuid