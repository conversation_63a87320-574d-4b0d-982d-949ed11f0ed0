# Upstream configuration for microservices
# This file is managed by Ansible - manual changes will be overwritten

# Gateway service upstream
upstream gateway_backend {
    least_conn;
    {% if deployment_strategy == 'blue-green' %}
    # Blue deployment
    {% if current_color == 'blue' or deployment_color == 'blue' %}
    server gateway-blue:{{ services.gateway.port }} max_fails=3 fail_timeout=30s;
    {% else %}
    # server gateway-blue:{{ services.gateway.port }} max_fails=3 fail_timeout=30s;
    {% endif %}
    
    # Green deployment
    {% if current_color == 'green' or deployment_color == 'green' %}
    server gateway-green:{{ services.gateway.port }} max_fails=3 fail_timeout=30s;
    {% else %}
    # server gateway-green:{{ services.gateway.port }} max_fails=3 fail_timeout=30s;
    {% endif %}
    {% else %}
    # Standard deployment
    server gateway:{{ services.gateway.port }} max_fails=3 fail_timeout=30s;
    {% endif %}
    
    keepalive 32;
}

# Auth service upstream
upstream auth_backend {
    least_conn;
    {% if deployment_strategy == 'blue-green' %}
    # Blue deployment
    {% if current_color == 'blue' or deployment_color == 'blue' %}
    server auth-blue:{{ services.auth.port }} max_fails=3 fail_timeout=30s;
    {% else %}
    # server auth-blue:{{ services.auth.port }} max_fails=3 fail_timeout=30s;
    {% endif %}
    
    # Green deployment
    {% if current_color == 'green' or deployment_color == 'green' %}
    server auth-green:{{ services.auth.port }} max_fails=3 fail_timeout=30s;
    {% else %}
    # server auth-green:{{ services.auth.port }} max_fails=3 fail_timeout=30s;
    {% endif %}
    {% else %}
    # Standard deployment
    server auth:{{ services.auth.port }} max_fails=3 fail_timeout=30s;
    {% endif %}
    
    keepalive 32;
}

# CRM Backend service upstream
upstream crm_backend {
    least_conn;
    {% if deployment_strategy == 'blue-green' %}
    # Blue deployment
    {% if current_color == 'blue' or deployment_color == 'blue' %}
    server crm_backend-blue:{{ services.crm_backend.port }} max_fails=3 fail_timeout=30s;
    {% else %}
    # server crm_backend-blue:{{ services.crm_backend.port }} max_fails=3 fail_timeout=30s;
    {% endif %}
    
    # Green deployment
    {% if current_color == 'green' or deployment_color == 'green' %}
    server crm_backend-green:{{ services.crm_backend.port }} max_fails=3 fail_timeout=30s;
    {% else %}
    # server crm_backend-green:{{ services.crm_backend.port }} max_fails=3 fail_timeout=30s;
    {% endif %}
    {% else %}
    # Standard deployment
    server crm_backend:{{ services.crm_backend.port }} max_fails=3 fail_timeout=30s;
    {% endif %}
    
    keepalive 32;
}