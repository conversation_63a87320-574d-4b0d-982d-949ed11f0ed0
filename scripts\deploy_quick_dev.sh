#!/bin/bash
# Quick deployment: migrate database and deploy web only
set -e

echo "🚀 Quick deployment: migrating database and deploying web..."

# Change to workspace directory
if [ -n "$BUILD_WORKSPACE_DIRECTORY" ]; then
    cd "$BUILD_WORKSPACE_DIRECTORY"
elif [ -n "$TEST_WORKSPACE" ]; then
    cd "$TEST_WORKSPACE"
else
    # Find the workspace root by looking for WORKSPACE file
    CURRENT_DIR="$PWD"
    while [ "$CURRENT_DIR" != "/" ]; do
        if [ -f "$CURRENT_DIR/WORKSPACE" ]; then
            cd "$CURRENT_DIR"
            break
        fi
        CURRENT_DIR="$(dirname "$CURRENT_DIR")"
    done
fi

echo "📊 Step 1: Running database migrations..."
bazel run //terraform:migrate_db_dev

echo "🌐 Step 2: Deploying web application..."
bazel run //terraform:deploy_web_dev

echo "✅ Quick deployment completed!"