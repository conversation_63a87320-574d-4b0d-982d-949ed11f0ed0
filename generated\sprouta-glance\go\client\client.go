// Package client provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.5.0 DO NOT EDIT.
package client

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/oapi-codegen/runtime"
	openapi_types "github.com/oapi-codegen/runtime/types"
)

const (
	BearerAuthScopes = "bearerAuth.Scopes"
)

// Defines values for AnalysisResponseStatus.
const (
	AnalysisResponseStatusCompleted  AnalysisResponseStatus = "completed"
	AnalysisResponseStatusFailed     AnalysisResponseStatus = "failed"
	AnalysisResponseStatusProcessing AnalysisResponseStatus = "processing"
)

// Defines values for CaseStudyDocumentType.
const (
	CaseStudyDocumentTypeDoc      CaseStudyDocumentType = "doc"
	CaseStudyDocumentTypeDocx     CaseStudyDocumentType = "docx"
	CaseStudyDocumentTypeMarkdown CaseStudyDocumentType = "markdown"
	CaseStudyDocumentTypePdf      CaseStudyDocumentType = "pdf"
)

// Defines values for CaseStudyCreateDocumentType.
const (
	CaseStudyCreateDocumentTypeDoc      CaseStudyCreateDocumentType = "doc"
	CaseStudyCreateDocumentTypeDocx     CaseStudyCreateDocumentType = "docx"
	CaseStudyCreateDocumentTypeMarkdown CaseStudyCreateDocumentType = "markdown"
	CaseStudyCreateDocumentTypePdf      CaseStudyCreateDocumentType = "pdf"
)

// Defines values for MeetingTranscriptStatus.
const (
	MeetingTranscriptStatusCompleted  MeetingTranscriptStatus = "completed"
	MeetingTranscriptStatusFailed     MeetingTranscriptStatus = "failed"
	MeetingTranscriptStatusPending    MeetingTranscriptStatus = "pending"
	MeetingTranscriptStatusProcessing MeetingTranscriptStatus = "processing"
)

// Defines values for MeetingTranscriptTranscriptFormat.
const (
	MeetingTranscriptTranscriptFormatDoc  MeetingTranscriptTranscriptFormat = "doc"
	MeetingTranscriptTranscriptFormatDocx MeetingTranscriptTranscriptFormat = "docx"
	MeetingTranscriptTranscriptFormatPdf  MeetingTranscriptTranscriptFormat = "pdf"
	MeetingTranscriptTranscriptFormatTxt  MeetingTranscriptTranscriptFormat = "txt"
	MeetingTranscriptTranscriptFormatVtt  MeetingTranscriptTranscriptFormat = "vtt"
)

// Defines values for MeetingTranscriptCreateTranscriptFormat.
const (
	MeetingTranscriptCreateTranscriptFormatDoc  MeetingTranscriptCreateTranscriptFormat = "doc"
	MeetingTranscriptCreateTranscriptFormatDocx MeetingTranscriptCreateTranscriptFormat = "docx"
	MeetingTranscriptCreateTranscriptFormatPdf  MeetingTranscriptCreateTranscriptFormat = "pdf"
	MeetingTranscriptCreateTranscriptFormatTxt  MeetingTranscriptCreateTranscriptFormat = "txt"
	MeetingTranscriptCreateTranscriptFormatVtt  MeetingTranscriptCreateTranscriptFormat = "vtt"
)

// Defines values for MeetingTranscriptUpdateStatus.
const (
	MeetingTranscriptUpdateStatusCompleted  MeetingTranscriptUpdateStatus = "completed"
	MeetingTranscriptUpdateStatusFailed     MeetingTranscriptUpdateStatus = "failed"
	MeetingTranscriptUpdateStatusPending    MeetingTranscriptUpdateStatus = "pending"
	MeetingTranscriptUpdateStatusProcessing MeetingTranscriptUpdateStatus = "processing"
)

// Defines values for TranscriptPreviewOriginalType.
const (
	TranscriptPreviewOriginalTypeDocx TranscriptPreviewOriginalType = "docx"
	TranscriptPreviewOriginalTypeMd   TranscriptPreviewOriginalType = "md"
	TranscriptPreviewOriginalTypePdf  TranscriptPreviewOriginalType = "pdf"
	TranscriptPreviewOriginalTypeVtt  TranscriptPreviewOriginalType = "vtt"
)

// Defines values for TranscriptPreviewStatus.
const (
	TranscriptPreviewStatusConverted  TranscriptPreviewStatus = "converted"
	TranscriptPreviewStatusFailed     TranscriptPreviewStatus = "failed"
	TranscriptPreviewStatusProcessing TranscriptPreviewStatus = "processing"
)

// Defines values for TranscriptUploadResponseOriginalType.
const (
	TranscriptUploadResponseOriginalTypeDocx TranscriptUploadResponseOriginalType = "docx"
	TranscriptUploadResponseOriginalTypeMd   TranscriptUploadResponseOriginalType = "md"
	TranscriptUploadResponseOriginalTypePdf  TranscriptUploadResponseOriginalType = "pdf"
	TranscriptUploadResponseOriginalTypeVtt  TranscriptUploadResponseOriginalType = "vtt"
)

// Defines values for TranscriptUploadResponseStatus.
const (
	TranscriptUploadResponseStatusConverted  TranscriptUploadResponseStatus = "converted"
	TranscriptUploadResponseStatusFailed     TranscriptUploadResponseStatus = "failed"
	TranscriptUploadResponseStatusProcessing TranscriptUploadResponseStatus = "processing"
)

// Defines values for GetAuthCallbackProviderParamsProvider.
const (
	GetAuthCallbackProviderParamsProviderGoogle GetAuthCallbackProviderParamsProvider = "google"
)

// Defines values for PostAuthSigninOauthJSONBodyProvider.
const (
	PostAuthSigninOauthJSONBodyProviderGoogle PostAuthSigninOauthJSONBodyProvider = "google"
)

// Defines values for ListMeetingTranscriptsParamsStatus.
const (
	ListMeetingTranscriptsParamsStatusCompleted  ListMeetingTranscriptsParamsStatus = "completed"
	ListMeetingTranscriptsParamsStatusFailed     ListMeetingTranscriptsParamsStatus = "failed"
	ListMeetingTranscriptsParamsStatusPending    ListMeetingTranscriptsParamsStatus = "pending"
	ListMeetingTranscriptsParamsStatusProcessing ListMeetingTranscriptsParamsStatus = "processing"
)

// AnalysisResponse defines model for AnalysisResponse.
type AnalysisResponse struct {
	Message *string                 `json:"message,omitempty"`
	Status  *AnalysisResponseStatus `json:"status,omitempty"`
}

// AnalysisResponseStatus defines model for AnalysisResponse.Status.
type AnalysisResponseStatus string

// AuthResponse defines model for AuthResponse.
type AuthResponse struct {
	AccessToken  *string `json:"access_token,omitempty"`
	ExpiresIn    *int    `json:"expires_in,omitempty"`
	RefreshToken *string `json:"refresh_token,omitempty"`
	TokenType    *string `json:"token_type,omitempty"`
	User         *User   `json:"user,omitempty"`
}

// CaseStudy defines model for CaseStudy.
type CaseStudy struct {
	CreatedAt        *time.Time             `json:"created_at,omitempty"`
	CreatedBy        *openapi_types.UUID    `json:"created_by,omitempty"`
	DocumentContent  *string                `json:"document_content,omitempty"`
	DocumentFilename *string                `json:"document_filename,omitempty"`
	DocumentType     *CaseStudyDocumentType `json:"document_type,omitempty"`
	Id               *openapi_types.UUID    `json:"id,omitempty"`
	RelatedPeople    *[]openapi_types.UUID  `json:"related_people,omitempty"`
	RelatedThemes    *[]openapi_types.UUID  `json:"related_themes,omitempty"`
	Title            *string                `json:"title,omitempty"`
	UpdatedAt        *time.Time             `json:"updated_at,omitempty"`
}

// CaseStudyDocumentType defines model for CaseStudy.DocumentType.
type CaseStudyDocumentType string

// CaseStudyCreate defines model for CaseStudyCreate.
type CaseStudyCreate struct {
	DocumentContent  *string                      `json:"document_content,omitempty"`
	DocumentFilename *string                      `json:"document_filename,omitempty"`
	DocumentType     *CaseStudyCreateDocumentType `json:"document_type,omitempty"`
	RelatedPeople    *[]openapi_types.UUID        `json:"related_people,omitempty"`
	RelatedThemes    *[]openapi_types.UUID        `json:"related_themes,omitempty"`
	Title            *string                      `json:"title,omitempty"`
}

// CaseStudyCreateDocumentType defines model for CaseStudyCreate.DocumentType.
type CaseStudyCreateDocumentType string

// CaseStudyUpdate defines model for CaseStudyUpdate.
type CaseStudyUpdate struct {
	DocumentContent *string               `json:"document_content,omitempty"`
	RelatedPeople   *[]openapi_types.UUID `json:"related_people,omitempty"`
	RelatedThemes   *[]openapi_types.UUID `json:"related_themes,omitempty"`
	Title           *string               `json:"title,omitempty"`
}

// Error defines model for Error.
type Error struct {
	Code    string                  `json:"code"`
	Details *map[string]interface{} `json:"details,omitempty"`
	Message string                  `json:"message"`
}

// MeetingTranscript defines model for MeetingTranscript.
type MeetingTranscript struct {
	Attendees          *[]openapi_types.UUID              `json:"attendees,omitempty"`
	CompanyInfo        *string                            `json:"company_info,omitempty"`
	CreatedAt          *time.Time                         `json:"created_at,omitempty"`
	CreatedBy          *openapi_types.UUID                `json:"created_by,omitempty"`
	Id                 *openapi_types.UUID                `json:"id,omitempty"`
	KeyInsights        *[]string                          `json:"key_insights,omitempty"`
	LinkedPeople       *[]openapi_types.UUID              `json:"linked_people,omitempty"`
	MeetingName        *string                            `json:"meeting_name,omitempty"`
	Status             *MeetingTranscriptStatus           `json:"status,omitempty"`
	Summary            *string                            `json:"summary,omitempty"`
	TranscriptContent  *string                            `json:"transcript_content,omitempty"`
	TranscriptFilename *string                            `json:"transcript_filename,omitempty"`
	TranscriptFormat   *MeetingTranscriptTranscriptFormat `json:"transcript_format,omitempty"`
	UpdatedAt          *time.Time                         `json:"updated_at,omitempty"`
}

// MeetingTranscriptStatus defines model for MeetingTranscript.Status.
type MeetingTranscriptStatus string

// MeetingTranscriptTranscriptFormat defines model for MeetingTranscript.TranscriptFormat.
type MeetingTranscriptTranscriptFormat string

// MeetingTranscriptCreate defines model for MeetingTranscriptCreate.
type MeetingTranscriptCreate struct {
	Attendees          *[]openapi_types.UUID                    `json:"attendees,omitempty"`
	CompanyInfo        *string                                  `json:"company_info,omitempty"`
	MeetingName        string                                   `json:"meeting_name"`
	TranscriptContent  *string                                  `json:"transcript_content,omitempty"`
	TranscriptFilename *string                                  `json:"transcript_filename,omitempty"`
	TranscriptFormat   *MeetingTranscriptCreateTranscriptFormat `json:"transcript_format,omitempty"`
}

// MeetingTranscriptCreateTranscriptFormat defines model for MeetingTranscriptCreate.TranscriptFormat.
type MeetingTranscriptCreateTranscriptFormat string

// MeetingTranscriptUpdate defines model for MeetingTranscriptUpdate.
type MeetingTranscriptUpdate struct {
	Attendees    *[]openapi_types.UUID          `json:"attendees,omitempty"`
	CompanyInfo  *string                        `json:"company_info,omitempty"`
	KeyInsights  *[]string                      `json:"key_insights,omitempty"`
	LinkedPeople *[]openapi_types.UUID          `json:"linked_people,omitempty"`
	MeetingName  *string                        `json:"meeting_name,omitempty"`
	Status       *MeetingTranscriptUpdateStatus `json:"status,omitempty"`
	Summary      *string                        `json:"summary,omitempty"`
}

// MeetingTranscriptUpdateStatus defines model for MeetingTranscriptUpdate.Status.
type MeetingTranscriptUpdateStatus string

// Person defines model for Person.
type Person struct {
	CompanyEmail *openapi_types.Email `json:"company_email,omitempty"`
	CompanyTitle *string              `json:"company_title,omitempty"`
	CreatedAt    *time.Time           `json:"created_at,omitempty"`
	CreatedBy    *openapi_types.UUID  `json:"created_by,omitempty"`
	Id           *openapi_types.UUID  `json:"id,omitempty"`
	Name         *string              `json:"name,omitempty"`
	Notes        *string              `json:"notes,omitempty"`
	PhoneNumber  *string              `json:"phone_number,omitempty"`
	Specialties  *[]string            `json:"specialties,omitempty"`
	UpdatedAt    *time.Time           `json:"updated_at,omitempty"`
}

// PersonCreate defines model for PersonCreate.
type PersonCreate struct {
	CompanyEmail *openapi_types.Email `json:"company_email,omitempty"`
	CompanyTitle *string              `json:"company_title,omitempty"`
	Name         string               `json:"name"`
	Notes        *string              `json:"notes,omitempty"`
	PhoneNumber  *string              `json:"phone_number,omitempty"`
	Specialties  *[]string            `json:"specialties,omitempty"`
}

// PersonUpdate defines model for PersonUpdate.
type PersonUpdate struct {
	CompanyEmail *openapi_types.Email `json:"company_email,omitempty"`
	CompanyTitle *string              `json:"company_title,omitempty"`
	Name         *string              `json:"name,omitempty"`
	Notes        *string              `json:"notes,omitempty"`
	PhoneNumber  *string              `json:"phone_number,omitempty"`
	Specialties  *[]string            `json:"specialties,omitempty"`
}

// Session defines model for Session.
type Session struct {
	AccessToken  *string `json:"access_token,omitempty"`
	ExpiresIn    *int    `json:"expires_in,omitempty"`
	RefreshToken *string `json:"refresh_token,omitempty"`
	TokenType    *string `json:"token_type,omitempty"`
	User         *User   `json:"user,omitempty"`
}

// Theme defines model for Theme.
type Theme struct {
	CreatedAt   *time.Time          `json:"created_at,omitempty"`
	CreatedBy   *openapi_types.UUID `json:"created_by,omitempty"`
	Description *string             `json:"description,omitempty"`
	Id          *openapi_types.UUID `json:"id,omitempty"`
	LongName    *string             `json:"long_name,omitempty"`
	ShortName   *string             `json:"short_name,omitempty"`
	Subcategory *string             `json:"subcategory,omitempty"`
	Title       *string             `json:"title,omitempty"`
	UpdatedAt   *time.Time          `json:"updated_at,omitempty"`
}

// ThemeCreate defines model for ThemeCreate.
type ThemeCreate struct {
	Description *string `json:"description,omitempty"`
	LongName    *string `json:"long_name,omitempty"`
	ShortName   string  `json:"short_name"`
	Subcategory *string `json:"subcategory,omitempty"`
	Title       string  `json:"title"`
}

// ThemeUpdate defines model for ThemeUpdate.
type ThemeUpdate struct {
	Description *string `json:"description,omitempty"`
	LongName    *string `json:"long_name,omitempty"`
	ShortName   *string `json:"short_name,omitempty"`
	Subcategory *string `json:"subcategory,omitempty"`
	Title       *string `json:"title,omitempty"`
}

// TitleGenerationResponse defines model for TitleGenerationResponse.
type TitleGenerationResponse struct {
	Message *string `json:"message,omitempty"`
	Title   *string `json:"title,omitempty"`
}

// TranscriptPrefillResponse defines model for TranscriptPrefillResponse.
type TranscriptPrefillResponse struct {
	Message              *string   `json:"message,omitempty"`
	SuggestedAttendees   *[]string `json:"suggested_attendees,omitempty"`
	SuggestedCompanyInfo *string   `json:"suggested_company_info,omitempty"`
	SuggestedMeetingName *string   `json:"suggested_meeting_name,omitempty"`
}

// TranscriptPreview defines model for TranscriptPreview.
type TranscriptPreview struct {
	// ConvertedContent Converted markdown content
	ConvertedContent *string                        `json:"convertedContent,omitempty"`
	CreatedAt        *time.Time                     `json:"createdAt,omitempty"`
	ExpiresAt        *time.Time                     `json:"expiresAt,omitempty"`
	Filename         *string                        `json:"filename,omitempty"`
	Id               *openapi_types.UUID            `json:"id,omitempty"`
	MeetingName      *string                        `json:"meetingName"`
	OriginalType     *TranscriptPreviewOriginalType `json:"originalType,omitempty"`
	Status           *TranscriptPreviewStatus       `json:"status,omitempty"`
}

// TranscriptPreviewOriginalType defines model for TranscriptPreview.OriginalType.
type TranscriptPreviewOriginalType string

// TranscriptPreviewStatus defines model for TranscriptPreview.Status.
type TranscriptPreviewStatus string

// TranscriptUploadResponse defines model for TranscriptUploadResponse.
type TranscriptUploadResponse struct {
	// ConvertedContent Converted markdown content
	ConvertedContent *string                               `json:"convertedContent,omitempty"`
	CreatedAt        *time.Time                            `json:"createdAt,omitempty"`
	ExpiresAt        *time.Time                            `json:"expiresAt,omitempty"`
	Filename         *string                               `json:"filename,omitempty"`
	Id               *openapi_types.UUID                   `json:"id,omitempty"`
	MeetingName      *string                               `json:"meetingName"`
	OriginalType     *TranscriptUploadResponseOriginalType `json:"originalType,omitempty"`
	PreviewUrl       *string                               `json:"previewUrl,omitempty"`
	Status           *TranscriptUploadResponseStatus       `json:"status,omitempty"`
}

// TranscriptUploadResponseOriginalType defines model for TranscriptUploadResponse.OriginalType.
type TranscriptUploadResponseOriginalType string

// TranscriptUploadResponseStatus defines model for TranscriptUploadResponse.Status.
type TranscriptUploadResponseStatus string

// User defines model for User.
type User struct {
	CreatedAt        *time.Time           `json:"created_at,omitempty"`
	Email            *openapi_types.Email `json:"email,omitempty"`
	EmailConfirmedAt *time.Time           `json:"email_confirmed_at,omitempty"`
	Id               *openapi_types.UUID  `json:"id,omitempty"`
	UpdatedAt        *time.Time           `json:"updated_at,omitempty"`
}

// UserProfile defines model for UserProfile.
type UserProfile struct {
	AvatarUrl *string              `json:"avatar_url,omitempty"`
	CreatedAt *time.Time           `json:"created_at,omitempty"`
	Email     *openapi_types.Email `json:"email,omitempty"`
	Id        *openapi_types.UUID  `json:"id,omitempty"`
	Name      *string              `json:"name,omitempty"`
	UpdatedAt *time.Time           `json:"updated_at,omitempty"`
}

// UserProfileUpdate defines model for UserProfileUpdate.
type UserProfileUpdate struct {
	AvatarUrl *string `json:"avatar_url,omitempty"`
	Name      *string `json:"name,omitempty"`
}

// GetAuthCallbackProviderParams defines parameters for GetAuthCallbackProvider.
type GetAuthCallbackProviderParams struct {
	Code  string  `form:"code" json:"code"`
	State string  `form:"state" json:"state"`
	Error *string `form:"error,omitempty" json:"error,omitempty"`
}

// GetAuthCallbackProviderParamsProvider defines parameters for GetAuthCallbackProvider.
type GetAuthCallbackProviderParamsProvider string

// PostAuthSigninJSONBody defines parameters for PostAuthSignin.
type PostAuthSigninJSONBody struct {
	Email    openapi_types.Email `json:"email"`
	Password string              `json:"password"`
}

// PostAuthSigninOauthJSONBody defines parameters for PostAuthSigninOauth.
type PostAuthSigninOauthJSONBody struct {
	Provider   PostAuthSigninOauthJSONBodyProvider `json:"provider"`
	RedirectTo *string                             `json:"redirectTo,omitempty"`
}

// PostAuthSigninOauthJSONBodyProvider defines parameters for PostAuthSigninOauth.
type PostAuthSigninOauthJSONBodyProvider string

// ListMeetingTranscriptsParams defines parameters for ListMeetingTranscripts.
type ListMeetingTranscriptsParams struct {
	// Status Filter by transcript status
	Status *ListMeetingTranscriptsParamsStatus `form:"status,omitempty" json:"status,omitempty"`

	// Limit Maximum number of transcripts to return
	Limit *int `form:"limit,omitempty" json:"limit,omitempty"`

	// Offset Number of transcripts to skip
	Offset *int `form:"offset,omitempty" json:"offset,omitempty"`
}

// ListMeetingTranscriptsParamsStatus defines parameters for ListMeetingTranscripts.
type ListMeetingTranscriptsParamsStatus string

// UploadTranscriptMultipartBody defines parameters for UploadTranscript.
type UploadTranscriptMultipartBody struct {
	// File Transcript file (PDF, DOCX, MD, VTT)
	File openapi_types.File `json:"file"`

	// MeetingName Optional meeting name
	MeetingName *string `json:"meeting_name,omitempty"`
}

// PostAuthSigninJSONRequestBody defines body for PostAuthSignin for application/json ContentType.
type PostAuthSigninJSONRequestBody PostAuthSigninJSONBody

// PostAuthSigninOauthJSONRequestBody defines body for PostAuthSigninOauth for application/json ContentType.
type PostAuthSigninOauthJSONRequestBody PostAuthSigninOauthJSONBody

// CreateCaseStudyJSONRequestBody defines body for CreateCaseStudy for application/json ContentType.
type CreateCaseStudyJSONRequestBody = CaseStudyCreate

// UpdateCaseStudyJSONRequestBody defines body for UpdateCaseStudy for application/json ContentType.
type UpdateCaseStudyJSONRequestBody = CaseStudyUpdate

// CreateMeetingTranscriptJSONRequestBody defines body for CreateMeetingTranscript for application/json ContentType.
type CreateMeetingTranscriptJSONRequestBody = MeetingTranscriptCreate

// UpdateMeetingTranscriptJSONRequestBody defines body for UpdateMeetingTranscript for application/json ContentType.
type UpdateMeetingTranscriptJSONRequestBody = MeetingTranscriptUpdate

// CreatePersonJSONRequestBody defines body for CreatePerson for application/json ContentType.
type CreatePersonJSONRequestBody = PersonCreate

// UpdatePersonJSONRequestBody defines body for UpdatePerson for application/json ContentType.
type UpdatePersonJSONRequestBody = PersonUpdate

// CreateThemeJSONRequestBody defines body for CreateTheme for application/json ContentType.
type CreateThemeJSONRequestBody = ThemeCreate

// UpdateThemeJSONRequestBody defines body for UpdateTheme for application/json ContentType.
type UpdateThemeJSONRequestBody = ThemeUpdate

// UploadTranscriptMultipartRequestBody defines body for UploadTranscript for multipart/form-data ContentType.
type UploadTranscriptMultipartRequestBody UploadTranscriptMultipartBody

// PutUsersProfileJSONRequestBody defines body for PutUsersProfile for application/json ContentType.
type PutUsersProfileJSONRequestBody = UserProfileUpdate

// RequestEditorFn  is the function signature for the RequestEditor callback function
type RequestEditorFn func(ctx context.Context, req *http.Request) error

// Doer performs HTTP requests.
//
// The standard http.Client implements this interface.
type HttpRequestDoer interface {
	Do(req *http.Request) (*http.Response, error)
}

// Client which conforms to the OpenAPI3 specification for this service.
type Client struct {
	// The endpoint of the server conforming to this interface, with scheme,
	// https://api.deepmap.com for example. This can contain a path relative
	// to the server, such as https://api.deepmap.com/dev-test, and all the
	// paths in the swagger spec will be appended to the server.
	Server string

	// Doer for performing requests, typically a *http.Client with any
	// customized settings, such as certificate chains.
	Client HttpRequestDoer

	// A list of callbacks for modifying requests which are generated before sending over
	// the network.
	RequestEditors []RequestEditorFn
}

// ClientOption allows setting custom parameters during construction
type ClientOption func(*Client) error

// Creates a new Client, with reasonable defaults
func NewClient(server string, opts ...ClientOption) (*Client, error) {
	// create a client with sane default values
	client := Client{
		Server: server,
	}
	// mutate client and add all optional params
	for _, o := range opts {
		if err := o(&client); err != nil {
			return nil, err
		}
	}
	// ensure the server URL always has a trailing slash
	if !strings.HasSuffix(client.Server, "/") {
		client.Server += "/"
	}
	// create httpClient, if not already present
	if client.Client == nil {
		client.Client = &http.Client{}
	}
	return &client, nil
}

// WithHTTPClient allows overriding the default Doer, which is
// automatically created using http.Client. This is useful for tests.
func WithHTTPClient(doer HttpRequestDoer) ClientOption {
	return func(c *Client) error {
		c.Client = doer
		return nil
	}
}

// WithRequestEditorFn allows setting up a callback function, which will be
// called right before sending the request. This can be used to mutate the request.
func WithRequestEditorFn(fn RequestEditorFn) ClientOption {
	return func(c *Client) error {
		c.RequestEditors = append(c.RequestEditors, fn)
		return nil
	}
}

// The interface specification for the client above.
type ClientInterface interface {
	// GetAuthCallbackProvider request
	GetAuthCallbackProvider(ctx context.Context, provider GetAuthCallbackProviderParamsProvider, params *GetAuthCallbackProviderParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetAuthSession request
	GetAuthSession(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostAuthSigninWithBody request with any body
	PostAuthSigninWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostAuthSignin(ctx context.Context, body PostAuthSigninJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostAuthSigninOauthWithBody request with any body
	PostAuthSigninOauthWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostAuthSigninOauth(ctx context.Context, body PostAuthSigninOauthJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostAuthSignout request
	PostAuthSignout(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetAuthUser request
	GetAuthUser(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// ListCaseStudies request
	ListCaseStudies(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// CreateCaseStudyWithBody request with any body
	CreateCaseStudyWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	CreateCaseStudy(ctx context.Context, body CreateCaseStudyJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// DeleteCaseStudy request
	DeleteCaseStudy(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetCaseStudyById request
	GetCaseStudyById(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// UpdateCaseStudyWithBody request with any body
	UpdateCaseStudyWithBody(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	UpdateCaseStudy(ctx context.Context, id openapi_types.UUID, body UpdateCaseStudyJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GenerateCaseStudyTitle request
	GenerateCaseStudyTitle(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// ListMeetingTranscripts request
	ListMeetingTranscripts(ctx context.Context, params *ListMeetingTranscriptsParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// CreateMeetingTranscriptWithBody request with any body
	CreateMeetingTranscriptWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	CreateMeetingTranscript(ctx context.Context, body CreateMeetingTranscriptJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// DeleteMeetingTranscript request
	DeleteMeetingTranscript(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetMeetingTranscriptById request
	GetMeetingTranscriptById(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// UpdateMeetingTranscriptWithBody request with any body
	UpdateMeetingTranscriptWithBody(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	UpdateMeetingTranscript(ctx context.Context, id openapi_types.UUID, body UpdateMeetingTranscriptJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// AnalyzeMeetingTranscript request
	AnalyzeMeetingTranscript(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PrefillMeetingTranscript request
	PrefillMeetingTranscript(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// ListPeople request
	ListPeople(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// CreatePersonWithBody request with any body
	CreatePersonWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	CreatePerson(ctx context.Context, body CreatePersonJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// DeletePerson request
	DeletePerson(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetPersonById request
	GetPersonById(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// UpdatePersonWithBody request with any body
	UpdatePersonWithBody(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	UpdatePerson(ctx context.Context, id openapi_types.UUID, body UpdatePersonJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// ListThemes request
	ListThemes(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// CreateThemeWithBody request with any body
	CreateThemeWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	CreateTheme(ctx context.Context, body CreateThemeJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// DeleteTheme request
	DeleteTheme(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetThemeById request
	GetThemeById(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// UpdateThemeWithBody request with any body
	UpdateThemeWithBody(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	UpdateTheme(ctx context.Context, id openapi_types.UUID, body UpdateThemeJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// DeleteTranscriptPreview request
	DeleteTranscriptPreview(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetTranscriptPreview request
	GetTranscriptPreview(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// UploadTranscriptWithBody request with any body
	UploadTranscriptWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetUsersProfile request
	GetUsersProfile(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PutUsersProfileWithBody request with any body
	PutUsersProfileWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PutUsersProfile(ctx context.Context, body PutUsersProfileJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)
}

func (c *Client) GetAuthCallbackProvider(ctx context.Context, provider GetAuthCallbackProviderParamsProvider, params *GetAuthCallbackProviderParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetAuthCallbackProviderRequest(c.Server, provider, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetAuthSession(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetAuthSessionRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostAuthSigninWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostAuthSigninRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostAuthSignin(ctx context.Context, body PostAuthSigninJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostAuthSigninRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostAuthSigninOauthWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostAuthSigninOauthRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostAuthSigninOauth(ctx context.Context, body PostAuthSigninOauthJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostAuthSigninOauthRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostAuthSignout(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostAuthSignoutRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetAuthUser(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetAuthUserRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) ListCaseStudies(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewListCaseStudiesRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) CreateCaseStudyWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewCreateCaseStudyRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) CreateCaseStudy(ctx context.Context, body CreateCaseStudyJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewCreateCaseStudyRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeleteCaseStudy(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteCaseStudyRequest(c.Server, id)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetCaseStudyById(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetCaseStudyByIdRequest(c.Server, id)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) UpdateCaseStudyWithBody(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewUpdateCaseStudyRequestWithBody(c.Server, id, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) UpdateCaseStudy(ctx context.Context, id openapi_types.UUID, body UpdateCaseStudyJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewUpdateCaseStudyRequest(c.Server, id, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GenerateCaseStudyTitle(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGenerateCaseStudyTitleRequest(c.Server, id)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) ListMeetingTranscripts(ctx context.Context, params *ListMeetingTranscriptsParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewListMeetingTranscriptsRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) CreateMeetingTranscriptWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewCreateMeetingTranscriptRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) CreateMeetingTranscript(ctx context.Context, body CreateMeetingTranscriptJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewCreateMeetingTranscriptRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeleteMeetingTranscript(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteMeetingTranscriptRequest(c.Server, id)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetMeetingTranscriptById(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetMeetingTranscriptByIdRequest(c.Server, id)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) UpdateMeetingTranscriptWithBody(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewUpdateMeetingTranscriptRequestWithBody(c.Server, id, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) UpdateMeetingTranscript(ctx context.Context, id openapi_types.UUID, body UpdateMeetingTranscriptJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewUpdateMeetingTranscriptRequest(c.Server, id, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) AnalyzeMeetingTranscript(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewAnalyzeMeetingTranscriptRequest(c.Server, id)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PrefillMeetingTranscript(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPrefillMeetingTranscriptRequest(c.Server, id)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) ListPeople(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewListPeopleRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) CreatePersonWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewCreatePersonRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) CreatePerson(ctx context.Context, body CreatePersonJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewCreatePersonRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeletePerson(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeletePersonRequest(c.Server, id)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetPersonById(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetPersonByIdRequest(c.Server, id)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) UpdatePersonWithBody(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewUpdatePersonRequestWithBody(c.Server, id, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) UpdatePerson(ctx context.Context, id openapi_types.UUID, body UpdatePersonJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewUpdatePersonRequest(c.Server, id, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) ListThemes(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewListThemesRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) CreateThemeWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewCreateThemeRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) CreateTheme(ctx context.Context, body CreateThemeJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewCreateThemeRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeleteTheme(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteThemeRequest(c.Server, id)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetThemeById(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetThemeByIdRequest(c.Server, id)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) UpdateThemeWithBody(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewUpdateThemeRequestWithBody(c.Server, id, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) UpdateTheme(ctx context.Context, id openapi_types.UUID, body UpdateThemeJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewUpdateThemeRequest(c.Server, id, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeleteTranscriptPreview(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteTranscriptPreviewRequest(c.Server, id)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetTranscriptPreview(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetTranscriptPreviewRequest(c.Server, id)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) UploadTranscriptWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewUploadTranscriptRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetUsersProfile(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetUsersProfileRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutUsersProfileWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutUsersProfileRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutUsersProfile(ctx context.Context, body PutUsersProfileJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutUsersProfileRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

// NewGetAuthCallbackProviderRequest generates requests for GetAuthCallbackProvider
func NewGetAuthCallbackProviderRequest(server string, provider GetAuthCallbackProviderParamsProvider, params *GetAuthCallbackProviderParams) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "provider", runtime.ParamLocationPath, provider)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/auth/callback/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "code", runtime.ParamLocationQuery, params.Code); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "state", runtime.ParamLocationQuery, params.State); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

		if params.Error != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "error", runtime.ParamLocationQuery, *params.Error); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetAuthSessionRequest generates requests for GetAuthSession
func NewGetAuthSessionRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/auth/session")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostAuthSigninRequest calls the generic PostAuthSignin builder with application/json body
func NewPostAuthSigninRequest(server string, body PostAuthSigninJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostAuthSigninRequestWithBody(server, "application/json", bodyReader)
}

// NewPostAuthSigninRequestWithBody generates requests for PostAuthSignin with any type of body
func NewPostAuthSigninRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/auth/signin")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostAuthSigninOauthRequest calls the generic PostAuthSigninOauth builder with application/json body
func NewPostAuthSigninOauthRequest(server string, body PostAuthSigninOauthJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostAuthSigninOauthRequestWithBody(server, "application/json", bodyReader)
}

// NewPostAuthSigninOauthRequestWithBody generates requests for PostAuthSigninOauth with any type of body
func NewPostAuthSigninOauthRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/auth/signin/oauth")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostAuthSignoutRequest generates requests for PostAuthSignout
func NewPostAuthSignoutRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/auth/signout")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetAuthUserRequest generates requests for GetAuthUser
func NewGetAuthUserRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/auth/user")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewListCaseStudiesRequest generates requests for ListCaseStudies
func NewListCaseStudiesRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/case-studies")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewCreateCaseStudyRequest calls the generic CreateCaseStudy builder with application/json body
func NewCreateCaseStudyRequest(server string, body CreateCaseStudyJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewCreateCaseStudyRequestWithBody(server, "application/json", bodyReader)
}

// NewCreateCaseStudyRequestWithBody generates requests for CreateCaseStudy with any type of body
func NewCreateCaseStudyRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/case-studies")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewDeleteCaseStudyRequest generates requests for DeleteCaseStudy
func NewDeleteCaseStudyRequest(server string, id openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/case-studies/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetCaseStudyByIdRequest generates requests for GetCaseStudyById
func NewGetCaseStudyByIdRequest(server string, id openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/case-studies/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewUpdateCaseStudyRequest calls the generic UpdateCaseStudy builder with application/json body
func NewUpdateCaseStudyRequest(server string, id openapi_types.UUID, body UpdateCaseStudyJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewUpdateCaseStudyRequestWithBody(server, id, "application/json", bodyReader)
}

// NewUpdateCaseStudyRequestWithBody generates requests for UpdateCaseStudy with any type of body
func NewUpdateCaseStudyRequestWithBody(server string, id openapi_types.UUID, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/case-studies/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PUT", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGenerateCaseStudyTitleRequest generates requests for GenerateCaseStudyTitle
func NewGenerateCaseStudyTitleRequest(server string, id openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/case-studies/%s/generate-title", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewListMeetingTranscriptsRequest generates requests for ListMeetingTranscripts
func NewListMeetingTranscriptsRequest(server string, params *ListMeetingTranscriptsParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/meeting-transcripts")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.Status != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "status", runtime.ParamLocationQuery, *params.Status); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.Limit != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "limit", runtime.ParamLocationQuery, *params.Limit); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.Offset != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "offset", runtime.ParamLocationQuery, *params.Offset); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewCreateMeetingTranscriptRequest calls the generic CreateMeetingTranscript builder with application/json body
func NewCreateMeetingTranscriptRequest(server string, body CreateMeetingTranscriptJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewCreateMeetingTranscriptRequestWithBody(server, "application/json", bodyReader)
}

// NewCreateMeetingTranscriptRequestWithBody generates requests for CreateMeetingTranscript with any type of body
func NewCreateMeetingTranscriptRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/meeting-transcripts")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewDeleteMeetingTranscriptRequest generates requests for DeleteMeetingTranscript
func NewDeleteMeetingTranscriptRequest(server string, id openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/meeting-transcripts/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetMeetingTranscriptByIdRequest generates requests for GetMeetingTranscriptById
func NewGetMeetingTranscriptByIdRequest(server string, id openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/meeting-transcripts/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewUpdateMeetingTranscriptRequest calls the generic UpdateMeetingTranscript builder with application/json body
func NewUpdateMeetingTranscriptRequest(server string, id openapi_types.UUID, body UpdateMeetingTranscriptJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewUpdateMeetingTranscriptRequestWithBody(server, id, "application/json", bodyReader)
}

// NewUpdateMeetingTranscriptRequestWithBody generates requests for UpdateMeetingTranscript with any type of body
func NewUpdateMeetingTranscriptRequestWithBody(server string, id openapi_types.UUID, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/meeting-transcripts/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PUT", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewAnalyzeMeetingTranscriptRequest generates requests for AnalyzeMeetingTranscript
func NewAnalyzeMeetingTranscriptRequest(server string, id openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/meeting-transcripts/%s/analyze", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPrefillMeetingTranscriptRequest generates requests for PrefillMeetingTranscript
func NewPrefillMeetingTranscriptRequest(server string, id openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/meeting-transcripts/%s/prefill", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewListPeopleRequest generates requests for ListPeople
func NewListPeopleRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/people")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewCreatePersonRequest calls the generic CreatePerson builder with application/json body
func NewCreatePersonRequest(server string, body CreatePersonJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewCreatePersonRequestWithBody(server, "application/json", bodyReader)
}

// NewCreatePersonRequestWithBody generates requests for CreatePerson with any type of body
func NewCreatePersonRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/people")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewDeletePersonRequest generates requests for DeletePerson
func NewDeletePersonRequest(server string, id openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/people/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetPersonByIdRequest generates requests for GetPersonById
func NewGetPersonByIdRequest(server string, id openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/people/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewUpdatePersonRequest calls the generic UpdatePerson builder with application/json body
func NewUpdatePersonRequest(server string, id openapi_types.UUID, body UpdatePersonJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewUpdatePersonRequestWithBody(server, id, "application/json", bodyReader)
}

// NewUpdatePersonRequestWithBody generates requests for UpdatePerson with any type of body
func NewUpdatePersonRequestWithBody(server string, id openapi_types.UUID, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/people/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PUT", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewListThemesRequest generates requests for ListThemes
func NewListThemesRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/themes")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewCreateThemeRequest calls the generic CreateTheme builder with application/json body
func NewCreateThemeRequest(server string, body CreateThemeJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewCreateThemeRequestWithBody(server, "application/json", bodyReader)
}

// NewCreateThemeRequestWithBody generates requests for CreateTheme with any type of body
func NewCreateThemeRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/themes")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewDeleteThemeRequest generates requests for DeleteTheme
func NewDeleteThemeRequest(server string, id openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/themes/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetThemeByIdRequest generates requests for GetThemeById
func NewGetThemeByIdRequest(server string, id openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/themes/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewUpdateThemeRequest calls the generic UpdateTheme builder with application/json body
func NewUpdateThemeRequest(server string, id openapi_types.UUID, body UpdateThemeJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewUpdateThemeRequestWithBody(server, id, "application/json", bodyReader)
}

// NewUpdateThemeRequestWithBody generates requests for UpdateTheme with any type of body
func NewUpdateThemeRequestWithBody(server string, id openapi_types.UUID, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/themes/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PUT", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewDeleteTranscriptPreviewRequest generates requests for DeleteTranscriptPreview
func NewDeleteTranscriptPreviewRequest(server string, id openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/transcripts/preview/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetTranscriptPreviewRequest generates requests for GetTranscriptPreview
func NewGetTranscriptPreviewRequest(server string, id openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/transcripts/preview/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewUploadTranscriptRequestWithBody generates requests for UploadTranscript with any type of body
func NewUploadTranscriptRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/transcripts/upload")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetUsersProfileRequest generates requests for GetUsersProfile
func NewGetUsersProfileRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/users/profile")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPutUsersProfileRequest calls the generic PutUsersProfile builder with application/json body
func NewPutUsersProfileRequest(server string, body PutUsersProfileJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPutUsersProfileRequestWithBody(server, "application/json", bodyReader)
}

// NewPutUsersProfileRequestWithBody generates requests for PutUsersProfile with any type of body
func NewPutUsersProfileRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/users/profile")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PUT", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

func (c *Client) applyEditors(ctx context.Context, req *http.Request, additionalEditors []RequestEditorFn) error {
	for _, r := range c.RequestEditors {
		if err := r(ctx, req); err != nil {
			return err
		}
	}
	for _, r := range additionalEditors {
		if err := r(ctx, req); err != nil {
			return err
		}
	}
	return nil
}

// ClientWithResponses builds on ClientInterface to offer response payloads
type ClientWithResponses struct {
	ClientInterface
}

// NewClientWithResponses creates a new ClientWithResponses, which wraps
// Client with return type handling
func NewClientWithResponses(server string, opts ...ClientOption) (*ClientWithResponses, error) {
	client, err := NewClient(server, opts...)
	if err != nil {
		return nil, err
	}
	return &ClientWithResponses{client}, nil
}

// WithBaseURL overrides the baseURL.
func WithBaseURL(baseURL string) ClientOption {
	return func(c *Client) error {
		newBaseURL, err := url.Parse(baseURL)
		if err != nil {
			return err
		}
		c.Server = newBaseURL.String()
		return nil
	}
}

// ClientWithResponsesInterface is the interface specification for the client with responses above.
type ClientWithResponsesInterface interface {
	// GetAuthCallbackProviderWithResponse request
	GetAuthCallbackProviderWithResponse(ctx context.Context, provider GetAuthCallbackProviderParamsProvider, params *GetAuthCallbackProviderParams, reqEditors ...RequestEditorFn) (*GetAuthCallbackProviderResponse, error)

	// GetAuthSessionWithResponse request
	GetAuthSessionWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetAuthSessionResponse, error)

	// PostAuthSigninWithBodyWithResponse request with any body
	PostAuthSigninWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostAuthSigninResponse, error)

	PostAuthSigninWithResponse(ctx context.Context, body PostAuthSigninJSONRequestBody, reqEditors ...RequestEditorFn) (*PostAuthSigninResponse, error)

	// PostAuthSigninOauthWithBodyWithResponse request with any body
	PostAuthSigninOauthWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostAuthSigninOauthResponse, error)

	PostAuthSigninOauthWithResponse(ctx context.Context, body PostAuthSigninOauthJSONRequestBody, reqEditors ...RequestEditorFn) (*PostAuthSigninOauthResponse, error)

	// PostAuthSignoutWithResponse request
	PostAuthSignoutWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*PostAuthSignoutResponse, error)

	// GetAuthUserWithResponse request
	GetAuthUserWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetAuthUserResponse, error)

	// ListCaseStudiesWithResponse request
	ListCaseStudiesWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*ListCaseStudiesResponse, error)

	// CreateCaseStudyWithBodyWithResponse request with any body
	CreateCaseStudyWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*CreateCaseStudyResponse, error)

	CreateCaseStudyWithResponse(ctx context.Context, body CreateCaseStudyJSONRequestBody, reqEditors ...RequestEditorFn) (*CreateCaseStudyResponse, error)

	// DeleteCaseStudyWithResponse request
	DeleteCaseStudyWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteCaseStudyResponse, error)

	// GetCaseStudyByIdWithResponse request
	GetCaseStudyByIdWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*GetCaseStudyByIdResponse, error)

	// UpdateCaseStudyWithBodyWithResponse request with any body
	UpdateCaseStudyWithBodyWithResponse(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*UpdateCaseStudyResponse, error)

	UpdateCaseStudyWithResponse(ctx context.Context, id openapi_types.UUID, body UpdateCaseStudyJSONRequestBody, reqEditors ...RequestEditorFn) (*UpdateCaseStudyResponse, error)

	// GenerateCaseStudyTitleWithResponse request
	GenerateCaseStudyTitleWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*GenerateCaseStudyTitleResponse, error)

	// ListMeetingTranscriptsWithResponse request
	ListMeetingTranscriptsWithResponse(ctx context.Context, params *ListMeetingTranscriptsParams, reqEditors ...RequestEditorFn) (*ListMeetingTranscriptsResponse, error)

	// CreateMeetingTranscriptWithBodyWithResponse request with any body
	CreateMeetingTranscriptWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*CreateMeetingTranscriptResponse, error)

	CreateMeetingTranscriptWithResponse(ctx context.Context, body CreateMeetingTranscriptJSONRequestBody, reqEditors ...RequestEditorFn) (*CreateMeetingTranscriptResponse, error)

	// DeleteMeetingTranscriptWithResponse request
	DeleteMeetingTranscriptWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteMeetingTranscriptResponse, error)

	// GetMeetingTranscriptByIdWithResponse request
	GetMeetingTranscriptByIdWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*GetMeetingTranscriptByIdResponse, error)

	// UpdateMeetingTranscriptWithBodyWithResponse request with any body
	UpdateMeetingTranscriptWithBodyWithResponse(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*UpdateMeetingTranscriptResponse, error)

	UpdateMeetingTranscriptWithResponse(ctx context.Context, id openapi_types.UUID, body UpdateMeetingTranscriptJSONRequestBody, reqEditors ...RequestEditorFn) (*UpdateMeetingTranscriptResponse, error)

	// AnalyzeMeetingTranscriptWithResponse request
	AnalyzeMeetingTranscriptWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*AnalyzeMeetingTranscriptResponse, error)

	// PrefillMeetingTranscriptWithResponse request
	PrefillMeetingTranscriptWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*PrefillMeetingTranscriptResponse, error)

	// ListPeopleWithResponse request
	ListPeopleWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*ListPeopleResponse, error)

	// CreatePersonWithBodyWithResponse request with any body
	CreatePersonWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*CreatePersonResponse, error)

	CreatePersonWithResponse(ctx context.Context, body CreatePersonJSONRequestBody, reqEditors ...RequestEditorFn) (*CreatePersonResponse, error)

	// DeletePersonWithResponse request
	DeletePersonWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeletePersonResponse, error)

	// GetPersonByIdWithResponse request
	GetPersonByIdWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*GetPersonByIdResponse, error)

	// UpdatePersonWithBodyWithResponse request with any body
	UpdatePersonWithBodyWithResponse(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*UpdatePersonResponse, error)

	UpdatePersonWithResponse(ctx context.Context, id openapi_types.UUID, body UpdatePersonJSONRequestBody, reqEditors ...RequestEditorFn) (*UpdatePersonResponse, error)

	// ListThemesWithResponse request
	ListThemesWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*ListThemesResponse, error)

	// CreateThemeWithBodyWithResponse request with any body
	CreateThemeWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*CreateThemeResponse, error)

	CreateThemeWithResponse(ctx context.Context, body CreateThemeJSONRequestBody, reqEditors ...RequestEditorFn) (*CreateThemeResponse, error)

	// DeleteThemeWithResponse request
	DeleteThemeWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteThemeResponse, error)

	// GetThemeByIdWithResponse request
	GetThemeByIdWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*GetThemeByIdResponse, error)

	// UpdateThemeWithBodyWithResponse request with any body
	UpdateThemeWithBodyWithResponse(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*UpdateThemeResponse, error)

	UpdateThemeWithResponse(ctx context.Context, id openapi_types.UUID, body UpdateThemeJSONRequestBody, reqEditors ...RequestEditorFn) (*UpdateThemeResponse, error)

	// DeleteTranscriptPreviewWithResponse request
	DeleteTranscriptPreviewWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteTranscriptPreviewResponse, error)

	// GetTranscriptPreviewWithResponse request
	GetTranscriptPreviewWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*GetTranscriptPreviewResponse, error)

	// UploadTranscriptWithBodyWithResponse request with any body
	UploadTranscriptWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*UploadTranscriptResponse, error)

	// GetUsersProfileWithResponse request
	GetUsersProfileWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetUsersProfileResponse, error)

	// PutUsersProfileWithBodyWithResponse request with any body
	PutUsersProfileWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutUsersProfileResponse, error)

	PutUsersProfileWithResponse(ctx context.Context, body PutUsersProfileJSONRequestBody, reqEditors ...RequestEditorFn) (*PutUsersProfileResponse, error)
}

type GetAuthCallbackProviderResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *AuthResponse
	JSON400      *Error
	JSON401      *Error
}

// Status returns HTTPResponse.Status
func (r GetAuthCallbackProviderResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetAuthCallbackProviderResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetAuthSessionResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *Session
}

// Status returns HTTPResponse.Status
func (r GetAuthSessionResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetAuthSessionResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostAuthSigninResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *AuthResponse
}

// Status returns HTTPResponse.Status
func (r PostAuthSigninResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostAuthSigninResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostAuthSigninOauthResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *struct {
		Url *string `json:"url,omitempty"`
	}
}

// Status returns HTTPResponse.Status
func (r PostAuthSigninOauthResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostAuthSigninOauthResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostAuthSignoutResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r PostAuthSignoutResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostAuthSignoutResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetAuthUserResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *User
}

// Status returns HTTPResponse.Status
func (r GetAuthUserResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetAuthUserResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type ListCaseStudiesResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *[]CaseStudy
	JSON401      *Error
	JSON500      *Error
}

// Status returns HTTPResponse.Status
func (r ListCaseStudiesResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r ListCaseStudiesResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type CreateCaseStudyResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON201      *CaseStudy
	JSON400      *Error
	JSON401      *Error
	JSON500      *Error
}

// Status returns HTTPResponse.Status
func (r CreateCaseStudyResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r CreateCaseStudyResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type DeleteCaseStudyResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *struct {
		Message *string `json:"message,omitempty"`
	}
	JSON400 *Error
	JSON401 *Error
	JSON404 *Error
	JSON500 *Error
}

// Status returns HTTPResponse.Status
func (r DeleteCaseStudyResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeleteCaseStudyResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetCaseStudyByIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *CaseStudy
	JSON400      *Error
	JSON401      *Error
	JSON404      *Error
	JSON500      *Error
}

// Status returns HTTPResponse.Status
func (r GetCaseStudyByIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetCaseStudyByIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type UpdateCaseStudyResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *CaseStudy
	JSON400      *Error
	JSON401      *Error
	JSON404      *Error
	JSON500      *Error
}

// Status returns HTTPResponse.Status
func (r UpdateCaseStudyResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r UpdateCaseStudyResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GenerateCaseStudyTitleResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *TitleGenerationResponse
	JSON400      *Error
	JSON401      *Error
	JSON404      *Error
	JSON500      *Error
}

// Status returns HTTPResponse.Status
func (r GenerateCaseStudyTitleResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GenerateCaseStudyTitleResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type ListMeetingTranscriptsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *[]MeetingTranscript
	JSON401      *Error
	JSON500      *Error
}

// Status returns HTTPResponse.Status
func (r ListMeetingTranscriptsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r ListMeetingTranscriptsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type CreateMeetingTranscriptResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON201      *MeetingTranscript
	JSON400      *Error
	JSON401      *Error
	JSON500      *Error
}

// Status returns HTTPResponse.Status
func (r CreateMeetingTranscriptResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r CreateMeetingTranscriptResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type DeleteMeetingTranscriptResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *struct {
		Message *string `json:"message,omitempty"`
	}
	JSON400 *Error
	JSON401 *Error
	JSON404 *Error
	JSON500 *Error
}

// Status returns HTTPResponse.Status
func (r DeleteMeetingTranscriptResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeleteMeetingTranscriptResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetMeetingTranscriptByIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *MeetingTranscript
	JSON400      *Error
	JSON401      *Error
	JSON404      *Error
	JSON500      *Error
}

// Status returns HTTPResponse.Status
func (r GetMeetingTranscriptByIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetMeetingTranscriptByIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type UpdateMeetingTranscriptResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *MeetingTranscript
	JSON400      *Error
	JSON401      *Error
	JSON404      *Error
	JSON500      *Error
}

// Status returns HTTPResponse.Status
func (r UpdateMeetingTranscriptResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r UpdateMeetingTranscriptResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type AnalyzeMeetingTranscriptResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *AnalysisResponse
	JSON400      *Error
	JSON401      *Error
	JSON404      *Error
	JSON500      *Error
}

// Status returns HTTPResponse.Status
func (r AnalyzeMeetingTranscriptResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r AnalyzeMeetingTranscriptResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PrefillMeetingTranscriptResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *TranscriptPrefillResponse
	JSON400      *Error
	JSON401      *Error
	JSON404      *Error
	JSON500      *Error
}

// Status returns HTTPResponse.Status
func (r PrefillMeetingTranscriptResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PrefillMeetingTranscriptResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type ListPeopleResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *[]Person
	JSON401      *Error
	JSON500      *Error
}

// Status returns HTTPResponse.Status
func (r ListPeopleResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r ListPeopleResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type CreatePersonResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON201      *Person
	JSON400      *Error
	JSON401      *Error
	JSON500      *Error
}

// Status returns HTTPResponse.Status
func (r CreatePersonResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r CreatePersonResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type DeletePersonResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *struct {
		Message *string `json:"message,omitempty"`
	}
	JSON400 *Error
	JSON401 *Error
	JSON404 *Error
	JSON500 *Error
}

// Status returns HTTPResponse.Status
func (r DeletePersonResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeletePersonResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetPersonByIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *Person
	JSON400      *Error
	JSON401      *Error
	JSON404      *Error
	JSON500      *Error
}

// Status returns HTTPResponse.Status
func (r GetPersonByIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetPersonByIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type UpdatePersonResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *Person
	JSON400      *Error
	JSON401      *Error
	JSON404      *Error
	JSON500      *Error
}

// Status returns HTTPResponse.Status
func (r UpdatePersonResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r UpdatePersonResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type ListThemesResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *[]Theme
	JSON401      *Error
	JSON500      *Error
}

// Status returns HTTPResponse.Status
func (r ListThemesResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r ListThemesResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type CreateThemeResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON201      *Theme
	JSON400      *Error
	JSON401      *Error
	JSON500      *Error
}

// Status returns HTTPResponse.Status
func (r CreateThemeResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r CreateThemeResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type DeleteThemeResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *struct {
		Message *string `json:"message,omitempty"`
	}
	JSON400 *Error
	JSON401 *Error
	JSON404 *Error
	JSON500 *Error
}

// Status returns HTTPResponse.Status
func (r DeleteThemeResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeleteThemeResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetThemeByIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *Theme
	JSON400      *Error
	JSON401      *Error
	JSON404      *Error
	JSON500      *Error
}

// Status returns HTTPResponse.Status
func (r GetThemeByIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetThemeByIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type UpdateThemeResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *Theme
	JSON400      *Error
	JSON401      *Error
	JSON404      *Error
	JSON500      *Error
}

// Status returns HTTPResponse.Status
func (r UpdateThemeResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r UpdateThemeResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type DeleteTranscriptPreviewResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *struct {
		Message *string `json:"message,omitempty"`
	}
	JSON401 *Error
	JSON404 *Error
	JSON500 *Error
}

// Status returns HTTPResponse.Status
func (r DeleteTranscriptPreviewResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeleteTranscriptPreviewResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetTranscriptPreviewResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *TranscriptPreview
	JSON401      *Error
	JSON404      *Error
	JSON500      *Error
}

// Status returns HTTPResponse.Status
func (r GetTranscriptPreviewResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetTranscriptPreviewResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type UploadTranscriptResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *TranscriptUploadResponse
	JSON400      *Error
	JSON401      *Error
	JSON413      *Error
	JSON500      *Error
}

// Status returns HTTPResponse.Status
func (r UploadTranscriptResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r UploadTranscriptResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetUsersProfileResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *UserProfile
}

// Status returns HTTPResponse.Status
func (r GetUsersProfileResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetUsersProfileResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PutUsersProfileResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *UserProfile
}

// Status returns HTTPResponse.Status
func (r PutUsersProfileResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PutUsersProfileResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

// GetAuthCallbackProviderWithResponse request returning *GetAuthCallbackProviderResponse
func (c *ClientWithResponses) GetAuthCallbackProviderWithResponse(ctx context.Context, provider GetAuthCallbackProviderParamsProvider, params *GetAuthCallbackProviderParams, reqEditors ...RequestEditorFn) (*GetAuthCallbackProviderResponse, error) {
	rsp, err := c.GetAuthCallbackProvider(ctx, provider, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetAuthCallbackProviderResponse(rsp)
}

// GetAuthSessionWithResponse request returning *GetAuthSessionResponse
func (c *ClientWithResponses) GetAuthSessionWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetAuthSessionResponse, error) {
	rsp, err := c.GetAuthSession(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetAuthSessionResponse(rsp)
}

// PostAuthSigninWithBodyWithResponse request with arbitrary body returning *PostAuthSigninResponse
func (c *ClientWithResponses) PostAuthSigninWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostAuthSigninResponse, error) {
	rsp, err := c.PostAuthSigninWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostAuthSigninResponse(rsp)
}

func (c *ClientWithResponses) PostAuthSigninWithResponse(ctx context.Context, body PostAuthSigninJSONRequestBody, reqEditors ...RequestEditorFn) (*PostAuthSigninResponse, error) {
	rsp, err := c.PostAuthSignin(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostAuthSigninResponse(rsp)
}

// PostAuthSigninOauthWithBodyWithResponse request with arbitrary body returning *PostAuthSigninOauthResponse
func (c *ClientWithResponses) PostAuthSigninOauthWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostAuthSigninOauthResponse, error) {
	rsp, err := c.PostAuthSigninOauthWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostAuthSigninOauthResponse(rsp)
}

func (c *ClientWithResponses) PostAuthSigninOauthWithResponse(ctx context.Context, body PostAuthSigninOauthJSONRequestBody, reqEditors ...RequestEditorFn) (*PostAuthSigninOauthResponse, error) {
	rsp, err := c.PostAuthSigninOauth(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostAuthSigninOauthResponse(rsp)
}

// PostAuthSignoutWithResponse request returning *PostAuthSignoutResponse
func (c *ClientWithResponses) PostAuthSignoutWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*PostAuthSignoutResponse, error) {
	rsp, err := c.PostAuthSignout(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostAuthSignoutResponse(rsp)
}

// GetAuthUserWithResponse request returning *GetAuthUserResponse
func (c *ClientWithResponses) GetAuthUserWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetAuthUserResponse, error) {
	rsp, err := c.GetAuthUser(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetAuthUserResponse(rsp)
}

// ListCaseStudiesWithResponse request returning *ListCaseStudiesResponse
func (c *ClientWithResponses) ListCaseStudiesWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*ListCaseStudiesResponse, error) {
	rsp, err := c.ListCaseStudies(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseListCaseStudiesResponse(rsp)
}

// CreateCaseStudyWithBodyWithResponse request with arbitrary body returning *CreateCaseStudyResponse
func (c *ClientWithResponses) CreateCaseStudyWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*CreateCaseStudyResponse, error) {
	rsp, err := c.CreateCaseStudyWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseCreateCaseStudyResponse(rsp)
}

func (c *ClientWithResponses) CreateCaseStudyWithResponse(ctx context.Context, body CreateCaseStudyJSONRequestBody, reqEditors ...RequestEditorFn) (*CreateCaseStudyResponse, error) {
	rsp, err := c.CreateCaseStudy(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseCreateCaseStudyResponse(rsp)
}

// DeleteCaseStudyWithResponse request returning *DeleteCaseStudyResponse
func (c *ClientWithResponses) DeleteCaseStudyWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteCaseStudyResponse, error) {
	rsp, err := c.DeleteCaseStudy(ctx, id, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteCaseStudyResponse(rsp)
}

// GetCaseStudyByIdWithResponse request returning *GetCaseStudyByIdResponse
func (c *ClientWithResponses) GetCaseStudyByIdWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*GetCaseStudyByIdResponse, error) {
	rsp, err := c.GetCaseStudyById(ctx, id, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetCaseStudyByIdResponse(rsp)
}

// UpdateCaseStudyWithBodyWithResponse request with arbitrary body returning *UpdateCaseStudyResponse
func (c *ClientWithResponses) UpdateCaseStudyWithBodyWithResponse(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*UpdateCaseStudyResponse, error) {
	rsp, err := c.UpdateCaseStudyWithBody(ctx, id, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseUpdateCaseStudyResponse(rsp)
}

func (c *ClientWithResponses) UpdateCaseStudyWithResponse(ctx context.Context, id openapi_types.UUID, body UpdateCaseStudyJSONRequestBody, reqEditors ...RequestEditorFn) (*UpdateCaseStudyResponse, error) {
	rsp, err := c.UpdateCaseStudy(ctx, id, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseUpdateCaseStudyResponse(rsp)
}

// GenerateCaseStudyTitleWithResponse request returning *GenerateCaseStudyTitleResponse
func (c *ClientWithResponses) GenerateCaseStudyTitleWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*GenerateCaseStudyTitleResponse, error) {
	rsp, err := c.GenerateCaseStudyTitle(ctx, id, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGenerateCaseStudyTitleResponse(rsp)
}

// ListMeetingTranscriptsWithResponse request returning *ListMeetingTranscriptsResponse
func (c *ClientWithResponses) ListMeetingTranscriptsWithResponse(ctx context.Context, params *ListMeetingTranscriptsParams, reqEditors ...RequestEditorFn) (*ListMeetingTranscriptsResponse, error) {
	rsp, err := c.ListMeetingTranscripts(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseListMeetingTranscriptsResponse(rsp)
}

// CreateMeetingTranscriptWithBodyWithResponse request with arbitrary body returning *CreateMeetingTranscriptResponse
func (c *ClientWithResponses) CreateMeetingTranscriptWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*CreateMeetingTranscriptResponse, error) {
	rsp, err := c.CreateMeetingTranscriptWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseCreateMeetingTranscriptResponse(rsp)
}

func (c *ClientWithResponses) CreateMeetingTranscriptWithResponse(ctx context.Context, body CreateMeetingTranscriptJSONRequestBody, reqEditors ...RequestEditorFn) (*CreateMeetingTranscriptResponse, error) {
	rsp, err := c.CreateMeetingTranscript(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseCreateMeetingTranscriptResponse(rsp)
}

// DeleteMeetingTranscriptWithResponse request returning *DeleteMeetingTranscriptResponse
func (c *ClientWithResponses) DeleteMeetingTranscriptWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteMeetingTranscriptResponse, error) {
	rsp, err := c.DeleteMeetingTranscript(ctx, id, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteMeetingTranscriptResponse(rsp)
}

// GetMeetingTranscriptByIdWithResponse request returning *GetMeetingTranscriptByIdResponse
func (c *ClientWithResponses) GetMeetingTranscriptByIdWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*GetMeetingTranscriptByIdResponse, error) {
	rsp, err := c.GetMeetingTranscriptById(ctx, id, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetMeetingTranscriptByIdResponse(rsp)
}

// UpdateMeetingTranscriptWithBodyWithResponse request with arbitrary body returning *UpdateMeetingTranscriptResponse
func (c *ClientWithResponses) UpdateMeetingTranscriptWithBodyWithResponse(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*UpdateMeetingTranscriptResponse, error) {
	rsp, err := c.UpdateMeetingTranscriptWithBody(ctx, id, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseUpdateMeetingTranscriptResponse(rsp)
}

func (c *ClientWithResponses) UpdateMeetingTranscriptWithResponse(ctx context.Context, id openapi_types.UUID, body UpdateMeetingTranscriptJSONRequestBody, reqEditors ...RequestEditorFn) (*UpdateMeetingTranscriptResponse, error) {
	rsp, err := c.UpdateMeetingTranscript(ctx, id, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseUpdateMeetingTranscriptResponse(rsp)
}

// AnalyzeMeetingTranscriptWithResponse request returning *AnalyzeMeetingTranscriptResponse
func (c *ClientWithResponses) AnalyzeMeetingTranscriptWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*AnalyzeMeetingTranscriptResponse, error) {
	rsp, err := c.AnalyzeMeetingTranscript(ctx, id, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseAnalyzeMeetingTranscriptResponse(rsp)
}

// PrefillMeetingTranscriptWithResponse request returning *PrefillMeetingTranscriptResponse
func (c *ClientWithResponses) PrefillMeetingTranscriptWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*PrefillMeetingTranscriptResponse, error) {
	rsp, err := c.PrefillMeetingTranscript(ctx, id, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePrefillMeetingTranscriptResponse(rsp)
}

// ListPeopleWithResponse request returning *ListPeopleResponse
func (c *ClientWithResponses) ListPeopleWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*ListPeopleResponse, error) {
	rsp, err := c.ListPeople(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseListPeopleResponse(rsp)
}

// CreatePersonWithBodyWithResponse request with arbitrary body returning *CreatePersonResponse
func (c *ClientWithResponses) CreatePersonWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*CreatePersonResponse, error) {
	rsp, err := c.CreatePersonWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseCreatePersonResponse(rsp)
}

func (c *ClientWithResponses) CreatePersonWithResponse(ctx context.Context, body CreatePersonJSONRequestBody, reqEditors ...RequestEditorFn) (*CreatePersonResponse, error) {
	rsp, err := c.CreatePerson(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseCreatePersonResponse(rsp)
}

// DeletePersonWithResponse request returning *DeletePersonResponse
func (c *ClientWithResponses) DeletePersonWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeletePersonResponse, error) {
	rsp, err := c.DeletePerson(ctx, id, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeletePersonResponse(rsp)
}

// GetPersonByIdWithResponse request returning *GetPersonByIdResponse
func (c *ClientWithResponses) GetPersonByIdWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*GetPersonByIdResponse, error) {
	rsp, err := c.GetPersonById(ctx, id, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetPersonByIdResponse(rsp)
}

// UpdatePersonWithBodyWithResponse request with arbitrary body returning *UpdatePersonResponse
func (c *ClientWithResponses) UpdatePersonWithBodyWithResponse(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*UpdatePersonResponse, error) {
	rsp, err := c.UpdatePersonWithBody(ctx, id, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseUpdatePersonResponse(rsp)
}

func (c *ClientWithResponses) UpdatePersonWithResponse(ctx context.Context, id openapi_types.UUID, body UpdatePersonJSONRequestBody, reqEditors ...RequestEditorFn) (*UpdatePersonResponse, error) {
	rsp, err := c.UpdatePerson(ctx, id, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseUpdatePersonResponse(rsp)
}

// ListThemesWithResponse request returning *ListThemesResponse
func (c *ClientWithResponses) ListThemesWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*ListThemesResponse, error) {
	rsp, err := c.ListThemes(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseListThemesResponse(rsp)
}

// CreateThemeWithBodyWithResponse request with arbitrary body returning *CreateThemeResponse
func (c *ClientWithResponses) CreateThemeWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*CreateThemeResponse, error) {
	rsp, err := c.CreateThemeWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseCreateThemeResponse(rsp)
}

func (c *ClientWithResponses) CreateThemeWithResponse(ctx context.Context, body CreateThemeJSONRequestBody, reqEditors ...RequestEditorFn) (*CreateThemeResponse, error) {
	rsp, err := c.CreateTheme(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseCreateThemeResponse(rsp)
}

// DeleteThemeWithResponse request returning *DeleteThemeResponse
func (c *ClientWithResponses) DeleteThemeWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteThemeResponse, error) {
	rsp, err := c.DeleteTheme(ctx, id, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteThemeResponse(rsp)
}

// GetThemeByIdWithResponse request returning *GetThemeByIdResponse
func (c *ClientWithResponses) GetThemeByIdWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*GetThemeByIdResponse, error) {
	rsp, err := c.GetThemeById(ctx, id, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetThemeByIdResponse(rsp)
}

// UpdateThemeWithBodyWithResponse request with arbitrary body returning *UpdateThemeResponse
func (c *ClientWithResponses) UpdateThemeWithBodyWithResponse(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*UpdateThemeResponse, error) {
	rsp, err := c.UpdateThemeWithBody(ctx, id, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseUpdateThemeResponse(rsp)
}

func (c *ClientWithResponses) UpdateThemeWithResponse(ctx context.Context, id openapi_types.UUID, body UpdateThemeJSONRequestBody, reqEditors ...RequestEditorFn) (*UpdateThemeResponse, error) {
	rsp, err := c.UpdateTheme(ctx, id, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseUpdateThemeResponse(rsp)
}

// DeleteTranscriptPreviewWithResponse request returning *DeleteTranscriptPreviewResponse
func (c *ClientWithResponses) DeleteTranscriptPreviewWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteTranscriptPreviewResponse, error) {
	rsp, err := c.DeleteTranscriptPreview(ctx, id, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteTranscriptPreviewResponse(rsp)
}

// GetTranscriptPreviewWithResponse request returning *GetTranscriptPreviewResponse
func (c *ClientWithResponses) GetTranscriptPreviewWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*GetTranscriptPreviewResponse, error) {
	rsp, err := c.GetTranscriptPreview(ctx, id, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetTranscriptPreviewResponse(rsp)
}

// UploadTranscriptWithBodyWithResponse request with arbitrary body returning *UploadTranscriptResponse
func (c *ClientWithResponses) UploadTranscriptWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*UploadTranscriptResponse, error) {
	rsp, err := c.UploadTranscriptWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseUploadTranscriptResponse(rsp)
}

// GetUsersProfileWithResponse request returning *GetUsersProfileResponse
func (c *ClientWithResponses) GetUsersProfileWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetUsersProfileResponse, error) {
	rsp, err := c.GetUsersProfile(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetUsersProfileResponse(rsp)
}

// PutUsersProfileWithBodyWithResponse request with arbitrary body returning *PutUsersProfileResponse
func (c *ClientWithResponses) PutUsersProfileWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutUsersProfileResponse, error) {
	rsp, err := c.PutUsersProfileWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutUsersProfileResponse(rsp)
}

func (c *ClientWithResponses) PutUsersProfileWithResponse(ctx context.Context, body PutUsersProfileJSONRequestBody, reqEditors ...RequestEditorFn) (*PutUsersProfileResponse, error) {
	rsp, err := c.PutUsersProfile(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutUsersProfileResponse(rsp)
}

// ParseGetAuthCallbackProviderResponse parses an HTTP response from a GetAuthCallbackProviderWithResponse call
func ParseGetAuthCallbackProviderResponse(rsp *http.Response) (*GetAuthCallbackProviderResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetAuthCallbackProviderResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest AuthResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	}

	return response, nil
}

// ParseGetAuthSessionResponse parses an HTTP response from a GetAuthSessionWithResponse call
func ParseGetAuthSessionResponse(rsp *http.Response) (*GetAuthSessionResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetAuthSessionResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest Session
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParsePostAuthSigninResponse parses an HTTP response from a PostAuthSigninWithResponse call
func ParsePostAuthSigninResponse(rsp *http.Response) (*PostAuthSigninResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostAuthSigninResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest AuthResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParsePostAuthSigninOauthResponse parses an HTTP response from a PostAuthSigninOauthWithResponse call
func ParsePostAuthSigninOauthResponse(rsp *http.Response) (*PostAuthSigninOauthResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostAuthSigninOauthResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest struct {
			Url *string `json:"url,omitempty"`
		}
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParsePostAuthSignoutResponse parses an HTTP response from a PostAuthSignoutWithResponse call
func ParsePostAuthSignoutResponse(rsp *http.Response) (*PostAuthSignoutResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostAuthSignoutResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParseGetAuthUserResponse parses an HTTP response from a GetAuthUserWithResponse call
func ParseGetAuthUserResponse(rsp *http.Response) (*GetAuthUserResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetAuthUserResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest User
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseListCaseStudiesResponse parses an HTTP response from a ListCaseStudiesWithResponse call
func ParseListCaseStudiesResponse(rsp *http.Response) (*ListCaseStudiesResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &ListCaseStudiesResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest []CaseStudy
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	}

	return response, nil
}

// ParseCreateCaseStudyResponse parses an HTTP response from a CreateCaseStudyWithResponse call
func ParseCreateCaseStudyResponse(rsp *http.Response) (*CreateCaseStudyResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &CreateCaseStudyResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 201:
		var dest CaseStudy
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON201 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	}

	return response, nil
}

// ParseDeleteCaseStudyResponse parses an HTTP response from a DeleteCaseStudyWithResponse call
func ParseDeleteCaseStudyResponse(rsp *http.Response) (*DeleteCaseStudyResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeleteCaseStudyResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest struct {
			Message *string `json:"message,omitempty"`
		}
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	}

	return response, nil
}

// ParseGetCaseStudyByIdResponse parses an HTTP response from a GetCaseStudyByIdWithResponse call
func ParseGetCaseStudyByIdResponse(rsp *http.Response) (*GetCaseStudyByIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetCaseStudyByIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest CaseStudy
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	}

	return response, nil
}

// ParseUpdateCaseStudyResponse parses an HTTP response from a UpdateCaseStudyWithResponse call
func ParseUpdateCaseStudyResponse(rsp *http.Response) (*UpdateCaseStudyResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &UpdateCaseStudyResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest CaseStudy
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	}

	return response, nil
}

// ParseGenerateCaseStudyTitleResponse parses an HTTP response from a GenerateCaseStudyTitleWithResponse call
func ParseGenerateCaseStudyTitleResponse(rsp *http.Response) (*GenerateCaseStudyTitleResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GenerateCaseStudyTitleResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest TitleGenerationResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	}

	return response, nil
}

// ParseListMeetingTranscriptsResponse parses an HTTP response from a ListMeetingTranscriptsWithResponse call
func ParseListMeetingTranscriptsResponse(rsp *http.Response) (*ListMeetingTranscriptsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &ListMeetingTranscriptsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest []MeetingTranscript
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	}

	return response, nil
}

// ParseCreateMeetingTranscriptResponse parses an HTTP response from a CreateMeetingTranscriptWithResponse call
func ParseCreateMeetingTranscriptResponse(rsp *http.Response) (*CreateMeetingTranscriptResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &CreateMeetingTranscriptResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 201:
		var dest MeetingTranscript
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON201 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	}

	return response, nil
}

// ParseDeleteMeetingTranscriptResponse parses an HTTP response from a DeleteMeetingTranscriptWithResponse call
func ParseDeleteMeetingTranscriptResponse(rsp *http.Response) (*DeleteMeetingTranscriptResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeleteMeetingTranscriptResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest struct {
			Message *string `json:"message,omitempty"`
		}
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	}

	return response, nil
}

// ParseGetMeetingTranscriptByIdResponse parses an HTTP response from a GetMeetingTranscriptByIdWithResponse call
func ParseGetMeetingTranscriptByIdResponse(rsp *http.Response) (*GetMeetingTranscriptByIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetMeetingTranscriptByIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest MeetingTranscript
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	}

	return response, nil
}

// ParseUpdateMeetingTranscriptResponse parses an HTTP response from a UpdateMeetingTranscriptWithResponse call
func ParseUpdateMeetingTranscriptResponse(rsp *http.Response) (*UpdateMeetingTranscriptResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &UpdateMeetingTranscriptResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest MeetingTranscript
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	}

	return response, nil
}

// ParseAnalyzeMeetingTranscriptResponse parses an HTTP response from a AnalyzeMeetingTranscriptWithResponse call
func ParseAnalyzeMeetingTranscriptResponse(rsp *http.Response) (*AnalyzeMeetingTranscriptResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &AnalyzeMeetingTranscriptResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest AnalysisResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	}

	return response, nil
}

// ParsePrefillMeetingTranscriptResponse parses an HTTP response from a PrefillMeetingTranscriptWithResponse call
func ParsePrefillMeetingTranscriptResponse(rsp *http.Response) (*PrefillMeetingTranscriptResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PrefillMeetingTranscriptResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest TranscriptPrefillResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	}

	return response, nil
}

// ParseListPeopleResponse parses an HTTP response from a ListPeopleWithResponse call
func ParseListPeopleResponse(rsp *http.Response) (*ListPeopleResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &ListPeopleResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest []Person
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	}

	return response, nil
}

// ParseCreatePersonResponse parses an HTTP response from a CreatePersonWithResponse call
func ParseCreatePersonResponse(rsp *http.Response) (*CreatePersonResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &CreatePersonResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 201:
		var dest Person
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON201 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	}

	return response, nil
}

// ParseDeletePersonResponse parses an HTTP response from a DeletePersonWithResponse call
func ParseDeletePersonResponse(rsp *http.Response) (*DeletePersonResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeletePersonResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest struct {
			Message *string `json:"message,omitempty"`
		}
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	}

	return response, nil
}

// ParseGetPersonByIdResponse parses an HTTP response from a GetPersonByIdWithResponse call
func ParseGetPersonByIdResponse(rsp *http.Response) (*GetPersonByIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetPersonByIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest Person
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	}

	return response, nil
}

// ParseUpdatePersonResponse parses an HTTP response from a UpdatePersonWithResponse call
func ParseUpdatePersonResponse(rsp *http.Response) (*UpdatePersonResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &UpdatePersonResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest Person
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	}

	return response, nil
}

// ParseListThemesResponse parses an HTTP response from a ListThemesWithResponse call
func ParseListThemesResponse(rsp *http.Response) (*ListThemesResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &ListThemesResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest []Theme
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	}

	return response, nil
}

// ParseCreateThemeResponse parses an HTTP response from a CreateThemeWithResponse call
func ParseCreateThemeResponse(rsp *http.Response) (*CreateThemeResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &CreateThemeResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 201:
		var dest Theme
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON201 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	}

	return response, nil
}

// ParseDeleteThemeResponse parses an HTTP response from a DeleteThemeWithResponse call
func ParseDeleteThemeResponse(rsp *http.Response) (*DeleteThemeResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeleteThemeResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest struct {
			Message *string `json:"message,omitempty"`
		}
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	}

	return response, nil
}

// ParseGetThemeByIdResponse parses an HTTP response from a GetThemeByIdWithResponse call
func ParseGetThemeByIdResponse(rsp *http.Response) (*GetThemeByIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetThemeByIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest Theme
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	}

	return response, nil
}

// ParseUpdateThemeResponse parses an HTTP response from a UpdateThemeWithResponse call
func ParseUpdateThemeResponse(rsp *http.Response) (*UpdateThemeResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &UpdateThemeResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest Theme
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	}

	return response, nil
}

// ParseDeleteTranscriptPreviewResponse parses an HTTP response from a DeleteTranscriptPreviewWithResponse call
func ParseDeleteTranscriptPreviewResponse(rsp *http.Response) (*DeleteTranscriptPreviewResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeleteTranscriptPreviewResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest struct {
			Message *string `json:"message,omitempty"`
		}
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	}

	return response, nil
}

// ParseGetTranscriptPreviewResponse parses an HTTP response from a GetTranscriptPreviewWithResponse call
func ParseGetTranscriptPreviewResponse(rsp *http.Response) (*GetTranscriptPreviewResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetTranscriptPreviewResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest TranscriptPreview
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	}

	return response, nil
}

// ParseUploadTranscriptResponse parses an HTTP response from a UploadTranscriptWithResponse call
func ParseUploadTranscriptResponse(rsp *http.Response) (*UploadTranscriptResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &UploadTranscriptResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest TranscriptUploadResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 413:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON413 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest Error
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	}

	return response, nil
}

// ParseGetUsersProfileResponse parses an HTTP response from a GetUsersProfileWithResponse call
func ParseGetUsersProfileResponse(rsp *http.Response) (*GetUsersProfileResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetUsersProfileResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest UserProfile
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParsePutUsersProfileResponse parses an HTTP response from a PutUsersProfileWithResponse call
func ParsePutUsersProfileResponse(rsp *http.Response) (*PutUsersProfileResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PutUsersProfileResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest UserProfile
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}
