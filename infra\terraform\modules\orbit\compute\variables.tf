# Compute Engine Module Variables

variable "project_id" {
  description = "GCP project ID"
  type        = string
}

variable "project_name" {
  description = "Name of the project (used for resource naming)"
  type        = string
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
  default     = "dev"
  
  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "Environment must be one of: dev, staging, prod."
  }
}

variable "region" {
  description = "GCP region for resources"
  type        = string
  default     = "australia-southeast1"
}

variable "zone" {
  description = "GCP zone for the compute instance"
  type        = string
  default     = "australia-southeast1-a"
}

variable "machine_type" {
  description = "Machine type for the compute instance"
  type        = string
  default     = "e2-standard-2"
  
  validation {
    condition = can(regex("^(e2|n1|n2|c2|m1|m2|t2d)-", var.machine_type))
    error_message = "Machine type must be a valid GCP machine type."
  }
}

variable "boot_disk_image" {
  description = "Boot disk image for the compute instance"
  type        = string
  default     = "cos-cloud/cos-stable"  # Container-Optimized OS
}

variable "boot_disk_size_gb" {
  description = "Boot disk size in GB"
  type        = number
  default     = 20
  
  validation {
    condition     = var.boot_disk_size_gb >= 10 && var.boot_disk_size_gb <= 1000
    error_message = "Boot disk size must be between 10 and 1000 GB."
  }
}

variable "docker_volumes_disk_size_gb" {
  description = "Additional disk size in GB for Docker volumes"
  type        = number
  default     = 50
  
  validation {
    condition     = var.docker_volumes_disk_size_gb >= 10 && var.docker_volumes_disk_size_gb <= 1000
    error_message = "Docker volumes disk size must be between 10 and 1000 GB."
  }
}

variable "subnet_name" {
  description = "Name of the subnet to place the instance in"
  type        = string
}

variable "assign_external_ip" {
  description = "Whether to assign an external IP to the instance"
  type        = bool
  default     = false
}

variable "create_static_ip" {
  description = "Whether to create a static IP address"
  type        = bool
  default     = false
}

# Registry configuration
variable "registry_url" {
  description = "URL of the Artifact Registry for Docker images"
  type        = string
}

# Database configuration
variable "database_host" {
  description = "Database host (private IP of Cloud SQL instance)"
  type        = string
}

variable "database_name" {
  description = "Name of the database"
  type        = string
}

variable "database_user" {
  description = "Database username"
  type        = string
}

variable "database_password_secret" {
  description = "Secret Manager secret name containing database password"
  type        = string
}

# OAuth configuration
variable "google_oauth_client_id_secret" {
  description = "Secret Manager secret name containing Google OAuth Client ID"
  type        = string
  default     = "oauth-client-id"
}

variable "google_oauth_client_secret_secret" {
  description = "Secret Manager secret name containing Google OAuth Client Secret"
  type        = string
  default     = "oauth-client-secret"
}

variable "oauth_state_secret" {
  description = "Secret Manager secret name containing OAuth state secret for CSRF protection"
  type        = string
  default     = "oauth-state-secret"
}

# Service port configuration
variable "auth_service_port" {
  description = "Port for the auth service"
  type        = number
  default     = 8004
  
  validation {
    condition     = var.auth_service_port >= 1024 && var.auth_service_port <= 65535
    error_message = "Auth service port must be between 1024 and 65535."
  }
}

variable "crm_service_port" {
  description = "Port for the CRM backend service"
  type        = number
  default     = 8003
  
  validation {
    condition     = var.crm_service_port >= 1024 && var.crm_service_port <= 65535
    error_message = "CRM service port must be between 1024 and 65535."
  }
}

variable "gateway_port" {
  description = "Port for the gateway service"
  type        = number
  default     = 8085
  
  validation {
    condition     = var.gateway_port >= 1024 && var.gateway_port <= 65535
    error_message = "Gateway port must be between 1024 and 65535."
  }
}

variable "frontend_domain" {
  description = "Primary frontend domain for this environment"
  type        = string
  default     = ""
}

variable "api_domain" {
  description = "API domain for backend routing"
  type        = string
  default     = ""
}

variable "ssl_domains" {
  description = "List of domains for SSL certificate"
  type        = list(string)
  default     = []
}

# Additional variables for Ansible integration

variable "service_versions" {
  description = "Docker image versions for each service"
  type = object({
    gateway = string
    auth    = string
    backend = string
  })
  default = {
    gateway = "latest"
    auth    = "latest"
    backend = "latest"
  }
}

variable "service_config" {
  description = "Service configuration overrides"
  type = map(object({
    replicas     = optional(number, 1)
    cpu_limit    = optional(string, "0.5")
    memory_limit = optional(string, "512m")
    environment  = optional(map(string), {})
  }))
  default = {}
}

variable "initial_deployment_color" {
  description = "Initial deployment color for blue-green deployments"
  type        = string
  default     = "blue"
  
  validation {
    condition     = contains(["blue", "green"], var.initial_deployment_color)
    error_message = "Deployment color must be either 'blue' or 'green'."
  }
}

variable "gcp_credentials_file" {
  description = "Path to GCP service account credentials file for Ansible"
  type        = string
  default     = ""
}

variable "enable_ansible_provisioning" {
  description = "Whether to run Ansible provisioning automatically"
  type        = bool
  default     = false
}

variable "ansible_playbook_path" {
  description = "Path to the main Ansible playbook"
  type        = string
  default     = "playbooks/deploy-services.yml"
}

variable "ansible_extra_vars" {
  description = "Additional variables to pass to Ansible"
  type        = map(string)
  default     = {}
}

variable "force_ansible_run" {
  description = "Force Ansible to run even if no changes detected"
  type        = bool
  default     = false
}