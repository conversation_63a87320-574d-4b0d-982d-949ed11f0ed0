#!/bin/bash
# Quick status check for production deployment

echo "🔍 Checking Production Deployment Status..."
echo "==========================================="

# Check if terraform state exists
if terraform state list &>/dev/null; then
    echo "✅ Terraform state exists"
    
    # Check key resources
    echo ""
    echo "📊 Resource Status:"
    
    # Database
    if terraform state show module.database.google_sql_database_instance.postgres &>/dev/null; then
        echo "✅ Database: Created"
        DB_IP=$(terraform output -raw database_ip 2>/dev/null || echo "Not available")
        echo "   - Private IP: $DB_IP"
    else
        echo "❌ Database: Not found"
    fi
    
    # Compute instance
    if terraform state show module.compute.google_compute_instance.platform_instance &>/dev/null; then
        echo "✅ Compute Instance: Created"
        INSTANCE_NAME=$(terraform state show module.compute.google_compute_instance.platform_instance | grep -E "^\s*name\s*=" | awk -F'"' '{print $2}')
        echo "   - Instance Name: $INSTANCE_NAME"
    else
        echo "❌ Compute Instance: Not found"
    fi
    
    # Load Balancer
    if terraform state show module.loadbalancer.google_compute_global_forwarding_rule.https_rule &>/dev/null; then
        echo "✅ Load Balancer: Created"
        LB_IP=$(terraform output -raw load_balancer_ip 2>/dev/null || echo "Not available")
        echo "   - Public IP: $LB_IP"
    else
        echo "❌ Load Balancer: Not found"
    fi
    
    # Registry
    if terraform state show module.registry.google_artifact_registry_repository.docker_repo &>/dev/null; then
        echo "✅ Artifact Registry: Created"
    else
        echo "❌ Artifact Registry: Not found"
    fi
    
    # Storage
    if terraform state show module.storage.google_storage_bucket.web_bucket &>/dev/null; then
        echo "✅ Storage Bucket: Created"
        BUCKET=$(terraform output -raw storage_bucket 2>/dev/null || echo "Not available")
        echo "   - Bucket Name: $BUCKET"
    else
        echo "❌ Storage Bucket: Not found"
    fi
    
    echo ""
    echo "🔗 Access Information:"
    if [ ! -z "$LB_IP" ] && [ "$LB_IP" != "Not available" ]; then
        echo "   - Load Balancer: http://$LB_IP"
        echo "   - Health Check: http://$LB_IP/health"
    fi
    
    # Check Ansible provisioning status
    echo ""
    echo "🤖 Ansible Provisioning:"
    if ps aux | grep -q "[a]nsible-playbook.*deploy-services.yml"; then
        echo "⏳ Currently running..."
    else
        if terraform state show module.compute.null_resource.ansible_provision &>/dev/null; then
            echo "✅ Completed"
        else
            echo "❌ Not started"
        fi
    fi
    
else
    echo "❌ No terraform state found. Run 'terraform init' first."
fi

echo ""
echo "==========================================="
echo "Run './tf plan' to see pending changes"
echo "Run './tf apply' to apply any pending changes"