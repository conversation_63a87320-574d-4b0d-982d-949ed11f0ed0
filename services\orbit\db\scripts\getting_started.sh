#!/bin/bash
set -e

echo "🚀 Starting fresh database setup..."
echo ""

# Check if <PERSON><PERSON> is running
if ! docker info >/dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

echo "📦 Step 1: Starting database container..."
# When run through Bazel, we need to find the actual script location
if [[ "${PWD}" == *"bazel-out"* ]]; then
    # Running through Bazel - find the workspace root
    WORKSPACE_ROOT="$(pwd | sed 's|/bazel-out/.*||')"
    DB_DIR="${WORKSPACE_ROOT}/platform/db"
else
    # Running directly - use script location
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    DB_DIR="$(dirname "$SCRIPT_DIR")"
fi

echo "Working from directory: $DB_DIR"
cd "$DB_DIR"
bash scripts/start.sh

echo ""
echo "⏳ Waiting for database to be ready..."
sleep 5

# Wait for database to be actually ready
echo "🔍 Checking database connectivity..."
max_attempts=30
attempt=1
while [ $attempt -le $max_attempts ]; do
    if docker exec platform_postgres pg_isready -U postgres >/dev/null 2>&1; then
        echo "✅ Database is ready!"
        break
    fi
    echo "   Attempt $attempt/$max_attempts - waiting for database..."
    sleep 2
    ((attempt++))
done

if [ $attempt -gt $max_attempts ]; then
    echo "❌ Database failed to become ready after $max_attempts attempts"
    exit 1
fi

echo ""
echo "📋 Step 2: Running database migrations..."
bash scripts/migrate.sh

echo ""
echo "🧹 Step 3: Clearing any existing data..."
bash scripts/clear_data.sh

echo ""
echo "📊 Step 4: Inserting test data..."
bash scripts/simple_test_data.sh

echo ""
echo "🎉 Database setup complete!"
echo ""
echo "You now have a fully configured database with:"
echo "  • Complete schema (users, companies, deals, contacts, etc.)"
echo "  • Test users ready for login"
echo "  • Sample companies, deals, and contacts"
echo ""
echo "🔑 Test login credentials:"
echo "  Email: <EMAIL>"
echo "  Password: TestPassword"
echo ""
echo "🌐 Next steps:"
echo "  1. Start the backend services:"
echo "     bazel run //services/orbit/auth:auth_service"
echo "     bazel run //services/orbit/crm_backend:crm_backend"
echo "     bazel run //services/orbit/gateway:gateway"
echo ""
echo "  2. Start the web frontend:"
echo "     bazel run //services/orbit/web:dev"