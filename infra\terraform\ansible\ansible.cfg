[defaults]
# Basic configuration
inventory = inventory/
remote_user = ansible
private_key_file = ~/.ssh/ansible-key
host_key_checking = False
retry_files_enabled = False
timeout = 30
roles_path = roles/

# Output configuration
stdout_callback = default
callback_result_format = yaml
display_skipped_hosts = False
display_ok_hosts = True
deprecation_warnings = False

# Performance tuning
gathering = smart
fact_caching = memory
fact_caching_timeout = 86400
forks = 10
pipelining = True

# Logging
log_path = logs/ansible.log

# SSH configuration
[ssh_connection]
ssh_args = -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null
retries = 3
timeout = 30

# Privilege escalation
[privilege_escalation]
become = True
become_method = sudo
become_user = root
become_ask_pass = False

# Inventory configuration
[inventory]
enable_plugins = host_list, script, auto, yaml, ini, toml
cache = True
cache_plugin = memory
cache_timeout = 3600