openapi: 3.0.3
info:
  title: CRM Deals API
  version: 1.0.0
  description: Deal pipeline management endpoints for the CRM system

paths:
  /deals:
    get:
      summary: List deals
      description: Retrieve a list of all deals with company and stage information
      parameters:
        - name: stage_id
          in: query
          schema:
            type: string
            format: uuid
          description: Filter by deal stage ID
        - name: company_id
          in: query
          schema:
            type: string
            format: uuid
          description: Filter by company ID
      responses:
        '200':
          description: List of deals
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/DealWithDetails'

    post:
      summary: Create deal
      description: Create a new deal
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DealCreate'
      responses:
        '201':
          description: Deal created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Deal'
        '400':
          description: Invalid input

  /deals/{id}:
    get:
      summary: Get deal
      description: Retrieve a specific deal by ID
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Deal details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DealDetails'
        '404':
          description: Deal not found

    put:
      summary: Update deal
      description: Update an existing deal
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DealUpdate'
      responses:
        '200':
          description: Deal updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Deal'
        '404':
          description: Deal not found

    delete:
      summary: Delete deal
      description: Delete a deal
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '204':
          description: Deal deleted successfully
        '404':
          description: Deal not found

  /deals/{id}/stage:
    put:
      summary: Update deal stage
      description: Move a deal to a different stage in the pipeline
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                deal_stage_id:
                  type: string
                  format: uuid
              required:
                - deal_stage_id
      responses:
        '200':
          description: Deal stage updated successfully
        '404':
          description: Deal not found

  /deal-stages:
    get:
      summary: List deal stages
      description: Retrieve all deal stages ordered by pipeline order
      responses:
        '200':
          description: List of deal stages
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/DealStage'

  /deal-stages/{id}/revenue:
    get:
      summary: Get stage revenue
      description: Get total estimated revenue for deals in a specific stage
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Revenue statistics for the stage
          content:
            application/json:
              schema:
                type: object
                properties:
                  stage_id:
                    type: string
                    format: uuid
                  total_estimated_value:
                    type: number
                    format: decimal
                  deal_count:
                    type: integer

  /analytics/pipeline:
    get:
      summary: Get pipeline analytics
      description: Get deal pipeline analytics with stage breakdown
      responses:
        '200':
          description: Pipeline analytics data
          content:
            application/json:
              schema:
                type: object
                properties:
                  stages:
                    type: array
                    items:
                      type: object
                      properties:
                        stage_id:
                          type: string
                          format: uuid
                        stage_name:
                          type: string
                        deal_count:
                          type: integer
                        total_value:
                          type: number
                          format: decimal
                        pipeline_order:
                          type: integer
                  total_pipeline_value:
                    type: number
                    format: decimal
                  total_deal_count:
                    type: integer

components:
  schemas:
    Deal:
      type: object
      properties:
        id:
          type: string
          format: uuid
        title:
          type: string
        description:
          type: string
        estimated_value:
          type: number
          format: decimal
        company_id:
          type: string
          format: uuid
        deal_stage_id:
          type: string
          format: uuid
        expected_close_date:
          type: string
          format: date
        created_by:
          type: string
          format: uuid
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    DealWithDetails:
      allOf:
        - $ref: '#/components/schemas/Deal'
        - type: object
          properties:
            company:
              type: object
              properties:
                id:
                  type: string
                  format: uuid
                name:
                  type: string
            deal_stage:
              type: object
              properties:
                id:
                  type: string
                  format: uuid
                name:
                  type: string
                pipeline_order:
                  type: integer

    DealDetails:
      allOf:
        - $ref: '#/components/schemas/Deal'
        - type: object
          properties:
            company:
              type: object
              properties:
                id:
                  type: string
                  format: uuid
                name:
                  type: string
                website:
                  type: string
                phone:
                  type: string
            deal_stage:
              type: object
              properties:
                id:
                  type: string
                  format: uuid
                name:
                  type: string
                pipeline_order:
                  type: integer
                is_closed_won:
                  type: boolean
                is_closed_lost:
                  type: boolean

    DealCreate:
      type: object
      properties:
        title:
          type: string
          minLength: 1
        description:
          type: string
        estimated_value:
          type: number
          format: decimal
          minimum: 0
        company_id:
          type: string
          format: uuid
        deal_stage_id:
          type: string
          format: uuid
        expected_close_date:
          type: string
          format: date
      required:
        - title
        - company_id
        - deal_stage_id

    DealUpdate:
      type: object
      properties:
        title:
          type: string
          minLength: 1
        description:
          type: string
        estimated_value:
          type: number
          format: decimal
          minimum: 0
        company_id:
          type: string
          format: uuid
        deal_stage_id:
          type: string
          format: uuid
        expected_close_date:
          type: string
          format: date

    DealStage:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        pipeline_order:
          type: integer
        is_closed_won:
          type: boolean
          default: false
        is_closed_lost:
          type: boolean
          default: false
        created_at:
          type: string
          format: date-time