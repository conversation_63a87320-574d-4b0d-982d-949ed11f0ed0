"""N8N-style visual workflow engine API endpoints."""

from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTP<PERSON><PERSON><PERSON>, BackgroundTasks, Body
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any, AsyncGenerator
from pydantic import BaseModel, Field
from datetime import datetime
import json
import asyncio

from database.connection import get_db
from auth.dependencies import get_current_user
from services.workflow_engine import WorkflowEngine, WorkflowExecutionResult, NodeCategory
from utils.logging import get_logger

logger = get_logger(__name__)
router = APIRouter(prefix="/workflows", tags=["workflows"])

# Workflow engine will be initialized when needed
workflow_engine = None

def get_workflow_engine() -> WorkflowEngine:
    """Get workflow engine instance with lazy initialization."""
    global workflow_engine
    if workflow_engine is None:
        from intelligence.gateway import AIGateway
        ai_gateway = AIGateway()
        workflow_engine = WorkflowEngine(ai_gateway)
    return workflow_engine

# Pydantic models
class WorkflowNode(BaseModel):
    id: str
    type: str
    name: str
    position: Dict[str, float]  # {x: 100, y: 200}
    parameters: Dict[str, Any] = Field(default_factory=dict)
    credentials: Optional[Dict[str, Any]] = None

class WorkflowConnection(BaseModel):
    source: str  # node id
    target: str  # node id
    source_output: str = "main"
    target_input: str = "main"

class WorkflowDefinition(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = None
    nodes: List[WorkflowNode]
    connections: List[WorkflowConnection]
    settings: Dict[str, Any] = Field(default_factory=dict)
    tags: List[str] = Field(default_factory=list)

class WorkflowResponse(BaseModel):
    id: str
    name: str
    description: Optional[str]
    status: str  # active, inactive, draft
    created_at: str
    updated_at: str
    created_by: str
    node_count: int
    execution_count: int
    last_execution: Optional[str]

class WorkflowExecutionResponse(BaseModel):
    execution_id: str
    workflow_id: str
    status: str  # running, completed, failed, cancelled
    started_at: str
    completed_at: Optional[str]
    duration: Optional[float]  # seconds
    trigger_data: Dict[str, Any]
    results: Dict[str, Any]
    error: Optional[str]

class NodeExecutionResponse(BaseModel):
    node_id: str
    node_name: str
    status: str  # pending, running, completed, failed, skipped
    started_at: Optional[str]
    completed_at: Optional[str]
    duration: Optional[float]
    input_data: Dict[str, Any]
    output_data: Dict[str, Any]
    error: Optional[str]

@router.post("/create", response_model=WorkflowResponse)
async def create_workflow(
    workflow: WorkflowDefinition,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new workflow."""
    
    try:
        # Validate workflow definition
        validation_result = await get_workflow_engine().validate_workflow(workflow.dict())
        if not validation_result["valid"]:
            raise HTTPException(
                status_code=400, 
                detail=f"Workflow validation failed: {validation_result['errors']}"
            )
        
        # Create workflow
        workflow_id = await get_workflow_engine().create_workflow(workflow.dict())
        workflow_data = await get_workflow_engine().get_workflow(workflow_id)
        
        logger.info(
            f"Workflow created",
            workflow_id=workflow_id,
            name=workflow.name,
            user_id=current_user.id
        )
        
        return WorkflowResponse(
            id=workflow_data["id"],
            name=workflow_data["name"],
            description=workflow_data.get("description"),
            status=workflow_data["status"],
            created_at=workflow_data["created_at"].isoformat(),
            updated_at=workflow_data["updated_at"].isoformat(),
            created_by=workflow_data["created_by"],
            node_count=len(workflow_data["nodes"]),
            execution_count=workflow_data.get("execution_count", 0),
            last_execution=workflow_data.get("last_execution")
        )
        
    except Exception as e:
        logger.error(f"Error creating workflow: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to create workflow")

@router.get("/list", response_model=List[WorkflowResponse])
async def list_workflows(
    status: Optional[str] = None,
    tag: Optional[str] = None,
    limit: int = 50,
    offset: int = 0,
    current_user = Depends(get_current_user)
):
    """List workflows with optional filtering."""
    
    try:
        workflows = await get_workflow_engine().list_workflows(
            status_filter=status,
            tag_filter=tag,
            limit=limit,
            offset=offset
        )
        
        return [
            WorkflowResponse(
                id=wf["id"],
                name=wf["name"],
                description=wf.get("description"),
                status=wf["status"],
                created_at=wf["created_at"].isoformat(),
                updated_at=wf["updated_at"].isoformat(),
                created_by=wf["created_by"],
                node_count=len(wf["nodes"]),
                execution_count=wf.get("execution_count", 0),
                last_execution=wf.get("last_execution")
            )
            for wf in workflows
        ]
        
    except Exception as e:
        logger.error(f"Error listing workflows: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to list workflows")

@router.get("/{workflow_id}", response_model=WorkflowDefinition)
async def get_workflow(
    workflow_id: str,
    current_user = Depends(get_current_user)
):
    """Get a specific workflow definition."""
    
    try:
        workflow_data = await get_workflow_engine().get_workflow(workflow_id)
        
        if not workflow_data:
            raise HTTPException(status_code=404, detail="Workflow not found")
        
        return WorkflowDefinition(
            name=workflow_data["name"],
            description=workflow_data.get("description"),
            nodes=[
                WorkflowNode(
                    id=node["id"],
                    type=node["type"],
                    name=node["name"],
                    position=node.get("position", {"x": 0, "y": 0}),
                    parameters=node.get("parameters", {}),
                    credentials=node.get("credentials")
                )
                for node in workflow_data["nodes"]
            ],
            connections=[
                WorkflowConnection(
                    source=conn["source"],
                    target=conn["target"],
                    source_output=conn.get("source_output", "main"),
                    target_input=conn.get("target_input", "main")
                )
                for conn in workflow_data["connections"]
            ],
            settings=workflow_data.get("settings", {}),
            tags=workflow_data.get("tags", [])
        )
        
    except Exception as e:
        logger.error(f"Error fetching workflow: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to fetch workflow")

@router.put("/{workflow_id}", response_model=WorkflowResponse)
async def update_workflow(
    workflow_id: str,
    workflow: WorkflowDefinition,
    current_user = Depends(get_current_user)
):
    """Update an existing workflow."""
    
    try:
        # Validate workflow definition
        validation_result = await get_workflow_engine().validate_workflow(workflow.dict())
        if not validation_result["valid"]:
            raise HTTPException(
                status_code=400,
                detail=f"Workflow validation failed: {validation_result['errors']}"
            )
        
        # Update workflow
        await get_workflow_engine().update_workflow(workflow_id, workflow.dict())
        workflow_data = await get_workflow_engine().get_workflow(workflow_id)
        
        logger.info(
            f"Workflow updated",
            workflow_id=workflow_id,
            user_id=current_user.id
        )
        
        return WorkflowResponse(
            id=workflow_data["id"],
            name=workflow_data["name"],
            description=workflow_data.get("description"),
            status=workflow_data["status"],
            created_at=workflow_data["created_at"].isoformat(),
            updated_at=workflow_data["updated_at"].isoformat(),
            created_by=workflow_data["created_by"],
            node_count=len(workflow_data["nodes"]),
            execution_count=workflow_data.get("execution_count", 0),
            last_execution=workflow_data.get("last_execution")
        )
        
    except Exception as e:
        logger.error(f"Error updating workflow: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to update workflow")

@router.post("/{workflow_id}/execute", response_model=WorkflowExecutionResponse)
async def execute_workflow(
    workflow_id: str,
    trigger_data: Dict[str, Any] = Body(default_factory=dict),
    background_tasks: BackgroundTasks = None,
    current_user = Depends(get_current_user)
):
    """Execute a workflow."""
    
    try:
        execution_id = await get_workflow_engine().execute_workflow(
            workflow_id, 
            trigger_data=trigger_data
        )
        
        # Get initial execution status
        execution = await get_workflow_engine().get_execution_status(execution_id)
        
        logger.info(
            f"Workflow execution started",
            workflow_id=workflow_id,
            execution_id=execution_id,
            user_id=current_user.id
        )
        
        return WorkflowExecutionResponse(
            execution_id=execution["id"],
            workflow_id=execution["workflow_id"],
            status=execution["status"],
            started_at=execution["started_at"].isoformat(),
            completed_at=execution["completed_at"].isoformat() if execution.get("completed_at") else None,
            duration=execution.get("duration"),
            trigger_data=execution["trigger_data"],
            results=execution.get("results", {}),
            error=execution.get("error")
        )
        
    except Exception as e:
        logger.error(f"Error executing workflow: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to execute workflow")

@router.get("/{workflow_id}/executions", response_model=List[WorkflowExecutionResponse])
async def get_workflow_executions(
    workflow_id: str,
    limit: int = 50,
    offset: int = 0,
    current_user = Depends(get_current_user)
):
    """Get execution history for a workflow."""
    
    try:
        executions = await get_workflow_engine().get_workflow_executions(
            workflow_id,
            limit=limit,
            offset=offset
        )
        
        return [
            WorkflowExecutionResponse(
                execution_id=exec["id"],
                workflow_id=exec["workflow_id"],
                status=exec["status"],
                started_at=exec["started_at"].isoformat(),
                completed_at=exec["completed_at"].isoformat() if exec.get("completed_at") else None,
                duration=exec.get("duration"),
                trigger_data=exec["trigger_data"],
                results=exec.get("results", {}),
                error=exec.get("error")
            )
            for exec in executions
        ]
        
    except Exception as e:
        logger.error(f"Error fetching workflow executions: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to fetch executions")

@router.get("/executions/{execution_id}", response_model=WorkflowExecutionResponse)
async def get_execution_status(
    execution_id: str,
    current_user = Depends(get_current_user)
):
    """Get detailed status of a workflow execution."""
    
    try:
        execution = await get_workflow_engine().get_execution_status(execution_id)
        
        if not execution:
            raise HTTPException(status_code=404, detail="Execution not found")
        
        return WorkflowExecutionResponse(
            execution_id=execution["id"],
            workflow_id=execution["workflow_id"],
            status=execution["status"],
            started_at=execution["started_at"].isoformat(),
            completed_at=execution["completed_at"].isoformat() if execution.get("completed_at") else None,
            duration=execution.get("duration"),
            trigger_data=execution["trigger_data"],
            results=execution.get("results", {}),
            error=execution.get("error")
        )
        
    except Exception as e:
        logger.error(f"Error fetching execution status: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to fetch execution status")

@router.get("/executions/{execution_id}/nodes", response_model=List[NodeExecutionResponse])
async def get_execution_node_details(
    execution_id: str,
    current_user = Depends(get_current_user)
):
    """Get detailed execution status for each node in the workflow."""
    
    try:
        node_executions = await get_workflow_engine().get_execution_node_details(execution_id)
        
        return [
            NodeExecutionResponse(
                node_id=node["node_id"],
                node_name=node["node_name"],
                status=node["status"],
                started_at=node["started_at"].isoformat() if node.get("started_at") else None,
                completed_at=node["completed_at"].isoformat() if node.get("completed_at") else None,
                duration=node.get("duration"),
                input_data=node.get("input_data", {}),
                output_data=node.get("output_data", {}),
                error=node.get("error")
            )
            for node in node_executions
        ]
        
    except Exception as e:
        logger.error(f"Error fetching node execution details: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to fetch node details")

@router.post("/executions/{execution_id}/cancel")
async def cancel_execution(
    execution_id: str,
    current_user = Depends(get_current_user)
):
    """Cancel a running workflow execution."""
    
    try:
        result = await get_workflow_engine().cancel_execution(execution_id)
        
        logger.info(
            f"Workflow execution cancelled",
            execution_id=execution_id,
            user_id=current_user.id
        )
        
        return {
            "message": "Execution cancelled successfully",
            "execution_id": execution_id,
            "status": result["status"]
        }
        
    except Exception as e:
        logger.error(f"Error cancelling execution: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to cancel execution")

@router.get("/node-types")
async def get_available_node_types():
    """Get all available node types and their configurations."""
    
    try:
        node_types = await get_workflow_engine().get_available_node_types()
        
        return {
            "node_types": node_types,
            "categories": {
                "trigger": [nt for nt in node_types if nt["category"] == "trigger"],
                "regular": [nt for nt in node_types if nt["category"] == "regular"],
                "output": [nt for nt in node_types if nt["category"] == "output"]
            }
        }
        
    except Exception as e:
        logger.error(f"Error fetching node types: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to fetch node types")

@router.post("/{workflow_id}/activate")
async def activate_workflow(
    workflow_id: str,
    current_user = Depends(get_current_user)
):
    """Activate a workflow for automatic execution."""
    
    try:
        result = await get_workflow_engine().activate_workflow(workflow_id)
        
        logger.info(
            f"Workflow activated",
            workflow_id=workflow_id,
            user_id=current_user.id
        )
        
        return {
            "message": "Workflow activated successfully",
            "workflow_id": workflow_id,
            "status": result["status"]
        }
        
    except Exception as e:
        logger.error(f"Error activating workflow: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to activate workflow")

@router.post("/{workflow_id}/deactivate")
async def deactivate_workflow(
    workflow_id: str,
    current_user = Depends(get_current_user)
):
    """Deactivate a workflow."""
    
    try:
        result = await get_workflow_engine().deactivate_workflow(workflow_id)
        
        logger.info(
            f"Workflow deactivated",
            workflow_id=workflow_id,
            user_id=current_user.id
        )
        
        return {
            "message": "Workflow deactivated successfully",
            "workflow_id": workflow_id,
            "status": result["status"]
        }
        
    except Exception as e:
        logger.error(f"Error deactivating workflow: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to deactivate workflow")

@router.delete("/{workflow_id}")
async def delete_workflow(
    workflow_id: str,
    current_user = Depends(get_current_user)
):
    """Delete a workflow."""
    
    try:
        await get_workflow_engine().delete_workflow(workflow_id)
        
        logger.info(
            f"Workflow deleted",
            workflow_id=workflow_id,
            user_id=current_user.id
        )
        
        return {
            "message": "Workflow deleted successfully",
            "workflow_id": workflow_id
        }
        
    except Exception as e:
        logger.error(f"Error deleting workflow: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to delete workflow")

@router.get("/executions/{execution_id}/stream")
async def stream_execution_logs(
    execution_id: str,
    current_user = Depends(get_current_user)
):
    """Stream real-time execution logs."""
    
    async def log_generator() -> AsyncGenerator[str, None]:
        try:
            async for log_entry in get_workflow_engine().stream_execution_logs(execution_id):
                yield f"data: {json.dumps(log_entry)}\n\n"
        except Exception as e:
            yield f"data: {json.dumps({'error': str(e)})}\n\n"
    
    return StreamingResponse(
        log_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
        }
    )

@router.get("/health")
async def workflow_service_health():
    """Check health of the workflow service."""
    
    try:
        health_status = await get_workflow_engine().check_service_health()
        
        return {
            "status": "healthy",
            "workflow_engine": health_status,
            "active_workflows": health_status.get("active_workflows", 0),
            "running_executions": health_status.get("running_executions", 0),
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Workflow service health check failed: {str(e)}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }