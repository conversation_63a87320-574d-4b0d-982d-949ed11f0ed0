/**
 * Task Management Integration Tests
 * Tests real API calls to the backend task endpoints
 */

import { agentService } from '@/services/agent.service';
import { taskService, Task, TaskCreateData } from '@/services/task.service';
import { apiService } from '../../services/api.test';
import {
  User,
  LoginRequest,
  RegisterRequest,
  TokenResponse,
} from '@/types/api';

class AuthServiceTest {
  async login(credentials: LoginRequest): Promise<User> {
    const params = new URLSearchParams();
    params.append('username', credentials.username);
    params.append('password', credentials.password);

    const response = await apiService.post<TokenResponse>(
      '/auth/login',
      params
    );
    apiService.setTokens(response.data);

    // Get user data after login
    const userResponse = await apiService.get<User>('/auth/me');
    return userResponse.data;
  }

  async register(data: RegisterRequest): Promise<User> {
    const response = await apiService.post<User>('/auth/register', data);
    return response.data;
  }

  logout() {
    apiService.logout();
  }

  isAuthenticated(): boolean {
    return !!apiService.getToken();
  }
}

const authService = new AuthServiceTest();

describe('Task Management Integration Tests', () => {
  let testUser: any;
  let testAgent: any;
  let authToken: string | null = null;
  let createdTaskIds: string[] = [];
  let createdAgentIds: string[] = [];

  beforeAll(async () => {
    // Setup authenticated user for all task tests
    testUser = (global as any).testUtils.generateTestUser();
    
    // Register user
    await authService.register({
      username: testUser.username,
      email: testUser.email,
      password: testUser.password,
      full_name: testUser.full_name
    });

    // Login user
    await authService.login({
      username: testUser.username,
      password: testUser.password
    });

    authToken = apiService.getToken();

    // Create a test agent for task assignment
    testAgent = await agentService.createAgent({
      name: 'Test Agent for Tasks',
      description: 'Agent for task integration testing',
      type: 'assistant',
      config: { max_concurrent_tasks: 5, timeout_seconds: 300 },
      capabilities: ['task_execution', 'data_processing']
    });
    createdAgentIds.push(testAgent.id);
  });

  afterEach(async () => {
    // Cleanup created tasks
    for (const taskId of createdTaskIds) {
      try {
        await taskService.deleteTask(taskId);
      } catch (error) {
        console.warn(`Failed to cleanup task ${taskId}:`, error);
      }
    }
    createdTaskIds = [];
  });

  afterAll(async () => {
    // Cleanup created agents
    for (const agentId of createdAgentIds) {
      try {
        await agentService.deleteAgent(agentId);
      } catch (error) {
        console.warn(`Failed to cleanup agent ${agentId}:`, error);
      }
    }
    
    // Cleanup authentication
    if (authToken) {
      authService.logout();
    }
  });

  const generateTestTask = (): TaskCreateData => ({
    title: `Test Task ${Date.now()}`,
    description: 'A test task for integration testing',
    type: 'data_processing',
    priority: 'medium',
    agent_id: testAgent.id,
    estimated_duration: 300,
    auto_retry: true,
    max_retries: 3,
    timeout_seconds: 600,
    input_data: {
      message: 'Test input data',
      config: { test: true }
    },
    config: {
      test_mode: true,
      debug: false
    }
  });

  describe('Task CRUD Operations', () => {
    test('should create a new task', async () => {
      const taskData = generateTestTask();
      
      const task = await taskService.createTask(taskData);

      expect(task).toBeDefined();
      expect(task.id).toBeDefined();
      expect(task.title).toBe(taskData.title);
      expect(task.description).toBe(taskData.description);
      expect(task.type).toBe(taskData.type);
      expect(task.priority).toBe(taskData.priority);
      expect(task.agent_id).toBe(taskData.agent_id);
      expect(task.agent_name).toBeDefined();
      expect(task.status).toBeDefined();
      expect(task.progress).toBeDefined();
      expect(task.created_at).toBeDefined();
      expect(task.updated_at).toBeDefined();
      expect(task.auto_retry).toBe(taskData.auto_retry);
      expect(task.max_retries).toBe(taskData.max_retries);
      expect(task.timeout_seconds).toBe(taskData.timeout_seconds);

      createdTaskIds.push(task.id);
    });

    test('should list tasks', async () => {
      // Create a few tasks first
      const task1 = await taskService.createTask({
        ...generateTestTask(),
        title: 'Task 1'
      });
      createdTaskIds.push(task1.id);

      const task2 = await taskService.createTask({
        ...generateTestTask(),
        title: 'Task 2'
      });
      createdTaskIds.push(task2.id);

      const response = await taskService.getTasks();

      expect(response).toBeDefined();
      expect(response.tasks).toBeDefined();
      expect(Array.isArray(response.tasks)).toBe(true);
      expect(response.tasks.length).toBeGreaterThanOrEqual(2);
      expect(response.total).toBeGreaterThanOrEqual(2);

      // Check that our created tasks are in the list
      const taskIds = response.tasks.map(task => task.id);
      expect(taskIds).toContain(task1.id);
      expect(taskIds).toContain(task2.id);
    });

    test('should list tasks with pagination', async () => {
      // Create multiple tasks
      for (let i = 0; i < 5; i++) {
        const task = await taskService.createTask({
          ...generateTestTask(),
          title: `Pagination Task ${i}`
        });
        createdTaskIds.push(task.id);
      }

      // Test pagination
      const page1 = await taskService.getTasks({ page: 1, per_page: 2 });
      expect(page1.tasks.length).toBeLessThanOrEqual(2);
      expect(page1.page).toBe(1);
      expect(page1.per_page).toBe(2);

      const page2 = await taskService.getTasks({ page: 2, per_page: 2 });
      expect(page2.tasks.length).toBeLessThanOrEqual(2);
      expect(page2.page).toBe(2);
      expect(page2.per_page).toBe(2);
    });

    test('should filter tasks by status', async () => {
      // Create tasks with different priorities
      const task1 = await taskService.createTask({
        ...generateTestTask(),
        priority: 'high'
      });
      createdTaskIds.push(task1.id);

      const task2 = await taskService.createTask({
        ...generateTestTask(),
        priority: 'low'
      });
      createdTaskIds.push(task2.id);

      // Filter by priority
      const highPriorityTasks = await taskService.getTasks({ priority: 'high' });
      const highPriorityIds = highPriorityTasks.tasks.map(task => task.id);
      expect(highPriorityIds).toContain(task1.id);

      const lowPriorityTasks = await taskService.getTasks({ priority: 'low' });
      const lowPriorityIds = lowPriorityTasks.tasks.map(task => task.id);
      expect(lowPriorityIds).toContain(task2.id);
    });

    test('should filter tasks by agent', async () => {
      const task = await taskService.createTask({
        ...generateTestTask(),
        agent_id: testAgent.id
      });
      createdTaskIds.push(task.id);

      const agentTasks = await taskService.getTasks({ agent_id: testAgent.id });
      const taskIds = agentTasks.tasks.map(task => task.id);
      expect(taskIds).toContain(task.id);
    });

    test('should get task by id', async () => {
      const createdTask = await taskService.createTask(generateTestTask());
      createdTaskIds.push(createdTask.id);

      const retrievedTask = await taskService.getTask(createdTask.id);

      expect(retrievedTask).toBeDefined();
      expect(retrievedTask.id).toBe(createdTask.id);
      expect(retrievedTask.title).toBe(createdTask.title);
      expect(retrievedTask.description).toBe(createdTask.description);
      expect(retrievedTask.type).toBe(createdTask.type);
      expect(retrievedTask.priority).toBe(createdTask.priority);
    });

    test('should update task', async () => {
      const createdTask = await taskService.createTask(generateTestTask());
      createdTaskIds.push(createdTask.id);

      const updateData = {
        title: 'Updated Task Title',
        description: 'Updated description',
        priority: 'high' as const
      };

      const updatedTask = await taskService.updateTask(createdTask.id, updateData);

      expect(updatedTask.id).toBe(createdTask.id);
      expect(updatedTask.title).toBe(updateData.title);
      expect(updatedTask.description).toBe(updateData.description);
      expect(updatedTask.priority).toBe(updateData.priority);
      expect(updatedTask.updated_at).not.toBe(createdTask.updated_at);
    });

    test('should delete task', async () => {
      const createdTask = await taskService.createTask(generateTestTask());

      await taskService.deleteTask(createdTask.id);

      await expect(taskService.getTask(createdTask.id)).rejects.toThrow();

      // Remove from cleanup list since it's already deleted
      createdTaskIds = createdTaskIds.filter(id => id !== createdTask.id);
    });

    test('should fail to get non-existent task', async () => {
      const nonExistentId = 'non-existent-task-id-12345';
      
      await expect(taskService.getTask(nonExistentId)).rejects.toThrow();
    });

    test('should fail to update non-existent task', async () => {
      const nonExistentId = 'non-existent-task-id-12345';
      
      await expect(taskService.updateTask(nonExistentId, {
        title: 'Updated Title'
      })).rejects.toThrow();
    });

    test('should fail to delete non-existent task', async () => {
      const nonExistentId = 'non-existent-task-id-12345';
      
      await expect(taskService.deleteTask(nonExistentId)).rejects.toThrow();
    });
  });

  describe('Task Lifecycle Management', () => {
    let task: Task;

    beforeEach(async () => {
      task = await taskService.createTask(generateTestTask());
      createdTaskIds.push(task.id);
    });

    test('should start task', async () => {
      const result = await taskService.startTask(task.id);

      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      expect(result.message).toBeDefined();
    });

    test('should pause task', async () => {
      // Start task first
      await taskService.startTask(task.id);

      const result = await taskService.pauseTask(task.id);

      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      expect(result.message).toBeDefined();
    });

    test('should resume task', async () => {
      // Start and pause task first
      await taskService.startTask(task.id);
      await taskService.pauseTask(task.id);

      const result = await taskService.resumeTask(task.id);

      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      expect(result.message).toBeDefined();
    });

    test('should stop task', async () => {
      // Start task first
      await taskService.startTask(task.id);

      const result = await taskService.stopTask(task.id);

      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      expect(result.message).toBeDefined();
    });

    test('should cancel task', async () => {
      const result = await taskService.cancelTask(task.id);

      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      expect(result.message).toBeDefined();
    });

    test('should retry task', async () => {
      const result = await taskService.retryTask(task.id);

      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      expect(result.message).toBeDefined();
    });

    test('should get task progress', async () => {
      const progress = await taskService.getTaskProgress(task.id);

      expect(progress).toBeDefined();
      expect(typeof progress.progress).toBe('number');
      expect(progress.status).toBeDefined();
      expect(progress.progress).toBeGreaterThanOrEqual(0);
      expect(progress.progress).toBeLessThanOrEqual(100);
    });

    test('should get task logs', async () => {
      const logs = await taskService.getTaskLogs(task.id);

      expect(logs).toBeDefined();
      expect(Array.isArray(logs.logs)).toBe(true);
      expect(typeof logs.total).toBe('number');
    });

    test('should get task logs with filters', async () => {
      const logs = await taskService.getTaskLogs(task.id, {
        level: 'info',
        limit: 10,
        offset: 0
      });

      expect(logs).toBeDefined();
      expect(Array.isArray(logs.logs)).toBe(true);
      expect(logs.logs.length).toBeLessThanOrEqual(10);
    });

    test('should get task result', async () => {
      try {
        const result = await taskService.getTaskResult(task.id);
        expect(result).toBeDefined();
        expect(result.task_id).toBe(task.id);
        expect(result.status).toBeDefined();
      } catch (error) {
        // Task might not have a result yet, which is acceptable
        expect(error).toBeDefined();
      }
    });

    test('should get task metrics', async () => {
      const metrics = await taskService.getTaskMetrics(task.id);

      expect(metrics).toBeDefined();
      expect(typeof metrics.execution_time).toBe('number');
      expect(typeof metrics.memory_usage).toBe('number');
      expect(typeof metrics.cpu_usage).toBe('number');
      expect(typeof metrics.error_count).toBe('number');
      expect(typeof metrics.retry_count).toBe('number');
    });
  });

  describe('Task Templates and Cloning', () => {
    let task: Task;

    beforeEach(async () => {
      task = await taskService.createTask(generateTestTask());
      createdTaskIds.push(task.id);
    });

    test('should get task templates', async () => {
      const templates = await taskService.getTaskTemplates();

      expect(Array.isArray(templates)).toBe(true);
      // Each template should have required fields
      templates.forEach(template => {
        expect(template.id).toBeDefined();
        expect(template.name).toBeDefined();
        expect(template.type).toBeDefined();
        expect(template.default_config).toBeDefined();
      });
    });

    test('should clone task', async () => {
      const overrides = {
        title: 'Cloned Task',
        priority: 'high' as const
      };

      const clonedTask = await taskService.cloneTask(task.id, overrides);

      expect(clonedTask).toBeDefined();
      expect(clonedTask.id).not.toBe(task.id); // Should be different ID
      expect(clonedTask.title).toBe(overrides.title);
      expect(clonedTask.priority).toBe(overrides.priority);
      expect(clonedTask.type).toBe(task.type); // Should inherit original type
      expect(clonedTask.agent_id).toBe(task.agent_id); // Should inherit original agent

      createdTaskIds.push(clonedTask.id);
    });

    test('should create task from template if templates exist', async () => {
      const templates = await taskService.getTaskTemplates();
      
      if (templates.length > 0) {
        const template = templates[0];
        
        const taskData = {
          title: 'Task from Template',
          description: 'Created from template',
          priority: 'medium' as const,
          agent_id: testAgent.id,
          input_data: { test: true }
        };

        const createdTask = await taskService.createTaskFromTemplate(template.id, taskData);

        expect(createdTask).toBeDefined();
        expect(createdTask.title).toBe(taskData.title);
        expect(createdTask.type).toBe(template.type);
        expect(createdTask.agent_id).toBe(taskData.agent_id);

        createdTaskIds.push(createdTask.id);
      }
    });
  });

  describe('Batch Operations', () => {
    let tasks: Task[];

    beforeEach(async () => {
      // Create multiple tasks for batch operations
      tasks = [];
      for (let i = 0; i < 3; i++) {
        const task = await taskService.createTask({
          ...generateTestTask(),
          title: `Batch Task ${i}`
        });
        tasks.push(task);
        createdTaskIds.push(task.id);
      }
    });

    test('should batch update tasks', async () => {
      const taskIds = tasks.map(task => task.id);
      const updateData = {
        priority: 'urgent' as const,
        description: 'Batch updated description'
      };

      const result = await taskService.batchUpdateTasks(taskIds, updateData);

      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      expect(result.updated).toBe(taskIds.length);
      expect(result.failed).toBe(0);

      // Verify tasks were updated
      for (const taskId of taskIds) {
        const updatedTask = await taskService.getTask(taskId);
        expect(updatedTask.priority).toBe(updateData.priority);
        expect(updatedTask.description).toBe(updateData.description);
      }
    });

    test('should batch delete tasks', async () => {
      const taskIds = tasks.map(task => task.id);

      const result = await taskService.batchDeleteTasks(taskIds);

      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      expect(result.deleted).toBe(taskIds.length);
      expect(result.failed).toBe(0);

      // Verify tasks were deleted
      for (const taskId of taskIds) {
        await expect(taskService.getTask(taskId)).rejects.toThrow();
      }

      // Remove from cleanup list since they're already deleted
      createdTaskIds = createdTaskIds.filter(id => !taskIds.includes(id));
    });
  });

  describe('Task Statistics', () => {
    beforeEach(async () => {
      // Create some tasks for statistics
      for (let i = 0; i < 3; i++) {
        const task = await taskService.createTask({
          ...generateTestTask(),
          title: `Stats Task ${i}`,
          priority: i % 2 === 0 ? 'high' : 'low'
        });
        createdTaskIds.push(task.id);
      }
    });

    test('should get task statistics', async () => {
      const stats = await taskService.getTaskStatistics();

      expect(stats).toBeDefined();
      expect(typeof stats.total_tasks).toBe('number');
      expect(typeof stats.completed_tasks).toBe('number');
      expect(typeof stats.failed_tasks).toBe('number');
      expect(typeof stats.running_tasks).toBe('number');
      expect(typeof stats.pending_tasks).toBe('number');
      expect(typeof stats.cancelled_tasks).toBe('number');
      expect(typeof stats.average_execution_time).toBe('number');
      expect(typeof stats.success_rate).toBe('number');
      expect(typeof stats.task_types).toBe('object');
      expect(typeof stats.priority_distribution).toBe('object');
      expect(Array.isArray(stats.hourly_distribution)).toBe(true);
      expect(Array.isArray(stats.daily_distribution)).toBe(true);

      expect(stats.total_tasks).toBeGreaterThanOrEqual(3);
    });

    test('should get task statistics with filters', async () => {
      const stats = await taskService.getTaskStatistics({
        agent_id: testAgent.id,
        start_date: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // Last 24 hours
        end_date: new Date().toISOString()
      });

      expect(stats).toBeDefined();
      expect(typeof stats.total_tasks).toBe('number');
    });
  });

  describe('Task Validation', () => {
    test('should fail to create task with invalid data', async () => {
      const invalidTaskData = {
        title: '', // Empty title
        type: 'invalid_type',
        priority: 'invalid_priority' as any,
        estimated_duration: -1, // Negative duration
        max_retries: -1, // Negative retries
        timeout_seconds: 0 // Zero timeout
      };

      await expect(taskService.createTask(invalidTaskData as any)).rejects.toThrow();
    });

    test('should fail to create task with missing required fields', async () => {
      const incompleteTaskData = {
        title: 'Incomplete Task'
        // Missing required fields
      };

      await expect(taskService.createTask(incompleteTaskData as any)).rejects.toThrow();
    });

    test('should fail to create task without authentication', async () => {
      // Logout to remove authentication
      authService.logout();

      const taskData = generateTestTask();

      await expect(taskService.createTask(taskData)).rejects.toThrow();

      // Re-login for other tests
      await authService.login({
        username: testUser.username,
        password: testUser.password
      });
    });
  });
});