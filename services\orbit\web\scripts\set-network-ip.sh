#!/bin/bash

# Get the current machine's IP address (excluding localhost)
get_network_ip() {
    # Try different methods to get the network IP
    if command -v ifconfig &> /dev/null; then
        # Using ifconfig (macOS/Linux)
        IP=$(ifconfig | grep -E "inet ([0-9]{1,3}\.){3}[0-9]{1,3}" | grep -v 127.0.0.1 | awk '{print $2}' | head -1)
    elif command -v ip &> /dev/null; then
        # Using ip command (Linux)
        IP=$(ip route get ******* | grep -oE '((1?[0-9][0-9]?|2[0-4][0-9]|25[0-5])\.){3}(1?[0-9][0-9]?|2[0-4][0-9]|25[0-5])' | head -1)
    elif command -v hostname &> /dev/null; then
        # Using hostname (fallback)
        IP=$(hostname -I | awk '{print $1}' 2>/dev/null || hostname -i | awk '{print $1}' 2>/dev/null)
    fi
    
    # If still no IP, try a different approach
    if [ -z "$IP" ]; then
        IP=$(ping -c 1 google.com | grep PING | sed 's/.*(\([0-9.]*\)).*/\1/' 2>/dev/null || echo "localhost")
    fi
    
    echo "$IP"
}

# Get the network IP
NETWORK_IP=$(get_network_ip)

if [ "$NETWORK_IP" != "localhost" ] && [ ! -z "$NETWORK_IP" ]; then
    echo "Setting API base URL to use network IP: $NETWORK_IP"
    export VITE_API_BASE_URL="http://$NETWORK_IP:8085/api/v1"
    echo "VITE_API_BASE_URL=http://$NETWORK_IP:8085/api/v1" > .env.local
    echo "✅ Network IP set successfully!"
    echo "Frontend will now connect to: http://$NETWORK_IP:8085/api/v1"
    echo "Gateway will be accessible at: http://$NETWORK_IP:8085"
    echo ""
    echo "To use this configuration:"
    echo "1. Start backend: bazel run //platform/crm_backend:crm_backend"
    echo "2. Start gateway: bazel run //platform/gateway:gateway"
    echo "3. Start frontend: npm run dev"
    echo "3. Access from other devices: http://$NETWORK_IP:8080"
else
    echo "❌ Could not determine network IP address"
    echo "Falling back to localhost configuration"
    export VITE_API_BASE_URL="http://localhost:8085/api/v1"
    echo "VITE_API_BASE_URL=http://localhost:8085/api/v1" > .env.local
fi