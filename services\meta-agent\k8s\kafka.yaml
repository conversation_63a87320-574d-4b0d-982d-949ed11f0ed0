apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: kafka-pvc
  namespace: ai-agent-platform
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: standard

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: zookeeper-pvc
  namespace: ai-agent-platform
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
  storageClassName: standard

---
# Zookeeper Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: zookeeper
  namespace: ai-agent-platform
  labels:
    app: zookeeper
    component: messaging
spec:
  replicas: 1
  strategy:
    type: Recreate
  selector:
    matchLabels:
      app: zookeeper
  template:
    metadata:
      labels:
        app: zookeeper
        component: messaging
    spec:
      containers:
      - name: zookeeper
        image: confluentinc/cp-zookeeper:7.4.0
        ports:
        - containerPort: 2181
          name: zookeeper
        env:
        - name: ZOOKEEPER_CLIENT_PORT
          value: "2181"
        - name: ZOOKEEPER_TICK_TIME
          value: "2000"
        - name: ZO<PERSON>EEPER_SYNC_LIMIT
          value: "2"
        - name: Z<PERSON><PERSON>EEPER_INIT_LIMIT
          value: "5"
        - name: ZOOKEEPER_MAX_CLIENT_CNXNS
          value: "60"
        - name: ZOOKEEPER_AUTOPURGE_SNAP_RETAIN_COUNT
          value: "3"
        - name: ZOOKEEPER_AUTOPURGE_PURGE_INTERVAL
          value: "24"
        volumeMounts:
        - name: zookeeper-storage
          mountPath: /var/lib/zookeeper/data
        - name: zookeeper-logs
          mountPath: /var/lib/zookeeper/log
        resources:
          requests:
            memory: "512Mi"
            cpu: "200m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          exec:
            command:
            - sh
            - -c
            - "echo ruok | nc localhost 2181 | grep imok"
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - sh
            - -c
            - "echo ruok | nc localhost 2181 | grep imok"
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 3
          failureThreshold: 3
      volumes:
      - name: zookeeper-storage
        persistentVolumeClaim:
          claimName: zookeeper-pvc
      - name: zookeeper-logs
        emptyDir: {}

---
apiVersion: v1
kind: Service
metadata:
  name: zookeeper
  namespace: ai-agent-platform
  labels:
    app: zookeeper
    component: messaging
spec:
  type: ClusterIP
  ports:
  - port: 2181
    targetPort: 2181
    protocol: TCP
    name: zookeeper
  selector:
    app: zookeeper

---
# Kafka Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kafka
  namespace: ai-agent-platform
  labels:
    app: kafka
    component: messaging
spec:
  replicas: 1
  strategy:
    type: Recreate
  selector:
    matchLabels:
      app: kafka
  template:
    metadata:
      labels:
        app: kafka
        component: messaging
    spec:
      containers:
      - name: kafka
        image: confluentinc/cp-kafka:7.4.0
        ports:
        - containerPort: 9092
          name: kafka
        - containerPort: 9093
          name: kafka-external
        env:
        - name: KAFKA_BROKER_ID
          value: "1"
        - name: KAFKA_ZOOKEEPER_CONNECT
          value: "zookeeper:2181"
        - name: KAFKA_ADVERTISED_LISTENERS
          value: "PLAINTEXT://kafka:9092,PLAINTEXT_HOST://localhost:9093"
        - name: KAFKA_LISTENER_SECURITY_PROTOCOL_MAP
          value: "PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT"
        - name: KAFKA_INTER_BROKER_LISTENER_NAME
          value: "PLAINTEXT"
        - name: KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR
          value: "1"
        - name: KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR
          value: "1"
        - name: KAFKA_TRANSACTION_STATE_LOG_MIN_ISR
          value: "1"
        - name: KAFKA_NUM_PARTITIONS
          value: "3"
        - name: KAFKA_DEFAULT_REPLICATION_FACTOR
          value: "1"
        - name: KAFKA_LOG_RETENTION_HOURS
          value: "168"  # 7 days
        - name: KAFKA_LOG_RETENTION_BYTES
          value: "1073741824"  # 1GB
        - name: KAFKA_LOG_SEGMENT_BYTES
          value: "104857600"  # 100MB
        - name: KAFKA_AUTO_CREATE_TOPICS_ENABLE
          value: "true"
        - name: KAFKA_DELETE_TOPIC_ENABLE
          value: "true"
        - name: KAFKA_JMX_PORT
          value: "9999"
        - name: KAFKA_JMX_HOSTNAME
          value: "kafka"
        volumeMounts:
        - name: kafka-storage
          mountPath: /var/lib/kafka/data
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          exec:
            command:
            - sh
            - -c
            - "kafka-broker-api-versions --bootstrap-server localhost:9092"
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - sh
            - -c
            - "kafka-broker-api-versions --bootstrap-server localhost:9092"
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
      volumes:
      - name: kafka-storage
        persistentVolumeClaim:
          claimName: kafka-pvc
      initContainers:
      - name: wait-for-zookeeper
        image: confluentinc/cp-kafka:7.4.0
        command:
        - sh
        - -c
        - |
          echo "Waiting for Zookeeper..."
          until nc -z zookeeper 2181; do
            echo "Zookeeper not ready, waiting..."
            sleep 2
          done
          echo "Zookeeper is ready."

---
apiVersion: v1
kind: Service
metadata:
  name: kafka
  namespace: ai-agent-platform
  labels:
    app: kafka
    component: messaging
spec:
  type: ClusterIP
  ports:
  - port: 9092
    targetPort: 9092
    protocol: TCP
    name: kafka
  - port: 9999
    targetPort: 9999
    protocol: TCP
    name: jmx
  selector:
    app: kafka

---
# Kafka Topic Creation Job
apiVersion: batch/v1
kind: Job
metadata:
  name: kafka-topics-setup
  namespace: ai-agent-platform
spec:
  template:
    spec:
      containers:
      - name: kafka-topics
        image: confluentinc/cp-kafka:7.4.0
        command:
        - sh
        - -c
        - |
          echo "Waiting for Kafka to be ready..."
          until kafka-broker-api-versions --bootstrap-server kafka:9092; do
            echo "Kafka not ready, waiting..."
            sleep 5
          done
          
          echo "Creating Kafka topics..."
          
          # Agent Events Topic
          kafka-topics --bootstrap-server kafka:9092 --create --topic agent-events \
            --partitions 3 --replication-factor 1 --if-not-exists \
            --config retention.ms=604800000 --config cleanup.policy=delete
          
          # Orchestration Topic
          kafka-topics --bootstrap-server kafka:9092 --create --topic orchestration-commands \
            --partitions 3 --replication-factor 1 --if-not-exists \
            --config retention.ms=604800000 --config cleanup.policy=delete
          
          # Intelligence Topic
          kafka-topics --bootstrap-server kafka:9092 --create --topic intelligence-requests \
            --partitions 3 --replication-factor 1 --if-not-exists \
            --config retention.ms=604800000 --config cleanup.policy=delete
          
          # Task Events Topic
          kafka-topics --bootstrap-server kafka:9092 --create --topic task-events \
            --partitions 3 --replication-factor 1 --if-not-exists \
            --config retention.ms=604800000 --config cleanup.policy=delete
          
          # System Events Topic
          kafka-topics --bootstrap-server kafka:9092 --create --topic system-events \
            --partitions 2 --replication-factor 1 --if-not-exists \
            --config retention.ms=2592000000 --config cleanup.policy=delete
          
          echo "Topics created successfully:"
          kafka-topics --bootstrap-server kafka:9092 --list
      restartPolicy: OnFailure
  backoffLimit: 5

---
# Kafka Exporter for Monitoring
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kafka-exporter
  namespace: ai-agent-platform
  labels:
    app: kafka-exporter
    component: monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: kafka-exporter
  template:
    metadata:
      labels:
        app: kafka-exporter
        component: monitoring
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9308"
    spec:
      containers:
      - name: kafka-exporter
        image: danielqsj/kafka-exporter:latest
        ports:
        - containerPort: 9308
          name: metrics
        args:
        - --kafka.server=kafka:9092
        - --web.listen-address=0.0.0.0:9308
        - --log.level=info
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /metrics
            port: 9308
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /metrics
            port: 9308
          initialDelaySeconds: 10
          periodSeconds: 10

---
apiVersion: v1
kind: Service
metadata:
  name: kafka-exporter
  namespace: ai-agent-platform
  labels:
    app: kafka-exporter
    component: monitoring
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "9308"
spec:
  type: ClusterIP
  ports:
  - port: 9308
    targetPort: 9308
    protocol: TCP
    name: metrics
  selector:
    app: kafka-exporter

---
# Kafka Management UI (optional)
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kafka-ui
  namespace: ai-agent-platform
  labels:
    app: kafka-ui
    component: management
spec:
  replicas: 1
  selector:
    matchLabels:
      app: kafka-ui
  template:
    metadata:
      labels:
        app: kafka-ui
        component: management
    spec:
      containers:
      - name: kafka-ui
        image: provectuslabs/kafka-ui:latest
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: KAFKA_CLUSTERS_0_NAME
          value: "ai-agent-platform"
        - name: KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS
          value: "kafka:9092"
        - name: KAFKA_CLUSTERS_0_ZOOKEEPER
          value: "zookeeper:2181"
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m"

---
apiVersion: v1
kind: Service
metadata:
  name: kafka-ui
  namespace: ai-agent-platform
  labels:
    app: kafka-ui
    component: management
spec:
  type: ClusterIP
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: kafka-ui