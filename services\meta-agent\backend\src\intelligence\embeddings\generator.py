"""
Embedding Generator using established libraries
Uses sentence-transformers for robust text embeddings
"""

from typing import List, Dict, Any, Optional, Union
import numpy as np
import asyncio
from dataclasses import dataclass
from datetime import datetime
import json

try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False

from utils.logging import get_logger

logger = get_logger(__name__)


@dataclass
class EmbeddingResult:
    """Result of embedding generation"""
    text: str
    embedding: np.ndarray
    model_name: str
    dimensions: int
    created_at: datetime
    metadata: Dict[str, Any]


class EmbeddingGenerator:
    """Embedding generator using sentence-transformers and other established libraries"""
    
    def __init__(self, model_name: str = "all-MiniLM-L6-v2"):
        """
        Initialize embedding generator
        
        Args:
            model_name: Name of the sentence-transformers model to use
        """
        self.model_name = model_name
        self.model = None
        self._initialize_model()
    
    def _initialize_model(self):
        """Initialize the embedding model"""
        if not SENTENCE_TRANSFORMERS_AVAILABLE:
            logger.warning("sentence-transformers not available, using fallback implementation")
            return
        
        try:
            logger.info(f"Loading sentence-transformers model: {self.model_name}")
            self.model = SentenceTransformer(self.model_name)
            logger.info(f"Successfully loaded model with {self.model.get_sentence_embedding_dimension()} dimensions")
        except Exception as e:
            logger.error(f"Failed to load sentence-transformers model: {e}")
            self.model = None
    
    async def generate_embeddings(
        self, 
        texts: Union[str, List[str]], 
        batch_size: int = 32,
        normalize: bool = True
    ) -> Union[EmbeddingResult, List[EmbeddingResult]]:
        """
        Generate embeddings for text(s)
        
        Args:
            texts: Single text or list of texts
            batch_size: Batch size for processing
            normalize: Whether to normalize embeddings
            
        Returns:
            EmbeddingResult or list of EmbeddingResults
        """
        if isinstance(texts, str):
            texts = [texts]
            single_text = True
        else:
            single_text = False
        
        if not self.model and SENTENCE_TRANSFORMERS_AVAILABLE:
            self._initialize_model()
        
        if self.model:
            # Use sentence-transformers
            embeddings = await self._generate_with_sentence_transformers(
                texts, batch_size, normalize
            )
        else:
            # Fallback to simple implementation
            embeddings = await self._generate_fallback(texts)
        
        results = []
        for i, text in enumerate(texts):
            result = EmbeddingResult(
                text=text,
                embedding=embeddings[i],
                model_name=self.model_name,
                dimensions=len(embeddings[i]),
                created_at=datetime.now(),
                metadata={"batch_size": batch_size, "normalized": normalize}
            )
            results.append(result)
        
        return results[0] if single_text else results
    
    async def _generate_with_sentence_transformers(
        self, 
        texts: List[str], 
        batch_size: int, 
        normalize: bool
    ) -> List[np.ndarray]:
        """Generate embeddings using sentence-transformers"""
        
        # Run in thread pool to avoid blocking
        loop = asyncio.get_event_loop()
        
        def _encode_batch(batch_texts):
            return self.model.encode(
                batch_texts,
                normalize_embeddings=normalize,
                show_progress_bar=False
            )
        
        all_embeddings = []
        
        # Process in batches
        for i in range(0, len(texts), batch_size):
            batch = texts[i:i + batch_size]
            embeddings = await loop.run_in_executor(None, _encode_batch, batch)
            all_embeddings.extend(embeddings)
        
        return all_embeddings
    
    async def _generate_fallback(self, texts: List[str]) -> List[np.ndarray]:
        """Fallback embedding generation using simple hashing"""
        logger.warning("Using fallback embedding generation - install sentence-transformers for better results")
        
        embeddings = []
        for text in texts:
            # Simple hash-based embedding (not suitable for production)
            import hashlib
            hash_obj = hashlib.sha256(text.encode())
            hash_bytes = hash_obj.digest()
            
            # Convert to float array and normalize
            embedding = np.frombuffer(hash_bytes, dtype=np.uint8).astype(np.float32)
            # Pad or truncate to 384 dimensions (common size)
            if len(embedding) < 384:
                embedding = np.pad(embedding, (0, 384 - len(embedding)))
            else:
                embedding = embedding[:384]
            
            # Normalize
            norm = np.linalg.norm(embedding)
            if norm > 0:
                embedding = embedding / norm
            
            embeddings.append(embedding)
        
        return embeddings
    
    def get_similarity(
        self, 
        embedding1: np.ndarray, 
        embedding2: np.ndarray,
        method: str = "cosine"
    ) -> float:
        """
        Calculate similarity between two embeddings
        
        Args:
            embedding1: First embedding
            embedding2: Second embedding
            method: Similarity method ("cosine", "euclidean", "dot")
            
        Returns:
            Similarity score
        """
        if method == "cosine":
            # Cosine similarity
            dot_product = np.dot(embedding1, embedding2)
            norm1 = np.linalg.norm(embedding1)
            norm2 = np.linalg.norm(embedding2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            return dot_product / (norm1 * norm2)
        
        elif method == "euclidean":
            # Euclidean distance (converted to similarity)
            distance = np.linalg.norm(embedding1 - embedding2)
            return 1.0 / (1.0 + distance)
        
        elif method == "dot":
            # Dot product
            return np.dot(embedding1, embedding2)
        
        else:
            raise ValueError(f"Unknown similarity method: {method}")
    
    async def find_similar_texts(
        self,
        query_text: str,
        candidate_texts: List[str],
        top_k: int = 5,
        similarity_threshold: float = 0.5
    ) -> List[Dict[str, Any]]:
        """
        Find similar texts to a query
        
        Args:
            query_text: Query text
            candidate_texts: List of candidate texts
            top_k: Number of top results to return
            similarity_threshold: Minimum similarity threshold
            
        Returns:
            List of similar texts with scores
        """
        # Generate embeddings
        query_result = await self.generate_embeddings(query_text)
        candidate_results = await self.generate_embeddings(candidate_texts)
        
        similarities = []
        for i, candidate_result in enumerate(candidate_results):
            similarity = self.get_similarity(
                query_result.embedding,
                candidate_result.embedding
            )
            
            if similarity >= similarity_threshold:
                similarities.append({
                    "text": candidate_texts[i],
                    "similarity": float(similarity),
                    "index": i
                })
        
        # Sort by similarity and return top_k
        similarities.sort(key=lambda x: x["similarity"], reverse=True)
        return similarities[:top_k]
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the current model"""
        info = {
            "model_name": self.model_name,
            "sentence_transformers_available": SENTENCE_TRANSFORMERS_AVAILABLE,
            "model_loaded": self.model is not None
        }
        
        if self.model and SENTENCE_TRANSFORMERS_AVAILABLE:
            info.update({
                "dimensions": self.model.get_sentence_embedding_dimension(),
                "max_seq_length": getattr(self.model, 'max_seq_length', 'unknown')
            })
        
        return info
    
    def save_embeddings(
        self,
        embeddings: List[EmbeddingResult],
        filepath: str,
        format: str = "npy"
    ) -> bool:
        """
        Save embeddings to file
        
        Args:
            embeddings: List of embedding results
            filepath: Path to save file
            format: Format to save ("npy", "json")
            
        Returns:
            Success status
        """
        try:
            if format == "npy":
                # Save as numpy arrays
                data = {
                    "embeddings": np.array([e.embedding for e in embeddings]),
                    "texts": [e.text for e in embeddings],
                    "metadata": [e.metadata for e in embeddings],
                    "model_name": embeddings[0].model_name if embeddings else self.model_name
                }
                np.savez(filepath, **data)
            
            elif format == "json":
                # Save as JSON (embeddings as lists)
                data = []
                for e in embeddings:
                    data.append({
                        "text": e.text,
                        "embedding": e.embedding.tolist(),
                        "model_name": e.model_name,
                        "dimensions": e.dimensions,
                        "created_at": e.created_at.isoformat(),
                        "metadata": e.metadata
                    })
                
                with open(filepath, 'w') as f:
                    json.dump(data, f, indent=2)
            
            else:
                raise ValueError(f"Unsupported format: {format}")
            
            logger.info(f"Saved {len(embeddings)} embeddings to {filepath}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save embeddings: {e}")
            return False
    
    def load_embeddings(self, filepath: str, format: str = "npy") -> List[EmbeddingResult]:
        """
        Load embeddings from file
        
        Args:
            filepath: Path to load file
            format: Format to load ("npy", "json")
            
        Returns:
            List of embedding results
        """
        try:
            if format == "npy":
                data = np.load(filepath, allow_pickle=True)
                embeddings = data["embeddings"]
                texts = data["texts"]
                metadata_list = data.get("metadata", [{}] * len(texts))
                model_name = str(data.get("model_name", self.model_name))
                
                results = []
                for i, (text, embedding) in enumerate(zip(texts, embeddings)):
                    result = EmbeddingResult(
                        text=str(text),
                        embedding=embedding,
                        model_name=model_name,
                        dimensions=len(embedding),
                        created_at=datetime.now(),
                        metadata=metadata_list[i] if i < len(metadata_list) else {}
                    )
                    results.append(result)
                
                return results
            
            elif format == "json":
                with open(filepath, 'r') as f:
                    data = json.load(f)
                
                results = []
                for item in data:
                    result = EmbeddingResult(
                        text=item["text"],
                        embedding=np.array(item["embedding"]),
                        model_name=item["model_name"],
                        dimensions=item["dimensions"],
                        created_at=datetime.fromisoformat(item["created_at"]),
                        metadata=item["metadata"]
                    )
                    results.append(result)
                
                return results
            
            else:
                raise ValueError(f"Unsupported format: {format}")
        
        except Exception as e:
            logger.error(f"Failed to load embeddings: {e}")
            return []