#!/usr/bin/env python3
"""
Test PostgreSQL database connection
"""
import os
import asyncio
from dotenv import load_dotenv
from sqlalchemy import text
from sqlalchemy.ext.asyncio import create_async_engine

async def test_connection():
    # Load environment variables from .env file
    load_dotenv()
    
    database_url = os.getenv("DATABASE_URL", "sqlite:///./ai_agent_platform.db")
    print(f"Database URL: {database_url}")
    
    if database_url.startswith("postgresql"):
        # Convert to async URL
        async_url = database_url.replace("postgresql://", "postgresql+asyncpg://")
        print(f"Async URL: {async_url}")
        
        try:
            # Create engine
            engine = create_async_engine(async_url, echo=True)
            
            # Test connection
            async with engine.begin() as conn:
                result = await conn.execute(text("SELECT version()"))
                version = result.fetchone()
                print(f"PostgreSQL Version: {version[0]}")
                
                # Test table creation
                await conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS test_table (
                        id SERIAL PRIMARY KEY,
                        name VARCHAR(100)
                    )
                """))
                print("Test table created successfully")
                
                # Clean up
                await conn.execute(text("DROP TABLE IF EXISTS test_table"))
                print("Test table dropped successfully")
                
            await engine.dispose()
            print("PostgreSQL connection test: PASSED")
            return True
            
        except Exception as e:
            print(f"PostgreSQL connection test: FAILED - {e}")
            return False
    else:
        print("Not using PostgreSQL, using SQLite fallback")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_connection())
    exit(0 if success else 1)