/* tslint:disable */
/* eslint-disable */
/**
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface InteractionWithDetailsAllOfCompany
 */
export interface InteractionWithDetailsAllOfCompany {
    /**
     * 
     * @type {string}
     * @memberof InteractionWithDetailsAllOfCompany
     */
    id?: string;
    /**
     * 
     * @type {string}
     * @memberof InteractionWithDetailsAllOfCompany
     */
    name?: string;
}

/**
 * Check if a given object implements the InteractionWithDetailsAllOfCompany interface.
 */
export function instanceOfInteractionWithDetailsAllOfCompany(value: object): value is InteractionWithDetailsAllOfCompany {
    return true;
}

export function InteractionWithDetailsAllOfCompanyFromJSON(json: any): InteractionWithDetailsAllOfCompany {
    return InteractionWithDetailsAllOfCompanyFromJSONTyped(json, false);
}

export function InteractionWithDetailsAllOfCompanyFromJSONTyped(json: any, ignoreDiscriminator: boolean): InteractionWithDetailsAllOfCompany {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'] == null ? undefined : json['id'],
        'name': json['name'] == null ? undefined : json['name'],
    };
}

  export function InteractionWithDetailsAllOfCompanyToJSON(json: any): InteractionWithDetailsAllOfCompany {
      return InteractionWithDetailsAllOfCompanyToJSONTyped(json, false);
  }

  export function InteractionWithDetailsAllOfCompanyToJSONTyped(value?: InteractionWithDetailsAllOfCompany | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'name': value['name'],
    };
}

