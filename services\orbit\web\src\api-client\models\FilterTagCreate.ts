/* tslint:disable */
/* eslint-disable */
/**
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface FilterTagCreate
 */
export interface FilterTagCreate {
    /**
     * 
     * @type {string}
     * @memberof FilterTagCreate
     */
    name: string;
    /**
     * 
     * @type {string}
     * @memberof FilterTagCreate
     */
    color?: string;
}

/**
 * Check if a given object implements the FilterTagCreate interface.
 */
export function instanceOfFilterTagCreate(value: object): value is FilterTagCreate {
    if (!('name' in value) || value['name'] === undefined) return false;
    return true;
}

export function FilterTagCreateFromJSON(json: any): FilterTagCreate {
    return FilterTagCreateFromJSONTyped(json, false);
}

export function FilterTagCreateFromJSONTyped(json: any, ignoreDiscriminator: boolean): FilterTagCreate {
    if (json == null) {
        return json;
    }
    return {
        
        'name': json['name'],
        'color': json['color'] == null ? undefined : json['color'],
    };
}

  export function FilterTagCreateToJSON(json: any): FilterTagCreate {
      return FilterTagCreateToJSONTyped(json, false);
  }

  export function FilterTagCreateToJSONTyped(value?: FilterTagCreate | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'name': value['name'],
        'color': value['color'],
    };
}

