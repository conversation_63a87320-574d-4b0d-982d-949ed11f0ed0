---
# Terraform-generated variables for prod environment
# This file is automatically generated - do not edit manually

# GCP Configuration
gcp_project_id: "twodot-agent-prod"
environment: "prod"
deployment_environment: "prod"

# Registry Configuration
artifact_registry: "microservices"
artifact_registry_location: "us-central1"
registry_url: "australia-southeast1-docker.pkg.dev/twodot-agent-prod/platform-docker"

# Database Configuration
database_host: "**********"
database_name: "platform_prod_db"
database_user: "platform_prod_user"
database_password_secret: "platform-db-password"

# Service Versions
gateway_version: "latest"
auth_version: "latest"
backend_version: "latest"

# Deployment Configuration
deployment_color: "blue"
deployment_strategy: "blue-green"

# Service-specific configuration overrides from Terraform
# These will be merged with the base configuration in vars/prod.yml