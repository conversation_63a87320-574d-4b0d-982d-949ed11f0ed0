---
- name: Deploy Microservices to GCP Container-Optimized OS
  hosts: microservices
  become: yes
  gather_facts: yes
  
  vars:
    project_id: "{{ gcp_project_id }}"
    deployment_environment: "dev"
    deployment_color: "{{ 'green' if current_color | default('blue') == 'blue' else 'blue' }}"
    
  pre_tasks:
    - name: Load environment-specific variables
      include_vars: "../vars/{{ deployment_environment }}.yml"
      
    - name: Get database password from Secret Manager
      shell: |
        TOKEN=$(curl -H "Metadata-Flavor: Google" \
          http://metadata.google.internal/computeMetadata/v1/instance/service-accounts/default/token | \
          python3 -c "import sys, json; print(json.load(sys.stdin)['access_token'])")
        curl -H "Authorization: Bearer $TOKEN" \
          "https://secretmanager.googleapis.com/v1/projects/{{ project_id }}/secrets/platform-db-password/versions/latest:access" | \
          python3 -c "import sys, json, base64; print(base64.b64decode(json.load(sys.stdin)['payload']['data']).decode())"
      register: database_password_result
      
    - name: Set database password variable
      set_fact:
        database_password: "{{ database_password_result.stdout.strip() }}"
        
    - name: URL encode database password
      set_fact:
        database_password_encoded: "{{ database_password | urlencode }}"
      
    - name: Ensure required variables are defined
      assert:
        that:
          - project_id is defined
          - services is defined
          - database_password is defined
        fail_msg: "Required variables are not defined"
        
    - name: Check if Docker is running
      systemd:
        name: docker
        state: started
        enabled: yes
        
    - name: Create application directories
      file:
        path: "{{ item }}"
        state: directory
        mode: '0755'
      loop:
        - /mnt/stateful_partition/platform
        - /mnt/stateful_partition/platform/services
        - /mnt/stateful_partition/platform/logs
        
  tasks:
    - name: Create Docker Compose configuration
      template:
        src: "../templates/docker-compose-cos.yml.j2"
        dest: /mnt/stateful_partition/platform/docker-compose.yml
        mode: '0644'
      tags: ['compose']
      
    # Skip nginx for COS - gateway handles routing directly
      
    - name: Create Docker network
      shell: docker network create platform-network || true
      tags: ['network']
      
    - name: Create Docker config directory in writable location
      shell: |
        mkdir -p /mnt/stateful_partition/platform/.docker
        export DOCKER_CONFIG=/mnt/stateful_partition/platform/.docker
      tags: ['auth']
      
    - name: Configure Docker authentication for Artifact Registry  
      shell: |
        export DOCKER_CONFIG=/mnt/stateful_partition/platform/.docker
        TOKEN=$(curl -H "Metadata-Flavor: Google" \
          http://metadata.google.internal/computeMetadata/v1/instance/service-accounts/default/token | \
          python3 -c "import sys, json; print(json.load(sys.stdin)['access_token'])")
        echo $TOKEN | docker login -u oauth2accesstoken --password-stdin https://australia-southeast1-docker.pkg.dev
      tags: ['auth']
      
    - name: Pull Docker images
      shell: |
        export DOCKER_CONFIG=/mnt/stateful_partition/platform/.docker
        docker pull {{ item.value.image }}
      loop: "{{ services | dict2items }}"
      tags: ['images']
      
    - name: Stop existing containers (if any)
      shell: |
        docker stop {{ item.key }} || true
        docker rm {{ item.key }} || true
      loop: "{{ services | dict2items }}"
      tags: ['cleanup']
      
    - name: Start auth service
      shell: |
        docker run -d \
          --name auth \
          --network platform-network \
          -p {{ services.auth.port }}:{{ services.auth.port }} \
          -e LOG_LEVEL={{ services.auth.environment.LOG_LEVEL }} \
          -e JWT_EXPIRY={{ services.auth.environment.JWT_EXPIRY }} \
          -e SESSION_TIMEOUT={{ services.auth.environment.SESSION_TIMEOUT }} \
          -e DATABASE_URL="{{ services.auth.environment.DATABASE_URL }}" \
          --restart unless-stopped \
          {{ services.auth.image }}
      tags: ['deploy']
      
    - name: Start crm-backend service  
      shell: |
        docker run -d \
          --name crm-backend \
          --network platform-network \
          -p {{ services.crm_backend.port }}:{{ services.crm_backend.port }} \
          -e LOG_LEVEL={{ services.crm_backend.environment.LOG_LEVEL }} \
          -e ENABLE_SWAGGER={{ services.crm_backend.environment.ENABLE_SWAGGER }} \
          -e MAX_PAGE_SIZE={{ services.crm_backend.environment.MAX_PAGE_SIZE }} \
          -e DEFAULT_PAGE_SIZE={{ services.crm_backend.environment.DEFAULT_PAGE_SIZE }} \
          -e DATABASE_URL="{{ services.crm_backend.environment.DATABASE_URL }}" \
          --restart unless-stopped \
          {{ services.crm_backend.image }}
      tags: ['deploy']
      
    - name: Start gateway service
      shell: |
        docker run -d \
          --name gateway \
          --network platform-network \
          -p 80:{{ services.gateway.port }} \
          -p {{ services.gateway.port }}:{{ services.gateway.port }} \
          -e LOG_LEVEL={{ services.gateway.environment.LOG_LEVEL }} \
          -e AUTH_SERVICE_URL={{ services.gateway.environment.AUTH_SERVICE_URL }} \
          -e CRM_BACKEND_URL={{ services.gateway.environment.CRM_BACKEND_URL }} \
          -e CORS_ALLOWED_ORIGINS="{{ services.gateway.environment.CORS_ALLOWED_ORIGINS }}" \
          --restart unless-stopped \
          {{ services.gateway.image }}
      tags: ['deploy']
      
    - name: Wait for services to be healthy
      uri:
        url: "http://localhost:{{ item.value.port }}{{ item.value.health_check.path | default('/health') }}"
        method: GET
        status_code: 200
      retries: 30
      delay: 10
      loop: "{{ services | dict2items }}"
      when: item.value.health_check is defined
      tags: ['health-check']
      
    - name: Save deployment metadata
      copy:
        content: |
          deployment_time: {{ ansible_date_time.iso8601 }}
          deployment_color: {{ deployment_color }}
          services:
          {% for service, config in services.items() %}
            {{ service }}: {{ config.image }}
          {% endfor %}
        dest: /mnt/stateful_partition/platform/current-deployment.yml
      tags: ['metadata']