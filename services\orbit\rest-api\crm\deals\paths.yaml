# Deal paths with named anchors
deals: &deals
  get:
    tags: [Deals]
    summary: List deals
    description: Retrieve a list of all deals with company and stage information
    parameters:
      - name: stage_id
        in: query
        schema:
          type: string
          format: uuid
        description: Filter by deal stage ID
      - name: company_id
        in: query
        schema:
          type: string
          format: uuid
        description: Filter by company ID
    responses:
      '200':
        description: List of deals
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: './schemas.yaml#/DealWithDetails'
      '400':
        description: Invalid input
        content:
          application/json:
            schema:
              $ref: '../../auth/schemas.yaml#/ValidationError'

  post:
    tags: [Deals]
    summary: Create deal
    description: Create a new deal
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: './schemas.yaml#/DealCreate'
    responses:
      '201':
        description: Deal created successfully
        content:
          application/json:
            schema:
              $ref: './schemas.yaml#/Deal'
      '400':
        description: Invalid input
        content:
          application/json:
            schema:
              $ref: '../../auth/schemas.yaml#/ValidationError'

deals-by-id: &deals-by-id
  get:
    tags: [Deals]
    summary: Get deal
    description: Retrieve a specific deal by ID
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: string
          format: uuid
    responses:
      '200':
        description: Deal details
        content:
          application/json:
            schema:
              $ref: './schemas.yaml#/DealDetails'
      '404':
        description: Deal not found
        content:
          application/json:
            schema:
              $ref: '../../auth/schemas.yaml#/ErrorResponse'

  put:
    tags: [Deals]
    summary: Update deal
    description: Update an existing deal
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: string
          format: uuid
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: './schemas.yaml#/DealUpdate'
    responses:
      '200':
        description: Deal updated successfully
        content:
          application/json:
            schema:
              $ref: './schemas.yaml#/Deal'
      '400':
        description: Invalid input
        content:
          application/json:
            schema:
              $ref: '../../auth/schemas.yaml#/ValidationError'
      '404':
        description: Deal not found
        content:
          application/json:
            schema:
              $ref: '../../auth/schemas.yaml#/ErrorResponse'

  delete:
    tags: [Deals]
    summary: Delete deal
    description: Delete a deal
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: string
          format: uuid
    responses:
      '204':
        description: Deal deleted successfully
      '404':
        description: Deal not found
        content:
          application/json:
            schema:
              $ref: '../../auth/schemas.yaml#/ErrorResponse'

deal-stages: &deal-stages
  get:
    tags: [Deals]
    summary: List deal stages
    description: Retrieve all deal stages ordered by pipeline order
    responses:
      '200':
        description: List of deal stages
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: './schemas.yaml#/DealStage'