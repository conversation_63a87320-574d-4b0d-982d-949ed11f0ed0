/* tslint:disable */
/* eslint-disable */
/**
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface UserProfile
 */
export interface UserProfile {
    /**
     * 
     * @type {string}
     * @memberof UserProfile
     */
    id?: string;
    /**
     * 
     * @type {string}
     * @memberof UserProfile
     */
    fullName?: string;
    /**
     * 
     * @type {string}
     * @memberof UserProfile
     */
    avatarUrl?: string;
    /**
     * 
     * @type {string}
     * @memberof UserProfile
     */
    timezone?: string;
    /**
     * 
     * @type {Date}
     * @memberof UserProfile
     */
    createdAt?: Date;
    /**
     * 
     * @type {Date}
     * @memberof UserProfile
     */
    updatedAt?: Date;
}

/**
 * Check if a given object implements the UserProfile interface.
 */
export function instanceOfUserProfile(value: object): value is UserProfile {
    return true;
}

export function UserProfileFromJSON(json: any): UserProfile {
    return UserProfileFromJSONTyped(json, false);
}

export function UserProfileFromJSONTyped(json: any, ignoreDiscriminator: boolean): UserProfile {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'] == null ? undefined : json['id'],
        'fullName': json['full_name'] == null ? undefined : json['full_name'],
        'avatarUrl': json['avatar_url'] == null ? undefined : json['avatar_url'],
        'timezone': json['timezone'] == null ? undefined : json['timezone'],
        'createdAt': json['created_at'] == null ? undefined : (new Date(json['created_at'])),
        'updatedAt': json['updated_at'] == null ? undefined : (new Date(json['updated_at'])),
    };
}

  export function UserProfileToJSON(json: any): UserProfile {
      return UserProfileToJSONTyped(json, false);
  }

  export function UserProfileToJSONTyped(value?: UserProfile | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'full_name': value['fullName'],
        'avatar_url': value['avatarUrl'],
        'timezone': value['timezone'],
        'created_at': value['createdAt'] == null ? undefined : ((value['createdAt']).toISOString()),
        'updated_at': value['updatedAt'] == null ? undefined : ((value['updatedAt']).toISOString()),
    };
}

